variables:
  TEST_DOCKER_IMAGE: mcr.microsoft.com/playwright:v1.45.1-jammy
  API_E2E_PROJECT_ROOT: "$CI_PROJECT_DIR/services/api-e2e"

.if-mr-pipeline: &if-mr-pipeline
  if: '$CI_PIPELINE_SOURCE == "merge_request_event"'

.depend-on-e2e-api-build:
  needs:
    - job: api:e2e:build
      artifacts: true

.configure-api-test-job:
  image: $TEST_DOCKER_IMAGE
  before_script:
    - cd $API_E2E_PROJECT_ROOT
    - yarn install
    - yarn setup

.run-api-test-base:
  stage: test
  artifacts:
    when: always
    paths:
      - ${API_E2E_PROJECT_ROOT}/results
    reports:
      junit: ${API_E2E_PROJECT_ROOT}/results/junit/ict-test-results.xml
  allow_failure: false
  variables:
    ICT_ENV: $TEST_API_DOWNSTREAM_ENV
    GITLAB_ACCESS_TOKEN: $GROUP_ACCESS_TOKEN
  extends:
    - .configure-api-test-job

.run-api-smoke-test:
  stage: verify
  extends:
    - .run-api-test-base
    - .depend-on-e2e-api-build
  script:
    - yarn test:smoke

.run-api-regression-test:
  stage: verify
  extends:
    - .run-api-test-base
    - .depend-on-e2e-api-build
  allow_failure: true
  script:
    - yarn test:regression

api:e2e:build:
  stage: build
  extends:
    - .configure-api-test-job
  needs: []
  allow_failure: false
  variables:
    SECURE_FILES_DOWNLOAD_PATH: "./"
  artifacts:
    untracked: true
  script:
    - curl --silent "https://gitlab.com/gitlab-org/incubation-engineering/mobile-devops/download-secure-files/-/raw/main/installer" | bash
    - yarn run build
  rules:
    - <<: *if-mr-pipeline