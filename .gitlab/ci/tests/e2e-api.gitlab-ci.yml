include:
  - local: .gitlab/ci/tests/e2e-api-base.gitlab-ci.yml

.if-scheduled-regression-test: &if-scheduled-regression-test
  if: '$CI_PIPELINE_SOURCE == "schedule" && $ICT_API_TEST_ENV'

.if-stage-or-prod-mr-pipeline: &if-stage-or-prod-mr-pipeline
  if: '$CI_MERGE_REQUEST_TARGET_BRANCH_PROTECTED && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME != $DEV_BRANCH'

.if-dev-commit-protected: &if-dev-commit-protected
  if: "$CI_COMMIT_BRANCH == $DEV_BRANCH && $CI_COMMIT_REF_PROTECTED == 'true'"

.if-dev-mr-pipeline: &if-dev-mr-pipeline
  if: "$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $DEV_BRANCH"

.if-mr-pipeline: &if-mr-pipeline
  if: '$CI_PIPELINE_SOURCE == "merge_request_event"'

.if-dev-commit: &if-dev-commit
  if: '$CI_COMMIT_BRANCH == $DEV_BRANCH'

.if-stage-commit: &if-stage-commit
  if: '$CI_COMMIT_BRANCH == $STAGE_BRANCH'

.if-prod-commit: &if-prod-commit
  if: '$CI_COMMIT_BRANCH == $PROD_BRANCH'

api:e2e:smoke:
  stage: .post
  extends:
    - .run-api-smoke-test
  rules:
    - <<: [*if-stage-or-prod-mr-pipeline]

api:e2e:regression:post-deploy:dev:
  stage: verify
  extends:
    - .run-api-regression-test
  needs:
    - job: infrastructure:deploy
      artifacts: false
      optional: true
  rules:
    - <<: *if-dev-commit-protected

api:e2e:regression:schedule:
  stage: verify
  extends:
    - .run-api-regression-test
  allow_failure: true
  variables:
    ICT_ENV: dev
  script:
    - yarn test:regression
  rules:
    - <<: *if-scheduled-regression-test
