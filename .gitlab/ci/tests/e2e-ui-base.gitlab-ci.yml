include:
  - local: .gitlab/ci/includes/shared.gitlab-ci.yml

variables:
  TEST_ROOT: "$CI_PROJECT_DIR/services/ui-e2e"
  TEST_DOCKER_IMAGE: mcr.microsoft.com/playwright:v1.45.1-jammy

.if-mr-pipeline: &if-mr-pipeline
  if: '$CI_PIPELINE_SOURCE == "merge_request_event"'

.navigate-to-mise-directory: &navigate-to-mise-directory
  - cd $UI_PROJECT_ROOT

.playwright-check-test-result: &playwright-check-test-result
  - mise run test:check
  - echo "Testing Finished..."

.playwright-test-artifacts: &playwright-test-artifacts
  when: always
  paths:
    - ${TEST_ROOT}/results
  reports:
    junit: ${TEST_ROOT}/results/junit/ict-test-results.xml

.e2e:ui:playwright-artifacts:
  artifacts:
    <<: *playwright-test-artifacts

.depend-on-ui-build:
  needs:
    - job: ui:e2e:build
      artifacts: true

.configure-test-job:
  extends:
    - .common-base
  before_script:
    - *navigate-to-mise-directory
    - !reference [.common-base, before_script]

.run-test-base:
  extends:
    - .configure-test-job
  stage: test
  allow_failure: false
  variables:
    ICT_ENV: $TARGET_ENVIRONMENT
    ICT_BASE_URL: $TEST_SUITE_UI_URL
    ICT_API_URL: $TEST_SUITE_API_URL
    GITLAB_ACCESS_TOKEN: ${GROUP_ACCESS_TOKEN}
  after_script:
    - *navigate-to-mise-directory
    - *playwright-check-test-result
  
.run-smoke-test:
  stage: verify
  extends:
    - .run-test-base
    - .depend-on-ui-build
  script:
    - mise run test:smoke
  artifacts:
    <<: *playwright-test-artifacts

.run-regression-test:
  stage: verify
  extends:
    - .run-test-base
    - .depend-on-ui-build
  allow_failure: true
  script:
    - mise run test:regression
  artifacts:
    <<: *playwright-test-artifacts

# keeping for future ref on how to shard the job once it gets to long.
# .run-regression-test:
#   stage: verify
#   extends:
#     - .run-test-base
#     - .depend-on-ui-build
#   parallel: 10
#   allow_failure: false
#   script:
#     - mise run test:regression --shard=$CI_NODE_INDEX/$CI_NODE_TOTAL
#   artifacts:
#     <<: *playwright-test-artifacts

ui:e2e:build:
  stage: build
  extends:
    - .configure-test-job
  needs: []
  allow_failure: false
  variables:
      SECURE_FILES_DOWNLOAD_PATH: "./"
  artifacts:
    untracked: true
  script:
    - mise run test:build
  rules:
    - <<: *if-mr-pipeline