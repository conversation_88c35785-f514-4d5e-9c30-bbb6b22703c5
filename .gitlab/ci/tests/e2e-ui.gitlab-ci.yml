include:
  - local: .gitlab/ci/tests/e2e-ui-base.gitlab-ci.yml

.navigate-to-mise-directory: &navigate-to-mise-directory
  - cd $UI_PROJECT_ROOT

.if-dev-mr-pipeline: &if-dev-mr-pipeline
  if: "$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $DEV_BRANCH"

.if-stage-mr-pipeline: &if-stage-mr-pipeline
  if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $STAGE_BRANCH'

.if-stage-or-prod-mr-pipeline: &if-stage-or-prod-mr-pipeline
  if: '$CI_MERGE_REQUEST_TARGET_BRANCH_PROTECTED && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME != $DEV_BRANCH'

.if-scheduled-regression-test: &if-scheduled-regression-test
  if: '$CI_PIPELINE_SOURCE == "schedule" && $ICT_TEST_ENV'

.if-dev-commit-protected: &if-dev-commit-protected
  if: "$CI_COMMIT_BRANCH == $DEV_BRANCH && $CI_COMMIT_REF_PROTECTED == 'true'"

.if-web-triggered-test: &if-web-triggered-test
  if: '$CI_PIPELINE_SOURCE == "web" && $SUITE'

.if-dev-commit: &if-dev-commit
  if: '$CI_COMMIT_BRANCH == $DEV_BRANCH'

.if-stage-commit: &if-stage-commit
  if: '$CI_COMMIT_BRANCH == $STAGE_BRANCH'

.if-prod-commit: &if-prod-commit
  if: '$CI_COMMIT_BRANCH == $PROD_BRANCH'

.automation-base:
  variables:
    SECURE_FILES_DOWNLOAD_PATH: "./"
  extends:
    - .common-base
  after_script:
    - *navigate-to-mise-directory
    - mise run test:check

e2e:ui:smoke:post:
  allow_failure: true
  stage: .post
  extends:
    - .run-smoke-test
  variables:    
    ICT_BASE_URL: $TEST_SUITE_DOWNSTREAM_UI_URL
  rules:
    - <<: [*if-dev-commit, *if-stage-commit, *if-prod-commit]
      
e2e:ui:regression:
  allow_failure: true
  extends:
    - .run-regression-test
  rules:
    - <<: *if-stage-mr-pipeline

ui:e2e:regression:post-deploy:dev:
  stage: verify
  extends:
    - .run-regression-test
  needs:
    - job: infrastructure:deploy
      artifacts: false
      optional: true
    - job: ui:publish
      artifacts: false
      optional: true
  rules:
    - <<: *if-dev-commit-protected

e2e:ui:regression:schedule:
  stage: verify
  extends:
    - .automation-base
    - .e2e:ui:playwright-artifacts
    - .configure-test-job
  allow_failure: true
  variables:
    ICT_ENV: $ICT_ENVIRONMENT
  script:
    - mise run test:build 
    - mise run test:regression 
  rules:
    - <<: *if-scheduled-regression-test

e2e:ui:test-suite:web:
  stage: verify
  extends:
    - .automation-base
    - .e2e:ui:playwright-artifacts
    - .configure-test-job
  allow_failure: true
  variables:
    ICT_ENV: $DEV_BRANCH
    ENABLE_TEST_RAIL: "TRUE"
  script:
    - mise run test:playwright:setup
    - echo "Testing ICT Environment - ${ICT_BASE_URL} - SUITE ${SUITE}"
    - npm run test:$SUITE
  rules:
    - <<: *if-web-triggered-test
