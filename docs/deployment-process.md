# Deployment Process

## Overview

Control Tower follows a GitFlow-based deployment strategy with automated processes for managing deployments across development (dev), staging (stage), and production (prod) environments.

## Branch Strategy

### Main Branches

- **main** (Production): Contains production-ready code
- **stage**: Pre-production testing environment
- **dev**: Integration branch for feature development

### Branch Naming Convention

All branches must be prefixed with a JIRA ticket number:

```bash
ICT-1234-descriptive-name
```

Examples:
- `ICT-4321-add-dark-mode`
- `ICT-8765-fix-auth-loop`

### Hotfix Process

Hotfixes can be identified in two ways:
1. Optional: Using the `hotfix/` directory prefix
   ```bash
   hotfix/ICT-5678-fix-critical-bug
   ```
2. Required (if not using hotfix/ directory): Add "hotfix" label to the MR

## Deployment Flow

### Branch Strategy Overview
```mermaid
gitGraph
    commit id: "initial"
    branch dev
    checkout dev
    commit id: "dev work"
    branch feature/ICT-123
    commit id: "feature work"
    checkout dev
    merge feature/ICT-123 tag: "feature merge"
    branch stage
    checkout stage
    merge dev tag: "promote to stage"
    branch main
    checkout main
    merge stage tag: "release to prod"
    branch hotfix/ICT-456
    commit id: "hotfix"
    checkout main
    merge hotfix/ICT-456 tag: "emergency fix"
    checkout dev
    merge main tag: "auto-sync hotfix"
```

### Pipeline Stages Flow
```mermaid
graph LR
    A[validate] --> B[build]
    B --> C[test]
    C --> D[generate]
    D --> E[pre-deploy]
    E --> F[deploy]
    F --> G[verify]
    G --> H[scan]
    H --> I[post-deploy]
    
    style A fill:#e6f3ff,stroke:#4a90e2
    style B fill:#e6f3ff,stroke:#4a90e2
    style C fill:#e6f3ff,stroke:#4a90e2
    style D fill:#e6f3ff,stroke:#4a90e2
    style E fill:#e6f3ff,stroke:#4a90e2
    style F fill:#e6f3ff,stroke:#4a90e2
    style G fill:#e6f3ff,stroke:#4a90e2
    style H fill:#e6f3ff,stroke:#4a90e2
    style I fill:#e6f3ff,stroke:#4a90e2
```

### Environment Promotion Flow
```mermaid
graph LR
    A[Feature Branch] -->|MR + Tests| B[dev]
    B -->|MR + E2E| C[stage]
    C -->|MR + Full Suite| D[main/prod]
    
    E[Hotfix Branch] -->|Emergency MR| D
    D -->|Auto Sync| B
    
    style A fill:#f9f9f9,stroke:#999
    style B fill:#dff0d8,stroke:#5cb85c
    style C fill:#fcf8e3,stroke:#f0ad4e
    style D fill:#f2dede,stroke:#d9534f
    style E fill:#f9f9f9,stroke:#999
```

### Feature Development
1. Create branch from `dev` with JIRA ticket prefix
   ```bash
   git checkout -b ICT-1234-new-feature dev
   ```
2. Develop and test locally
3. Create MR targeting `dev`
4. Pipeline runs:
   - Static analysis
   - Unit tests
   - E2E tests
   - Infrastructure validation
5. Review and merge to `dev`
6. Automatic deployment to dev environment

### Staging Deployment
1. Create MR from `dev` to `stage` (automated & scheduled)
2. Pipeline runs:
   - All tests against stage environment
   - Infrastructure validation
   - E2E UI tests
   - E2E API tests
3. Review and merge to `stage` (release engineer)
4. Automatic deployment to stage environment

### Production Deployment
1. Create MR from `stage` to `main` (automated & schedule)
2. Pipeline runs:
   - Full test suite against prod configuration
   - Infrastructure validation
   - E2E tests using stage URLs
3. Review and merge to `main` (release engineer)
4. Automatic:
   - Deployment to production
   - Version tagging
   - Release creation
   - Changelog generation

### Hotfix Process

#### Hotfix Flow Overview
```mermaid
sequenceDiagram
    participant F as Feature Branch
    participant M as main
    participant A as Automation
    participant D as dev
    
    F->>M: 1. Create MR with hotfix label
    Note over F,M: Manual review & merge
    M->>M: 2. Deploy to prod
    M->>A: 3. Trigger automation
    A->>A: Create sync branch
    A->>D: 4. Create auto-merge MR
    Note over D: 5. Auto-merge when<br/>checks pass
```

#### Hotfix Branch Creation
1. Create branch with JIRA ticket prefix
   ```bash
   # Option 1: Using hotfix directory
   git checkout -b hotfix/ICT-5678-critical-fix main
   
   # Option 2: Regular branch (requires hotfix label on MR)
   git checkout -b ICT-5678-critical-fix main
   ```

#### Automated Sync Process
```mermaid
flowchart TD
    A[Hotfix Merged to Main] -->|Trigger| B[Sync Automation]
    B -->|Create Branch| C[hotfix/sync-SHA]
    C -->|Cherry Pick| D[Apply Changes]
    D -->|Create MR| E[Auto-merge MR to dev]
    E -->|Run Checks| F{All Checks Pass?}
    F -->|Yes| G[Auto Merge]
    F -->|No| H[Manual Review]
    
    classDef error fill:#f2dede,stroke:#d9534f;
    classDef info fill:#e6f3ff,stroke:#4a90e2;
    classDef success fill:#dff0d8,stroke:#5cb85c;
    classDef warning fill:#fcf8e3,stroke:#f0ad4e;
    
    class A,H error;
    class B info;
    class C,D,E,G success;
    class F warning;
```

#### Process Steps
1. Create MR to `main` (add "hotfix" label if not using hotfix/ directory)
2. Review and merge to main
3. Automated process triggers:
   - Deployment to production
   - Creation of sync branch
   - MR creation to sync changes back to `dev`
   - Auto-merge when all checks pass

## CI/CD Pipeline Stages

The CI/CD pipeline is defined in the following files:

- [`.gitlab-ci.yml`](.gitlab-ci.yml) - Main pipeline configuration
- [`.gitlab/ci/mr-labels.gitlab-ci.yml`](.gitlab/ci/mr-labels.gitlab-ci.yml) - Merge request label handling
- [`.gitlab/ci/debug-jobs.gitlab-ci.yml`](.gitlab/ci/debug-jobs.gitlab-ci.yml) - Debug job configurations
- [`.gitlab/ci/static-analysis.gitlab-ci.yml`](.gitlab/ci/static-analysis.gitlab-ci.yml) - Static code analysis
- [`.gitlab/ci/service-release.gitlab-ci.yml`](.gitlab/ci/service-release.gitlab-ci.yml) - Service release automation
- [`.gitlab/ci/api/*.gitlab-ci.yml`](.gitlab/ci/api/) - API service pipelines
- [`.gitlab/ci/ui/*.gitlab-ci.yml`](.gitlab/ci/ui/) - UI service pipelines
- [`.gitlab/ci/tests/e2e-ui.gitlab-ci.yml`](.gitlab/ci/tests/e2e-ui.gitlab-ci.yml) - UI E2E tests
- [`.gitlab/ci/tests/e2e-api.gitlab-ci.yml`](.gitlab/ci/tests/e2e-api.gitlab-ci.yml) - API E2E tests
- [`.gitlab/ci/infrastructure/terraform-module-publishing.gitlab-ci.yml`](.gitlab/ci/infrastructure/terraform-module-publishing.gitlab-ci.yml) - Terraform module publishing
- [`.gitlab/ci/infrastructure/environments-infrastructure.gitlab-ci.yml`](.gitlab/ci/infrastructure/environments-infrastructure.gitlab-ci.yml) - Environment infrastructure
- [`.gitlab/ci/hotfix-sync.gitlab-ci.yml`](.gitlab/ci/hotfix-sync.gitlab-ci.yml) - Hotfix synchronization

The pipeline consists of the following stages:

1. **validate**: Initial code validation
2. **build**: Build artifacts and containers
3. **test**: Run test suites
4. **generate**: Generate deployment artifacts
5. **pre-deploy**: Pre-deployment checks
6. **deploy**: Deploy to target environment
7. **verify**: Post-deployment verification
8. **scan**: Security and compliance scans
9. **post-deploy**: Post-deployment tasks (e.g., hotfix syncing)

## Environment URLs

### Production
- UI: https://ict.dematic.cloud/
- API: https://api.ict.dematic.cloud

### Staging
- UI: https://stage-us-east1.ict.dematic.dev/
- API: https://stage.api.ict.dematic.dev

### Development
- UI: https://dev.ict.dematic.dev/
- API: https://dev.api.ict.dematic.dev

## Automated Checks

### Pipeline Triggers and Checks
```mermaid
graph TD
    subgraph "Feature Branch Pipeline"
        F1[Static Analysis] --> F2[Unit Tests]
        F2 --> F3[E2E Tests]
        F3 --> F4[Infrastructure Validation]
        F4 --> F5[API Compatibility]
    end
    
    subgraph "Protected Branch Pipeline"
        P1[Feature Branch Checks] --> P2[Extended E2E Suite]
        P2 --> P3[Infrastructure Deployment]
        P3 --> P4[Release Automation]
        P4 --> P5[Hotfix Sync]
    end
    
    subgraph "Change Detection"
        C1[UI Changes] --> T1[UI Pipeline]
        C2[API Changes] --> T2[API Pipeline]
        C3[Infrastructure] --> T3[Terraform Pipeline]
        C4[SDK Changes] --> T4[SDK Pipeline]
    end
    
    style F1 fill:#dff0d8,stroke:#5cb85c
    style F2 fill:#dff0d8,stroke:#5cb85c
    style F3 fill:#dff0d8,stroke:#5cb85c
    style F4 fill:#dff0d8,stroke:#5cb85c
    style F5 fill:#dff0d8,stroke:#5cb85c
    
    style P1 fill:#fcf8e3,stroke:#f0ad4e
    style P2 fill:#fcf8e3,stroke:#f0ad4e
    style P3 fill:#fcf8e3,stroke:#f0ad4e
    style P4 fill:#fcf8e3,stroke:#f0ad4e
    style P5 fill:#fcf8e3,stroke:#f0ad4e
    
    style C1 fill:#e6f3ff,stroke:#4a90e2
    style C2 fill:#e6f3ff,stroke:#4a90e2
    style C3 fill:#e6f3ff,stroke:#4a90e2
    style C4 fill:#e6f3ff,stroke:#4a90e2
    style T1 fill:#f2dede,stroke:#d9534f
    style T2 fill:#f2dede,stroke:#d9534f
    style T3 fill:#f2dede,stroke:#d9534f
    style T4 fill:#f2dede,stroke:#d9534f
```

### Feature Branch Checks
- Static code analysis
- Unit tests
- E2E tests
- Infrastructure validation
- API compatibility checks

### Protected Branch Checks (stage, main)
```mermaid
graph LR
    A[Code Changes] --> B{Branch Type}
    B -->|Feature| C[Basic Checks]
    B -->|Protected| D[Extended Checks]
    
    subgraph "Basic Checks"
        C --> C1[Static Analysis]
        C --> C2[Unit Tests]
        C --> C3[Basic E2E]
    end
    
    subgraph "Extended Checks"
        D --> D1[All Basic Checks]
        D --> D2[Full E2E Suite]
        D --> D3[Infrastructure]
        D --> D4[Release Steps]
    end
    
    style A fill:#f9f9f9,stroke:#999
    style B fill:#e6f3ff,stroke:#4a90e2
    style C fill:#dff0d8,stroke:#5cb85c
    style D fill:#fcf8e3,stroke:#f0ad4e
```

### Check Details
- All feature branch checks
- Extended E2E test suite
- Infrastructure deployment validation
- Release automation (main only)
- Hotfix sync automation (main only)

## Change Detection

The pipeline automatically detects changes in:
- UI components
- API services
- Metric processor
- Infrastructure code
- Environment configurations
- ICT SDK changes

Each type of change triggers relevant pipeline jobs and tests.

## Best Practices

### Branch Management
1. Always branch from the correct source
   - Features from `dev`
   - Hotfixes from `main`
2. Keep branches up to date with their source
3. Delete branches after merging

### Commit Messages
```bash
<type>(<scope>): <ticket-number> - <description>

# Examples:
feat(api): ICT-1234 - add new inventory endpoint
fix(ui): ICT-5678 - resolve dashboard loading issue
```

### Merge Requests
1. Use descriptive titles with ticket numbers
2. Add appropriate labels
3. Link related issues
4. Ensure CI/CD pipeline passes
5. Get required approvals

### Hotfix Guidelines
1. Only use for critical production issues
2. Always include JIRA ticket number
3. Add "hotfix" label if not using hotfix/ directory
4. Monitor automated sync to dev

## Monitoring and Rollback

### Monitoring
- Watch pipeline status in GitLab
- Monitor application metrics
- Check deployment logs

### Rollback Process
1. Revert problematic merge
2. Create emergency MR
3. Fast-track through pipeline
4. Monitor recovery

## Support and Escalation

For deployment issues:
1. Check pipeline logs
2. Review environment status
3. Contact DevOps team if needed
