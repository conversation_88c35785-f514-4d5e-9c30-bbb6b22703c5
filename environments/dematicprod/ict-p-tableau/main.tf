# get a reference to the parent DNS managed zone
data "google_dns_managed_zone" "parent" {
  name    = data.terraform_remote_state.common.outputs.prod_dns_zone.zone_name
  project = data.terraform_remote_state.common.outputs.prod_dns_zone.project_id
}

# create the project
module "project" {
  source  = "terraform-google-modules/project-factory/google"
  version = "18.0.0"

  name                     = local.project_name
  random_project_id        = true
  random_project_id_length = 4
  deletion_policy          = "DELETE"

  billing_account = var.billing_account_id
  folder_id       = var.lz_folder_id
  org_id          = var.organization_id

  auto_create_network     = false
  default_service_account = "disable"
  activate_apis           = var.tableau_project_apis
  activate_api_identities = var.tableau_project_api_identities

  labels = local.project_labels
}

# create a dedicated DNS managed zone for the project
resource "google_dns_managed_zone" "main" {
  project = module.project.project_id

  name        = local.zone_name
  dns_name    = local.dns_name
  description = "Managed DNS zone for the production Tableau environment"

  labels = local.common_resource_labels
}

# delegate the subdomain from the parent managed zone to the Tableau managed zone
resource "google_dns_record_set" "parent_ns" {
  project = data.google_dns_managed_zone.parent.project

  name = local.dns_name
  type = "NS"

  managed_zone = data.google_dns_managed_zone.parent.name
  rrdatas      = google_dns_managed_zone.main.name_servers
  ttl          = var.global_dns_config.default_ns_record_ttl
}

# create the Service Account for Insights Terraform deployments
resource "google_service_account" "terraform" {
  project = module.project.project_id

  account_id   = "terraform-tableau-sa"
  display_name = "Terraform Service Account"
}

# assign the required roles
resource "google_project_iam_member" "terraform" {
  project  = module.project.project_id
  for_each = var.tableau_terraform_service_account_roles

  member = "serviceAccount:${google_service_account.terraform.email}"
  role   = each.value
}

# allow Terraform deployers to impersonate the Service Account
resource "google_service_account_iam_member" "tableau_deployers" {
  service_account_id = google_service_account.terraform.id

  for_each = {
    for assignment in setproduct(
      var.tableau_terraform_deployers,
      var.tableau_terraform_deployer_roles
    ) :
    "${assignment[0]}-${assignment[1]}" => {
      deployer = assignment[0]
      role     = assignment[1]
    }
  }
  role   = each.value.role
  member = each.value.deployer
}

# assign the admin group roles
resource "google_project_iam_member" "admin_group" {
  project  = module.project.project_id
  for_each = var.tableau_admin_group_roles

  role   = each.value
  member = "group:${var.tableau_admin_group}"
}

resource "google_service_account" "edp_integration" {
  account_id   = "edp-integration-sa"
  display_name = "EDP Integration Service Account"
  project      = module.project.project_id
}

resource "google_project_iam_member" "edp_integration_roles" {
  for_each = var.tableau_edp_integration_service_account_roles
  project  = module.project.project_id

  role   = each.value
  member = "serviceAccount:${google_service_account.edp_integration.email}"
}

resource "google_service_account_key" "edp_integration" {
  service_account_id = google_service_account.edp_integration.name
}

resource "google_secret_manager_secret" "edp_integration" {
  secret_id = "edp-integration-sa-key"
  replication {
    auto {
    }
  }

  depends_on = [google_service_account_key.edp_integration]
}

resource "google_secret_manager_secret_version" "edp_integration" {
  secret      = google_secret_manager_secret.edp_integration.id
  secret_data = google_service_account_key.edp_integration.private_key
}
