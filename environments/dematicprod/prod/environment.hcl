# =====================================
# Prod Environment Variables
# =====================================

locals {
  lz_config                      = read_terragrunt_config(find_in_parent_folders("lz.hcl"))
  cicd_service_account_principal = local.lz_config.locals.cicd_service_account_principal
}

inputs = {
  environment_folder_id     = "************" # DematicProd/ControlTower/prod
  environment_folder_name   = "prod"
  environment_friendly_name = "Prod"
  environment_name          = "prod"

  auth0_config = {
    domain   = "control-tower-prod.us.auth0.com"
    audience = "https://api.ict.dematic.cloud"
  }

  availability_config = {
    url        = "https://system-availability-api-************.us-east1.run.app"
    project_id = "edp-p-us-east1-etl"
  }

  edp_config = {
    project_id      = "edp-p-us-east1-etl"
    pubsub_topic_id = "adi-data"
  }

  ignition_upstream_url = "prod-dematic-software.ignition.ict.dematic.cloud"

  metric_processor_url = "https://prod-ict-etl-metric-processor-************.us-east1.run.app"

  aiml_config = {
    agent_search_url            = "https://genai.aiml.dematic.cloud"
    agent_search_audience_url   = "https://dematic-chat-********123.us-central1.run.app"
    gold_questions_audience_url = "https://ragengine-********123.us-central1.run.app"
  }

  postgres_databases = [
    "dematic_software",
    "dematic",
    "superior_uniform",
    "ict_development",
    "tti",
    "acehardware"
  ]
}
