locals {
  admin_api_url = "https://${var.auth0_config.domain}"

  api_backend_common_config = {
    environment_variables = merge(
      local.cloud_run_environment_variables,
      {
        AUTH_DOMAIN         = var.auth0_config.domain
        AUTH_AUDIENCE       = var.auth0_config.audience
        CORS_ORIGIN         = var.lz_cors_origin
        DEFAULT_CORS_ORIGIN = "https://ict.dematic.cloud" # module.ui_load_balancer.instance_base_url
        ENABLE_OTEL         = false
        ENVIRONMENT         = var.environment_name
        INSTANCE            = local.instance_prefix
        PROJECT_ID          = local.project_id
      },
    )

    security_roles = concat(
      var.global_api_security_roles,
      var.lz_api_security_roles,
      var.api_security_roles
    )
  }

  api_backend_default_config = {
    image_tag = "stable"
    region    = var.global_default_region

    basic_health_check_path = "healthcheck"
    full_health_check_path  = "healthcheck/full"

    artifact_registry_repository = data.terraform_remote_state.common.outputs.docker_repository

    backend_service_config = {
      protocol  = "HTTP"
      port_name = "http"
      timeout   = 30

      log_config = {
        enable      = true
        sample_rate = 1.0
      }
    }

    bucket_config = {
      location                = "US"
      access_logs_bucket_name = module.scaffolding.access_logs_bucket_name
      storage_class           = "STANDARD"
    }

    cloud_run_config         = var.lz_default_cloud_run_config
    cloud_run_ingress_config = var.lz_default_cloud_run_ingress_config
  }

  # Environment variable groups

  api_availability_environment_variables = {
    SYSTEM_AVAILABILITY_URL        = var.availability_config.url
    SYSTEM_AVAILABILITY_PROJECT_ID = var.availability_config.project_id
  }

  api_edp_environment_variables = {
    EDP_BIGQUERY_PROJECT_ID = var.edp_config.project_id
    EDP_PUBSUB_TOPIC_ID     = var.edp_config.pubsub_topic_id
  }

  api_metric_processor_environment_variables = {
    METRIC_PROCESSOR_METRICS_API_URL = module.metric-processor.service_url
  }

  api_aiml_environment_variables = {
    GOLD_QUESTIONS_AUDIENCE_URL = var.aiml_config.gold_questions_audience_url
    AGENT_SEARCH_URL            = var.aiml_config.agent_search_url
    AGENT_SEARCH_AUDIENCE_URL   = var.aiml_config.agent_search_audience_url
  }

  api_postgresql_environment_variables = {
    CLOUD_SQL_CONN_NAME      = module.postgresql_svpc.master_instance.connection_name
    GCP_POSTGRES_SECRET_NAME = module.postgresql_svpc.connection_string_secret_version
  }

  # TODO: the auth string needs to be removed ASAP
  api_redis_environment_variables = {
    REDISAUTHSTRING       = nonsensitive(module.redis-svpc.auth_string)
    REDISHOST             = module.redis-svpc.host_ip
    REDISPORT             = module.redis-svpc.port
    GCP_REDIS_SECRET_NAME = module.redis-svpc.auth_string_secret_name
  }
}
