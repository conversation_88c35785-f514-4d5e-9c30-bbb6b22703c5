inputs = {
  tableau_environment_name = "tableau"
  tableau_subdomain_id = "bi"
  
  tableau_project_api_identities = [
    {
      api   = "secretmanager.googleapis.com"
      roles = []
    },
    {
      api = "servicenetworking.googleapis.com"
      roles = [
        "roles/compute.networkAdmin",
      ]
    }
  ]
  
  tableau_project_apis = [
    "bigquery.googleapis.com",
    "cloudbuild.googleapis.com",
    "cloudfunctions.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "cloudscheduler.googleapis.com",
    "compute.googleapis.com",
    "dns.googleapis.com",
    "eventarc.googleapis.com",
    "iam.googleapis.com",
    "run.googleapis.com",
    "secretmanager.googleapis.com",
    "vpcaccess.googleapis.com",
  ]

  tableau_terraform_deployer_roles = [
    "roles/iam.serviceAccountTokenCreator",
    "roles/iam.serviceAccountUser",
  ]
  
  tableau_terraform_deployers = [
    "user:<EMAIL>"
  ]

  tableau_terraform_service_account_roles = [
    "roles/compute.instanceAdmin.v1",
    "roles/compute.loadBalancerAdmin",
    "roles/compute.networkAdmin",
    "roles/compute.publicIpAdmin",
    "roles/compute.securityAdmin",
    "roles/compute.viewer",
    "roles/iam.serviceAccountAdmin",
    "roles/iam.serviceAccountTokenCreator",
    "roles/iap.tunnelResourceAccessor",
    "roles/logging.logWriter",
    "roles/monitoring.metricWriter",
    "roles/resourcemanager.projectIamAdmin",
    "roles/run.admin",
    "roles/serviceusage.serviceUsageAdmin",
    "roles/vpcaccess.admin",
  ]

  tableau_admin_group = "<EMAIL>"
  tableau_admin_group_roles = [
    "roles/compute.instanceAdmin.v1",
    "roles/compute.loadBalancerAdmin",
    "roles/compute.networkAdmin",
    "roles/compute.publicIpAdmin",
    "roles/compute.securityAdmin",
    "roles/compute.viewer",
    "roles/iam.serviceAccountAdmin",
    "roles/iam.serviceAccountTokenCreator",
    "roles/iap.tunnelResourceAccessor",
    "roles/logging.logWriter",
    "roles/monitoring.metricWriter",
    "roles/iam.networkAdmin",
    "roles/resourcemanager.projectIamAdmin",
    "roles/run.admin",
    "roles/serviceusage.serviceUsageAdmin",
    "roles/vpcaccess.admin",
    "roles/bigquery.jobUser",
    "roles/bigquery.dataEditor",
  ]

  tableau_edp_integration_service_account_roles = [
    "roles/bigquery.jobUser",
    "roles/bigquery.dataEditor",
  ]
}