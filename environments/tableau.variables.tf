variable "tableau_environment_name" {
  description = "The environment name to use for Tableau deployments."
}

variable "tableau_project_api_identities" {
  description = <<EOF
    The list of APIs that use service agents that should be enabled in all Tableau projects. This 
    will also force the initialization of the service agent so that they can be immediately used by 
    other Terraform resources.
  EOF

  type = list(object({
    api   = string
    roles = list(string)
  }))
}

variable "tableau_project_apis" {
  description = "The list of APIs that should be enabled on the Tableau project."
  type        = list(string)
}

variable "tableau_subdomain_id" {
  description = <<EOT
    The subdomain identifier for Tableau, used to create the DNS zone. E.g., "bi".
  EOT

  type = string
}

variable "tableau_terraform_deployer_roles" {
  description = "The list of roles that the Tableau deployers require."
  type        = list(string)
}

variable "tableau_terraform_deployers" {
  description = <<EOT
    The list of users allowed to deploy Terraform configurations for Tableau. They will be granted 
    the Service Account User and Token Creator roles on the service account to allow impersonation 
    for deployments.
  EOT

  type = list(string)
}

variable "tableau_terraform_service_account_roles" {
  description = "The set of roles to assign to the Tableau Terraform service accounts."
  type        = set(string)
}

variable "tableau_admin_group" {
  description = "The GCP group that should have admin access to Tableau resources."
  type        = string
}

variable "tableau_admin_group_roles" {
  description = "The list of GCP roles to assign to the tableau_admin_group."
  type        = set(string)
}

variable "tableau_edp_integration_service_account_roles" {
  description = "The roles that should be applied to the Tableau integration service account."
  type        = set(string)
}