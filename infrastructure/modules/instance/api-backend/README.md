<!-- BEGIN_TF_DOCS-->

<!-- NOTE: 
This file is automatically generated. 
Do not modify it manually. 
If you'd like to update the docs, update the header.md file in the templates directory.
-->

# API Backends

## Table of Contents

- [Description](#description)
- [Details](#details)
- [Sub-Modules](#sub-modules)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module is used to create the individual API backends that are registered with the API application load balancer. It defines a collection of sub-modules describing the available types, or versions, of the service, and exposes configuration blocks that allow for the configuration of 0 to many of each supported service type.

## Details

Individual APIs are configured in `map(object)` variables that correspond to the different available service types. For example, each API that requires a storage bucket would be added to the `storage_enabled_apis` variable as on object in the map, with the key corresponding to the name - like "inventory" or "equipment" - and the value being the configuration for that specific API.

The module also exposes variables for common and default configuration values. Common config values are applied to all resources, and are merged with any values specified on individual APIs. Default configuration values are used as fallbacks for any of the optional values defined for the API type. For example, a default value for CPU and memory can be set and only APIs that require more resources (or less/fewer) would have to assign a value to those properties in the object map.

## Sub-Modules

This module defines two sub-modules for creating the individual API endpoints. The `backend-service` module is the standard API definition; it creates the Cloud Run, the Service Account, and the other required resources.

The `storage-enabled-backend-service` is an extension of the `backend-service` that adds a storage bucket and then register's its name as an environment variable on the Cloud Run.

## Usage

Basic usage of this module is as follows:

```hcl
module "api-backend" {
  source  = "gitlab.com/dematic/control-tower-api-backend/google"
  version = "~> 0.1.0"

  # Required variables
  backend_services              = var.backend_services
  cloud_armor_config            = var.cloud_armor_config
  common_config                 = var.common_config
  common_resource_labels        = var.common_resource_labels
  default_config                = var.default_config
  default_service_name          = var.default_service_name
  enforce_unique_naming         = var.enforce_unique_naming
  instance_display_name         = var.instance_display_name
  instance_prefix               = var.instance_prefix
  load_balancing_scheme         = var.load_balancing_scheme
  monitoring_notification_email = var.monitoring_notification_email
  naming_root                   = var.naming_root
  network_config                = var.network_config
  project_id                    = var.project_id
  region                        = var.region
  vpc_access_connector_config   = var.vpc_access_connector_config

  # Optional variables
  storage_enabled_backend_services = {}
  vpc_access_connector_id          = ""
}
```

## Requirements

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Providers

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| backend\_services | ./backend-service | n/a |
| cloud-armor | GoogleCloudPlatform/cloud-armor/google | 5.0.0 |
| storage\_enabled\_backend\_services | ./storage-enabled-backend-service | n/a |

## Resources

| Name | Type |
|------|------|
| [google_compute_subnetwork.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_subnetwork) | resource |
| [google_monitoring_notification_channel.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/monitoring_notification_channel) | resource |
| [google_vpc_access_connector.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/vpc_access_connector) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| backend\_services | The collection of standard backend services to deploy, with each service represented by an      object in the map. The map key is the service name, and will be used for naming the Cloud Run      and the supporting resources; the value is an object containing the configuration for that      service. See the `backend_service` sub-module for more details on the configuration options for      the service. | ```map(object({ service_friendly_name = string url_map_path = string container_image = object({ image_name = string image_tag = optional(string) }) labels = optional(map(string)) region = optional(string) required_roles = optional(list(string)) service_type = optional(string) basic_health_check_path = optional(string) enable_binary_auth = optional(bool) environment_variables = optional(map(string)) full_health_check_path = optional(string) security_policy_id = optional(string) artifact_registry_repository = optional(object({ location = string project = string repository = string })) backend_service_config = optional(object({ protocol = string port_name = string timeout = number log_config = object({ enable = bool sample_rate = optional(number) }) })) cloud_run_config = optional(object({ enable_binary_auth = bool cpu_limit = string memory_limit = string cpu_idle = bool startup_cpu_boost = bool min_instances = number max_instances = number })) }))``` | n/a | yes |
| cloud\_armor\_config | The configuration for the Cloud Armor security policy that will be applied to the backend      services. Layer 7 DDoS protection is optional and can be enabled or disabled. If enabled, all      additional configuration options must be specified; if disabled, the optional configuration      options can be omitted. | ```object({ module_version = string layer_7_ddos_config = object({ enable = bool default_rule_action = optional(string) rule_visibility = optional(string) json_parsing = optional(string) log_level = optional(string) }) })``` | n/a | yes |
| common\_config | The common configuration that should be applied to all Cloud Run services. | ```object({ security_roles = list(string) environment_variables = optional(map(string)) })``` | n/a | yes |
| common\_resource\_labels | The common labels that should be applied to all resources that can be labeled. | `map(string)` | n/a | yes |
| default\_config | The default configuration for the Cloud Run service. This will be used if no specific      configuration is provided for a service. | ```object({ image_tag = optional(string) region = string basic_health_check_path = string full_health_check_path = string artifact_registry_repository = object({ location = string project = string repository = string }) backend_service_config = object({ protocol = string port_name = string timeout = number log_config = object({ enable = bool sample_rate = optional(number) }) }) bucket_config = optional(object({ location = string access_logs_bucket_name = string storage_class = string })) cloud_run_config = object({ enable_binary_auth = bool cpu_limit = string memory_limit = string cpu_idle = bool startup_cpu_boost = bool min_instances = number max_instances = number }) })``` | n/a | yes |
| default\_service\_name | The name of the service that should serve as the default backend for the API. This backend will      be registered as the default route on the load balancer; the value must match the key of one      of the services defined in the `backend_services` or `storage_enabled_backend_services` map      variables. | `string` | n/a | yes |
| enforce\_unique\_naming | Set this to true to use the provided 'environment` value to create a unique prefix for all      resources in the module. This is required to allow multiple instances of the module to be      deployed in the same GCP project. Used by Review Apps to generate the preview environments. ` | `bool` | n/a | yes |
| instance\_display\_name | The human-readable name of the instance that the resources are being deployed to. | `string` | n/a | yes |
| instance\_prefix | The name of the instance that the resources are being deployed to. This value will be      combined with the `naming_root` to create a unique prefix for any resources requiring a      globally-unique identifier. Therefore, it must be unique among Control Tower instances but      should be as short as possible to comply with GCP resource name length restrictions. | `string` | n/a | yes |
| load\_balancing\_scheme | The load balancing scheme to use for the load balancer. For an external load balancer, this can      be either `EXTERNAL` or `EXTERNAL_MANAGED`; for an internal load balancer, this must be either      `INTERNAL` or `INTERNAL_MANAGED`. In both cases, the latter `MANAGED` option is recommended for     production use, as it provides better performance and reliability. | `string` | n/a | yes |
| monitoring\_notification\_email | The email address that monitoring notifications will be sent to. | `string` | n/a | yes |
| naming\_root | The root identifier for all Control Tower resources. This value will be combined with the     `environment` to create a unique prefix for any resources requiring a globally-unique name.      This value is intended to be shared across all Control Tower deployments. As of module      creation, this value is "ict". | `string` | n/a | yes |
| network\_config | Configuration for the network resources to be created.      Note: this module does not validate the flow log configuration beyond ensuring it is not null      if flow logs are enabled. It is intended that the flow log configuration is passed in from the      output of the landing zone module.      See the README and variables.tf of the network submodule of the landing zone module for more      details on the configuration | ```object({ enable_flow_logs = bool network_self_link = string subnet_ip_range = string flow_log_config = optional(object({ aggregation_interval = string flow_sampling = number metadata = string })) })``` | n/a | yes |
| project\_id | The ID of the GCP project to deploy to. | `string` | n/a | yes |
| region | The region to deploy regional resources in. | `string` | n/a | yes |
| storage\_enabled\_backend\_services | The collection of storage-enabled backend services to deploy, with each service represented by      an object in the map. The map key is the service name, and will be used for naming the Cloud      Run and the supporting resources; the value is an object containing the configuration for that      service. See the 'storage\_enabled\_backend\_service' sub-module for more details on the     configuration options for the service. | ```map(object({ service_friendly_name = string url_map_path = string container_image = object({ image_name = string image_tag = optional(string) }) labels = optional(map(string)) region = optional(string) required_roles = optional(list(string)) service_type = optional(string) basic_health_check_path = optional(string) enable_binary_auth = optional(bool) environment_variables = optional(map(string)) full_health_check_path = optional(string) security_policy_id = optional(string) artifact_registry_repository = optional(object({ location = string project = string repository = string })) backend_service_config = optional(object({ protocol = string port_name = string timeout = number log_config = object({ enable = bool sample_rate = optional(number) }) })) bucket_config = optional(object({ location = string access_logs_bucket_name = string storage_class = string })) bucket_objects = optional(list(object({ name = string object_source_path = string, detect_md5hash = bool }))) cloud_run_config = optional(object({ enable_binary_auth = bool cpu_limit = string memory_limit = string cpu_idle = bool startup_cpu_boost = bool min_instances = number max_instances = number })) }))``` | `{}` | no |
| vpc\_access\_connector\_config | The configuration for the VPC access connector. | ```object({ machine_type = string min_instances = number max_instances = number })``` | n/a | yes |
| vpc\_access\_connector\_id | (Optional) The ID of an existing VPC access connector. If provided, the Cloud Run backends will      use this instead of the connector created by the module.      Note: after migration to the shared VPC, the networking resources created by this module should      be removed and this should become non-optional. | `string` | `""` | no |

## Outputs

| Name | Description |
|------|-------------|
| backend\_services | The list of basic backend services created. |
| default\_service\_id | The ID of the backend service that should serve as the default backend for the API. This will      be the ID of the service named in the `default_service_name` variable. |
| storage\_enabled\_backend\_services | The list of storage-enabled backend services created. |

<!-- END_TF_DOCS-->
<!-- BEGIN_TFVARS_DOCS-->

## Terraform TFVARS file examples

```hcl
inputs {
    
  #     The collection of standard backend services to deploy, with each service represented by an
  #     object in the map. The map key is the service name, and will be used for naming the Cloud Run
  #     and the supporting resources; the value is an object containing the configuration for that
  #     service. See the `backend_service` sub-module for more details on the configuration options for
  #     the service.
  #
  backend_services = ""
  
  #     The configuration for the Cloud Armor security policy that will be applied to the backend
  #     services. Layer 7 DDoS protection is optional and can be enabled or disabled. If enabled, all
  #     additional configuration options must be specified; if disabled, the optional configuration
  #     options can be omitted.
  #
  cloud_armor_config = ""
  
  # The common configuration that should be applied to all Cloud Run services.
  common_config = ""
  
  # The common labels that should be applied to all resources that can be labeled.
  common_resource_labels = ""
  
  #     The default configuration for the Cloud Run service. This will be used if no specific
  #     configuration is provided for a service.
  #
  default_config = ""
  
  #     The name of the service that should serve as the default backend for the API. This backend will
  #     be registered as the default route on the load balancer; the value must match the key of one
  #     of the services defined in the `backend_services` or `storage_enabled_backend_services` map
  #     variables.
  #
  default_service_name = ""
  
  #     Set this to true to use the provided 'environment` value to create a unique prefix for all
  #     resources in the module. This is required to allow multiple instances of the module to be
  #     deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  #
  enforce_unique_naming = ""
  
  #     The human-readable name of the instance that the resources are being deployed to.
  #
  instance_display_name = ""
  
  #     The name of the instance that the resources are being deployed to. This value will be
  #     combined with the `naming_root` to create a unique prefix for any resources requiring a
  #     globally-unique identifier. Therefore, it must be unique among Control Tower instances but
  #     should be as short as possible to comply with GCP resource name length restrictions.
  #
  instance_prefix = ""
  
  #     The load balancing scheme to use for the load balancer. For an external load balancer, this can
  #     be either `EXTERNAL` or `EXTERNAL_MANAGED`; for an internal load balancer, this must be either
  #     `INTERNAL` or `INTERNAL_MANAGED`. In both cases, the latter `MANAGED` option is recommended for
  #     production use, as it provides better performance and reliability.
  #
  load_balancing_scheme = ""
  
  # The email address that monitoring notifications will be sent to.
  monitoring_notification_email = ""
  
  #     The root identifier for all Control Tower resources. This value will be combined with the
  #     `environment` to create a unique prefix for any resources requiring a globally-unique name.
  #     This value is intended to be shared across all Control Tower deployments. As of module
  #     creation, this value is "ict".
  #
  naming_root = ""
  
  #     Configuration for the network resources to be created.
  #
  #     Note: this module does not validate the flow log configuration beyond ensuring it is not null
  #     if flow logs are enabled. It is intended that the flow log configuration is passed in from the
  #     output of the landing zone module.
  #
  #     See the README and variables.tf of the network submodule of the landing zone module for more
  #     details on the configuration
  #
  network_config = ""
  
  # The ID of the GCP project to deploy to.
  project_id = ""
  
  # The region to deploy regional resources in.
  region = ""
  
  #     The collection of storage-enabled backend services to deploy, with each service represented by
  #     an object in the map. The map key is the service name, and will be used for naming the Cloud
  #     Run and the supporting resources; the value is an object containing the configuration for that
  #     service. See the 'storage_enabled_backend_service' sub-module for more details on the
  #     configuration options for the service.
  #
storage_enabled_backend_services = {}

# The configuration for the VPC access connector.
vpc_access_connector_config = ""

#     (Optional) The ID of an existing VPC access connector. If provided, the Cloud Run backends will
#     use this instead of the connector created by the module.
#
#     Note: after migration to the shared VPC, the networking resources created by this module should
#     be removed and this should become non-optional.
#
vpc_access_connector_id = ""
}
```

<!-- END_TFVARS_DOCS-->
