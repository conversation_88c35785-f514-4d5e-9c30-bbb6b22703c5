resource "google_service_account" "main" {
  project    = var.project_id
  account_id = local.root_name
}

resource "google_project_iam_member" "main" {
  project  = var.project_id
  for_each = toset(var.required_roles)

  role   = each.value
  member = "serviceAccount:${google_service_account.main.email}"
}

resource "google_cloud_run_v2_service" "main" {
  project  = var.project_id
  name     = local.root_name
  location = var.region

  labels = var.common_resource_labels

  deletion_protection = false

  ingress = var.ingress
  dynamic "binary_authorization" {
    for_each = var.cloud_run_config.enable_binary_auth ? [1] : []

    content {
      use_default = true
    }
  }

  template {
    service_account = google_service_account.main.email

    containers {
      image = data.google_artifact_registry_docker_image.main.self_link

      resources {
        limits = {
          cpu    = var.cloud_run_config.cpu_limit
          memory = var.cloud_run_config.memory_limit
        }

        cpu_idle          = var.cloud_run_config.cpu_idle
        startup_cpu_boost = var.cloud_run_config.startup_cpu_boost
      }

      dynamic "env" {
        for_each = var.environment_variables

        content {
          name  = env.key
          value = env.value
        }
      }
    }

    scaling {
      min_instance_count = var.cloud_run_config.min_instances
      max_instance_count = var.cloud_run_config.max_instances
    }

    vpc_access {
      egress = "PRIVATE_RANGES_ONLY"

      connector = var.vpc_access_connector_id
    }
  }
}

resource "google_cloud_run_service_iam_policy" "main" {
  project  = var.project_id
  location = var.region
  service  = google_cloud_run_v2_service.main.name

  policy_data = data.google_iam_policy.noauth.policy_data
}

resource "google_compute_region_network_endpoint_group" "main" {
  project = var.project_id
  region  = var.region
  name    = local.root_name

  network_endpoint_type = "SERVERLESS"

  cloud_run {
    service = google_cloud_run_v2_service.main.name
  }
}

resource "google_compute_backend_service" "main" {
  project = var.project_id
  name    = local.root_name

  load_balancing_scheme = var.load_balancing_scheme
  protocol              = var.backend_service_config.protocol
  port_name             = var.backend_service_config.port_name
  timeout_sec           = var.backend_service_config.timeout
  security_policy       = var.security_policy_id

  backend {
    group = google_compute_region_network_endpoint_group.main.id
  }

  log_config {
    enable = var.backend_service_config.log_config.enable
    sample_rate = coalesce(
      var.backend_service_config.log_config.sample_rate,
      0.1
    )
  }
}
