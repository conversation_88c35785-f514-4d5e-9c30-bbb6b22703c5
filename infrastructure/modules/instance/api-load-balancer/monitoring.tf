resource "google_monitoring_notification_channel" "main" {
  display_name = local.notification_channel_name
  type         = "email"
  labels = {
    email_address = var.notification_email
  }
  project = var.project_id
}

module "backend-service-monitors" {
  source = "./backend-monitor"

  project_id = var.project_id
  for_each   = var.backend_services

  dns_name                   = local.cname_record_dns_name
  enable_alerting            = var.backend_monitoring_config.alerting.enabled
  enforce_unique_naming      = var.enforce_unique_naming
  instance_display_name      = var.instance_display_name
  instance_prefix            = var.instance_prefix
  health_check_port          = var.backend_monitoring_config.monitoring.http_check_config.port
  notification_email_address = var.notification_email
  service_friendly_name      = each.value.backend_service_friendly_name
  service_url_map_path       = each.value.url_map_path
  use_ssl                    = var.backend_monitoring_config.monitoring.http_check_config.use_ssl
  validate_ssl               = var.backend_monitoring_config.monitoring.http_check_config.validate_ssl
  common_resource_labels     = var.common_resource_labels


  alerting_condition_threshold_aggregation_alignment_period = (
    var.backend_monitoring_config.alerting.condition_threshold == null
    ? null
    : var.backend_monitoring_config.alerting.condition_threshold.aggregation_alignment_period
  )
  alerting_condition_threshold_comparison = (
    var.backend_monitoring_config.alerting.condition_threshold == null
    ? null
    : var.backend_monitoring_config.alerting.condition_threshold.comparison
  )
  alerting_condition_threshold_duration = (
    var.backend_monitoring_config.alerting.condition_threshold == null
    ? null
    : var.backend_monitoring_config.alerting.condition_threshold.duration
  )
  alerting_condition_threshold_trigger_count = (
    var.backend_monitoring_config.alerting.condition_threshold == null
    ? null
    : var.backend_monitoring_config.alerting.condition_threshold.trigger_count
  )

  basic_health_check_path = each.value.basic_health_check_path
  basic_uptime_check_period = (
    var.backend_monitoring_config.monitoring.basic_uptime_check_config.period
  )
  basic_uptime_check_timeout = (
    var.backend_monitoring_config.monitoring.basic_uptime_check_config.timeout
  )

  full_health_check_path = each.value.full_health_check_path
  full_uptime_check_period = (
    var.backend_monitoring_config.monitoring.full_uptime_check_config.period
  )
  full_uptime_check_timeout = (
    var.backend_monitoring_config.monitoring.full_uptime_check_config.timeout
  )
}

# Load Balancer Level Monitoring - Conservative Alert for High Error Rate
resource "google_monitoring_alert_policy" "load_balancer_high_error_rate" {
  project = var.project_id

  display_name = "API Load Balancer High Error Rate - ${var.instance_display_name}"

  enabled  = true
  combiner = "OR"

  conditions {
    display_name = "High HTTP 5xx Error Rate"

    condition_threshold {
      comparison = "COMPARISON_GT"
      duration   = "600s" # 10 minutes - conservative

      filter = <<EOF
metric.type="loadbalancing.googleapis.com/https/request_count" 
  AND resource.type="https_lb_rule"
  AND metric.label."response_code_class"="500"
  AND resource.label.url_map_name="${local.root_name}-load-balancer"
      EOF

      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_SUM"
        per_series_aligner   = "ALIGN_RATE"
      }

      trigger {
        count = 1
      }

      threshold_value = 10.0 # Conservative: 10 errors per second
    }
  }

  notification_channels = [
    google_monitoring_notification_channel.main.name
  ]

  alert_strategy {
    auto_close = "86400s" # 24 hours
  }

  documentation {
    content = "The API Load Balancer is experiencing a high rate of HTTP 5xx errors (>10/sec for 10 minutes). This indicates potential backend service issues or load balancer problems."
  }
}

# Load Balancer Level Monitoring - Conservative Alert for Very High Latency
resource "google_monitoring_alert_policy" "load_balancer_high_latency" {
  project = var.project_id

  display_name = "API Load Balancer High Latency - ${var.instance_display_name}"

  enabled  = true
  combiner = "OR"

  conditions {
    display_name = "Very High Request Latency"

    condition_threshold {
      comparison = "COMPARISON_GT"
      duration   = "600s" # 10 minutes - conservative

      filter = <<EOF
metric.type="loadbalancing.googleapis.com/https/total_latencies" 
  AND resource.type="https_lb_rule"
  AND resource.label.url_map_name="${local.root_name}-load-balancer"
      EOF

      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_PERCENTILE_95"
        per_series_aligner   = "ALIGN_DELTA"
      }

      trigger {
        count = 1
      }

      threshold_value = 30000.0 # Conservative: 30 seconds p95 latency
    }
  }

  notification_channels = [
    google_monitoring_notification_channel.main.name
  ]

  alert_strategy {
    auto_close = "86400s" # 24 hours
  }

  documentation {
    content = "The API Load Balancer is experiencing very high latency (p95 > 30s for 10 minutes). This indicates potential performance issues with backend services or infrastructure problems."
  }
}
