# Neo4j AuraDB Terraform Module

## Table of Contents

- [Description](#description)
- [How It Works](#how-it-works)
- [Components](#components)
- [Prerequisites](#prerequisites)
- [Usage](#usage)
- [Authentication Setup](#authentication-setup)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This Terraform module creates Neo4j AuraDB instances for multiple tenants using direct API calls to the Neo4j Aura service and securely stores their credentials in Google Cloud Secret Manager.

## How It Works

This module uses a **three-phase approach** to manage Neo4j AuraDB instances through direct API integration:

### Phase 1: Plan

- Queries existing Neo4j instances via Aura API (`https://api.neo4j.io/v1/instances`)
- Compares desired state (from `neo4j_customers` variable) with existing instances
- Identifies which instances need to be created
- Returns planning data to Terraform with no side effects

### Phase 2: Apply

- Creates missing Neo4j instances using direct API calls to Neo4j Aura
- Configures instances with specified memory, storage, and region settings
- Waits for instances to become available and retrieves connection credentials
- Stores results in local state file (`.neo4j_created.json`) for consistency
- Only executes when new instances are required

### Phase 3: Read & Store

- Reads created instance details from local state file
- Creates GCP Secret Manager secrets containing connection details for each tenant
- Merges existing and newly created instance data for complete state management
- Ensures all tenants have accessible credentials in GCP

**Key Features:**

- **Direct API Integration**: No GCP Marketplace dependency - uses Neo4j Aura API directly
- **Idempotent Operations**: Safe to run multiple times without creating duplicates
- **State Management**: Local state file ensures consistency across Terraform runs
- **Automated Secrets**: Connection details automatically stored in GCP Secret Manager

## Components

### Neo4j Instance Management

- **Python Script**: `scripts/neo4j_manage.py` handles all Neo4j Aura API interactions
- **Authentication**: OAuth2 flow using credentials stored in GCP Secret Manager
- **Instance Creation**: Creates AuraDB instances with configurable memory, storage, and region
- **State Tracking**: Maintains local state file for idempotent operations

### Secret Management

- **API Credentials**: Retrieves Neo4j API credentials from `auradb_api_client_credentials` secret
- **Connection Secrets**: Creates individual GCP secrets for each tenant's database credentials
- **Naming Convention**: Secrets follow the pattern `{tenant_name}_{facility_name}_AuraDB`

## Prerequisites

Before using this module, ensure you have:

1. **Neo4j Aura Account**: Active account with API access enabled
2. **API Credentials**: Client ID, Client Secret, and Tenant ID from Neo4j Aura Console
3. **GCP Secret Manager**: Secret Manager API enabled in your GCP project
4. **Stored Credentials**: API credentials stored in GCP Secret Manager as `auradb_api_client_credentials`

## Documentation Links

- **Neo4j Aura API Documentation**: https://neo4j.com/docs/aura/api/overview/
- **Neo4j Aura API Specification**: https://neo4j.com/docs/aura/platform/api/specification/

## Usage

```hcl
module "neo4j" {
  source = "../../../../../infrastructure/modules/instance/neo4j"

  # Standard Control Tower variables
  project_id             = local.project_id
  common_resource_labels = local.common_resource_labels

  neo4j_customers = [{
    database_name = "superior_uniform_eudoraar"  # Must be ≤30 chars
    tenant_name   = "superior_uniform"           # Must match EDP pub/sub
    facility_name = "superioruniform_eudoraar"   # Must match EDP pub/sub
  }, {
    database_name = "acehardware_wilmertx"
    tenant_name   = "acehardware"
    facility_name = "acehardware_wilmertx"
  }]

  # Optional: Neo4j specific configuration
  aura_region      = "gcp-us-central1"
  instance_memory  = "1GB"              # Default: "1GB"
  instance_storage = "2GB"              # Default: "2GB"
  aura_type        = "professional-db"  # Default: "professional-db"
  neo4j_version    = "5"                # Default: "5"
}
```

## Important Configuration Notes

### Customer Configuration Requirements

The `neo4j_customers` variable has specific requirements that must be followed:

1. **Database Name Constraints**:

   - Must be between 1-30 characters (Neo4j Aura limitation)
   - Generally should follow pattern: `{tenant_name}_{facility_name}`
   - Use underscores, not hyphens or spaces

2. **EDP Integration Requirements**:

   - `tenant_name` and `facility_name` must **exactly match** the values from EDP silver pub/sub
   - These values are used for data processing and metric correlation
   - Mismatched names will cause data processing failures
   - TBD: We will use the `dataset_id` from the EDP silver pub/sub as the source of truth for these values once they are able
   to send in pubsub attributes of messages per https://jira.dematic.net/browse/EDP-1938

3. **Multiple Tenants**:
   - The module supports multiple customer facilities in a single deployment
   - Each gets its own Neo4j instance and GCP secret
   - Secrets are named: `{tenant_name}_{facility_name}_AuraDB`

### Example Real Configuration

```hcl
neo4j_customers = [
  {
    database_name = "superior_uniform_eudoraar"    # 26 chars (under 30 limit)
    tenant_name   = "superior_uniform"             # From EDP pub/sub
    facility_name = "superioruniform_eudoraar"     # From EDP pub/sub
  },
  {
    database_name = "acehardware_wilmertx"         # 21 chars (under 30 limit)
    tenant_name   = "acehardware"                  # From EDP pub/sub
    facility_name = "acehardware_wilmertx"         # From EDP pub/sub
  }
]
```

## Secret Format

Each tenant gets a GCP secret containing connection details:

```json
{
  "NEO4J_URI": "neo4j+s://{instanceId}.databases.neo4j.io",
  "NEO4J_USERNAME": "neo4j",
  "NEO4J_PASSWORD": "generated-secure-password",
  "AURA_INSTANCEID": "aura-instance-id",
  "AURA_INSTANCENAME": "database_instance_name"
}
```

## Authentication Setup

### Step 1: Create Neo4j Aura API Credentials

1. **Access Neo4j Aura Console**:

   - Go to [Neo4j Aura Console](https://console.neo4j.io/)
   - Log in to your Neo4j Aura account

2. **Get Tenant ID**:

   - Note the tenant ID from the URL: `https://console.neo4j.io/projects/{your_tenant_id}/instances`
   - Save this value - you'll need it for the secret

3. **Generate API Key**:
   - Navigate to **Account Settings** → **API Keys**
   - Click **Generate API Key**
   - Name it descriptively (e.g., `CT CIT TF Key`)
   - Copy the **Client ID** and **Client Secret**

### Step 2: Store Credentials in GCP Secret Manager

Create a secret named `auradb_api_client_credentials` with the following JSON structure:

```json
{
  "CLIENT_ID": "your_client_id_here",
  "CLIENT_SECRET": "your_client_secret_here",
  "TENANT_ID": "your_tenant_id_here"
}
```

**Using gcloud CLI:**

```bash
echo '{
  "CLIENT_ID": "your_client_id_here",
  "CLIENT_SECRET": "your_client_secret_here",
  "TENANT_ID": "your_tenant_id_here"
}' | gcloud secrets create auradb_api_client_credentials \
  --project=your-project-id \
  --data-file=-
```

**Using GCP Console:**

1. Go to Secret Manager in your GCP project
2. Click "Create Secret"
3. Name: `auradb_api_client_credentials`
4. Secret value: Paste the JSON above with your actual values

Once these steps are completed, this module can be executed.
