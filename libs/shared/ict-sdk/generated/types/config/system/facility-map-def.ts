export type FacilityMap = {
  id: string; // string that matches that facility role in auth0 (ex. "acehardware#jeffersonga")
  name: string;
  default?: boolean;
  dataset: string; // dataset in EDP that hosts the data tables for the facility
  tableauProject?: string;
  tenantId?: string; // the tenant_id used by EDP in Pub/Sub attributes and silver table fields (ex. "acehardware")
  facilityId?: string; // the facility_id used by EDP in Pub/Sub attributes and silver table fields (ex. "jeffersonga")
  aimlDatasetId?: string; // the EDP dataset_id AIML uses for this facility
};

export const FacilityMapSchema = {
  $schema: 'http://json-schema.org/draft-07/schema#',
  title: 'facilities',
  type: 'array',
  items: {
    type: 'object',
    properties: {
      id: {
        type: 'string',
      },
      name: {
        type: 'string',
      },
      default: {
        type: 'boolean',
      },
      dataset: {
        type: 'string',
      },
      facilityId: {
        type: 'string',
        description:
          'The facility_id used by EDP in Pub/Sub attributes and silver table fields (ex. "jeffersonga")',
      },
      tenantId: {
        type: 'string',
        description:
          'The tenant_id used by EDP in Pub/Sub attributes and silver table fields (ex. "acehardware")',
      },
      tableauProject: {
        type: 'string',
        description: 'Optional Tableau project name for this facility',
      },
      aimlDatasetId: {
        type: 'string',
        description: 'Optional EDP dataset_id AIML uses for this facility',
      },
    },
    required: ['id', 'name', 'dataset'],
    additionalProperties: false,
  },
};
