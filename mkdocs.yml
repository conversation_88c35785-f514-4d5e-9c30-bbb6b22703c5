site_name: control-tower
site_description: 'Control Tower monorepo documentation'
site_dir: 'site'
docs_dir: 'docs'
repo_url: https://gitlab.com/dematic/controltower/control-tower
edit_uri: blob/main/docs
repo_name: control-tower

nav:
  - Control Tower: index.md
  - Development:
      - Development Guide: development-guide.md
      - Deployment Process: deployment-process.md
      - API Services Architecture: api-services.md
      - CI/CD Pipelines: cicd-pipelines.md
      - Terraform Module Development: terraform-module-development.md
  - Services:
      - API Backend: services/api-backend.md
      - API Services:
          - Admin API: services/api-services/admin-api.md
          - AI API: services/api-services/ai-api.md
          - Config API: services/api-services/config-api.md
          - Data Explorer API: services/api-services/dataexplorer-api.md
          - Diagnostics API: services/api-services/diagnostics-api.md
          - Equipment API: services/api-services/equipment-api.md
          - Instrumentation: services/api-services/instrumentation.md
          - Inventory API: services/api-services/inventory-api.md
          - Operators API: services/api-services/operators-api.md
          - Orders API: services/api-services/orders-api.md
          - Simulation API: services/api-services/simulation-api.md
          - Tableau Management API: services/api-services/tableau-management-api.md
          - Tableau Proxy: services/api-services/tableau-proxy.md
          - TypeORM Migrations CR: services/api-services/typeorm-migrations-cr.md
          - Workstation API: services/api-services/workstation-api.md
          - Mock Data API: services/api-services/mock-data-api.md
      - API E2E Testing: services/api-e2e-testing.md
      - Metric Processor: services/metric-processor.md
      - UI Frontend: services/ui-frontend.md
      - UI E2E Testing: services/ui-e2e-testing.md
  - Infrastructure:
      - Architecture: infrastructure.md
      - Environments: environments.md
      - Modules Overview: infrastructure/modules-overview.md
      - Infrastructure Modules:
          - API Backend Module: infrastructure/modules/api-backend.md
          - UI Load Balancer Module: infrastructure/modules/ui-load-balancer.md
          - PostgreSQL Module: infrastructure/modules/postgresql.md
          - Redis Module: infrastructure/modules/redis.md
          - Ignition Proxy Module: infrastructure/modules/ignition-proxy.md 
  - Backstage Documentation Guide: backstage-documentation-guide.md

plugins:
  - techdocs-core:
      use_material_search: true
  - search:
      separator: '[\s\-,:!=\[\]()"/]+|(?!\b)(?=[A-Z][a-z])|\.(?!\d)|&[lg]t;'
      lang: en
      pipeline:
        - stemmer
        - stopWordFilter
        - trimmer

extra_css:
  - stylesheets/extra.css

theme:
    name: 'material'
    locale: en
    favicon: assets/favicon.ico
    logo: assets/42_01-28-2022_1_Standard_All_White.png
    features:
      - search.highlight
      - search.suggest
      - toc.follow
      - content.code.copy      
      - navigation.top
      - navigation.indexes

markdown_extensions:
  - markdown.extensions.attr_list
  - pymdownx.snippets:
      url_download: true
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format

copyright: Copyright &copy; 2025 Dematic