{"name": "api-e2e", "version": "1.0.0", "main": "index.js", "license": "MIT", "devDependencies": {"@eslint/js": "9.35.0", "@open-wc/eslint-config": "13.0.0", "@playwright/test": "1.53.0", "@types/node": "22.15.31", "@typescript-eslint/eslint-plugin": "8.44.0", "@typescript-eslint/parser": "8.44.0", "dotenv": "16.5.0", "eslint": "9.35.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-unicorn": "61.0.2", "open": "10.1.2", "prettier": "3.6.2", "typedoc": "0.28.10", "typescript": "5.8.3", "typescript-eslint": "8.44.0"}, "scripts": {"setup": "npx playwright install --with-deps chromium", "docs:build": "npx typedoc --tsconfig tsconfig.json --options typedoc.config.mjs --readme ./README.md", "docs": "yarn docs:build; open ./docs/index.html", "build": "tsc --incremental -p tsconfig.json", "test:smoke": "npx playwright test ./tests/smoke/*", "test:regression": "npx playwright test ./tests/regression/*", "test:config": "npx playwright test ./tests/regression/config-api.spec*", "test:debug": "npx playwright test --headed", "lint": "eslint", "lint:fix": "eslint --fix"}, "dependencies": {"auth0": "^4.23.0", "axios": "^1.9.0", "luxon": "^3.6.1", "reflect-metadata": "^0.2.2", "typedi": "^0.10.0", "zod": "^3.24.3", "allure-js-commons": "3.2.2", "allure-playwright": "3.2.2"}}