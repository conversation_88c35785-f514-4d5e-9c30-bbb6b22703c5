// needed for dependency injection
import 'reflect-metadata';

import express from 'express';

import {
  ApiMiddleware,
  Container,
  EnvironmentService,
  WinstonLogger,
} from 'ict-api-foundations';
import {RegisterRoutes} from '../build/routes.ts';

/**
 * Express middleware that will add needed services.
 * @param req Express request.
 * @param res Express response.
 * @param next Express next function.
 */

const app = express();

// setup middlewares
app.use(
  express.urlencoded({
    extended: true,
  }),
);

app.use(express.json());
ApiMiddleware.applyApiDefaultMiddlewares(app);
RegisterRoutes(app);

ApiMiddleware.applyErrorMiddlewares(app);

// get the logger from DI
const logger = Container.get(WinstonLogger);
const envService = Container.get(EnvironmentService);
const port = envService.ports.ai || 8080;
app.listen(port, () => logger.info(`AI API listening on local port ${port}.`));
