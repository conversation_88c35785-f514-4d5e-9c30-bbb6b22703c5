import {
  Container,
  ProtectedRouteMiddleware,
  WinstonLogger,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Middlewares,
  OperationId,
  Route,
  SuccessResponse,
  Tags,
  Query,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.js';
import {GetAvailableEquipmentResponse} from '../defs/equipment-def.js';

@Route('availability')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [configPostgresDatabase()],
  }),
])
export class EquipmentController extends Controller {
  /**
   * Get available equipment with filters using offset-based pagination
   *
   * @param {number} page - Page number for pagination (1-based)
   * @param {number} limit - Maximum number of records to return
   * @param {string} search_string - Optional search term to filter equipment
   * @param {number} section_id - Optional section ID to filter equipment by section
   * @returns {Promise<GetAvailableEquipmentResponse>} Paginated response with available equipment
   * @throws IctError
   */
  @SuccessResponse('200', 'Successfully retrieved available equipment')
  @Get('/equipment/list')
  @OperationId('GetAvailableEquipment')
  @Tags('availability')
  public async getAvailableEquipment(
    @Query() page?: number,
    @Query() limit?: number,
    @Query() search_string?: string,
    @Query() section_id?: number,
  ): Promise<GetAvailableEquipmentResponse> {
    const logger = Container.get(WinstonLogger);
    const equipmentService = Container.get(EquipmentService);

    logger.info('Getting available equipment list', {
      page,
      limit,
      search_string,
      section_id,
    });

    return await equipmentService.getAvailableEquipment({
      page,
      limit,
      searchString: search_string,
      sectionId: section_id,
    });
  }
}
