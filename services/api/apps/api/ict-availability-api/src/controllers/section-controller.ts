import {
  Container,
  ProtectedRouteMiddleware,
  WinstonLogger,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Middlewares,
  OperationId,
  Route,
  SuccessResponse,
  Tags,
  Query,
} from 'tsoa';
import {SectionService} from '../services/section-service.js';
import {GetAvailableSectionsResponse} from '../defs/section-def.js';

@Route('availability')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [configPostgresDatabase()],
  }),
])
export class SectionController extends Controller {
  /**
   * Get available sections with filters using offset-based pagination
   *
   * @param {number} page - Page number for pagination (1-based)
   * @param {number} limit - Maximum number of records to return
   * @param {string} search_string - Optional search term to filter sections
   * @param {number} equipment_id - Optional equipment ID to filter sections by equipment
   * @returns {Promise<GetAvailableSectionsResponse>} Paginated response with available sections
   * @throws IctError
   */
  @SuccessResponse('200', 'Successfully retrieved available sections')
  @Get('/sections/list')
  @OperationId('GetAvailableSections')
  @Tags('availability')
  public async getAvailableSections(
    @Query() page?: number,
    @Query() limit?: number,
    @Query() search_string?: string,
    @Query() equipment_id?: number,
  ): Promise<GetAvailableSectionsResponse> {
    const logger = Container.get(WinstonLogger);
    const sectionService = Container.get(SectionService);

    logger.info('Getting available sections list', {
      page,
      limit,
      search_string,
      equipment_id,
    });

    return await sectionService.getAvailableSections({
      page,
      limit,
      searchString: search_string,
      equipmentId: equipment_id,
    });
  }
}
