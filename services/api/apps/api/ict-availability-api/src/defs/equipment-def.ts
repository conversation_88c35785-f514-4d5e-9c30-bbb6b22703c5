import {ApiResponseArray} from '@ict/sdk-foundations/types';

// Pagination metadata for equipment
export interface EquipmentPaginationInfo {
  page: number;
  limit: number;
  totalResults: number;
  totalPages: number;
}

// Standard API response type for available equipment (like sections)
export type GetAvailableEquipmentResponse = ApiResponseArray<
  {
    name: string;
    pk: number;
  }[],
  EquipmentPaginationInfo
>;
