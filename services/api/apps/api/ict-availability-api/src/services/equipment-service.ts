import {DiS<PERSON><PERSON>, WinstonLogger, ContextService} from 'ict-api-foundations';
import {
  EDPAvailabilityService,
  EDPEquipmentQueryParams,
} from './edp-availability-service.js';
import {GetAvailableEquipmentResponse} from '../defs/equipment-def.js';
import {ContextUtils} from '../utils/context-utils.js';
import {type PaginatedRequestNoDates} from '@ict/sdk-foundations/types';

@DiService()
export class EquipmentService {
  constructor(
    private edpAvailabilityService: EDPAvailabilityService,
    private logger: WinstonLogger,
    private context: ContextService,
  ) {}

  public async getAvailableEquipment(
    queryParams: PaginatedRequestNoDates & {sectionId?: number},
  ): Promise<GetAvailableEquipmentResponse> {
    const tenantId = ContextUtils.getTenantId(this.context, this.logger);
    const facilityId = ContextUtils.getFacilityId(this.context, this.logger);

    this.logger.info('Getting available equipment', {
      tenantId,
      facilityId,
      queryParams,
    });

    try {
      const edpParams =
        this.transformAvailableEquipmentRequestToEDPParams(queryParams);

      const edpResponse =
        await this.edpAvailabilityService.getAvailableEquipment(
          tenantId,
          facilityId,
          edpParams,
        );

      return {
        data: edpResponse.data,
        metadata: {
          page: edpResponse.metadata?.page ?? 1,
          limit: queryParams.limit || 50,
          totalResults: edpResponse.metadata?.total ?? 0,
          totalPages:
            edpResponse.metadata?.totalPages ??
            Math.ceil(
              (edpResponse.metadata?.total ?? 0) / (queryParams.limit || 50),
            ),
        },
      };
    } catch (error) {
      this.logger.error('Failed to get available equipment', {
        error,
        queryParams,
      });
      throw error;
    }
  }

  private transformAvailableEquipmentRequestToEDPParams(
    queryParams: PaginatedRequestNoDates & {sectionId?: number},
  ): EDPEquipmentQueryParams {
    const params: EDPEquipmentQueryParams = {};

    if (queryParams.page !== undefined) {
      params.page = queryParams.page;
    } else {
      params.page = 1;
    }

    if (queryParams.limit !== undefined) params.limit = queryParams.limit;
    if (queryParams.searchString) params.searchTerm = queryParams.searchString;
    if (queryParams.sectionId !== undefined)
      params.sectionPk = queryParams.sectionId;

    return params;
  }
}
