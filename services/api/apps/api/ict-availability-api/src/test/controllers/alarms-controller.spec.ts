import 'reflect-metadata';
import sinon from 'sinon';
import request from 'supertest';
import {
  Container,
  appSetup,
  <PERSON><PERSON><PERSON><PERSON>,
  IctError,
} from 'ict-api-foundations';
import {expect} from 'chai';
import {AlarmsService} from '../../services/alarms-service.ts';
import {AlarmsController} from '../../controllers/alarms-controller.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import type {PostAlarmsListResponse, Alarm} from '../../defs/alarm-def.ts';
import type {AlarmCreateDto} from '../../defs/alarm-create-dto.ts';
import type {AlarmUpdateDto} from '../../defs/alarm-update-dto.ts';

describe('AlarmsController', () => {
  const app = appSetup(RegisterRoutes);
  const baseUrl = '/availability/alarms';

  let alarmsServiceStub: sinon.SinonStubbedInstance<AlarmsService>;
  let loggerStub: sinon.SinonStubbedInstance<WinstonLogger>;

  const mockAlarm: Alarm = {
    id: 'c356e13f-8898-4e4f-bdce-bffcd148e605',
    faultId: 'b307f935',
    title: 'Test Alarm Description',
    description: 'Test Alarm Description',
    tag: 'TEST_ALARM_001',
    location: {
      area: 'TestArea',
      section: 'TestSection',
      equipment: 'TestEquipment',
    },
    timing: {
      startTime: new Date('2023-01-15T10:30:00Z'),
      endTime: new Date('2023-01-15T11:30:00Z'),
      duration: '1:00:00',
      updatedStartTime: new Date('2023-01-15T10:30:00Z'),
      updatedEndTime: new Date('2023-01-15T11:30:00Z'),
      updatedDuration: '1:00:00',
    },
    status: 'ACTIVE',
    reason: 'Under investigation',
  };

  // Expected response with serialized dates (as they would appear in JSON)
  const expectedSerializedAlarm = {
    id: 'c356e13f-8898-4e4f-bdce-bffcd148e605',
    faultId: 'b307f935',
    title: 'Test Alarm Description',
    description: 'Test Alarm Description',
    tag: 'TEST_ALARM_001',
    location: {
      area: 'TestArea',
      section: 'TestSection',
      equipment: 'TestEquipment',
    },
    timing: {
      startTime: '2023-01-15T10:30:00.000Z',
      endTime: '2023-01-15T11:30:00.000Z',
      duration: '1:00:00',
      updatedStartTime: '2023-01-15T10:30:00.000Z',
      updatedEndTime: '2023-01-15T11:30:00.000Z',
      updatedDuration: '1:00:00',
    },
    status: 'ACTIVE',
    reason: 'Under investigation',
  };

  const mockResponse: PostAlarmsListResponse = {
    data: [mockAlarm],
    metadata: {
      page: 1,
      limit: 20,
      totalResults: 1,
      totalPages: 1,
    },
  };

  const expectedResponse = {
    data: [expectedSerializedAlarm],
    metadata: {
      page: 1,
      limit: 20,
      totalResults: 1,
      totalPages: 1,
    },
  };

  beforeEach(() => {
    alarmsServiceStub = sinon.createStubInstance(AlarmsService);
    loggerStub = sinon.createStubInstance(WinstonLogger);

    Container.set(AlarmsService, alarmsServiceStub);
    Container.set(WinstonLogger, loggerStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('POST /availability/alarms/list', () => {
    const endpoint = `${baseUrl}/list`;

    const validRequest = {
      start_date: '2023-01-01T00:00:00.000Z',
      end_date: '2023-01-31T23:59:59.000Z',
      limit: 20,
      page: 1,
    };

    beforeEach(() => {
      alarmsServiceStub.getAlarms.resolves(mockResponse);
    });

    it('should successfully retrieve alarms with minimal request', async () => {
      const response = await request(app)
        .post(endpoint)
        .send(validRequest)
        .expect(200);

      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(expectedResponse);

      // Verify service was called with correct parameters
      expect(alarmsServiceStub.getAlarms.calledOnce).to.be.true;
      const serviceCall = alarmsServiceStub.getAlarms.firstCall.args[0];
      expect(serviceCall.start_date).to.be.an.instanceOf(Date);
      expect(serviceCall.end_date).to.be.an.instanceOf(Date);
      expect(serviceCall.limit).to.equal(20);
      expect(serviceCall.page).to.equal(1);
    });

    it('should handle request with all optional parameters', async () => {
      const fullRequest = {
        ...validRequest,
        filters: {
          type: 'multiple',
          areas: ['Area1', 'Area2'],
          statuses: ['ACTIVE', 'RESOLVED'],
        },
        sortFields: [
          {
            columnName: 'startTime',
            isDescending: true,
          },
        ],

        searchString: 'test alarm',
      };

      const response = await request(app)
        .post(endpoint)
        .send(fullRequest)
        .expect(200);

      expect(response.body).to.deep.equal(expectedResponse);

      // Verify all parameters were passed to service
      const serviceCall = alarmsServiceStub.getAlarms.firstCall.args[0];
      expect(serviceCall.filters).to.deep.equal(fullRequest.filters);
      expect(serviceCall.sortFields).to.deep.equal(fullRequest.sortFields);

      expect(serviceCall.searchString).to.equal(fullRequest.searchString);
    });

    it('should handle pagination parameters correctly', async () => {
      const paginatedRequest = {
        ...validRequest,
        limit: 50,
        page: 3,
      };

      await request(app).post(endpoint).send(paginatedRequest).expect(200);

      const serviceCall = alarmsServiceStub.getAlarms.firstCall.args[0];
      expect(serviceCall.limit).to.equal(50);
      expect(serviceCall.page).to.equal(3);
    });

    it('should validate required start_date field', async () => {
      const invalidRequest = {
        end_date: '2023-01-31T23:59:59.000Z',
        limit: 20,
        page: 1,
      };

      await request(app).post(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.getAlarms.called).to.be.false;
    });

    it('should validate required end_date field', async () => {
      const invalidRequest = {
        start_date: '2023-01-01T00:00:00.000Z',
        limit: 20,
        page: 1,
      };

      await request(app).post(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.getAlarms.called).to.be.false;
    });

    it('should validate date format for start_date', async () => {
      const invalidRequest = {
        start_date: 'invalid-date',
        end_date: '2023-01-31T23:59:59.000Z',
      };

      await request(app).post(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.getAlarms.called).to.be.false;
    });

    it('should validate date format for end_date', async () => {
      const invalidRequest = {
        start_date: '2023-01-01T00:00:00.000Z',
        end_date: 'invalid-date',
      };

      await request(app).post(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.getAlarms.called).to.be.false;
    });

    it('should validate that start_date is before end_date', async () => {
      const invalidRequest = {
        start_date: '2023-01-31T23:59:59.000Z',
        end_date: '2023-01-01T00:00:00.000Z', // end before start
      };

      await request(app).post(endpoint).send(invalidRequest).expect(400);

      expect(alarmsServiceStub.getAlarms.called).to.be.false;
    });

    it('should validate limit parameter is non-negative integer', async () => {
      const invalidRequest = {
        ...validRequest,
        limit: -5,
      };

      await request(app).post(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.getAlarms.called).to.be.false;
    });

    it('should validate page parameter is non-negative integer', async () => {
      const invalidRequest = {
        ...validRequest,
        page: -10,
      };

      await request(app).post(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.getAlarms.called).to.be.false;
    });

    it('should handle service errors and propagate them correctly', async () => {
      const serviceError = IctError.internalServerError('Service unavailable');
      alarmsServiceStub.getAlarms.rejects(serviceError);

      await request(app).post(endpoint).send(validRequest).expect(500);

      expect(alarmsServiceStub.getAlarms.calledOnce).to.be.true;
    });

    it('should handle service bad request errors', async () => {
      const serviceError = IctError.badRequest('Invalid parameters');
      alarmsServiceStub.getAlarms.rejects(serviceError);

      await request(app).post(endpoint).send(validRequest).expect(400);

      expect(alarmsServiceStub.getAlarms.calledOnce).to.be.true;
    });

    it('should handle service not found errors', async () => {
      const serviceError = IctError.notFound('No alarms found');
      alarmsServiceStub.getAlarms.rejects(serviceError);

      await request(app).post(endpoint).send(validRequest).expect(404);

      expect(alarmsServiceStub.getAlarms.calledOnce).to.be.true;
    });

    it('should handle service unauthorized errors', async () => {
      const serviceError = IctError.unauthorized('Access denied');
      alarmsServiceStub.getAlarms.rejects(serviceError);

      await request(app).post(endpoint).send(validRequest).expect(401);

      expect(alarmsServiceStub.getAlarms.calledOnce).to.be.true;
    });

    it('should log the request information', async () => {
      await request(app).post(endpoint).send(validRequest).expect(200);

      expect(loggerStub.info.calledWith('Getting alarms list')).to.be.true;
      const logCall = loggerStub.info.firstCall.args[1] as any;
      expect(logCall).to.exist;
      expect(logCall.request).to.exist;
    });

    it('should handle empty response from service', async () => {
      const emptyResponse: PostAlarmsListResponse = {
        data: [],
        metadata: {
          page: 1,
          limit: 20,
          totalResults: 0,
          totalPages: 0,
        },
      };

      alarmsServiceStub.getAlarms.resolves(emptyResponse);

      const response = await request(app)
        .post(endpoint)
        .send(validRequest)
        .expect(200);

      expect(response.body).to.deep.equal(emptyResponse);
      expect(response.body.data).to.be.an('array').that.is.empty;
    });

    it('should handle large pagination requests', async () => {
      const largeRequest = {
        ...validRequest,
        limit: 1000,
        page: 6,
      };

      await request(app).post(endpoint).send(largeRequest).expect(200);

      const serviceCall = alarmsServiceStub.getAlarms.firstCall.args[0];
      expect(serviceCall.limit).to.equal(1000);
      expect(serviceCall.page).to.equal(6);
    });

    it('should preserve date objects in service call', async () => {
      await request(app).post(endpoint).send(validRequest).expect(200);

      const serviceCall = alarmsServiceStub.getAlarms.firstCall.args[0];
      expect(serviceCall.start_date).to.be.an.instanceOf(Date);
      expect(serviceCall.end_date).to.be.an.instanceOf(Date);
      expect(serviceCall.start_date.toISOString()).to.equal(
        validRequest.start_date,
      );
      expect(serviceCall.end_date.toISOString()).to.equal(
        validRequest.end_date,
      );
    });

    it('should handle complex filter structures', async () => {
      const complexRequest = {
        ...validRequest,
        filters: {
          type: 'multiple',
          areas: ['Area1', 'Area2', 'Area3'],
          sections: ['Section1', 'Section2'],
          equipment: ['Equipment1'],
          statuses: ['ACTIVE', 'RESOLVED', 'PENDING'],
          faultIds: ['FAULT-001', 'FAULT-002'],
          reasons: ['Maintenance', 'Investigation'],
        },
      };

      await request(app).post(endpoint).send(complexRequest).expect(200);

      const serviceCall = alarmsServiceStub.getAlarms.firstCall.args[0];
      expect(serviceCall.filters).to.deep.equal(complexRequest.filters);
    });

    it('should handle multiple sort fields', async () => {
      const sortRequest = {
        ...validRequest,
        sortFields: [
          {
            columnName: 'startTime',
            isDescending: true,
          },
          {
            columnName: 'area',
            isDescending: false,
          },
          {
            columnName: 'status',
            isDescending: true,
          },
        ],
      };

      await request(app).post(endpoint).send(sortRequest).expect(200);

      const serviceCall = alarmsServiceStub.getAlarms.firstCall.args[0];
      expect(serviceCall.sortFields).to.deep.equal(sortRequest.sortFields);
      expect(serviceCall.sortFields).to.have.length(3);
    });
  });

  describe('GET /availability/alarms/{id}', () => {
    const alarmId = 'test-alarm-001';
    const endpoint = `${baseUrl}/${alarmId}`;

    beforeEach(() => {
      alarmsServiceStub.getAlarmById.resolves(mockAlarm);
    });

    it('should successfully retrieve a single alarm by ID', async () => {
      const response = await request(app).get(endpoint).expect(200);

      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(expectedSerializedAlarm);

      // Verify service was called with correct alarm ID
      expect(alarmsServiceStub.getAlarmById.calledOnce).to.be.true;
      expect(alarmsServiceStub.getAlarmById.firstCall.args[0]).to.equal(
        alarmId,
      );
    });

    it('should handle service not found errors', async () => {
      const serviceError = IctError.notFound(
        `Alarm with ID ${alarmId} not found`,
      );
      alarmsServiceStub.getAlarmById.rejects(serviceError);

      await request(app).get(endpoint).expect(404);

      expect(alarmsServiceStub.getAlarmById.calledOnce).to.be.true;
    });

    it('should handle service internal server errors', async () => {
      const serviceError = IctError.internalServerError('Service unavailable');
      alarmsServiceStub.getAlarmById.rejects(serviceError);

      await request(app).get(endpoint).expect(500);

      expect(alarmsServiceStub.getAlarmById.calledOnce).to.be.true;
    });

    it('should handle service unauthorized errors', async () => {
      const serviceError = IctError.unauthorized('Access denied');
      alarmsServiceStub.getAlarmById.rejects(serviceError);

      await request(app).get(endpoint).expect(401);

      expect(alarmsServiceStub.getAlarmById.calledOnce).to.be.true;
    });

    it('should handle service bad request errors', async () => {
      const serviceError = IctError.badRequest('Invalid alarm ID format');
      alarmsServiceStub.getAlarmById.rejects(serviceError);

      await request(app).get(endpoint).expect(400);

      expect(alarmsServiceStub.getAlarmById.calledOnce).to.be.true;
    });

    it('should log the request information', async () => {
      await request(app).get(endpoint).expect(200);

      expect(loggerStub.info.calledWith('Getting alarm by ID')).to.be.true;
      const logCall = loggerStub.info.firstCall.args[1] as any;
      expect(logCall).to.exist;
      expect(logCall.alarmId).to.equal(alarmId);
    });

    it('should handle different alarm ID formats', async () => {
      const testIds = ['abc123', 'test-alarm-001', 'b307f935', '12345678'];

      for (const testId of testIds) {
        const testEndpoint = `${baseUrl}/${testId}`;
        await request(app).get(testEndpoint).expect(200);

        expect(alarmsServiceStub.getAlarmById.calledWith(testId)).to.be.true;
      }
    });

    it('should return proper response structure', async () => {
      const response = await request(app).get(endpoint).expect(200);

      // Verify response structure matches Alarm interface
      expect(response.body).to.have.property('id');
      expect(response.body).to.have.property('title');
      expect(response.body).to.have.property('description');
      expect(response.body).to.have.property('tag');
      expect(response.body).to.have.property('location');
      expect(response.body).to.have.property('timing');
      expect(response.body).to.have.property('status');
      expect(response.body).to.have.property('reason');

      // Verify nested objects
      expect(response.body.location).to.have.property('area');
      expect(response.body.location).to.have.property('section');
      expect(response.body.location).to.have.property('equipment');

      expect(response.body.timing).to.have.property('startTime');
      expect(response.body.timing).to.have.property('duration');
    });
  });

  describe('POST /availability/alarms', () => {
    const endpoint = `${baseUrl}`;
    const validAlarmCreateRequest: AlarmCreateDto = {
      startDateLocal: new Date('2023-01-15T10:30:00.000Z'),
      endDateLocal: new Date('2023-01-15T11:30:00.000Z'),
      isIncluded: true,
      section: 'TestSection',
      equipment: 'TestEquipment',
      description: 'Test alarm description',
      comments: 'No additional comments',
    };
    beforeEach(() => {
      alarmsServiceStub.createAlarm.resolves();
    });
    it('should successfully create a new alarm', async () => {
      await request(app)
        .post(endpoint)
        .send(validAlarmCreateRequest)
        .expect(201);

      expect(alarmsServiceStub.createAlarm.calledOnce).to.be.true;
      expect(alarmsServiceStub.createAlarm.firstCall.args[0]).to.deep.equal(
        validAlarmCreateRequest,
      );
    });
    it('should throw error for invalid request body', async () => {
      const invalidRequest = {
        startDateLocal: 'invalid-date',
        endDateLocal: '2023-01-15T11:30:00.000Z',
        isIncluded: true,
        section: 'TestSection',
        equipment: 'TestEquipment',
        description: 'Test alarm description',
        comments: 'No additional comments',
      };

      await request(app).post(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.createAlarm.called).to.be.false;
    });
  });

  describe('PUT /availability/alarms', () => {
    const endpoint = `${baseUrl}`;
    const validAlarmUpdateRequest: AlarmUpdateDto = {
      id: 'test-alarm-001',
      startDateLocal: new Date('2023-01-15T10:30:00.000Z'),
      endDateLocal: new Date('2023-01-15T11:30:00.000Z'),
      isIncluded: true,
      comments: 'Updated alarm comments',
    };

    beforeEach(() => {
      alarmsServiceStub.updateAlarm.resolves();
    });

    it('should successfully update an existing alarm', async () => {
      await request(app)
        .put(endpoint)
        .send(validAlarmUpdateRequest)
        .expect(200);

      expect(alarmsServiceStub.updateAlarm.calledOnce).to.be.true;
      expect(alarmsServiceStub.updateAlarm.firstCall.args[0]).to.deep.equal(
        validAlarmUpdateRequest,
      );
    });

    it('should log the request information', async () => {
      await request(app)
        .put(endpoint)
        .send(validAlarmUpdateRequest)
        .expect(200);

      expect(loggerStub.info.calledWith('Updating alarm')).to.be.true;
      const logCall = loggerStub.info.firstCall.args[1] as any;
      expect(logCall).to.exist;
      expect(logCall.request).to.deep.equal(validAlarmUpdateRequest);
    });

    it('should validate required id field', async () => {
      const invalidRequest = {
        startDateLocal: '2023-01-15T10:30:00.000Z',
        endDateLocal: '2023-01-15T11:30:00.000Z',
        isIncluded: false,
        comments: 'Updated alarm comments',
      };

      await request(app).put(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.updateAlarm.called).to.be.false;
    });

    it('should successfully exclude an alarm with excludeReason', async () => {
      const excludeRequest: AlarmUpdateDto = {
        id: 'test-alarm-001',
        isIncluded: false,
        excludeReason: 'Equipment malfunction - scheduled maintenance',
        comments: 'Updated alarm comments',
      };

      await request(app).put(endpoint).send(excludeRequest).expect(200);

      expect(alarmsServiceStub.updateAlarm.calledOnce).to.be.true;
      expect(alarmsServiceStub.updateAlarm.firstCall.args[0]).to.deep.equal(
        excludeRequest,
      );
    });

    it('should successfully include an alarm with optional time changes', async () => {
      const includeRequest: AlarmUpdateDto = {
        id: 'test-alarm-001',
        startDateLocal: new Date('2023-01-15T10:30:00.000Z'),
        endDateLocal: new Date('2023-01-15T11:30:00.000Z'),
        isIncluded: true,
        comments: 'Time corrected based on maintenance logs',
      };

      await request(app).put(endpoint).send(includeRequest).expect(200);

      expect(alarmsServiceStub.updateAlarm.calledOnce).to.be.true;
      expect(alarmsServiceStub.updateAlarm.firstCall.args[0]).to.deep.equal(
        includeRequest,
      );
    });

    it('should successfully include an alarm without time changes', async () => {
      const includeRequest: AlarmUpdateDto = {
        id: 'test-alarm-001',
        isIncluded: true,
        comments: 'Marked as included',
      };

      await request(app).put(endpoint).send(includeRequest).expect(200);

      expect(alarmsServiceStub.updateAlarm.calledOnce).to.be.true;
      expect(alarmsServiceStub.updateAlarm.firstCall.args[0]).to.deep.equal(
        includeRequest,
      );
    });

    it('should validate required isIncluded field', async () => {
      const invalidRequest = {
        id: 'test-alarm-001',

        startDateLocal: '2023-01-15T10:30:00.000Z',
        endDateLocal: '2023-01-15T11:30:00.000Z',
        comments: 'Updated alarm comments',
      };

      await request(app).put(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.updateAlarm.called).to.be.false;
    });

    it('should validate required comments field', async () => {
      const invalidRequest = {
        id: 'test-alarm-001',

        startDateLocal: '2023-01-15T10:30:00.000Z',
        endDateLocal: '2023-01-15T11:30:00.000Z',
        isIncluded: false,
      };

      await request(app).put(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.updateAlarm.called).to.be.false;
    });

    it('should validate date format for startDateLocal', async () => {
      const invalidRequest = {
        ...validAlarmUpdateRequest,
        startDateLocal: 'invalid-date',
      };

      await request(app).put(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.updateAlarm.called).to.be.false;
    });

    it('should validate date format for endDateLocal', async () => {
      const invalidRequest = {
        ...validAlarmUpdateRequest,
        endDateLocal: 'invalid-date',
      };

      await request(app).put(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.updateAlarm.called).to.be.false;
    });

    // New business rule validation tests
    it('should require excludeReason when isIncluded is false', async () => {
      const invalidRequest = {
        id: 'test-alarm-001',
        isIncluded: false,
        comments: 'Updated alarm comments',
      };

      await request(app).put(endpoint).send(invalidRequest).expect(400);

      expect(alarmsServiceStub.updateAlarm.called).to.be.false;
    });

    it('should reject dates when isIncluded is false', async () => {
      const invalidRequest = {
        id: 'test-alarm-001',
        startDateLocal: new Date('2023-01-15T10:30:00.000Z'),
        endDateLocal: new Date('2023-01-15T11:30:00.000Z'),
        isIncluded: false,
        excludeReason: 'Equipment malfunction',
        comments: 'Updated alarm comments',
      };

      await request(app).put(endpoint).send(invalidRequest).expect(400);

      expect(alarmsServiceStub.updateAlarm.called).to.be.false;
    });

    it('should reject excludeReason when isIncluded is true', async () => {
      const invalidRequest = {
        id: 'test-alarm-001',
        isIncluded: true,
        excludeReason: 'Should not be provided',
        comments: 'Updated alarm comments',
      };

      await request(app).put(endpoint).send(invalidRequest).expect(400);

      expect(alarmsServiceStub.updateAlarm.called).to.be.false;
    });

    it('should accept string "true" for isIncluded (type coercion)', async () => {
      const requestWithStringBoolean = {
        ...validAlarmUpdateRequest,
        isIncluded: 'true' as any, // TSOA might coerce this to boolean
      };

      await request(app)
        .put(endpoint)
        .send(requestWithStringBoolean)
        .expect(200);

      expect(alarmsServiceStub.updateAlarm.calledOnce).to.be.true;
    });

    it('should handle service not found errors', async () => {
      const serviceError = IctError.notFound(
        `Alarm with ID ${validAlarmUpdateRequest.id} not found in EDP system`,
      );
      alarmsServiceStub.updateAlarm.rejects(serviceError);

      await request(app)
        .put(endpoint)
        .send(validAlarmUpdateRequest)
        .expect(404);

      expect(alarmsServiceStub.updateAlarm.calledOnce).to.be.true;
    });

    it('should handle service bad request errors', async () => {
      const serviceError = IctError.badRequest('Invalid alarm data provided');
      alarmsServiceStub.updateAlarm.rejects(serviceError);

      await request(app)
        .put(endpoint)
        .send(validAlarmUpdateRequest)
        .expect(400);

      expect(alarmsServiceStub.updateAlarm.calledOnce).to.be.true;
    });

    it('should handle service unauthorized errors', async () => {
      const serviceError = IctError.unauthorized(
        'EDP API authentication failed',
      );
      alarmsServiceStub.updateAlarm.rejects(serviceError);

      await request(app)
        .put(endpoint)
        .send(validAlarmUpdateRequest)
        .expect(401);

      expect(alarmsServiceStub.updateAlarm.calledOnce).to.be.true;
    });

    it('should handle service internal server errors', async () => {
      const serviceError = IctError.internalServerError(
        'Failed to update alarm in EDP API',
      );
      alarmsServiceStub.updateAlarm.rejects(serviceError);

      await request(app)
        .put(endpoint)
        .send(validAlarmUpdateRequest)
        .expect(500);

      expect(alarmsServiceStub.updateAlarm.calledOnce).to.be.true;
    });

    it('should accept empty strings for string fields', async () => {
      const requestWithEmptyString = {
        ...validAlarmUpdateRequest,
      };

      await request(app).put(endpoint).send(requestWithEmptyString).expect(200);

      expect(alarmsServiceStub.updateAlarm.calledOnce).to.be.true;
    });

    it('should accept empty comments field', async () => {
      const requestWithEmptyComments = {
        ...validAlarmUpdateRequest,
        comments: '',
      };

      await request(app)
        .put(endpoint)
        .send(requestWithEmptyComments)
        .expect(200);

      expect(alarmsServiceStub.updateAlarm.calledOnce).to.be.true;
    });

    it('should handle null values for required fields', async () => {
      const invalidRequest = {
        ...validAlarmUpdateRequest,
        id: null,
      };

      await request(app).put(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.updateAlarm.called).to.be.false;
    });

    it('should handle undefined values for required fields', async () => {
      const invalidRequest = {
        ...validAlarmUpdateRequest,
        id: undefined,
      };

      await request(app).put(endpoint).send(invalidRequest).expect(422);

      expect(alarmsServiceStub.updateAlarm.called).to.be.false;
    });

    it('should reject extra fields in request body', async () => {
      const requestWithExtraFields = {
        ...validAlarmUpdateRequest,
        extraField: 'should be rejected',
        anotherField: 123,
      };

      await request(app).put(endpoint).send(requestWithExtraFields).expect(422);

      expect(alarmsServiceStub.updateAlarm.called).to.be.false;
    });

    it('should handle valid Date objects', async () => {
      const validDatePairs = [
        {
          start: new Date('2023-01-15T09:30:00.000Z'),
          end: new Date('2023-01-15T11:30:00.000Z'),
        },
        {
          start: new Date('2023-12-31T10:00:00.000Z'),
          end: new Date('2023-12-31T23:59:59.999Z'),
        },
        {
          start: new Date('2024-02-29T08:00:00.000Z'), // Leap year
          end: new Date('2024-02-29T12:00:00.000Z'),
        },
      ];

      for (const datePair of validDatePairs) {
        const requestData = {
          ...validAlarmUpdateRequest,
          startDateLocal: datePair.start,
          endDateLocal: datePair.end,
        };

        await request(app).put(endpoint).send(requestData).expect(200);
      }

      expect(alarmsServiceStub.updateAlarm.callCount).to.equal(
        validDatePairs.length,
      );
    });

    it('should handle different ID formats', async () => {
      const validIds = [
        'uuid-format-1234-5678-9abc-def0',
        '12345678-1234-1234-1234-123456789abc',
        'simple-string-id',
        'UPPERCASE-ID',
      ];

      for (const id of validIds) {
        const requestData = {
          ...validAlarmUpdateRequest,
          id: id,
        };

        await request(app).put(endpoint).send(requestData).expect(200);
      }

      expect(alarmsServiceStub.updateAlarm.callCount).to.equal(validIds.length);
    });
  });

  describe('Controller static properties', () => {
    it('should have a valid example response', () => {
      expect(AlarmsController.exampleResponse).to.exist;
      expect(AlarmsController.exampleResponse.data).to.be.an('array');
      expect(AlarmsController.exampleResponse.metadata).to.exist;
      expect(AlarmsController.exampleResponse.metadata.page).to.be.a('number');
      expect(AlarmsController.exampleResponse.metadata.limit).to.be.a('number');
      expect(AlarmsController.exampleResponse.metadata.totalResults).to.be.a(
        'number',
      );

      // Validate structure of example alarm
      if (AlarmsController.exampleResponse.data.length > 0) {
        const exampleAlarm = AlarmsController.exampleResponse.data[0];
        expect(exampleAlarm.id).to.be.a('string');
        expect(exampleAlarm.title).to.be.a('string');
        expect(exampleAlarm.description).to.be.a('string');
        expect(exampleAlarm.tag).to.be.a('string');
        expect(exampleAlarm.location).to.exist;
        expect(exampleAlarm.timing).to.exist;
        expect(exampleAlarm.status).to.be.a('string');
        expect(exampleAlarm.reason).to.be.a('string');
      }
    });
  });
});
