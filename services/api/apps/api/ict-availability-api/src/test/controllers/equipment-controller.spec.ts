import sinon from 'sinon';
import {expect} from 'chai';
import {Container, WinstonLogger} from 'ict-api-foundations';
import {EquipmentController} from '../../controllers/equipment-controller.ts';
import {EquipmentService} from '../../services/equipment-service.js';
import {GetAvailableEquipmentResponse} from '../../defs/equipment-def.js';

describe('EquipmentController', () => {
  let controller: EquipmentController;
  let equipmentServiceStub: sinon.SinonStubbedInstance<EquipmentService>;
  let loggerStub: sinon.SinonStubbedInstance<WinstonLogger>;

  beforeEach(() => {
    equipmentServiceStub = sinon.createStubInstance(EquipmentService);
    loggerStub = sinon.createStubInstance(WinstonLogger);

    sinon.stub(Container, 'get').callsFake(token => {
      if (token === EquipmentService) return equipmentServiceStub;
      if (token === WinstonLogger) return loggerStub;
      throw new Error(`Unexpected token: ${token}`);
    });

    controller = new EquipmentController();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getAvailableEquipment', () => {
    it('should return available equipment from the service', async () => {
      const mockResponse: GetAvailableEquipmentResponse = {
        data: [
          {name: 'Equipment 1', pk: 1},
          {name: 'Equipment 2', pk: 2},
        ],
        metadata: {
          page: 1,
          limit: 10,
          totalResults: 2,
          totalPages: 1,
        },
      };

      equipmentServiceStub.getAvailableEquipment.resolves(mockResponse);

      const result = await controller.getAvailableEquipment(2, 10, 'test', 171);

      expect(result).to.deep.equal(mockResponse);
      expect(
        equipmentServiceStub.getAvailableEquipment.calledOnceWith({
          page: 2,
          limit: 10,
          searchString: 'test',
          sectionId: 171,
        }),
      ).to.be.true;
    });

    it('should handle errors from the service', async () => {
      const error = new Error('Service error');
      equipmentServiceStub.getAvailableEquipment.rejects(error);

      try {
        await controller.getAvailableEquipment(0, 10, 'test');
        expect.fail('Should have thrown an error');
      } catch (err) {
        expect(err).to.equal(error);
      }
    });

    it('should handle requests without section_id parameter', async () => {
      const mockResponse: GetAvailableEquipmentResponse = {
        data: [{name: 'Equipment 1', pk: 1}],
        metadata: {
          page: 1,
          limit: 10,
          totalResults: 1,
          totalPages: 1,
        },
      };

      equipmentServiceStub.getAvailableEquipment.resolves(mockResponse);

      const result = await controller.getAvailableEquipment(1, 10, 'test');

      expect(result).to.deep.equal(mockResponse);
      expect(
        equipmentServiceStub.getAvailableEquipment.calledOnceWith({
          page: 1,
          limit: 10,
          searchString: 'test',
          sectionId: undefined,
        }),
      ).to.be.true;
    });

    it('should filter equipment by section when section_id is provided', async () => {
      const mockResponse: GetAvailableEquipmentResponse = {
        data: [
          {name: 'Equipment 1', pk: 1},
          {name: 'Equipment 2', pk: 2},
        ],
        metadata: {
          page: 1,
          limit: 10,
          totalResults: 2,
          totalPages: 1,
        },
      };

      equipmentServiceStub.getAvailableEquipment.resolves(mockResponse);

      const result = await controller.getAvailableEquipment(
        1,
        10,
        undefined,
        171,
      );

      expect(result).to.deep.equal(mockResponse);
      expect(
        equipmentServiceStub.getAvailableEquipment.calledOnceWith({
          page: 1,
          limit: 10,
          searchString: undefined,
          sectionId: 171,
        }),
      ).to.be.true;
    });
  });
});
