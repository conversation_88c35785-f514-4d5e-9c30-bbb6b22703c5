import sinon from 'sinon';
import {expect} from 'chai';
import {
  ContextService,
  IctError,
  WinstonLogger,
  type BaseFilterType,
} from 'ict-api-foundations';
import {AlarmsService} from '../../services/alarms-service.ts';
import {
  EDPAvailabilityService,
  type EDPAlarm,
  type EDPAlarmsResponse,
} from '../../services/edp-availability-service.ts';
import type {PostAlarmsListResponse, Alarm} from '../../defs/alarm-def.ts';
import {type PaginatedRequest} from '@ict/sdk-foundations/types';
import type {AlarmCreateDto} from '../../defs/alarm-create-dto.ts';
import type {AlarmUpdateDto} from '../../defs/alarm-update-dto.ts';
import {ContextUtils} from '../../utils/context-utils.js';

describe('AlarmsService', () => {
  let service: AlarmsService;
  let edpServiceStub: sinon.SinonStubbedInstance<EDPAvailabilityService>;
  let loggerStub: sinon.SinonStubbedInstance<WinstonLogger>;
  let contextServiceStub: sinon.SinonStubbedInstance<ContextService>;
  let getTenantIdStub: sinon.SinonStub;
  let getFacilityIdStub: sinon.SinonStub;
  let getUserEmailStub: sinon.SinonStub;

  const mockTenantId = 'test-tenant';
  const mockFacilityId = 'test-facility';
  const mockUserEmail = '<EMAIL>';

  beforeEach(() => {
    // Create stubs for dependencies
    edpServiceStub = sinon.createStubInstance(EDPAvailabilityService);
    loggerStub = sinon.createStubInstance(WinstonLogger);
    contextServiceStub = sinon.createStubInstance(ContextService);
    getTenantIdStub = sinon
      .stub(ContextUtils, 'getTenantId')
      .returns(mockTenantId);
    getFacilityIdStub = sinon
      .stub(ContextUtils, 'getFacilityId')
      .returns(mockFacilityId);
    getUserEmailStub = sinon
      .stub(ContextUtils, 'getUserEmail')
      .returns(mockUserEmail);

    // Create service instance with stubs
    service = new AlarmsService(edpServiceStub, loggerStub, contextServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getAlarms', () => {
    const mockQueryParams: PaginatedRequest = {
      limit: 10,
      page: 1,
      start_date: new Date('2023-01-01T00:00:00Z'),
      end_date: new Date('2023-01-31T23:59:59Z'),
      searchString: 'test fault',
      sortFields: [
        {
          columnName: 'startTime',
          isDescending: true,
        },
      ],
      filters: {
        type: 'multiple',
        areas: ['Area A', 'Area B'],
        statuses: ['ACTIVE'],
      } as BaseFilterType,
    };

    const mockEDPAlarm: EDPAlarm = {
      id: 'c356e13f-8898-4e4f-bdce-bffcd148e605',
      sectionArea: 'Area A',
      sectionName: 'Section 1',
      equipmentName: 'Equipment 1',
      faultId: 'FAULT-001',
      faultDescription: 'Test fault description',
      faultTag: 'TAG-001',
      severity: 'High',
      faultStartTimeUtc: '2023-01-15T10:30:00Z',
      faultEndTimeUtc: '2023-01-15T11:30:00Z',
      faultDuration: 3600000,
      splitStartTimeUtc: '2023-01-15T10:30:00Z',
      splitEndTimeUtc: '2023-01-15T11:30:00Z',
      splitDuration: 3600000,
      origStartTimeUtc: '2023-01-15T10:30:00Z',
      origEndTimeUtc: '2023-01-15T11:30:00Z',
      origDuration: 3600000,
      removalStatus: 'ACTIVE',
      removalReason: 'Under investigation',
      updatedStartTimeUtc: '2023-01-15T10:30:00Z',
      updatedEndTimeUtc: '2023-01-15T11:30:00Z',
      updatedDuration: 3600000,
      comments: 'Example comments',
      lastUpdatedUser: '<EMAIL>',
    };

    const mockEDPResponse: EDPAlarmsResponse = {
      data: [mockEDPAlarm],
      metadata: {
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    };

    const expectedAlarm: Alarm = {
      id: 'c356e13f-8898-4e4f-bdce-bffcd148e605',
      faultId: 'FAULT-001',
      title: 'Test fault description',
      description: 'Test fault description',
      tag: 'TAG-001',
      severity: 'High',
      location: {
        area: 'Area A',
        section: 'Section 1',
        equipment: 'Equipment 1',
      },
      timing: {
        startTime: new Date('2023-01-15T10:30:00Z'),
        endTime: new Date('2023-01-15T11:30:00Z'),
        duration: '3600000',
        updatedStartTime: new Date('2023-01-15T10:30:00Z'),
        updatedEndTime: new Date('2023-01-15T11:30:00Z'),
        updatedDuration: '3600000',
        splitStartTime: new Date('2023-01-15T10:30:00Z'),
        splitEndTime: new Date('2023-01-15T11:30:00Z'),
        splitDuration: '3600000',
        origStartTime: new Date('2023-01-15T10:30:00Z'),
        origEndTime: new Date('2023-01-15T11:30:00Z'),
        origDuration: '3600000',
      },
      status: 'ACTIVE',
      reason: 'Under investigation',
      comments: 'Example comments',
      lastUpdatedUser: '<EMAIL>',
    };

    beforeEach(() => {
      edpServiceStub.getAlarms.resolves(mockEDPResponse);
    });

    it('should successfully retrieve and transform alarms', async () => {
      const result = await service.getAlarms(mockQueryParams);

      expect(result).to.deep.equal({
        data: [expectedAlarm],
        metadata: {
          page: 1,
          limit: 10,
          totalResults: 1,
          totalPages: 1,
        },
      });

      expect(edpServiceStub.getAlarms.calledOnce).to.be.true;
      expect(loggerStub.info.calledWith('Getting alarms')).to.be.true;
    });

    it('should call EDP service with correct tenant and facility IDs', async () => {
      await service.getAlarms(mockQueryParams);

      expect(
        edpServiceStub.getAlarms.calledWith(
          mockTenantId,
          mockFacilityId,
          sinon.match.any,
        ),
      ).to.be.true;
    });

    it('should transform query parameters correctly', async () => {
      await service.getAlarms(mockQueryParams);

      const edpParams = edpServiceStub.getAlarms.firstCall.args[2];

      expect(edpParams).to.exist;
      expect(edpParams).to.deep.include({
        page: 1, // page passed directly
        limit: 10,
        searchTerm: 'test fault',
        startTimestamp: '2023-01-01T00:00:00.000Z',
        endTimestamp: '2023-01-31T23:59:59.000Z',
        sortBy: 'faultStartTimeUtc',
        orderBy: 'DESC',
        sectionAreas: '["Area A","Area B"]',
        removalStatuses: '["ACTIVE"]',
      });
    });

    it('should pass page parameter directly to EDP service', async () => {
      const params = {...mockQueryParams, page: 3, limit: 10};
      await service.getAlarms(params);

      const edpParams = edpServiceStub.getAlarms.firstCall.args[2];
      expect(edpParams).to.exist;
      expect(edpParams!.page).to.equal(3); // page passed directly
    });

    it('should handle missing sort fields', async () => {
      const params = {...mockQueryParams};
      delete params.sortFields;

      await service.getAlarms(params);

      const edpParams = edpServiceStub.getAlarms.firstCall.args[2];
      expect(edpParams).to.exist;
      expect(edpParams!.sortBy).to.be.undefined;
      expect(edpParams!.orderBy).to.be.undefined;
    });

    it('should handle missing filters', async () => {
      const params = {...mockQueryParams};
      delete params.filters;

      await service.getAlarms(params);

      const edpParams = edpServiceStub.getAlarms.firstCall.args[2];
      expect(edpParams).to.exist;
      expect(edpParams!.sectionAreas).to.be.undefined;
      expect(edpParams!.removalStatuses).to.be.undefined;
    });

    it('should handle various filter types', async () => {
      const params: PaginatedRequest = {
        ...mockQueryParams,
        filters: {
          type: 'multiple',
          areas: ['Area A'],
          sections: ['Section 1'],
          equipment: ['Equipment 1'],
          statuses: ['ACTIVE', 'RESOLVED'],
          faultIds: ['FAULT-001'],
          reasons: ['Maintenance'],
        } as BaseFilterType,
      };

      await service.getAlarms(params);

      const edpParams = edpServiceStub.getAlarms.firstCall.args[2];
      expect(edpParams).to.exist;
      expect(edpParams!.sectionAreas).to.equal('["Area A"]');
      expect(edpParams!.sectionNames).to.equal('["Section 1"]');
      expect(edpParams!.equipmentNames).to.equal('["Equipment 1"]');
      expect(edpParams!.removalStatuses).to.equal('["ACTIVE","RESOLVED"]');
      expect(edpParams!.faultIds).to.equal('["FAULT-001"]');
      expect(edpParams!.removalReasons).to.equal('["Maintenance"]');
    });

    it('should handle different sort field mappings', async () => {
      const testCases = [
        {columnName: 'area', expected: 'sectionArea'},
        {columnName: 'section', expected: 'sectionName'},
        {columnName: 'equipment', expected: 'equipmentName'},
        {columnName: 'faultId', expected: 'faultId'},
        {columnName: 'startTime', expected: 'faultStartTimeUtc'},
        {columnName: 'endTime', expected: 'faultEndTimeUtc'},
        {columnName: 'status', expected: 'removalStatus'},
        {columnName: 'updatedStartTime', expected: 'updatedStartTimeUtc'},
        {columnName: 'updatedEndTime', expected: 'updatedEndTimeUtc'},
        {columnName: 'lastUpdatedUser', expected: 'lastUpdatedUser'},
        {columnName: 'unmappedField', expected: 'unmappedField'},
      ];

      for (const testCase of testCases) {
        const params = {
          ...mockQueryParams,
          sortFields: [
            {
              columnName: testCase.columnName,
              isDescending: false,
            },
          ],
        };

        await service.getAlarms(params);

        const edpParams = edpServiceStub.getAlarms.lastCall.args[2];
        expect(edpParams).to.exist;
        expect(edpParams!.sortBy).to.equal(testCase.expected);
        expect(edpParams!.orderBy).to.equal('ASC');
      }
    });

    it('should handle EDP service errors', async () => {
      const error = new IctError(500, 'EDP service error');
      edpServiceStub.getAlarms.rejects(error);

      try {
        await service.getAlarms(mockQueryParams);
        expect.fail('Should have thrown an error');
      } catch (thrownError) {
        expect(thrownError).to.equal(error);
        expect(loggerStub.error.calledWith('Failed to get alarms')).to.be.true;
      }
    });

    it('should transform EDP alarms without optional fields', async () => {
      const minimalEDPAlarm: EDPAlarm = {
        id: 'c356e13f-8898-4e4f-bdce-bffcd148e605',
        sectionArea: 'Area A',
        sectionName: 'Section 1',
        equipmentName: 'Equipment 1',
        faultId: 'FAULT-002',
        faultDescription: 'Minimal fault',
        faultTag: 'TAG-002',
        severity: undefined,
        faultStartTimeUtc: '2023-01-15T10:30:00Z',
        faultEndTimeUtc: '', // Empty string
        faultDuration: 1800000,
        splitStartTimeUtc: '',
        splitEndTimeUtc: '',
        splitDuration: 0,
        origStartTimeUtc: '',
        origEndTimeUtc: '',
        origDuration: 0,
        removalStatus: 'ACTIVE',
        removalReason: '',
        updatedStartTimeUtc: '', // Empty string
        updatedEndTimeUtc: '',
        updatedDuration: 0,
      };

      const minimalResponse: EDPAlarmsResponse = {
        data: [minimalEDPAlarm],
        metadata: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      };

      edpServiceStub.getAlarms.resolves(minimalResponse);

      const result = await service.getAlarms(mockQueryParams);

      expect(result.data[0].timing.endTime).to.be.undefined;
      expect(result.data[0].timing.updatedStartTime).to.be.undefined;
      expect(result.data[0].timing.updatedEndTime).to.be.undefined;
      expect(result.data[0].timing.updatedDuration).to.equal('0');
    });
  });

  describe('createAlarm', () => {
    beforeEach(() => {
      edpServiceStub.createAlarm.reset();
    });
    it('should successfully create a new alarm', async () => {
      const validAlarmCreateRequest: AlarmCreateDto = {
        startDateLocal: new Date('2023-01-15T10:30:00.000Z'),
        endDateLocal: new Date('2023-01-15T11:30:00.000Z'),
        isIncluded: true,
        section: 'TestSection',
        equipment: 'TestEquipment',
        description: 'Test alarm description',
        comments: 'No additional comments',
      };
      edpServiceStub.createAlarm.resolves();
      await service.createAlarm(validAlarmCreateRequest);
      expect(edpServiceStub.createAlarm.calledOnce).to.be.true;
      expect(edpServiceStub.createAlarm.firstCall.args).to.deep.equal([
        mockTenantId,
        mockFacilityId,
        validAlarmCreateRequest,
        mockUserEmail,
      ]);
    });

    it('should throw error if edp service fails to create alarm', async () => {
      const validAlarmCreateRequest: AlarmCreateDto = {
        startDateLocal: new Date('2023-01-15T10:30:00.000Z'),
        endDateLocal: new Date('2023-01-15T11:30:00.000Z'),
        isIncluded: true,
        section: 'TestSection',
        equipment: 'TestEquipment',
        description: 'Test alarm description',
        comments: 'No additional comments',
      };
      const error = new IctError(500, 'EDP service error');
      edpServiceStub.createAlarm.rejects(error);

      try {
        await service.createAlarm(validAlarmCreateRequest);
        expect.fail('Should have thrown an error');
      } catch (thrownError) {
        expect(thrownError).to.equal(error);
        expect(loggerStub.error.calledWith('Failed to create alarm')).to.be
          .true;
      }
    });
  });

  describe('updateAlarm', () => {
    const validAlarmUpdateRequest: AlarmUpdateDto = {
      id: 'test-alarm-001',

      startDateLocal: new Date('2023-01-15T10:30:00.000Z'),
      endDateLocal: new Date('2023-01-15T11:30:00.000Z'),
      isIncluded: false,
      comments: 'Updated alarm comments',
    };

    beforeEach(() => {
      edpServiceStub.updateAlarm.reset();
    });

    it('should successfully update an existing alarm', async () => {
      edpServiceStub.updateAlarm.resolves();

      await service.updateAlarm(validAlarmUpdateRequest);

      expect(edpServiceStub.updateAlarm.calledOnce).to.be.true;
      expect(edpServiceStub.updateAlarm.firstCall.args).to.deep.equal([
        mockTenantId,
        mockFacilityId,
        validAlarmUpdateRequest,
        mockUserEmail,
      ]);
    });

    it('should call EDP service with correct tenant and facility IDs', async () => {
      edpServiceStub.updateAlarm.resolves();

      await service.updateAlarm(validAlarmUpdateRequest);

      expect(
        edpServiceStub.updateAlarm.calledWith(
          mockTenantId,
          mockFacilityId,
          validAlarmUpdateRequest,
        ),
      ).to.be.true;
    });

    it('should log the update request information', async () => {
      edpServiceStub.updateAlarm.resolves();

      await service.updateAlarm(validAlarmUpdateRequest);

      expect(loggerStub.info.calledWith('Updating alarm')).to.be.true;
      const logCall = loggerStub.info.firstCall.args[1] as any;
      expect(logCall).to.exist;
      expect(logCall.tenantId).to.equal(mockTenantId);
      expect(logCall.facilityId).to.equal(mockFacilityId);
      expect(logCall.alarm).to.deep.equal(validAlarmUpdateRequest);
    });

    it('should handle different alarm update data', async () => {
      const differentUpdateRequest: AlarmUpdateDto = {
        id: 'different-alarm-id',

        startDateLocal: new Date('2024-02-20T14:00:00.000Z'),
        endDateLocal: new Date('2024-02-20T16:00:00.000Z'),
        isIncluded: true,
        comments: 'Different updated comments',
      };

      edpServiceStub.updateAlarm.resolves();

      await service.updateAlarm(differentUpdateRequest);

      expect(edpServiceStub.updateAlarm.calledOnce).to.be.true;
      expect(edpServiceStub.updateAlarm.firstCall.args[2]).to.deep.equal(
        differentUpdateRequest,
      );
      expect(edpServiceStub.updateAlarm.firstCall.args[3]).to.equal(
        mockUserEmail,
      );
    });

    it('should handle alarms with minimal required fields', async () => {
      const minimalUpdateRequest: AlarmUpdateDto = {
        id: 'minimal-alarm',

        startDateLocal: new Date('2023-01-01T00:00:00.000Z'),
        endDateLocal: new Date('2023-01-01T01:00:00.000Z'),
        isIncluded: true,
        comments: '',
      };

      edpServiceStub.updateAlarm.resolves();

      await service.updateAlarm(minimalUpdateRequest);

      expect(edpServiceStub.updateAlarm.calledOnce).to.be.true;
      expect(edpServiceStub.updateAlarm.firstCall.args[2]).to.deep.equal(
        minimalUpdateRequest,
      );
      expect(edpServiceStub.updateAlarm.firstCall.args[3]).to.equal(
        mockUserEmail,
      );
    });

    it('should throw error if EDP service fails to update alarm', async () => {
      const error = new IctError(500, 'EDP service error');
      edpServiceStub.updateAlarm.rejects(error);

      try {
        await service.updateAlarm(validAlarmUpdateRequest);
        expect.fail('Should have thrown an error');
      } catch (thrownError) {
        expect(thrownError).to.equal(error);
        expect(loggerStub.error.calledWith('Failed to update alarm')).to.be
          .true;
      }
    });

    it('should handle not found errors from EDP service', async () => {
      const notFoundError = IctError.notFound(
        `Alarm with ID ${validAlarmUpdateRequest.id} not found in EDP system`,
      );
      edpServiceStub.updateAlarm.rejects(notFoundError);

      try {
        await service.updateAlarm(validAlarmUpdateRequest);
        expect.fail('Should have thrown an error');
      } catch (thrownError) {
        expect(thrownError).to.equal(notFoundError);
        expect(loggerStub.error.calledWith('Failed to update alarm')).to.be
          .true;

        // Verify error logging includes alarm details
        const errorLogCall = loggerStub.error.firstCall.args[1] as any;
        expect(errorLogCall).to.exist;
        expect(errorLogCall.id).to.equal(validAlarmUpdateRequest.id);
        expect(errorLogCall.alarm).to.deep.equal(validAlarmUpdateRequest);
      }
    });

    it('should handle bad request errors from EDP service', async () => {
      const badRequestError = IctError.badRequest(
        'Invalid alarm data provided',
      );
      edpServiceStub.updateAlarm.rejects(badRequestError);

      try {
        await service.updateAlarm(validAlarmUpdateRequest);
        expect.fail('Should have thrown an error');
      } catch (thrownError) {
        expect(thrownError).to.equal(badRequestError);
        expect(loggerStub.error.calledWith('Failed to update alarm')).to.be
          .true;
      }
    });

    it('should handle unauthorized errors from EDP service', async () => {
      const unauthorizedError = IctError.unauthorized(
        'EDP API authentication failed',
      );
      edpServiceStub.updateAlarm.rejects(unauthorizedError);

      try {
        await service.updateAlarm(validAlarmUpdateRequest);
        expect.fail('Should have thrown an error');
      } catch (thrownError) {
        expect(thrownError).to.equal(unauthorizedError);
        expect(loggerStub.error.calledWith('Failed to update alarm')).to.be
          .true;
      }
    });

    it('should extract tenant and facility IDs from context', async () => {
      edpServiceStub.updateAlarm.resolves();

      await service.updateAlarm(validAlarmUpdateRequest);

      // Verify context utils were called
      expect(getTenantIdStub.calledOnce).to.be.true;
      expect(getFacilityIdStub.calledOnce).to.be.true;

      // Verify the correct tenant and facility IDs were used
      expect(edpServiceStub.updateAlarm.firstCall.args[0]).to.equal(
        mockTenantId,
      );
      expect(edpServiceStub.updateAlarm.firstCall.args[1]).to.equal(
        mockFacilityId,
      );
      expect(edpServiceStub.updateAlarm.firstCall.args[3]).to.equal(
        mockUserEmail,
      );
    });

    it('should handle missing context data gracefully', async () => {
      getTenantIdStub.throws(new Error('Tenant ID not found in context'));

      try {
        await service.updateAlarm(validAlarmUpdateRequest);
        expect.fail('Should have thrown an error for missing tenant ID');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect((error as Error).message).to.equal(
          'Tenant ID not found in context',
        );
      }
    });

    it('should handle facility ID extraction errors', async () => {
      getFacilityIdStub.throws(new Error('Facility ID not found in context'));

      try {
        await service.updateAlarm(validAlarmUpdateRequest);
        expect.fail('Should have thrown an error for missing facility ID');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect((error as Error).message).to.equal(
          'Facility ID not found in context',
        );
      }
    });

    it('should handle different date formats correctly', async () => {
      const updateWithDifferentDates: AlarmUpdateDto = {
        id: 'date-test-alarm',

        startDateLocal: new Date('2024-12-25T08:00:00.000Z'), // Christmas
        endDateLocal: new Date('2024-12-25T18:00:00.000Z'),
        isIncluded: true,
        comments: 'Holiday alarm update',
      };

      edpServiceStub.updateAlarm.resolves();

      await service.updateAlarm(updateWithDifferentDates);

      expect(edpServiceStub.updateAlarm.calledOnce).to.be.true;
      const passedAlarm = edpServiceStub.updateAlarm.firstCall.args[2];
      expect(passedAlarm.startDateLocal).to.be.instanceOf(Date);
      expect(passedAlarm.endDateLocal).to.be.instanceOf(Date);
      expect(passedAlarm.startDateLocal!.getTime()).to.equal(
        new Date('2024-12-25T08:00:00.000Z').getTime(),
      );
      expect(passedAlarm.endDateLocal!.getTime()).to.equal(
        new Date('2024-12-25T18:00:00.000Z').getTime(),
      );
    });

    it('should handle boolean values correctly', async () => {
      const testCases = [
        {...validAlarmUpdateRequest, isIncluded: true},
        {...validAlarmUpdateRequest, isIncluded: false},
      ];

      edpServiceStub.updateAlarm.resolves();

      for (const testCase of testCases) {
        await service.updateAlarm(testCase);
        const passedAlarm = edpServiceStub.updateAlarm.lastCall.args[2];
        expect(passedAlarm.isIncluded).to.equal(testCase.isIncluded);
        expect(typeof passedAlarm.isIncluded).to.equal('boolean');
      }

      expect(edpServiceStub.updateAlarm.callCount).to.equal(testCases.length);
    });

    it('should handle various alarm ID formats', async () => {
      const testIds = [
        'uuid-format-1234-5678-9abc-def0',
        '12345678-1234-1234-1234-123456789abc',
        'simple-string-id',
        'UPPERCASE-ID',
        'mixed-Case_ID123',
      ];

      edpServiceStub.updateAlarm.resolves();

      for (const testId of testIds) {
        const updateRequest = {
          ...validAlarmUpdateRequest,
          id: testId,
        };

        await service.updateAlarm(updateRequest);

        const passedAlarm = edpServiceStub.updateAlarm.lastCall.args[2];
        expect(passedAlarm.id).to.equal(testId);
      }

      expect(edpServiceStub.updateAlarm.callCount).to.equal(testIds.length);
    });

    it('should preserve all alarm properties during update', async () => {
      const completeUpdateRequest: AlarmUpdateDto = {
        id: 'complete-test-id',

        startDateLocal: new Date('2023-06-15T09:00:00.000Z'),
        endDateLocal: new Date('2023-06-15T17:00:00.000Z'),
        isIncluded: true,
        comments: 'Complete test comments with special characters: @#$%^&*()',
      };

      edpServiceStub.updateAlarm.resolves();

      await service.updateAlarm(completeUpdateRequest);

      const passedAlarm = edpServiceStub.updateAlarm.firstCall.args[2];
      expect(passedAlarm).to.deep.equal(completeUpdateRequest);

      // Verify all properties are preserved
      expect(passedAlarm.id).to.equal(completeUpdateRequest.id);

      expect(passedAlarm.startDateLocal).to.deep.equal(
        completeUpdateRequest.startDateLocal,
      );
      expect(passedAlarm.endDateLocal).to.deep.equal(
        completeUpdateRequest.endDateLocal,
      );
      expect(passedAlarm.isIncluded).to.equal(completeUpdateRequest.isIncluded);
      expect(passedAlarm.comments).to.equal(completeUpdateRequest.comments);
    });
  });

  describe('context methods', () => {
    describe('getTenantId', () => {
      it('should extract tenant ID from context datasetId', async () => {
        edpServiceStub.getAlarms.resolves({
          data: [],
          metadata: {
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        });

        await service.getAlarms({
          start_date: new Date(),
          end_date: new Date(),
        });

        expect(getTenantIdStub.calledOnce).to.be.true;
        expect(
          edpServiceStub.getAlarms.calledWith(
            mockTenantId,
            sinon.match.any,
            sinon.match.any,
          ),
        ).to.be.true;
      });

      it('should throw error when tenant ID not found', async () => {
        getTenantIdStub.throws(new Error('Tenant ID not found in context'));
        try {
          await service.getAlarms({
            start_date: new Date(),
            end_date: new Date(),
          });
          expect.fail('Should have thrown an error');
        } catch (error) {
          expect(error).to.be.instanceOf(Error);
          expect((error as Error).message).to.equal(
            'Tenant ID not found in context',
          );
        }
      });
    });

    describe('getFacilityId', () => {
      it('should extract facility ID from context datasetId split', async () => {
        edpServiceStub.getAlarms.resolves({
          data: [],
          metadata: {
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        });

        await service.getAlarms({
          start_date: new Date(),
          end_date: new Date(),
        });

        expect(getFacilityIdStub.calledOnce).to.be.true;
        expect(
          edpServiceStub.getAlarms.calledWith(
            sinon.match.any,
            mockFacilityId,
            sinon.match.any,
          ),
        ).to.be.true;
      });

      it('should throw error when facility ID not found', async () => {
        getFacilityIdStub.throws(new Error('Facility ID not found in context'));
        try {
          await service.getAlarms({
            start_date: new Date(),
            end_date: new Date(),
          });
          expect.fail('Should have thrown an error');
        } catch (error) {
          expect(error).to.be.instanceOf(Error);
          expect((error as Error).message).to.equal(
            'Facility ID not found in context',
          );
        }
      });
    });
  });

  describe('data transformation', () => {
    it('should correctly transform multiple EDP alarms', async () => {
      const edpAlarms: EDPAlarm[] = [
        {
          id: 'c356e13f-8898-4e4f-bdce-bffcd148e605',
          sectionArea: 'Area A',
          sectionName: 'Section 1',
          equipmentName: 'Equipment 1',
          faultId: 'FAULT-001',
          faultDescription: 'First fault',
          faultTag: 'TAG-001',
          severity: 'Medium',
          faultStartTimeUtc: '2023-01-15T10:30:00Z',
          faultEndTimeUtc: '2023-01-15T11:30:00Z',
          faultDuration: 3600000,
          splitStartTimeUtc: '2023-01-15T10:30:00Z',
          splitEndTimeUtc: '2023-01-15T11:30:00Z',
          splitDuration: 3600000,
          origStartTimeUtc: '2023-01-15T10:30:00Z',
          origEndTimeUtc: '2023-01-15T11:30:00Z',
          origDuration: 3600000,
          removalStatus: 'ACTIVE',
          removalReason: 'Investigating',
          updatedStartTimeUtc: '2023-01-15T10:30:00Z',
          updatedEndTimeUtc: '2023-01-15T11:30:00Z',
          updatedDuration: 3600000,
        },
        {
          id: 'j765e13f-3398-4e4f-bdce-bffcd148e123',
          sectionArea: 'Area B',
          sectionName: 'Section 2',
          equipmentName: 'Equipment 2',
          faultId: 'FAULT-002',
          faultDescription: 'Second fault',
          faultTag: 'TAG-002',
          severity: 'Low',
          faultStartTimeUtc: '2023-01-16T14:15:00Z',
          faultEndTimeUtc: '',
          faultDuration: 9000000,
          splitStartTimeUtc: '2023-01-16T14:15:00Z',
          splitEndTimeUtc: '',
          splitDuration: 0,
          origStartTimeUtc: '2023-01-16T14:15:00Z',
          origEndTimeUtc: '',
          origDuration: 0,
          removalStatus: 'RESOLVED',
          removalReason: 'Fixed',
          updatedStartTimeUtc: '',
          updatedEndTimeUtc: '',
          updatedDuration: 0,
        },
      ];

      const response: EDPAlarmsResponse = {
        data: edpAlarms,
        metadata: {
          page: 1,
          limit: 10,
          total: 2,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      };

      edpServiceStub.getAlarms.resolves(response);

      const result = await service.getAlarms({
        start_date: new Date(),
        end_date: new Date(),
      });

      expect(result.data).to.have.length(2);
      expect(result.data[0].id).to.equal(
        'c356e13f-8898-4e4f-bdce-bffcd148e605',
      );
      expect(result.data[1].id).to.equal(
        'j765e13f-3398-4e4f-bdce-bffcd148e123',
      );
      expect(result.data[0].location.area).to.equal('Area A');
      expect(result.data[1].location.area).to.equal('Area B');
    });
  });

  describe('pagination calculation', () => {
    it('should use EDP page number in response', async () => {
      const testCases = [
        {page: 1, limit: 10},
        {page: 2, limit: 10},
        {page: 3, limit: 25},
      ];

      for (const testCase of testCases) {
        // Mock EDP response with the expected page for this test case
        edpServiceStub.getAlarms.resolves({
          data: [],
          metadata: {
            page: testCase.page,
            limit: testCase.limit,
            total: 100,
            totalPages: 10,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        });

        const params: PaginatedRequest = {
          page: testCase.page,
          limit: testCase.limit,
          start_date: new Date(),
          end_date: new Date(),
        };

        const result = await service.getAlarms(params);

        expect(result.metadata.page).to.equal(testCase.page);
        expect(result.metadata.limit).to.equal(testCase.limit);
      }
    });

    it('should default to page 1 when page is undefined', async () => {
      edpServiceStub.getAlarms.resolves({
        data: [],
        metadata: {
          page: 1,
          limit: 50,
          total: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      });

      const result = await service.getAlarms({
        start_date: new Date(),
        end_date: new Date(),
      });

      expect(result.metadata.page).to.equal(1);
      expect(result.metadata.limit).to.equal(50);
    });
  });

  describe('getAlarmById', () => {
    const mockAlarmId = 'b307f935';
    const mockEDPAlarm: EDPAlarm = {
      id: 'c356e13f-8898-4e4f-bdce-bffcd148e605',
      sectionArea: 'DMSInboundMergeSorter',
      sectionName: 'ENET32',
      equipmentName: 'ENET32_COMM',
      faultId: 'b307f935',
      faultDescription: 'ENET32 COMMUNICATION ERROR',
      faultTag: 'ENET32_COMM_ERR',
      severity: 'High',
      faultStartTimeUtc: '2025-07-10T21:45:06.000Z',
      faultEndTimeUtc: '2025-07-10T21:46:02.000Z',
      faultDuration: 56000,
      splitStartTimeUtc: '2025-07-10T21:45:06.000Z',
      splitEndTimeUtc: '2025-07-10T21:46:02.000Z',
      splitDuration: 56000,
      origStartTimeUtc: '2025-07-10T21:45:06.000Z',
      origEndTimeUtc: '2025-07-10T21:46:02.000Z',
      origDuration: 56000,
      removalStatus: 'KEPT',
      removalReason: 'KEPT',
      updatedStartTimeUtc: '2025-07-10T21:45:06.000Z',
      updatedEndTimeUtc: '2025-07-10T21:46:02.000Z',
      updatedDuration: 56,
      comments: 'Example comments',
      lastUpdatedUser: '<EMAIL>',
    };

    const expectedAlarm: Alarm = {
      id: 'c356e13f-8898-4e4f-bdce-bffcd148e605',
      faultId: 'b307f935',
      title: 'ENET32 COMMUNICATION ERROR',
      description: 'ENET32 COMMUNICATION ERROR',
      tag: 'ENET32_COMM_ERR',
      severity: 'High',
      location: {
        area: 'DMSInboundMergeSorter',
        section: 'ENET32',
        equipment: 'ENET32_COMM',
      },
      timing: {
        startTime: new Date('2025-07-10T21:45:06.000Z'),
        endTime: new Date('2025-07-10T21:46:02.000Z'),
        duration: '56000',
        updatedStartTime: new Date('2025-07-10T21:45:06.000Z'),
        updatedEndTime: new Date('2025-07-10T21:46:02.000Z'),
        updatedDuration: '56',
        splitStartTime: new Date('2025-07-10T21:45:06.000Z'),
        splitEndTime: new Date('2025-07-10T21:46:02.000Z'),
        splitDuration: '56000',
        origStartTime: new Date('2025-07-10T21:45:06.000Z'),
        origEndTime: new Date('2025-07-10T21:46:02.000Z'),
        origDuration: '56000',
      },
      status: 'KEPT',
      reason: 'KEPT',
      comments: 'Example comments',
      lastUpdatedUser: '<EMAIL>',
    };

    beforeEach(() => {
      edpServiceStub.getAlarmById.resolves(mockEDPAlarm);
    });

    it('should successfully retrieve and transform a single alarm', async () => {
      const result = await service.getAlarmById(mockAlarmId);

      expect(result).to.deep.equal(expectedAlarm);

      // Verify EDP service was called with correct parameters
      expect(edpServiceStub.getAlarmById.calledOnce).to.be.true;
      expect(edpServiceStub.getAlarmById.firstCall.args[0]).to.equal(
        mockTenantId,
      );
      expect(edpServiceStub.getAlarmById.firstCall.args[1]).to.equal(
        mockFacilityId,
      );
      expect(edpServiceStub.getAlarmById.firstCall.args[2]).to.equal(
        mockAlarmId,
      );

      // Verify logging
      expect(loggerStub.info.calledWith('Getting alarm by ID')).to.be.true;
    });

    it('should handle undefined/null fields in EDP response', async () => {
      const incompleteEDPAlarm: EDPAlarm = {
        ...mockEDPAlarm,
        faultDescription: '',
        faultTag: '',
        faultEndTimeUtc: '',
        updatedStartTimeUtc: '',
        updatedEndTimeUtc: '',
        updatedDuration: 0,
      };

      edpServiceStub.getAlarmById.resolves(incompleteEDPAlarm);

      const result = await service.getAlarmById(mockAlarmId);

      expect(result.title).to.equal('');
      expect(result.description).to.equal('');
      expect(result.tag).to.equal('');
      expect(result.timing.endTime).to.be.undefined;
      expect(result.timing.updatedStartTime).to.be.undefined;
      expect(result.timing.updatedEndTime).to.be.undefined;
      expect(result.timing.updatedDuration).to.equal('0');
    });

    it('should propagate EDP service errors', async () => {
      const serviceError = IctError.notFound('Alarm not found');
      edpServiceStub.getAlarmById.rejects(serviceError);

      try {
        await service.getAlarmById(mockAlarmId);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.equal(serviceError);
      }

      expect(loggerStub.error.calledWith('Failed to get alarm by ID')).to.be
        .true;
    });

    it('should extract tenant and facility IDs from context', async () => {
      await service.getAlarmById(mockAlarmId);

      // Verify the correct tenant and facility IDs were extracted and used
      expect(edpServiceStub.getAlarmById.firstCall.args[0]).to.equal(
        mockTenantId,
      );
      expect(edpServiceStub.getAlarmById.firstCall.args[1]).to.equal(
        mockFacilityId,
      );
    });

    it('should handle different alarm ID formats', async () => {
      const testIds = ['abc123', 'test-alarm-001', 'b307f935', '12345678'];

      for (const testId of testIds) {
        await service.getAlarmById(testId);
        expect(
          edpServiceStub.getAlarmById.calledWith(
            mockTenantId,
            mockFacilityId,
            testId,
          ),
        ).to.be.true;
      }
    });

    it('should handle missing context data gracefully', async () => {
      getTenantIdStub.throws(new Error('Tenant ID not found in context'));

      try {
        await service.getAlarmById(mockAlarmId);
        expect.fail('Should have thrown an error for missing tenant ID');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
      }
    });

    it('should log detailed information for debugging', async () => {
      await service.getAlarmById(mockAlarmId);

      const logCall = loggerStub.info.firstCall;
      expect(logCall.args[0]).to.equal('Getting alarm by ID');
      expect(logCall.args[1]).to.have.property('tenantId', mockTenantId);
      expect(logCall.args[1]).to.have.property('facilityId', mockFacilityId);
      expect(logCall.args[1]).to.have.property('alarmId', mockAlarmId);
    });
  });
});
