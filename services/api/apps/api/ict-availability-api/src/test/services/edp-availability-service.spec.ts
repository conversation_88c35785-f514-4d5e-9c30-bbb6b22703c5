import sinon from 'sinon';
import axios, {AxiosError} from 'axios';
import {expect} from 'chai';
import {
  Container,
  ContextService,
  EnvironmentService,
  WinstonLogger,
  IctError,
} from 'ict-api-foundations';
import {GoogleAuth} from 'google-auth-library';
import {EDPAvailabilityService} from '../../services/edp-availability-service.ts';
import type {
  EDPAlarmQueryParams,
  EDPAlarm,
  EDPAlarmsResponse,
  EDPSingleAlarmResponse,
} from '../../services/edp-availability-service.ts';
import {AlarmCreateDto} from '../../defs/alarm-create-dto.ts';
import {AlarmUpdateDto} from '../../defs/alarm-update-dto.ts';

describe('EDPAvailabilityService', () => {
  let service: EDPAvailabilityService;
  let loggerStub: sinon.SinonStubbedInstance<WinstonLogger>;
  let envServiceStub: sinon.SinonStubbedInstance<EnvironmentService>;
  let contextServiceStub: sinon.SinonStubbedInstance<ContextService>;
  let axiosGetStub: sinon.SinonStub;
  let axiosPostStub: sinon.SinonStub;
  let axiosPatchStub: sinon.SinonStub;
  let googleAuthStub: sinon.SinonStub;

  const mockBaseUrl = 'http://host.docker.internal:3000';
  const mockProjectId = 'test-project-id';
  const mockToken = 'mock-auth-token';
  const mockTenantBid = 'test-tenant';
  const mockFacilityBid = 'test-facility';
  const mockUserEmail = '<EMAIL>';

  beforeEach(() => {
    // Create stubs for dependencies
    loggerStub = sinon.createStubInstance(WinstonLogger);
    contextServiceStub = sinon.createStubInstance(ContextService);

    // Create environment service stub with systemAvailability property
    envServiceStub = {
      systemAvailability: {
        projectId: mockProjectId,
      },
    } as sinon.SinonStubbedInstance<EnvironmentService>;

    // Set up environment variables
    process.env.EDP_API_BASE_URL = mockBaseUrl;
    process.env.NODE_ENV = 'test';
    process.env.ENVIRONMENT = 'local';
    process.env.LOCAL_ID_TOKEN = mockToken;

    // Mock Container.get calls
    sinon.stub(Container, 'get').callsFake((token: any) => {
      if (token === WinstonLogger) return loggerStub;
      if (token === EnvironmentService) return envServiceStub;
      if (token === ContextService) return contextServiceStub;
      throw new Error(`Unexpected token: ${token}`);
    });

    // Set up axios stub
    axiosGetStub = sinon.stub(axios, 'get');
    axiosPostStub = sinon.stub(axios, 'post');
    axiosPatchStub = sinon.stub(axios, 'patch');

    // Set up GoogleAuth mock
    googleAuthStub = sinon.stub(GoogleAuth.prototype, 'getIdTokenClient');

    // Create service instance
    service = new EDPAvailabilityService();
  });

  afterEach(() => {
    sinon.restore();
    delete process.env.EDP_API_BASE_URL;
    delete process.env.NODE_ENV;
    delete process.env.ENVIRONMENT;
    delete process.env.LOCAL_ID_TOKEN;
  });

  describe('constructor', () => {
    it('should initialize with correct base URL from environment', () => {
      expect(loggerStub.info.calledWith('EDP Service initialized')).to.be.true;
      const initCall = loggerStub.info.firstCall.args[1] as any;
      expect(initCall.baseUrl).to.equal(mockBaseUrl);
      expect(initCall.projectId).to.equal(mockProjectId);
    });

    it('should use default URL when EDP_API_BASE_URL not set', () => {
      delete process.env.EDP_API_BASE_URL;
      const newService = new EDPAvailabilityService();
      const initCall = loggerStub.info.secondCall.args[1] as any;
      expect(initCall.baseUrl).to.equal('http://host.docker.internal:3000');
    });

    it('should use URL from environment service when provided', () => {
      // Create a fresh service instance with URL from environment service
      const freshLoggerStub = sinon.createStubInstance(WinstonLogger);
      const freshEnvServiceStub = {
        systemAvailability: {
          projectId: mockProjectId,
          url: 'http://localhost:3000', // Provide URL via environment service
        },
      } as sinon.SinonStubbedInstance<EnvironmentService>;
      const freshContextServiceStub = sinon.createStubInstance(ContextService);

      // Restore existing Container.get stub and create a new one
      (Container.get as sinon.SinonStub).restore();
      sinon.stub(Container, 'get').callsFake((token: any) => {
        if (token === WinstonLogger) return freshLoggerStub;
        if (token === EnvironmentService) return freshEnvServiceStub;
        if (token === ContextService) return freshContextServiceStub;
        throw new Error(`Unexpected token: ${token}`);
      });

      // Create a new service instance to test URL from environment service
      const devService = new EDPAvailabilityService();

      // Check the initialization call
      const initCall = freshLoggerStub.info.firstCall.args[1] as any;
      expect(initCall.baseUrl).to.equal('http://localhost:3000');

      // Restore Container
      (Container.get as sinon.SinonStub).restore();

      // Re-establish the original Container.get stub for other tests
      sinon.stub(Container, 'get').callsFake((token: any) => {
        if (token === WinstonLogger) return loggerStub;
        if (token === EnvironmentService) return envServiceStub;
        if (token === ContextService) return contextServiceStub;
        throw new Error(`Unexpected token: ${token}`);
      });
    });
  });

  describe('getAlarms', () => {
    const mockQueryParams: EDPAlarmQueryParams = {
      page: 1,
      limit: 10,
      sortBy: 'faultStartTimeUtc',
      orderBy: 'DESC',
    };

    const mockEDPResponse: EDPAlarmsResponse = {
      data: [
        {
          id: 'ALARM-123',
          sectionArea: 'Area A',
          sectionName: 'Section 1',
          equipmentName: 'Equipment 1',
          faultId: 'FAULT-001',
          faultDescription: 'Test fault',
          faultTag: 'TAG-001',
          severity: 'High',
          faultStartTimeUtc: '2023-01-01T00:00:00Z',
          faultEndTimeUtc: '2023-01-01T01:00:00Z',
          faultDuration: 3600000,
          splitStartTimeUtc: '2023-01-01T00:00:00Z',
          splitEndTimeUtc: '2023-01-01T01:00:00Z',
          splitDuration: 3600000,
          origStartTimeUtc: '2023-01-01T00:00:00Z',
          origEndTimeUtc: '2023-01-01T01:00:00Z',
          origDuration: 3600000,
          removalStatus: 'ACTIVE',
          removalReason: '',
          updatedStartTimeUtc: '2023-01-01T00:00:00Z',
          updatedEndTimeUtc: '2023-01-01T01:00:00Z',
          updatedDuration: 3600000,
        },
      ],
      metadata: {
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    };

    beforeEach(() => {
      // Set up successful auth token response
      axiosGetStub.resolves({
        data: mockEDPResponse,
        status: 200,
      });
    });

    it('should successfully retrieve alarms', async () => {
      const result = await service.getAlarms(
        mockTenantBid,
        mockFacilityBid,
        mockQueryParams,
      );

      expect(result).to.deep.equal(mockEDPResponse);
      expect(axiosGetStub.calledOnce).to.be.true;
      expect(loggerStub.info.calledWith('Getting alarms from EDP API')).to.be
        .true;
      expect(
        loggerStub.info.calledWith(
          'Successfully retrieved alarms from EDP API',
        ),
      ).to.be.true;
    });

    it('should include correct headers in API call', async () => {
      await service.getAlarms(mockTenantBid, mockFacilityBid, mockQueryParams);

      const axiosCall = axiosGetStub.firstCall;
      const headers = axiosCall.args[1].headers;

      expect(headers['Authorization']).to.equal(`Bearer ${mockToken}`);
      expect(headers['edp-tenant-bid']).to.equal(mockTenantBid);
      expect(headers['edp-facility-bid']).to.equal(mockFacilityBid);
      expect(headers['Content-Type']).to.equal('application/json');
    });

    it('should pass query parameters correctly', async () => {
      await service.getAlarms(mockTenantBid, mockFacilityBid, mockQueryParams);

      const axiosCall = axiosGetStub.firstCall;
      expect(axiosCall.args[1].params).to.deep.equal(mockQueryParams);
    });

    it('should handle 401 unauthorized error', async () => {
      const axiosError = new AxiosError('Request failed', '401');
      axiosError.response = {status: 401} as any;
      axiosGetStub.rejects(axiosError);

      try {
        await service.getAlarms(
          mockTenantBid,
          mockFacilityBid,
          mockQueryParams,
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).message).to.equal(
          'EDP API authentication failed',
        );
      }

      // Verify token cache is cleared
      expect(
        loggerStub.error.calledWith('Failed to retrieve alarms from EDP API'),
      ).to.be.true;
    });

    it('should handle 400 bad request error', async () => {
      const axiosError = new AxiosError('Request failed', '400');
      axiosError.response = {status: 400} as any;
      axiosGetStub.rejects(axiosError);

      try {
        await service.getAlarms(
          mockTenantBid,
          mockFacilityBid,
          mockQueryParams,
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).message).to.equal(
          'Invalid request parameters for EDP API',
        );
      }
    });

    it('should handle 404 not found error', async () => {
      const axiosError = new AxiosError('Request failed', '404');
      axiosError.response = {status: 404} as any;
      axiosGetStub.rejects(axiosError);

      try {
        await service.getAlarms(
          mockTenantBid,
          mockFacilityBid,
          mockQueryParams,
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).message).to.equal(
          'EDP API endpoint not found',
        );
      }
    });

    it('should handle general network errors', async () => {
      axiosGetStub.rejects(new Error('Network error'));

      try {
        await service.getAlarms(
          mockTenantBid,
          mockFacilityBid,
          mockQueryParams,
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).message).to.equal(
          'Failed to retrieve alarms from EDP API',
        );
      }
    });
  });

  describe('createAlarm', () => {
    const mockAlarm: AlarmCreateDto = {
      startDateLocal: new Date('2023-01-01T00:00:00Z'),
      endDateLocal: new Date('2023-01-01T01:00:00Z'),
      isIncluded: true,
      section: 'Section 1',
      equipment: 'Equipment 1',
      description: 'Test alarm description',
      comments: 'Test comments',
    };

    beforeEach(() => {
      // Set up successful create response
      axiosPostStub.resolves({
        status: 201,
      });
    });
    it('should successfully create an alarm', async () => {
      await service.createAlarm(
        mockTenantBid,
        mockFacilityBid,
        mockAlarm,
        mockUserEmail,
      );

      expect(axiosPostStub.calledOnce).to.be.true;
      const axiosCall = axiosPostStub.firstCall;
      expect(axiosCall.args[0]).to.equal(`${mockBaseUrl}/alarms/`);
    });

    it('should include correct headers in create request', async () => {
      await service.createAlarm(
        mockTenantBid,
        mockFacilityBid,
        mockAlarm,
        mockUserEmail,
      );

      const axiosCall = axiosPostStub.firstCall;
      const headers = axiosCall.args[2].headers;

      expect(headers['Authorization']).to.equal(`Bearer ${mockToken}`);
      expect(headers['edp-tenant-bid']).to.equal(mockTenantBid);
      expect(headers['edp-facility-bid']).to.equal(mockFacilityBid);
      expect(headers['Content-Type']).to.equal('application/json');
    });

    it('should handle 401 unauthorized error on create', async () => {
      const axiosError = new AxiosError('Request failed', '401');
      axiosError.response = {status: 401} as any;
      axiosPostStub.rejects(axiosError);

      try {
        await service.createAlarm(
          mockTenantBid,
          mockFacilityBid,
          mockAlarm,
          mockUserEmail,
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).message).to.equal(
          'EDP API authentication failed',
        );
      }

      // Verify token cache is cleared
      expect(loggerStub.error.calledWith('Failed to create alarm in EDP API'))
        .to.be.true;
    });

    it('should handle 400 bad request error on create', async () => {
      const axiosError = new AxiosError('Request failed', '400');
      axiosError.response = {status: 400} as any;
      axiosPostStub.rejects(axiosError);

      try {
        await service.createAlarm(
          mockTenantBid,
          mockFacilityBid,
          mockAlarm,
          mockUserEmail,
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).message).to.equal(
          'Invalid alarm data provided',
        );
      }
    });

    it('should handle 404 not found error on create', async () => {
      const axiosError = new AxiosError('Request failed', '404');
      axiosError.response = {status: 404} as any;
      axiosPostStub.rejects(axiosError);

      try {
        await service.createAlarm(
          mockTenantBid,
          mockFacilityBid,
          mockAlarm,
          mockUserEmail,
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).message).to.equal(
          'Failed to create alarm in EDP API',
        );
      }
    });

    it('should handle general network errors on create', async () => {
      axiosPostStub.rejects(new Error('Network error'));

      try {
        await service.createAlarm(
          mockTenantBid,
          mockFacilityBid,
          mockAlarm,
          mockUserEmail,
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).message).to.equal(
          'Failed to create alarm in EDP API',
        );
      }
    });
  });

  describe('updateAlarm', () => {
    const mockAlarmUpdate: AlarmUpdateDto = {
      id: 'test-alarm-001',

      startDateLocal: new Date('2023-01-15T10:30:00.000Z'),
      endDateLocal: new Date('2023-01-15T11:30:00.000Z'),
      isIncluded: false,
      comments: 'Updated alarm comments',
    };

    beforeEach(() => {
      // Set up successful update response
      axiosPatchStub.resolves({
        status: 200,
      });
    });

    it('should successfully update an alarm', async () => {
      await service.updateAlarm(
        mockTenantBid,
        mockFacilityBid,
        mockAlarmUpdate,
        mockUserEmail,
      );

      expect(axiosPatchStub.calledOnce).to.be.true;
      const axiosCall = axiosPatchStub.firstCall;
      expect(axiosCall.args[0]).to.equal(`${mockBaseUrl}/alarms`);

      // Verify the alarm payload has been converted to strings
      const alarmPayload = axiosCall.args[1];
      expect(alarmPayload.id).to.equal(mockAlarmUpdate.id);

      expect(alarmPayload.startDateLocal).to.equal('2023-01-15T10:30:00.000Z');
      expect(alarmPayload.endDateLocal).to.equal('2023-01-15T11:30:00.000Z');
      expect(alarmPayload.isIncluded).to.equal(mockAlarmUpdate.isIncluded);
      expect(alarmPayload.comments).to.equal(mockAlarmUpdate.comments);
    });

    it('should include correct headers in update request', async () => {
      await service.updateAlarm(
        mockTenantBid,
        mockFacilityBid,
        mockAlarmUpdate,
        mockUserEmail,
      );

      const axiosCall = axiosPatchStub.firstCall;
      const headers = axiosCall.args[2].headers;

      expect(headers['Authorization']).to.equal(`Bearer ${mockToken}`);
      expect(headers['edp-tenant-bid']).to.equal(mockTenantBid);
      expect(headers['edp-facility-bid']).to.equal(mockFacilityBid);
      expect(headers['Content-Type']).to.equal('application/json');
    });

    it('should convert Date objects to ISO strings in request payload', async () => {
      const alarmWithDates: AlarmUpdateDto = {
        id: 'date-test-alarm',

        startDateLocal: new Date('2024-12-25T08:00:00.000Z'),
        endDateLocal: new Date('2024-12-25T18:00:00.000Z'),
        isIncluded: true,
        comments: 'Holiday alarm update',
      };

      await service.updateAlarm(
        mockTenantBid,
        mockFacilityBid,
        alarmWithDates,
        mockUserEmail,
      );

      const axiosCall = axiosPatchStub.firstCall;
      const alarmPayload = axiosCall.args[1];

      expect(alarmPayload.startDateLocal).to.equal('2024-12-25T08:00:00.000Z');
      expect(alarmPayload.endDateLocal).to.equal('2024-12-25T18:00:00.000Z');
      expect(typeof alarmPayload.startDateLocal).to.equal('string');
      expect(typeof alarmPayload.endDateLocal).to.equal('string');
    });

    it('should preserve all other alarm properties in request payload', async () => {
      await service.updateAlarm(
        mockTenantBid,
        mockFacilityBid,
        mockAlarmUpdate,
        mockUserEmail,
      );

      const axiosCall = axiosPatchStub.firstCall;
      const alarmPayload = axiosCall.args[1];

      expect(alarmPayload.id).to.equal(mockAlarmUpdate.id);

      expect(alarmPayload.isIncluded).to.equal(mockAlarmUpdate.isIncluded);
      expect(alarmPayload.comments).to.equal(mockAlarmUpdate.comments);
    });

    it('should log update request details', async () => {
      await service.updateAlarm(
        mockTenantBid,
        mockFacilityBid,
        mockAlarmUpdate,
        mockUserEmail,
      );

      expect(loggerStub.info.calledWith('Updating alarm in EDP API')).to.be
        .true;

      // Find the specific log call for updating alarm (not the initialization log)
      const updateLogCall = loggerStub.info
        .getCalls()
        .find(call => call.args[0] === 'Updating alarm in EDP API');
      expect(updateLogCall).to.exist;

      const logCall = updateLogCall!.args[1] as any;
      expect(logCall.tenantBid).to.equal(mockTenantBid);
      expect(logCall.facilityBid).to.equal(mockFacilityBid);
      expect(logCall.alarmId).to.equal(mockAlarmUpdate.id);
      expect(logCall.alarm).to.deep.equal(mockAlarmUpdate);
    });

    it('should log successful update completion', async () => {
      await service.updateAlarm(
        mockTenantBid,
        mockFacilityBid,
        mockAlarmUpdate,
        mockUserEmail,
      );

      expect(
        loggerStub.info.calledWith('Successfully updated alarm in EDP API'),
      ).to.be.true;

      // Find the specific log call for successful update
      const successLogCall = loggerStub.info
        .getCalls()
        .find(call => call.args[0] === 'Successfully updated alarm in EDP API');
      expect(successLogCall).to.exist;

      const logCall = successLogCall!.args[1] as any;
      expect(logCall.tenantBid).to.equal(mockTenantBid);
      expect(logCall.facilityBid).to.equal(mockFacilityBid);
      expect(logCall.alarmId).to.equal(mockAlarmUpdate.id);
    });

    it('should handle 401 unauthorized error on update', async () => {
      const axiosError = new AxiosError('Request failed', '401');
      axiosError.response = {status: 401} as any;
      axiosPatchStub.rejects(axiosError);

      try {
        await service.updateAlarm(
          mockTenantBid,
          mockFacilityBid,
          mockAlarmUpdate,
          mockUserEmail,
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).message).to.equal(
          'EDP API authentication failed',
        );
      }

      // Verify error logging
      expect(loggerStub.error.calledWith('Failed to update alarm in EDP API'))
        .to.be.true;
      const errorLogCall = loggerStub.error.firstCall.args[1] as any;
      expect(errorLogCall.tenantBid).to.equal(mockTenantBid);
      expect(errorLogCall.facilityBid).to.equal(mockFacilityBid);
      expect(errorLogCall.alarmId).to.equal(mockAlarmUpdate.id);
    });

    it('should handle 400 bad request error on update', async () => {
      const axiosError = new AxiosError('Request failed', '400');
      axiosError.response = {status: 400} as any;
      axiosPatchStub.rejects(axiosError);

      try {
        await service.updateAlarm(
          mockTenantBid,
          mockFacilityBid,
          mockAlarmUpdate,
          mockUserEmail,
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).message).to.equal(
          'Invalid alarm data provided',
        );
      }
    });

    it('should handle 404 not found error on update', async () => {
      const axiosError = new AxiosError('Request failed', '404');
      axiosError.response = {status: 404} as any;
      axiosPatchStub.rejects(axiosError);

      try {
        await service.updateAlarm(
          mockTenantBid,
          mockFacilityBid,
          mockAlarmUpdate,
          mockUserEmail,
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).message).to.equal(
          `Alarm with ID ${mockAlarmUpdate.id} not found in EDP system`,
        );
      }
    });

    it('should handle general network errors on update', async () => {
      axiosPatchStub.rejects(new Error('Network error'));

      try {
        await service.updateAlarm(
          mockTenantBid,
          mockFacilityBid,
          mockAlarmUpdate,
          mockUserEmail,
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).message).to.equal(
          'Failed to update alarm in EDP API',
        );
      }
    });

    it('should clear token cache on 401 authentication failure', async () => {
      const axiosError = new AxiosError('Request failed', '401');
      axiosError.response = {status: 401} as any;
      axiosPatchStub.rejects(axiosError);

      try {
        await service.updateAlarm(
          mockTenantBid,
          mockFacilityBid,
          mockAlarmUpdate,
          mockUserEmail,
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        // Expected error
      }

      // Verify token cache clearing is attempted
      // (This would be reflected in the internal implementation)
    });

    it('should handle various alarm ID formats', async () => {
      const testIds = [
        'uuid-format-1234-5678-9abc-def0',
        '12345678-1234-1234-1234-123456789abc',
        'simple-string-id',
        'UPPERCASE-ID',
        'mixed-Case_ID123',
      ];

      for (const testId of testIds) {
        const updateRequest = {
          ...mockAlarmUpdate,
          id: testId,
        };

        await service.updateAlarm(
          mockTenantBid,
          mockFacilityBid,
          updateRequest,
          mockUserEmail,
        );

        const axiosCall = axiosPatchStub.lastCall;
        const alarmPayload = axiosCall.args[1];
        expect(alarmPayload.id).to.equal(testId);
      }

      expect(axiosPatchStub.callCount).to.equal(testIds.length);
    });

    it('should handle boolean values correctly', async () => {
      const testCases = [
        {...mockAlarmUpdate, isIncluded: true},
        {...mockAlarmUpdate, isIncluded: false},
      ];

      for (const testCase of testCases) {
        await service.updateAlarm(
          mockTenantBid,
          mockFacilityBid,
          testCase,
          mockUserEmail,
        );
        const axiosCall = axiosPatchStub.lastCall;
        const alarmPayload = axiosCall.args[1];
        expect(alarmPayload.isIncluded).to.equal(testCase.isIncluded);
        expect(typeof alarmPayload.isIncluded).to.equal('boolean');
      }

      expect(axiosPatchStub.callCount).to.equal(testCases.length);
    });

    it('should handle empty comments correctly', async () => {
      const updateWithEmptyComments = {
        ...mockAlarmUpdate,
        comments: '',
      };

      await service.updateAlarm(
        mockTenantBid,
        mockFacilityBid,
        updateWithEmptyComments,
        mockUserEmail,
      );

      const axiosCall = axiosPatchStub.firstCall;
      const alarmPayload = axiosCall.args[1];
      expect(alarmPayload.comments).to.equal('');
    });

    it('should handle special characters in alarm data', async () => {
      const updateWithSpecialChars = {
        ...mockAlarmUpdate,

        comments: 'Comments with unicode: 🚨 ñoéñ',
      };

      await service.updateAlarm(
        mockTenantBid,
        mockFacilityBid,
        updateWithSpecialChars,
        mockUserEmail,
      );

      const axiosCall = axiosPatchStub.firstCall;
      const alarmPayload = axiosCall.args[1];

      expect(alarmPayload.comments).to.equal(updateWithSpecialChars.comments);
    });

    it('should handle large date ranges correctly', async () => {
      const updateWithLargeDateRange = {
        ...mockAlarmUpdate,
        startDateLocal: new Date('2020-01-01T00:00:00.000Z'),
        endDateLocal: new Date('2025-12-31T23:59:59.999Z'),
      };

      await service.updateAlarm(
        mockTenantBid,
        mockFacilityBid,
        updateWithLargeDateRange,
        mockUserEmail,
      );

      const axiosCall = axiosPatchStub.firstCall;
      const alarmPayload = axiosCall.args[1];
      expect(alarmPayload.startDateLocal).to.equal('2020-01-01T00:00:00.000Z');
      expect(alarmPayload.endDateLocal).to.equal('2025-12-31T23:59:59.999Z');
    });

    it('should use correct HTTP method for update', async () => {
      await service.updateAlarm(
        mockTenantBid,
        mockFacilityBid,
        mockAlarmUpdate,
        mockUserEmail,
      );

      expect(axiosPatchStub.calledOnce).to.be.true;
      expect(axiosGetStub.called).to.be.false;
      expect(axiosPostStub.called).to.be.false;
    });

    it('should call correct endpoint URL for update', async () => {
      await service.updateAlarm(
        mockTenantBid,
        mockFacilityBid,
        mockAlarmUpdate,
        mockUserEmail,
      );

      const axiosCall = axiosPatchStub.firstCall;
      expect(axiosCall.args[0]).to.equal(`${mockBaseUrl}/alarms`);
    });
  });

  describe('getAlarmById', () => {
    const mockAlarmId = 'ALARM-123';
    const mockAlarm: EDPAlarm = {
      id: 'ALARM-123',
      sectionArea: 'Area A',
      sectionName: 'Section 1',
      equipmentName: 'Equipment 1',
      faultId: 'FAULT-001',
      faultDescription: 'Test fault',
      faultTag: 'TAG-001',
      severity: 'Critical',
      faultStartTimeUtc: '2023-01-01T00:00:00Z',
      faultEndTimeUtc: '2023-01-01T01:00:00Z',
      faultDuration: 3600000,
      splitStartTimeUtc: '2023-01-01T00:00:00Z',
      splitEndTimeUtc: '2023-01-01T01:00:00Z',
      splitDuration: 3600000,
      origStartTimeUtc: '2023-01-01T00:00:00Z',
      origEndTimeUtc: '2023-01-01T01:00:00Z',
      origDuration: 3600000,
      removalStatus: 'ACTIVE',
      removalReason: '',
      updatedStartTimeUtc: '2023-01-01T00:00:00Z',
      updatedEndTimeUtc: '2023-01-01T01:00:00Z',
      updatedDuration: 3600000,
      comments: 'Test comments',
      lastUpdatedUser: 'TESTUSER',
    };

    beforeEach(() => {
      axiosGetStub.resolves({
        data: {
          statusCode: 200,
          success: true,
          message: 'Data fetched successfully',
          data: mockAlarm,
        },
        status: 200,
      });
    });

    it('should successfully retrieve alarm by ID', async () => {
      const result = await service.getAlarmById(
        mockTenantBid,
        mockFacilityBid,
        mockAlarmId,
      );

      expect(result).to.deep.equal(mockAlarm);
      expect(axiosGetStub.calledOnce).to.be.true;
      const axiosCall = axiosGetStub.firstCall;
      expect(axiosCall.args[0]).to.equal(
        `${mockBaseUrl}/alarms/${mockAlarmId}`,
      );
    });

    it('should handle 404 alarm not found', async () => {
      const axiosError = new AxiosError('Request failed', '404');
      axiosError.response = {status: 404} as any;
      axiosGetStub.rejects(axiosError);

      try {
        await service.getAlarmById(mockTenantBid, mockFacilityBid, mockAlarmId);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).message).to.equal(
          `Alarm with ID ${mockAlarmId} not found in EDP system`,
        );
      }
    });
  });

  describe('healthCheck', () => {
    it('should successfully perform health check', async () => {
      const healthResponse = 'Service is healthy';
      axiosGetStub.resolves({
        data: healthResponse,
        status: 200,
      });

      const result = await service.healthCheck();

      expect(result).to.equal(healthResponse);
      const axiosCall = axiosGetStub.firstCall;
      expect(axiosCall.args[0]).to.equal(`${mockBaseUrl}/health`);
      expect(loggerStub.info.calledWith('EDP API health check successful')).to
        .be.true;
    });

    it('should handle health check failure', async () => {
      axiosGetStub.rejects(new Error('Service unavailable'));

      try {
        await service.healthCheck();
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).message).to.equal(
          'EDP API health check failed',
        );
      }

      expect(loggerStub.error.calledWith('EDP API health check failed')).to.be
        .true;
    });
  });

  describe('authentication token management', () => {
    beforeEach(() => {
      // Reset axios stub for auth tests
      axiosGetStub.restore();
      axiosGetStub = sinon.stub(axios, 'get');
    });

    it('should use local token in local environment', async () => {
      process.env.ENVIRONMENT = 'local';
      process.env.LOCAL_ID_TOKEN = mockToken;

      axiosGetStub.resolves({
        data: {
          data: [],
          metadata: {
            page: 1,
            limit: 10,
            totalItems: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        },
        status: 200,
      });

      await service.getAlarms(mockTenantBid, mockFacilityBid);

      const headers = axiosGetStub.firstCall.args[1].headers;
      expect(headers['Authorization']).to.equal(`Bearer ${mockToken}`);
    });

    it('should cache authentication tokens', async () => {
      // First call
      axiosGetStub.resolves({
        data: {
          data: [],
          metadata: {
            page: 1,
            limit: 10,
            totalItems: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        },
        status: 200,
      });
      await service.getAlarms(mockTenantBid, mockFacilityBid);

      // Second call - should use cached token
      axiosGetStub.resolves({
        data: {
          data: [],
          metadata: {
            page: 1,
            limit: 10,
            totalItems: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        },
        status: 200,
      });
      await service.getAlarms(mockTenantBid, mockFacilityBid);

      // Both calls should use the same token
      const firstCallHeaders = axiosGetStub.firstCall.args[1].headers;
      const secondCallHeaders = axiosGetStub.secondCall.args[1].headers;
      expect(firstCallHeaders['Authorization']).to.equal(
        secondCallHeaders['Authorization'],
      );
    });

    it('should clear token cache on authentication failure', async () => {
      // First call fails with 401
      axiosGetStub.rejects({
        response: {status: 401},
        isAxiosError: true,
      });

      try {
        await service.getAlarms(mockTenantBid, mockFacilityBid);
      } catch (error) {
        // Expected
      }

      // Verify clearTokenCache was called internally
      // (This would be tested by checking internal state if accessible)
    });
  });

  describe('clearTokenCache', () => {
    it('should clear cached token and expiry', () => {
      service.clearTokenCache();

      expect(loggerStub.info.calledWith('Clearing EDP API token cache')).to.be
        .true;
    });
  });

  describe('buildRequestHeaders', () => {
    it('should build correct request headers', async () => {
      axiosGetStub.resolves({
        data: {
          data: [],
          metadata: {
            page: 1,
            limit: 10,
            totalItems: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        },
        status: 200,
      });

      await service.getAlarms(mockTenantBid, mockFacilityBid);

      const headers = axiosGetStub.firstCall.args[1].headers;
      expect(headers).to.deep.include({
        Authorization: `Bearer ${mockToken}`,
        'edp-tenant-bid': mockTenantBid,
        'edp-facility-bid': mockFacilityBid,
        'Content-Type': 'application/json',
      });
    });
  });
});
