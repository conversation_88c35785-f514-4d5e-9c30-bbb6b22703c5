import sinon from 'sinon';
import {expect} from 'chai';
import {
  Container,
  ContextService,
  WinstonLog<PERSON>,
  IctError,
} from 'ict-api-foundations';
import {EDPAvailabilityService} from '../../services/edp-availability-service.ts';
import {EquipmentService} from '../../services/equipment-service.ts';
import {type PaginatedRequestNoDates} from '@ict/sdk-foundations/types';
import {GetAvailableEquipmentResponse} from '../../defs/equipment-def.ts';
import {ContextUtils} from '../../utils/context-utils.js';

describe('EquipmentService', () => {
  let service: EquipmentService;
  let edpAvailabilityServiceStub: sinon.SinonStubbedInstance<EDPAvailabilityService>;
  let loggerStub: sinon.SinonStubbedInstance<WinstonLogger>;
  let contextServiceStub: sinon.SinonStubbedInstance<ContextService>;
  let getTenantIdStub: sinon.SinonStub;
  let getFacilityIdStub: sinon.SinonStub;

  const mockTenantId = 'test-tenant';
  const mockFacilityId = 'test-facility';

  beforeEach(() => {
    edpAvailabilityServiceStub = sinon.createStubInstance(
      EDPAvailabilityService,
    );
    loggerStub = sinon.createStubInstance(WinstonLogger);
    contextServiceStub = sinon.createStubInstance(ContextService);

    contextServiceStub.datasetId = `${mockTenantId}_${mockFacilityId}`;

    getTenantIdStub = sinon
      .stub(ContextUtils, 'getTenantId')
      .returns(mockTenantId);
    getFacilityIdStub = sinon
      .stub(ContextUtils, 'getFacilityId')
      .returns(mockFacilityId);

    sinon.stub(Container, 'get').callsFake(token => {
      if (token === EDPAvailabilityService) return edpAvailabilityServiceStub;
      if (token === WinstonLogger) return loggerStub;
      if (token === ContextService) return contextServiceStub;
      throw new Error(`Unexpected token: ${token}`);
    });

    service = new EquipmentService(
      edpAvailabilityServiceStub,
      loggerStub,
      contextServiceStub,
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getAvailableEquipment', () => {
    const mockQueryParams: PaginatedRequestNoDates & {sectionId?: number} = {
      limit: 10,
      page: 1,
      searchString: 'test',
      sectionId: 171,
    };

    const mockEdpResponse = {
      data: [
        {name: 'Equipment 1', pk: 1},
        {name: 'Equipment 2', pk: 2},
      ],
      metadata: {
        page: 1,
        limit: 10,
        total: 2,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    };

    const mockAppResponse: GetAvailableEquipmentResponse = {
      data: [
        {name: 'Equipment 1', pk: 1},
        {name: 'Equipment 2', pk: 2},
      ],
      metadata: {
        page: 1,
        limit: 10,
        totalResults: 2,
        totalPages: 1,
      },
    };

    it('should successfully retrieve available equipment', async () => {
      edpAvailabilityServiceStub.getAvailableEquipment.resolves(
        mockEdpResponse,
      );

      const result = await service.getAvailableEquipment(mockQueryParams);

      expect(result).to.deep.equal(mockAppResponse);
      expect(
        edpAvailabilityServiceStub.getAvailableEquipment.calledOnceWith(
          mockTenantId,
          mockFacilityId,
          sinon.match.any,
        ),
      ).to.be.true;
    });

    it('should handle errors from the EDP service', async () => {
      const error = new Error('EDP service error');
      edpAvailabilityServiceStub.getAvailableEquipment.rejects(error);

      try {
        await service.getAvailableEquipment(mockQueryParams);
        expect.fail('Should have thrown an error');
      } catch (err) {
        expect(err).to.equal(error);
      }
    });

    it('should throw an error if tenant ID is not found', async () => {
      getTenantIdStub.throws(new Error('Tenant ID not found in context'));

      try {
        await service.getAvailableEquipment(mockQueryParams);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect((error as Error).message).to.equal(
          'Tenant ID not found in context',
        );
      }
    });

    it('should throw an error if facility ID is not found', async () => {
      getFacilityIdStub.throws(new Error('Facility ID not found in context'));

      try {
        await service.getAvailableEquipment(mockQueryParams);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect((error as Error).message).to.equal(
          'Facility ID not found in context',
        );
      }
    });
  });
});
