import {
  Container,
  ProtectedRouteMiddleware,
  DatabaseTypes,
  PostgresDatabaseOptions,
  IctError,
  HttpStatusCodes,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Put,
  Body,
  Query,
  Path,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Response,
  Middlewares,
} from 'tsoa';
import {EntityTypes, CustomMetricConfigurationEntity} from 'ict-api-schema';
import type {
  MetricConfigDetail,
  MetricConfigValueResponse,
} from '../defs/metric-config-detail.ts';
import {MetricConfigService} from '../services/metric-config-service.ts';
import {MetricConfigSummary} from '../defs/metric-config-summary.ts';

@Route('config/process-flow')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [
      {
        type: DatabaseTypes.Postgres,
        options: {
          entityType: EntityTypes.Config,
        } as PostgresDatabaseOptions,
      },
      {
        type: DatabaseTypes.Postgres,
        options: {
          entityType: EntityTypes.ProcessFlow,
        } as PostgresDatabaseOptions,
      },
    ],
  }),
])
export class MetricConfigController extends Controller {
  static exampleResponse: MetricConfigSummary[] = [
    {
      id: '1',
      metricName: 'test-metric',
      configType: 'node',
      nodeName: 'test-node',
      factType: 'test-fact',
      enabled: true,
      active: true,
      isCustom: false,
      facilityId: 'default',
      views: ['facility', 'multishuttle'],
    },
  ];

  /**
   * Get metric configurations based on filter criteria.
   * Without any query params, returns all metric configs that are not soft_deleted.
   * @param {string} metricName Filter by metric name
   * @param {string} metricId Filter by metric ID
   * @param {string} factType Filter by fact type
   * @param {string} nodeName Filter by node name
   * @param {boolean} active Filter by active status
   * @param {boolean} enabled Filter by enabled status
   * @param {string} configType Filter by config type
   */
  @Example<MetricConfigSummary[]>(MetricConfigController.exampleResponse)
  @SuccessResponse(
    HttpStatusCodes.OK,
    'Returns a list of metric configurations',
  )
  @Response(
    HttpStatusCodes.NOT_FOUND,
    'No metric configurations found matching the criteria',
  )
  @Response(HttpStatusCodes.INTERNAL_SERVER_ERROR, 'Internal server error')
  @Get('metric-configs')
  @OperationId('GetMetricConfigSummary')
  @Tags('config')
  public async getMetricConfigs(
    @Query() metric_name?: string,
    @Query() metric_id?: string,
    @Query() fact_type?: string,
    @Query() node_name?: string,
    @Query() active?: boolean,
    @Query() enabled?: boolean,
    @Query() config_type?: string,
  ): Promise<MetricConfigSummary[]> {
    // Get the metric config service
    const service: MetricConfigService = Container.get(MetricConfigService);

    const filters = {
      metricName: metric_name,
      metricId: metric_id,
      factType: fact_type,
      nodeName: node_name,
      active,
      enabled,
      configType: config_type,
    };

    const metricConfigs = await service.getMetricConfigSummaries(filters);

    if (!metricConfigs || metricConfigs.length === 0) {
      const filterDetails = Object.entries(filters)
        .filter(([, value]) => value !== undefined)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ');

      throw IctError.notFound(
        `No metric configurations found with filters: ${filterDetails}`,
      );
    }

    return metricConfigs;
  }

  static exampleDetailResponse: MetricConfigValueResponse = {
    default: {
      metricConfigName: 'test-metric',
      configType: 'node',
      views: ['facility'],
      matchConditions: {eventType: 'test-event'},
      factType: 'test-fact',
      sourceSystem: 'diq',
      displayName: 'Test Metric',
      enabled: {'facility-1': true},
      active: {'facility-1': true},
      isCustom: false,
      nodeName: 'test-node',
      metricType: 'stockTime',
      timeWindow: '60m_set',
      aggregation: 'sum',
      redisOperation: 'event_set',
      graphOperation: 'area_node',
      metricUnits: '/hr',
    },
    custom: {
      metricConfigName: 'test-metric',
      configType: 'node',
      views: ['facility'],
      matchConditions: {eventType: 'test-event'},
      factType: 'test-fact',
      sourceSystem: 'diq',
      displayName: 'Test Metric Custom',
      enabled: true,
      active: true,
      isCustom: true,
      nodeName: 'test-node',
      metricType: 'stockTime',
      timeWindow: '60m_set',
      aggregation: 'sum',
      redisOperation: 'event_set',
      graphOperation: 'area_node',
      metricUnits: '/hr',
      description: 'Example metric configuration for testing',
    },
  };

  /**
   * Get a single metric configuration by name.
   * Returns both default and custom configurations if they exist.
   * If config_type is specified, only returns that type.
   * @param {string} metricName The name of the metric configuration to retrieve
   * @param {string} config_type Optional filter for 'default' or 'custom' configurations only
   */
  @Example<MetricConfigValueResponse>(
    MetricConfigController.exampleDetailResponse,
  )
  @SuccessResponse(
    HttpStatusCodes.OK,
    'Metric configuration retrieved successfully',
  )
  @Response(HttpStatusCodes.NO_CONTENT, 'No metric configuration found')
  @Response(HttpStatusCodes.INTERNAL_SERVER_ERROR, 'Internal server error')
  @Get('metric-config/{metricName}')
  @OperationId('GetMetricConfig')
  @Tags('config')
  public async getMetricConfigDetail(
    @Path() metricName: string,
    @Query() config_type?: 'default' | 'custom',
  ): Promise<MetricConfigValueResponse> {
    // Get the metric config service
    const service: MetricConfigService = Container.get(MetricConfigService);

    const result = await service.getMetricConfigDetail(metricName, config_type);

    // Return 204 No Content if no configurations are found
    if (!result.default && !result.custom) {
      this.setStatus(HttpStatusCodes.NO_CONTENT);
      return result;
    }

    return result;
  }

  static examplePutResponse: CustomMetricConfigurationEntity = {
    id: '1',
    metricConfigName: 'test-metric',
    configType: 'node',
    views: ['facility', 'multishuttle'],
    matchConditions: {eventType: 'test-event'},
    factType: 'test-fact',
    sourceSystem: 'diq',
    displayName: 'Test Metric',
    description: 'Example metric configuration for testing',
    enabled: true,
    active: true,
    facilityId: 'facility-1', // Set by facility middleware from ict-facility-id header
    nodeName: 'test-node',
    metricType: 'stockTime',
    timeWindow: '60m_set',
    aggregation: 'sum',
    redisOperation: 'event_set',
    graphOperation: 'area_node',
    metricUnits: '/hr',
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
  };

  /**
   * Updates or creates a metric configuration.
   * If a configuration with the same metric name and facility already exists, it will be updated.
   * Otherwise, a new configuration will be created.
   * The facility ID is determined from the ict-facility-id header.
   * @param {MetricConfig} requestBody The metric configuration data
   */
  @Example<Partial<CustomMetricConfigurationEntity>>(
    MetricConfigController.examplePutResponse,
  )
  @Response(HttpStatusCodes.OK, 'Metric configuration updated successfully')
  @Response(
    HttpStatusCodes.CREATED,
    'Metric configuration created successfully',
  )
  @Response(HttpStatusCodes.BAD_REQUEST, 'Invalid metric configuration data')
  @Put('metric-config')
  @OperationId('PutMetricConfig')
  @Tags('config')
  public async putMetricConfig(
    @Body() requestBody: MetricConfigDetail,
  ): Promise<CustomMetricConfigurationEntity> {
    // Get the metric config service
    const service: MetricConfigService = Container.get(MetricConfigService);

    const result = await service.updateOrCreateMetricConfig(requestBody);

    if (result.isNew) {
      this.setStatus(HttpStatusCodes.CREATED);
    } else {
      this.setStatus(HttpStatusCodes.OK);
    }

    return result.config;
  }
}
