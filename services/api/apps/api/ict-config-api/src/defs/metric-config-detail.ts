import {
  MatchConditions,
  RedisParams,
  NodeLabel,
  NameConfig,
  ConfigType,
  GraphOperation,
  TimeWindow,
  Aggregation,
  RedisOperation,
  CustomMetricConfigurationEntity,
} from 'ict-api-schema';

/**
 * Base interface for all metric configurations
 */
export interface BaseMetricConfigDetail {
  /**
   * Identifier for the metric config
   * Example: multishuttleAisleStorageLocationDistributionAvailable
   */
  metricConfigName: string;

  /**
   * The hierarchy graph view at which this metric applies
   * Each view pertains to a unique graph node.
   * Example: ["facility", "multishuttle"]
   */
  views: string[];

  /**
   * Conditions an event must meet for this metric to be processed
   */
  matchConditions: MatchConditions;

  /**
   * The type of metric being processed (node or edge)
   */
  configType: ConfigType;

  /**
   * The type of graph database operation
   */
  graphOperation?: GraphOperation | null;

  /**
   * Optional parameters required for metric processing
   * Example: {"rowHash": "{rowHash}"}
   */
  redisParams?: RedisParams | null;

  /**
   * The label for the node. Defaults to "Area"
   */
  label?: NodeLabel | null;

  /**
   * Defines the parent node hierarchy for this metric
   */
  parentNodes?: string[] | null;

  /**
   * Defines how to dynamically generate metric names based on source fields.
   * Note: Either this or node_name should be provided, but not both.
   */
  nameFormula?: NameConfig | null;

  /**
   * The type of fact this metric represents
   */
  factType: string;

  /**
   * The source system of the facts
   * Example: "diq"
   */
  sourceSystem: string;

  /**
   * Display name for the UI
   */
  displayName: string;

  /**
   * Description of the metric configuration
   */
  description?: string | null;

  /**
   * Acceptable low range
   * Used to indicate if the actual metric value is acceptable
   */
  acceptableLowRange?: number | null;

  /**
   * Acceptable high range
   * Used to indicate if the actual metric value is acceptable
   */
  acceptableHighRange?: number | null;

  /**
   * Example message for testing
   */
  exampleMessage?: unknown | null;

  /**
   * Whether the metric is enabled
   * For default configurations, this is an hstore where keys are facility IDs and values are booleans
   * For custom configurations, this is a boolean indicating if the metric is enabled for that facility
   * Can be undefined for default configurations if no facilities have enabled/disabled the metric
   * Can be null for custom configurations where enabled status is not applicable
   */
  enabled?: boolean | Record<string, boolean> | null;

  /**
   * Whether the metric is active
   * For default configurations, this is an hstore where keys are facility IDs and values are booleans
   * For custom configurations, this is a boolean indicating if the metric is active for that facility
   * Can be undefined for default configurations if no facilities have active/inactive status
   */
  active?: boolean | Record<string, boolean>;

  /**
   * Whether this is a custom configuration (true) or default configuration (false)
   */
  isCustom: boolean;
}

/**
 * Node Metric Configuration
 */
export type NodeMetricConfigDetail = BaseMetricConfigDetail & {
  configType: 'node';

  /**
   * The type of operation performed on the graph database
   * Example: "area_node"
   */
  graphOperation: GraphOperation;

  /**
   * The type of metric being measured
   * Example: "stockTime"
   */
  metricType: string;

  /**
   * The time window for the metric
   */
  timeWindow: TimeWindow;

  /**
   * The aggregation method for the metric
   */
  aggregation: Aggregation;

  /**
   * The Redis method to be performed
   * Example: "eventSet"
   */
  redisOperation: RedisOperation;

  /**
   * Optional units for the metric
   * Example: "/hr"
   */
  metricUnits?: string | null;

  /**
   * Static node name (alternative to nameFormula)
   * Note: Either this or nameFormula should be provided, but not both.
   * If provided, nameFormula should not be used.
   */
  nodeName?: string | null;
};

/**
 * Inbound Edge Metric Configuration
 * Tracks movement INTO an area
 */
export type InboundEdgeMetricConfigDetail = BaseMetricConfigDetail & {
  configType: 'inbound-edge';

  /**
   * The key in event data that identifies the handling unit
   * Example: "handlingUnitCode"
   */
  huId: string;

  /**
   * The name of the area where the handling unit is arriving
   * Example: "multishuttle"
   */
  inboundArea: string;

  /**
   * The Redis operation performed upon arrival
   * Example: "eventSet"
   */
  redisOperation: RedisOperation;

  /**
   * The type of operation performed on the graph database
   * Example: "area_node"
   */
  graphOperation: GraphOperation;

  /**
   * Optional units for the metric
   * Default: "units/hr"
   */
  metricUnits?: string | null;

  /**
   * The parent nodes for the inbound area
   */
  inboundParentNodes?: string[] | null;
};

/**
 * Outbound Edge Metric Configuration
 * Tracks movement OUT OF an area
 */
export type OutboundEdgeMetricConfigDetail = BaseMetricConfigDetail & {
  configType: 'outbound-edge';

  /**
   * The name of the area the handling unit is leaving
   * Example: "multishuttle"
   */
  outboundArea: string;

  /**
   * The key in event data that identifies the handling unit
   * Example: "handlingUnitCode"
   */
  huId: string;

  /**
   * The type of units being counted
   * Example: "handlingUnit"
   */
  units: string;

  /**
   * The parent nodes for the outbound area
   */
  outboundParentNodes?: string[] | null;
};

/**
 * Complete Edge Metric Configuration
 * Tracks movement BETWEEN two areas
 */
export interface CompleteEdgeMetricConfigDetail extends BaseMetricConfigDetail {
  configType: 'complete-edge';

  /**
   * The name of the area where the handling unit is arriving
   * Example: "multishuttle"
   */
  inboundArea: string;

  /**
   * The name of the area the handling unit is leaving
   * Example: "multishuttle"
   */
  outboundArea: string;

  /**
   * The Redis operation performed
   * Example: "eventSet"
   */
  redisOperation: RedisOperation;

  /**
   * The type of operation performed on the graph database
   * Example: "area_node"
   */
  graphOperation: GraphOperation;

  /**
   * The label for the outbound node
   * Default: "Area"
   */
  outboundNodeLabel: string;

  /**
   * The parent nodes for the inbound area
   */
  inboundParentNodes?: string[] | null;

  /**
   * The parent nodes for the outbound area
   */
  outboundParentNodes?: string[] | null;

  /**
   * Optional units for the metric
   * Default: "units/hr"
   */
  metricUnits?: string | null;
}

/**
 * Union type for all metric configurations (API request/response format)
 */
export type MetricConfigDetail =
  | NodeMetricConfigDetail
  | InboundEdgeMetricConfigDetail
  | OutboundEdgeMetricConfigDetail
  | CompleteEdgeMetricConfigDetail;

/**
 * Result of saving a custom metric configuration
 */
export interface CustomMetricConfigSaveResult {
  /**
   * The saved custom metric configuration entity
   */
  config: CustomMetricConfigurationEntity;

  /**
   * Whether this was a new configuration (true) or an update to existing (false)
   */
  isNew: boolean;
}

/**
 * Response structure for GET metric config detail endpoint
 * Returns a single metric configuration with precedence rules applied
 */
export interface MetricConfigValueResponse {
  /**
   * Default metric configuration, if available
   */
  default?: MetricConfigDetail;

  /**
   * Custom metric configuration, if available
   */
  custom?: MetricConfigDetail;
}
