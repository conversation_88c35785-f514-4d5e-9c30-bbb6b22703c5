import {
  DiService,
  ContextService,
  WinstonLogger,
  RedisClient,
} from 'ict-api-foundations';
import {
  DefaultMetricConfigurationEntity,
  CustomMetricConfigurationEntity,
} from 'ict-api-schema';
import {SelectQueryBuilder} from 'typeorm';
import {MetricConfigSummaryFilters} from '../defs/metric-config-filters.ts';
import type {
  MetricConfigDetail,
  CustomMetricConfigSaveResult,
} from '../defs/metric-config-detail.ts';
import {
  getNodeMetricKeyPattern,
  getInboundEdgeMetricKeyPattern,
  getOutboundEdgeMetricKeyPattern,
  getCompleteEdgeMetricKeyPattern,
} from '../utils/metric-key-pattern-utils.ts';

@DiService()
export class MetricConfigStore {
  public static readonly type: string = 'metric-config-store';

  constructor(
    private context: ContextService,
    private logger: WinstonLogger,
    private redisClient: RedisClient,
  ) {}

  get dbProvider() {
    return this.context.dbProvider;
  }

  async getMetricConfigSummaries(
    filters: MetricConfigSummaryFilters,
  ): Promise<
    (DefaultMetricConfigurationEntity | CustomMetricConfigurationEntity)[]
  > {
    this.logger.debug('Getting metric configurations from database', {filters});

    const entityManager =
      this.dbProvider.processFlowPostgres.datasource.manager;
    const defaultRepo = entityManager.getRepository(
      DefaultMetricConfigurationEntity,
    );
    const customRepo = entityManager.getRepository(
      CustomMetricConfigurationEntity,
    );

    // Build query for default configs
    const defaultQueryBuilder = defaultRepo.createQueryBuilder('defaultConfig');
    this.applyFilters(defaultQueryBuilder, filters, 'defaultConfig');

    // Build query for custom configs
    const customQueryBuilder = customRepo.createQueryBuilder('customConfig');
    this.applyFilters(customQueryBuilder, filters, 'customConfig');

    // Get results from both tables
    const [defaultConfigs, customConfigs] = await Promise.all([
      defaultQueryBuilder.getMany(),
      customQueryBuilder.getMany(),
    ]);

    // Create a set of metric names from custom configs
    const customMetricNames = new Set(
      customConfigs.map(config => config.metricConfigName),
    );

    // Filter out default configs that have a custom version
    const filteredDefaultConfigs = defaultConfigs.filter(
      config => !customMetricNames.has(config.metricConfigName),
    );

    // Combine results
    const results = [...filteredDefaultConfigs, ...customConfigs];

    // Transform HSTORE fields to booleans
    const transformedResults = results.map(config =>
      this.transformHstoreFields(config),
    );

    // Apply enabled and active filters in application layer
    const filteredResults = transformedResults.filter(config => {
      // Apply enabled filter (only for default configs)
      if (filters.enabled !== undefined && 'enabled' in config) {
        if (config.enabled !== filters.enabled) {
          return false;
        }
      }

      // Apply active filter
      if (filters.active !== undefined && 'active' in config) {
        if (config.active !== filters.active) {
          return false;
        }
      }

      return true;
    });

    this.logger.debug('Retrieved metric configurations from database', {
      count: filteredResults.length,
      defaultCount: defaultConfigs.length,
      customCount: customConfigs.length,
    });

    return filteredResults;
  }

  private applyFilters<
    T extends
      | DefaultMetricConfigurationEntity
      | CustomMetricConfigurationEntity,
  >(
    queryBuilder: SelectQueryBuilder<T>,
    filters: MetricConfigSummaryFilters,
    alias: string,
  ): void {
    if (filters.metricName) {
      queryBuilder.andWhere(`"${alias}".metric_config_name LIKE :metricName`, {
        metricName: `%${filters.metricName}%`,
      });
    }

    if (filters.metricId) {
      queryBuilder.andWhere(`"${alias}".id = :metricId`, {
        metricId: filters.metricId,
      });
    }

    if (filters.factType) {
      queryBuilder.andWhere(`"${alias}".fact_type = :factType`, {
        factType: filters.factType,
      });
    }

    if (filters.nodeName) {
      queryBuilder.andWhere(`"${alias}".node_name LIKE :nodeName`, {
        nodeName: `%${filters.nodeName}%`,
      });
    }

    if (filters.configType) {
      queryBuilder.andWhere(`"${alias}".config_type = :configType`, {
        configType: filters.configType,
      });
    }

    // Note: enabled and active filters are handled in application layer
    // to avoid HSTORE parsing issues in the database
  }

  private transformHstoreFields<
    T extends
      | DefaultMetricConfigurationEntity
      | CustomMetricConfigurationEntity,
  >(config: T): T {
    // Transform active field: null/empty HSTORE -> false, otherwise check if contains 'true'
    if ('active' in config) {
      const activeValue = config.active as unknown;
      config.active =
        activeValue === null ||
        activeValue === '{}' ||
        (typeof activeValue === 'object' &&
          activeValue !== null &&
          Object.keys(activeValue).length === 0)
          ? false
          : typeof activeValue === 'object' &&
            activeValue !== null &&
            'true' in activeValue &&
            (activeValue as Record<string, unknown>).true === true;
    }

    // Transform enabled field: null/empty HSTORE -> true, otherwise check if contains 'true'
    if ('enabled' in config) {
      const enabledValue = config.enabled as unknown;
      config.enabled =
        enabledValue === null ||
        enabledValue === '{}' ||
        (typeof enabledValue === 'object' &&
          enabledValue !== null &&
          Object.keys(enabledValue).length === 0)
          ? true
          : typeof enabledValue === 'object' &&
            enabledValue !== null &&
            'true' in enabledValue &&
            (enabledValue as Record<string, unknown>).true === true;
    }

    return config;
  }

  async putMetricConfig(
    input: MetricConfigDetail,
    tenantId: string,
    facilityId: string,
  ): Promise<CustomMetricConfigSaveResult> {
    this.logger.debug('Updating or creating metric configuration', {
      metricConfigName: input.metricConfigName,
      configType: input.configType,
      facilityId,
    });

    const entityManager =
      this.dbProvider.processFlowPostgres.datasource.manager;
    const customRepo = entityManager.getRepository(
      CustomMetricConfigurationEntity,
    );

    // Check if a custom config already exists
    const existingConfig = await customRepo.findOne({
      where: {
        metricConfigName: input.metricConfigName,
        facilityId,
      },
    });

    let config: CustomMetricConfigurationEntity;
    let isNew = false;

    // Create the base config object
    const baseConfig = {
      factType: input.factType,
      sourceSystem: input.sourceSystem,
      displayName: input.displayName,
      description: input.description,
      acceptableLowRange: input.acceptableLowRange,
      acceptableHighRange: input.acceptableHighRange,
      exampleMessage: input.exampleMessage,
      configType: input.configType,
      views: input.views,
      matchConditions: input.matchConditions,
      redisParams: input.redisParams,
      label: input.label,
      parentNodes: input.parentNodes,
      active: input.active,
      enabled: input.enabled,
      graphOperation: input.graphOperation,
    };

    // Add config type specific fields
    const configWithSpecificFields = {
      ...baseConfig,
      ...this.getConfigTypeSpecificFields(input),
    };

    if (existingConfig) {
      // Update existing config
      this.logger.debug('Updating existing metric configuration', {
        id: existingConfig.id,
      });

      Object.assign(existingConfig, configWithSpecificFields);
      config = await customRepo.save(existingConfig);
    } else {
      // Create new config
      this.logger.debug('Creating new metric configuration');

      const newConfig = new CustomMetricConfigurationEntity();
      Object.assign(newConfig, {
        metricConfigName: input.metricConfigName,
        facilityId,
        ...configWithSpecificFields,
      });

      config = await customRepo.save(newConfig);
      isNew = true;
    }

    // save the last updated time for the fact type in redis
    // use fields from the facility map to create the key, same as graph caching
    const cacheKey = `${tenantId}:${facilityId}:${input.factType}:last_updated_timestamp`;
    const currentTimeSeconds = Date.now() / 1000;
    await this.redisClient.set(cacheKey, currentTimeSeconds.toString());

    this.logger.debug('Metric configuration saved successfully', {
      id: config.id,
      isNew,
    });

    return {config, isNew};
  }

  /**
   * Delete all metric keys associated with the given metric config
   * This is useful for when we update or delete a metric config and want to remove old keys
   * @param metricConfig
   * @returns the number of keys deleted
   */
  async deleteMetricConfigKeys(
    metricConfig: MetricConfigDetail,
    tenantId: string,
    facilityId: string,
  ): Promise<number> {
    switch (metricConfig.configType) {
      case 'node': {
        const keyPattern = getNodeMetricKeyPattern(
          metricConfig,
          tenantId,
          facilityId,
          this.logger,
        );
        this.logger.debug('Deleting node metric key pattern', {
          metricConfigName: metricConfig.metricConfigName,
          keyPattern,
        });

        // delete all keys matching the pattern
        return await this.redisClient.unlinkByPattern(keyPattern);
      }

      case 'inbound-edge': {
        const keyPattern = getInboundEdgeMetricKeyPattern(
          metricConfig,
          tenantId,
          facilityId,
        );
        this.logger.debug('Deleting inbound-edge metric key pattern', {
          metricConfigName: metricConfig.metricConfigName,
          keyPattern,
        });

        // delete all keys matching the pattern
        return await this.redisClient.unlinkByPattern(keyPattern);
      }

      case 'outbound-edge': {
        const keyPatterns = getOutboundEdgeMetricKeyPattern(
          metricConfig,
          tenantId,
          facilityId,
        );
        this.logger.debug('Deleting outbound-edge metric key patterns', {
          metricConfigName: metricConfig.metricConfigName,
          keyPatterns,
        });

        // delete all keys matching the pattern in parallel
        // keep track of the total number of keys deleted
        const deletePromises = keyPatterns.map(pattern =>
          this.redisClient.unlinkByPattern(pattern),
        );
        const deletedCounts = await Promise.all(deletePromises);
        const totalDeletedCount = deletedCounts.reduce(
          (sum, count) => sum + count,
          0,
        );
        return totalDeletedCount;
      }

      case 'complete-edge': {
        const keyPattern = getCompleteEdgeMetricKeyPattern(
          metricConfig,
          tenantId,
          facilityId,
        );
        this.logger.debug('Deleting complete-edge metric key patterns', {
          metricConfigName: metricConfig.metricConfigName,
          keyPattern,
        });

        // delete all keys matching the pattern
        return await this.redisClient.unlinkByPattern(keyPattern);
      }

      default:
        // this shouldn't ever happen
        return 0;
    }
  }

  private getConfigTypeSpecificFields(input: MetricConfigDetail) {
    switch (input.configType) {
      case 'node':
        return {
          nodeName: input.nodeName,
          metricType: input.metricType,
          timeWindow: input.timeWindow,
          aggregation: input.aggregation,
          redisOperation: input.redisOperation,
          metricUnits: input.metricUnits,
        };

      case 'inbound-edge':
        return {
          huId: input.huId,
          inboundArea: input.inboundArea,
          redisOperation: input.redisOperation,
          metricUnits: input.metricUnits,
          inboundParentNodes: input.inboundParentNodes,
        };

      case 'outbound-edge':
        return {
          outboundArea: input.outboundArea,
          huId: input.huId,
          units: input.units,
          outboundParentNodes: input.outboundParentNodes,
        };

      case 'complete-edge':
        return {
          inboundArea: input.inboundArea,
          outboundArea: input.outboundArea,
          redisOperation: input.redisOperation,
          outboundNodeLabel: input.outboundNodeLabel,
          inboundParentNodes: input.inboundParentNodes,
          outboundParentNodes: input.outboundParentNodes,
          metricUnits: input.metricUnits,
        };

      default:
        return {};
    }
  }

  /**
   * Gets a single metric configuration by name, returning both default and custom versions if they exist
   * @param metricName The name of the metric configuration to retrieve
   * @param facilityId The facility ID for custom configurations
   * @param configType Optional filter for 'default' or 'custom' configurations only
   * @returns Object containing default and/or custom configurations
   */
  async getMetricConfigDetail(
    metricName: string,
    facilityId: string,
    configType?: 'default' | 'custom',
  ): Promise<{
    default?: DefaultMetricConfigurationEntity;
    custom?: CustomMetricConfigurationEntity;
  }> {
    this.logger.debug('Getting metric configuration detail from database', {
      metricName,
      configType,
      facilityId,
    });

    const entityManager =
      this.dbProvider.processFlowPostgres.datasource.manager;
    const defaultRepo = entityManager.getRepository(
      DefaultMetricConfigurationEntity,
    );
    const customRepo = entityManager.getRepository(
      CustomMetricConfigurationEntity,
    );

    const result: {
      default?: DefaultMetricConfigurationEntity;
      custom?: CustomMetricConfigurationEntity;
    } = {};

    // Get default configuration if configType is not 'custom' (includes undefined/null and 'default')
    if (configType !== 'custom') {
      const defaultConfig = await defaultRepo.findOne({
        where: {metricConfigName: metricName},
      });

      if (defaultConfig) {
        result.default = this.transformHstoreFields(defaultConfig);
      }
    }

    // Get custom configuration if configType is not 'default' (includes undefined/null and 'custom')
    if (configType !== 'default') {
      const customConfig = await customRepo.findOne({
        where: {
          metricConfigName: metricName,
          facilityId,
        },
      });

      if (customConfig) {
        result.custom = this.transformHstoreFields(customConfig);
      }
    }

    this.logger.debug('Retrieved metric configuration detail from database', {
      metricName,
      hasDefault: !!result.default,
      hasCustom: !!result.custom,
    });

    return result;
  }
}
