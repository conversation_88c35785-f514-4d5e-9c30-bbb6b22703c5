import {expect} from 'chai';
import {
  Container,
  ContextService,
  HttpStatusCodes,
  SecurityRoles,
  WinstonLogger,
  appSetup,
} from 'ict-api-foundations';
import 'reflect-metadata';
import * as sinon from 'sinon';
import request from 'supertest';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {ConfigFacilityConfigController} from '../../controllers/config-facility-config-controller.ts';
import {ConfigSettingsService} from '../../services/config-settings-service.ts';

describe('ConfigFacilityConfigController', () => {
  let controller: ConfigFacilityConfigController;
  let configSettingsService: sinon.SinonStubbedInstance<ConfigSettingsService>;
  let contextService: ContextService;
  let logger: sinon.SinonStubbedInstance<WinstonLogger>;
  let selectedFacilityIdStub: sinon.SinonStub;
  let userRolesStub: sinon.SinonStub;
  let facilityMapsStub: sinon.SinonStub;
  const app = appSetup(RegisterRoutes);

  beforeEach(() => {
    configSettingsService = sinon.createStubInstance(ConfigSettingsService);
    logger = sinon.createStubInstance(WinstonLogger);

    Container.set(ConfigSettingsService, configSettingsService);
    Container.set(WinstonLogger, logger);

    // Get the actual context service from container and stub its properties
    contextService = Container.get(ContextService);
    selectedFacilityIdStub = sinon
      .stub(contextService, 'selectedFacilityId')
      .value('test-facility-id');
    userRolesStub = sinon.stub(contextService, 'userRoles').value([]);
    facilityMapsStub = sinon.stub(contextService, 'facilityMaps').value([]);

    controller = new ConfigFacilityConfigController();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getConfigurationSettings', () => {
    it('should return all settings when facility is selected', async () => {
      const mockSettings =
        ConfigFacilityConfigController.exampleRelevantSetting;
      configSettingsService.getAllSettings.resolves(mockSettings);

      const result = await controller.getConfigurationSettings();

      expect(result).to.deep.equal(mockSettings);
      expect(configSettingsService.getAllSettings.calledOnce).to.be.true;
    });

    it('should find non-default but available facility when no facility is selected', async () => {
      const mockSettings =
        ConfigFacilityConfigController.exampleRelevantSetting;

      // Override the stub values for this test
      selectedFacilityIdStub.value(null);
      userRolesStub.value(['facility-2']);
      facilityMapsStub.value([
        {
          id: 'facility-1',
          default: true,
          name: 'Test Facility',
          dataset: 'test-dataset',
        },
        {
          id: 'facility-2',
          default: false,
          name: 'Test Facility 2',
          dataset: 'test-dataset-2',
        },
      ]);

      // Mock getSingleSetting to return undefined (no user setting)
      configSettingsService.getSingleSetting.resolves(undefined);
      configSettingsService.getAllSettings.resolves(mockSettings);

      const result = await controller.getConfigurationSettings();

      expect(result).to.deep.equal(mockSettings);
      expect(configSettingsService.getAllSettings.calledOnce).to.be.true;
    });

    it('should not use default facility when user has no roles', async () => {
      const mockSettings =
        ConfigFacilityConfigController.exampleRelevantSetting;

      // Override the stub values for this test
      selectedFacilityIdStub.value(null);
      userRolesStub.value([]);
      facilityMapsStub.value([
        {
          id: 'facility-1',
          default: true,
          name: 'Test Facility',
          dataset: 'test-dataset',
        },
      ]);

      // Mock getSingleSetting to return undefined (no user setting)
      configSettingsService.getSingleSetting.resolves(undefined);
      configSettingsService.getAllSettings.resolves(mockSettings);

      await expect(controller.getConfigurationSettings()).to.be.rejected;
    });

    it('should use user selected facility when available', async () => {
      const mockSettings =
        ConfigFacilityConfigController.exampleRelevantSetting;

      // Override the stub values for this test
      selectedFacilityIdStub.value(null);
      userRolesStub.value(['user-selected-facility-id']);
      facilityMapsStub.value([
        {
          id: 'user-selected-facility-id',
          default: false,
          name: 'Test Facility',
          dataset: 'test-dataset',
        },
      ]);

      // Mock getSingleSetting to return user's selected facility
      configSettingsService.getSingleSetting.resolves({
        id: 'test-id',
        name: 'selected-facility-id',
        dataType: 'string',
        value: 'user-selected-facility-id',
      });
      configSettingsService.getAllSettings.resolves(mockSettings);

      const result = await controller.getConfigurationSettings();

      expect(result).to.deep.equal(mockSettings);
      expect(configSettingsService.getAllSettings.calledOnce).to.be.true;
    });

    it('should return all settings for internal users when no facility found', async () => {
      const mockSettings =
        ConfigFacilityConfigController.exampleRelevantSetting;

      // Override the stub values for this test
      selectedFacilityIdStub.value(null);
      userRolesStub.value([SecurityRoles.CT_CONFIGURATORS]);
      facilityMapsStub.value([]);

      // Mock getSingleSetting to return undefined (no user setting)
      configSettingsService.getSingleSetting.resolves(undefined);
      configSettingsService.getAllSettings.resolves(mockSettings);

      const result = await controller.getConfigurationSettings();

      expect(result).to.deep.equal(mockSettings);
      expect(configSettingsService.getAllSettings.calledOnce).to.be.true;
    });

    it('should throw not found error when no facility found for regular user', async () => {
      // Override the stub values for this test
      selectedFacilityIdStub.value(null);
      userRolesStub.value([]);
      facilityMapsStub.value([]);

      // Mock getSingleSetting to return undefined (no user setting)
      configSettingsService.getSingleSetting.resolves(undefined);

      await expect(controller.getConfigurationSettings()).to.be.rejectedWith(
        'User does not have access to any facilities.',
      );

      expect(configSettingsService.getAllSettings.called).to.be.false;
    });
  });

  describe('determineFacilityId', () => {
    let determineFacilityIdMethod: (
      service: ConfigSettingsService,
      context: ContextService,
    ) => Promise<string>;

    beforeEach(() => {
      // Access the private method for testing
      determineFacilityIdMethod = (controller as any).determineFacilityId.bind(
        controller,
      );
    });

    it('should throw forbidden error when user has no access to any facilities', async () => {
      // Setup: no user roles, empty facility maps
      userRolesStub.value([]);
      facilityMapsStub.value([
        {
          id: 'facility-1',
          default: true,
          name: 'Test Facility',
          dataset: 'test-dataset',
        },
      ]);

      await expect(
        determineFacilityIdMethod(configSettingsService, contextService),
      ).to.be.rejectedWith(
        'User does not have access to any facilities. Please contact your administrator to assign appropriate facility roles.',
      );
    });

    it('should return user selected facility when valid and accessible', async () => {
      // Setup: user has role for facility-2, selected facility-2
      userRolesStub.value(['facility-2']);
      facilityMapsStub.value([
        {
          id: 'facility-1',
          default: true,
          name: 'Test Facility 1',
          dataset: 'test-dataset-1',
        },
        {
          id: 'facility-2',
          default: false,
          name: 'Test Facility 2',
          dataset: 'test-dataset-2',
        },
      ]);
      configSettingsService.getSingleSetting.resolves({
        id: 'test-id',
        name: 'selected-facility-id',
        dataType: 'string',
        value: 'facility-2',
      });

      const result = await determineFacilityIdMethod(
        configSettingsService,
        contextService,
      );

      expect(result).to.equal('facility-2');
    });

    it('should not return user selected facility when user has no access to it', async () => {
      // Setup: user has role for facility-1 only, but selected facility-2
      userRolesStub.value(['facility-1']);
      facilityMapsStub.value([
        {
          id: 'facility-1',
          default: false,
          name: 'Test Facility 1',
          dataset: 'test-dataset-1',
        },
        {
          id: 'facility-2',
          default: true,
          name: 'Test Facility 2',
          dataset: 'test-dataset-2',
        },
      ]);
      configSettingsService.getSingleSetting.resolves({
        id: 'test-id',
        name: 'selected-facility-id',
        dataType: 'string',
        value: 'facility-2',
      });

      const result = await determineFacilityIdMethod(
        configSettingsService,
        contextService,
      );

      // Should fall back to the facility the user has access to
      expect(result).to.equal('facility-1');
    });

    it('should return default facility when no user selection and user has access', async () => {
      // Setup: user has role for both facilities, no user selection, facility-1 is default
      userRolesStub.value(['facility-1', 'facility-2']);
      facilityMapsStub.value([
        {
          id: 'facility-1',
          default: true,
          name: 'Test Facility 1',
          dataset: 'test-dataset-1',
        },
        {
          id: 'facility-2',
          default: false,
          name: 'Test Facility 2',
          dataset: 'test-dataset-2',
        },
      ]);
      configSettingsService.getSingleSetting.resolves(undefined);

      const result = await determineFacilityIdMethod(
        configSettingsService,
        contextService,
      );

      expect(result).to.equal('facility-1');
    });

    it('should not return default facility when user has no access to it', async () => {
      // Setup: user has role for facility-2 only, facility-1 is default
      userRolesStub.value(['facility-2']);
      facilityMapsStub.value([
        {
          id: 'facility-1',
          default: true,
          name: 'Test Facility 1',
          dataset: 'test-dataset-1',
        },
        {
          id: 'facility-2',
          default: false,
          name: 'Test Facility 2',
          dataset: 'test-dataset-2',
        },
      ]);
      configSettingsService.getSingleSetting.resolves(undefined);

      const result = await determineFacilityIdMethod(
        configSettingsService,
        contextService,
      );

      // Should return the first available facility (facility-2)
      expect(result).to.equal('facility-2');
    });

    it('should return first available facility when no default facility exists', async () => {
      // Setup: user has role for multiple facilities, no default facility
      userRolesStub.value(['facility-1', 'facility-3']);
      facilityMapsStub.value([
        {
          id: 'facility-1',
          default: false,
          name: 'Test Facility 1',
          dataset: 'test-dataset-1',
        },
        {
          id: 'facility-2',
          default: false,
          name: 'Test Facility 2',
          dataset: 'test-dataset-2',
        },
        {
          id: 'facility-3',
          default: false,
          name: 'Test Facility 3',
          dataset: 'test-dataset-3',
        },
      ]);
      configSettingsService.getSingleSetting.resolves(undefined);

      const result = await determineFacilityIdMethod(
        configSettingsService,
        contextService,
      );

      // Should return the first available facility
      expect(result).to.equal('facility-1');
    });

    it('should handle user selected facility from array response', async () => {
      // Setup: user has role for facility-2, getSingleSetting returns array
      userRolesStub.value(['facility-2']);
      facilityMapsStub.value([
        {
          id: 'facility-2',
          default: false,
          name: 'Test Facility 2',
          dataset: 'test-dataset-2',
        },
      ]);
      configSettingsService.getSingleSetting.resolves([
        {
          id: 'test-id',
          name: 'selected-facility-id',
          dataType: 'string',
          value: 'facility-2',
        },
      ]);

      const result = await determineFacilityIdMethod(
        configSettingsService,
        contextService,
      );

      expect(result).to.equal('facility-2');
    });

    it('should handle empty facility maps gracefully', async () => {
      // Setup: no facility maps available
      userRolesStub.value(['some-role']);
      facilityMapsStub.value([]);

      await expect(
        determineFacilityIdMethod(configSettingsService, contextService),
      ).to.be.rejectedWith(
        'User does not have access to any facilities. Please contact your administrator to assign appropriate facility roles.',
      );
    });

    it('should handle null user roles gracefully', async () => {
      // Setup: null user roles
      userRolesStub.value(null);
      facilityMapsStub.value([
        {
          id: 'facility-1',
          default: true,
          name: 'Test Facility',
          dataset: 'test-dataset',
        },
      ]);

      await expect(
        determineFacilityIdMethod(configSettingsService, contextService),
      ).to.be.rejectedWith(
        'User does not have access to any facilities. Please contact your administrator to assign appropriate facility roles.',
      );
    });
  });

  describe('GET /config/facility-config', () => {
    it('should return facility configuration settings', async () => {
      const mockSettings =
        ConfigFacilityConfigController.exampleRelevantSetting;
      configSettingsService.getAllSettings.resolves(mockSettings);

      const response = await request(app).get('/config/facility-config');

      expect(response.status).to.equal(HttpStatusCodes.OK);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(mockSettings);
    });

    it('should return 403 when no facility found for regular user', async () => {
      // Override the stub values for this test
      selectedFacilityIdStub.value(null);
      userRolesStub.value([]);
      facilityMapsStub.value([]);

      // Mock getSingleSetting to return undefined (no user setting)
      configSettingsService.getSingleSetting.resolves(undefined);

      const response = await request(app).get('/config/facility-config');

      expect(response.status).to.equal(HttpStatusCodes.FORBIDDEN);
      expect(response.body.detail).to.equal(
        'User does not have access to any facilities. Please contact your administrator to assign appropriate facility roles.',
      );
    });
  });
});
