import type {<PERSON><PERSON><PERSON><PERSON>} from 'ict-api-foundations';
import type {NameConfig} from 'ict-api-schema';
import type {MetricConfigDetail} from '../defs/metric-config-detail.ts';

/**
 * Utility functions for generating Redis key patterns from metric configurations.
 * These recreate the logic from metric_processor.py for use on the API side.
 * Since we don't have access to event data on the API side, dynamic values are replaced with wildcards.
 */

/**
 * Resolves a NameConfig to a pattern with wildcards for dynamic values.
 * This recreates the logic from metric_processor.py's resolve_dynamic_name_value() method.
 *
 * @param nameConfig The name configuration
 * @returns A pattern string with wildcards for dynamic values
 */
export function resolveNameFormulaToPattern(nameConfig: NameConfig): string {
  // Extract named groups from the regex pattern
  const namedGroupRegex = /\(\?P<(\w+)>/g;
  const namedGroups: string[] = [];
  let match = namedGroupRegex.exec(nameConfig.pattern);

  while (match !== null) {
    namedGroups.push(match[1]);
    match = namedGroupRegex.exec(nameConfig.pattern);
  }

  // Replace named group references in template with wildcards
  let templatePattern = nameConfig.template;
  namedGroups.forEach(groupName => {
    const groupPlaceholder = `{${groupName}}`;
    templatePattern = templatePattern.replace(
      new RegExp(groupPlaceholder.replace(/[{}]/g, '\\$&'), 'g'),
      '*',
    );
  });

  return templatePattern;
}

/**
 * Resolves a dynamic string template to a pattern with wildcards.
 * This recreates the logic from metric_processor.py's resolve_dynamic_string() method.
 *
 * @param template The string template which could be static or dynamic
 * @returns A pattern string with wildcards replacing dynamic placeholders
 */
export function resolveDynamicStringToPattern(template: string): string {
  // If template contains placeholders (e.g., "{aisle_code}"), replace with wildcards
  if (template.includes('{') && template.includes('}')) {
    // Replace all placeholder patterns like {field_name} with wildcards
    return template.replace(/\{[^}]+\}/g, '*');
  }

  // If no placeholders, return as is (static value)
  return template;
}

/**
 * Generates a Redis key pattern for a node metric configuration.
 * This recreates the logic from metric_processor.py's get_name() method.
 *
 * @param metricConfig The node metric configuration
 * @param tenantId The tenant ID
 * @param facilityId The facility ID
 * @param logger Optional logger for debugging
 * @returns A Redis key pattern with wildcards for dynamic values
 */
export function getNodeMetricKeyPattern(
  metricConfig: MetricConfigDetail & {configType: 'node'},
  tenantId: string,
  facilityId: string,
  logger?: WinstonLogger,
): string {
  // First try to use nameFormula if it exists
  let nodeName: string;
  if (metricConfig.nameFormula) {
    nodeName = resolveNameFormulaToPattern(metricConfig.nameFormula);
  } else if (metricConfig.nodeName) {
    // Fall back to nodeName if nameFormula doesn't exist
    nodeName = resolveDynamicStringToPattern(metricConfig.nodeName);
  } else {
    // If neither nameFormula nor nodeName exist, return empty string to prevent accidental deletions
    logger?.warn('Node metric config has neither nameFormula nor nodeName', {
      metricConfigName: metricConfig.metricConfigName,
    });
    return '';
  }

  // Use resolve_dynamic_string equivalent for metric type
  const metricType = resolveDynamicStringToPattern(metricConfig.metricType);

  // Format: {tenant}:{facility}:{node_name}:{metric_type}:{time_window}:{aggregation}
  return `${tenantId}:${facilityId}:${nodeName}:${metricType}:${metricConfig.timeWindow}:${metricConfig.aggregation}`;
}

/**
 * Generates Redis key patterns for an inbound-edge metric configuration.
 * This only includes the edge metric keys, not the handling unit keys generated by an outbound-edge metric configuration.
 * Metric format: {tenant}:{facility}:{outbound_area}:{inbound_area}:15m_set:hourly_rate
 *
 * @param metricConfig The inbound-edge metric configuration
 * @param tenantId The tenant ID
 * @param facilityId The facility ID
 * @returns A Redis key pattern with wildcards for dynamic values
 */
export function getInboundEdgeMetricKeyPattern(
  metricConfig: MetricConfigDetail & {configType: 'inbound-edge'},
  tenantId: string,
  facilityId: string,
): string {
  // Resolve outbound and inbound areas
  // For outbound area, we need to match against any outbound area
  // For inbound try nameFormula first, then fall back to inboundArea
  const outboundArea = '*';
  const inboundArea = metricConfig.nameFormula
    ? resolveNameFormulaToPattern(metricConfig.nameFormula)
    : resolveDynamicStringToPattern(metricConfig.inboundArea);

  // Generate metric key pattern
  return `${tenantId}:${facilityId}:${outboundArea}:${inboundArea}:15m_set:hourly_rate`;
}

/**
 * Generates Redis key patterns for an outbound-edge metric configuration.
 * This creates the handling unit keys used to track units leaving an area.
 * Format: {tenant}:{facility}:handling_unit:{hu_id}:edge:{view}
 *
 * @param metricConfig The outbound-edge metric configuration
 * @param tenantId The tenant ID
 * @param facilityId The facility ID
 * @returns An array of Redis key patterns with wildcards for dynamic values
 */
export function getOutboundEdgeMetricKeyPattern(
  metricConfig: MetricConfigDetail & {configType: 'outbound-edge'},
  tenantId: string,
  facilityId: string,
): string[] {
  // Outbound edges create handling unit keys, not direct metric keys
  // The hu_id is dynamic based on event data, so we use wildcard to clear all related keys
  // Format: {tenant}:{facility}:handling_unit:{hu_id}:edge:{view}
  const patterns: string[] = [];
  metricConfig.views.forEach(view => {
    patterns.push(`${tenantId}:${facilityId}:handling_unit:*:edge:${view}`);
  });
  return patterns;
}

/**
 * Generates a Redis key pattern for a complete-edge metric configuration.
 * Complete-edge metrics create direct metric keys without intermediate handling unit keys.
 * Metric format: {tenant}:{facility}:{outbound_area}:{inbound_area}:15m_set:hourly_rate
 *
 * @param metricConfig The complete-edge metric configuration
 * @param tenantId The tenant ID
 * @param facilityId The facility ID
 * @returns A Redis key pattern with wildcards for dynamic values
 */
export function getCompleteEdgeMetricKeyPattern(
  metricConfig: MetricConfigDetail & {configType: 'complete-edge'},
  tenantId: string,
  facilityId: string,
): string {
  // Resolve outbound and inbound areas
  // For inbound try nameFormula first, then fall back to inboundArea
  const outboundArea = resolveDynamicStringToPattern(metricConfig.outboundArea);
  const inboundArea = metricConfig.nameFormula
    ? resolveNameFormulaToPattern(metricConfig.nameFormula)
    : resolveDynamicStringToPattern(metricConfig.inboundArea);

  // Generate metric key pattern
  return `${tenantId}:${facilityId}:${outboundArea}:${inboundArea}:15m_set:hourly_rate`;
}
