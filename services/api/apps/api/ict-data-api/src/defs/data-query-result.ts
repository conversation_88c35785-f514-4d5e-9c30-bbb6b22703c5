export type DataQueryResultType =
  | 'singleValue'
  | 'categorySeries'
  | 'timeSeries'
  | 'tabular'
  | 'filter';

export interface DataQueryField {
  name: string;
  type: 'string' | 'number' | 'timestamp' | 'boolean';
  label?: string;
  unit?: string;
}

export interface DataQueryValidation {
  isValid: boolean;
  reason?: string;
}

export interface DataQueryResult {
  type: DataQueryResultType;
  fields: DataQueryField[];
  rows: Record<string, unknown>[];
  validation: DataQueryValidation;
  metadata: Record<string, unknown>;
  queryRequest?: string;
}
