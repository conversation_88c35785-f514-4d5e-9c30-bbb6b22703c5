import {BigQuery} from '@google-cloud/bigquery';
import {
  ConfigStore,
  ContextService,
  DiService,
  EnvironmentService,
  IctError,
  WinstonLogger,
} from 'ict-api-foundations';
import {DataQuery, DataQueryProperty} from 'ict-api-schema';
import {
  type DataQueryField,
  type DataQueryResult,
  type DataQueryResultType,
  type DataQueryValidation,
} from '../defs/index.ts';
import {BigQueryErrorUtils} from '../utils/bigquery-error-utils.js';
import {BigQueryUtils} from '../utils/bigquery-utils.js';
import {
  HandlebarsQueryProcessor,
  type TemplateContext,
} from '../utils/handlebars-query-processor.js';
import {QueryParameterUtils} from '../utils/query-parameter-utils.js';
import {ValidationUtils} from '../utils/validation-utils.js';

/**
 * Service responsible for executing data queries with BigQuery.
 * Handles SQL template processing, config parameter injection, and query execution.
 */
@DiService()
export class DataQueryService {
  private readonly queryProcessor: HandlebarsQueryProcessor;

  constructor(
    private readonly logger: WinstonLogger,
    private readonly contextService: ContextService,
    private readonly envService: EnvironmentService,
    private readonly configStore: ConfigStore,
  ) {
    this.queryProcessor = new HandlebarsQueryProcessor(logger);
  }

  /**
   * Execute a data query and return results with data, value, and metadata
   */
  async executeDataQuery(
    dataQuery: DataQuery,
    queryParams: Record<string, unknown> = {},
  ): Promise<DataQueryResult> {
    this.logger.info('Executing data query', {
      queryId: dataQuery.id,
      queryLabel: dataQuery.label,
    });

    try {
      // Build both BigQuery parameters and template context
      const {parameters, templateContext} =
        await this.buildQueryParametersAndContext(dataQuery, queryParams);

      // Process SQL template with Handlebars
      const processedSql = this.processQueryTemplate(
        dataQuery,
        templateContext,
      );

      const dryRun = QueryParameterUtils.extractDryRunFlag(queryParams);

      // Execute the BigQuery query
      const queryResult = await this.executeQuery(
        processedSql,
        parameters,
        dataQuery,
        dryRun,
      );

      // Process and format the results
      const result = this.formatQueryResults(queryResult, dataQuery);

      // Add the full query request for user reference
      result.queryRequest = BigQueryUtils.generateFullQueryRequest(
        processedSql,
        parameters,
      );
      return result;
    } catch (error) {
      this.logger.error(`Failed to execute data query ${dataQuery.id}`, {
        error,
      });

      // If it's already an IctError, re-throw it
      if (error instanceof IctError) {
        throw error;
      }

      // For other unexpected errors, create a generic IctError
      const ictError = IctError.internalServerError(
        `Failed to execute data query ${dataQuery.id}`,
        error,
      );
      ictError.errorType = 'queryExecutionError';
      throw ictError;
    }
  }

  /**
   * Process SQL query template using Handlebars with full template context
   */
  private processQueryTemplate(
    dataQuery: DataQuery,
    templateContext: TemplateContext,
  ): string {
    return this.queryProcessor.processQuery(dataQuery, templateContext);
  }

  /**
   * Build full BigQuery table path for a data source
   */
  private buildTablePath(dataSource: {
    id: string;
    type: string;
    table: string;
  }): string {
    // For BigQuery data sources, construct the full table path
    if (dataSource.type === 'edp-bigquery') {
      const projectId = this.envService.edpBigQueryDb.projectId;
      const dataset = this.contextService.datasetId;
      return `${projectId}.${dataset}.${dataSource.table}`;
    }

    // For other data source types, could add support later
    const ictError = IctError.badRequest(
      `Unsupported data source type: ${dataSource.type} for data source ${dataSource.id}`,
    );
    ictError.errorType = 'unsupportedDataSource';
    throw ictError;
  }

  /**
   * Build a single flat context object used by both BigQuery and Handlebars
   */
  private async buildQueryParametersAndContext(
    dataQuery: DataQuery,
    queryParams: Record<string, unknown>,
  ): Promise<{
    parameters: Record<string, unknown>;
    templateContext: TemplateContext;
  }> {
    // Create a single flat context object for both BigQuery and Handlebars
    const context: Record<string, unknown> = {};

    // Validate required parameters first
    QueryParameterUtils.validateRequiredParameters(dataQuery, queryParams);

    // Add all query parameters with sanitized names
    Object.entries(queryParams).forEach(([key, value]) => {
      const sanitizedKey = QueryParameterUtils.sanitizeParameterName(key);
      context[sanitizedKey] = value;
    });

    // Process and add filter parameters
    if (dataQuery.filters) {
      for (const filterName of dataQuery.filters) {
        const filterKey = filterName.replace(/-/g, '_');
        const filterValue = queryParams[filterKey];

        if (
          filterValue !== undefined &&
          filterValue !== null &&
          filterValue !== ''
        ) {
          // Convert to string and split on pipe delimiter
          const filterString = String(filterValue);
          const filterArray = filterString
            .split('|')
            .map(item => item.trim())
            .filter(item => item.length > 0);

          if (filterArray.length > 0) {
            const sanitizedFilterName =
              QueryParameterUtils.sanitizeParameterName(filterName);
            context[sanitizedFilterName] = filterArray;
          }
        }
      }
    }

    // Add property parameters if specified
    if (dataQuery.queryProperties && dataQuery.queryProperties.length > 0) {
      this.addPropertyParametersToContext(
        context,
        dataQuery.queryProperties,
        queryParams,
      );
    }

    // Add config parameters if specified
    if (dataQuery.config && dataQuery.config.length > 0) {
      await this.addConfigParametersToContext(context, dataQuery.config);
    }

    // Add table paths directly to context
    for (const dataSource of dataQuery.dataSources) {
      try {
        const tablePath = this.buildTablePath(dataSource);
        context[dataSource.id] = tablePath;
      } catch (error) {
        this.logger.error('Failed to build table path for data source', {
          dataSourceId: dataSource.id,
          error: error instanceof Error ? error.message : String(error),
        });
        throw error;
      }
    }

    this.logger.debug('Built single flat context for BigQuery and Handlebars', {
      queryId: dataQuery.id,
      contextKeys: Object.keys(context),
      contextSize: Object.keys(context).length,
    });

    // Use the same context for both BigQuery parameters and Handlebars templates
    return {parameters: context, templateContext: context};
  }

  /**
   * Add config parameters directly to the flat context
   */
  private async addConfigParametersToContext(
    context: Record<string, unknown>,
    configKeys: string[],
  ): Promise<void> {
    const userId = this.contextService.userId;

    // Fetch all configs in parallel
    const configPromises = configKeys.map(async configKey => {
      try {
        const setting = await this.configStore.findMostRelevantSettingForUser(
          userId,
          undefined,
          configKey,
        );

        if (setting?.value) {
          let configValue = setting.value;

          // Try to parse as JSON if it's a string
          if (typeof setting.value === 'string') {
            try {
              configValue = JSON.parse(setting.value);
            } catch {
              // Use raw value if JSON parsing fails
              configValue = setting.value;
            }
          }

          // BigQuery does not support "-" in config keys, so we need to replace them with "_"
          const sanitizedKey =
            QueryParameterUtils.sanitizeParameterName(configKey);
          return {
            key: sanitizedKey,
            value: configValue,
          };
        }

        this.logger.warn(`No config value found for key: ${configKey}`);
        return null;
      } catch (error) {
        this.logger.warn(`Failed to fetch config for key: ${configKey}`, {
          error,
        });
        return null;
      }
    });

    const configResults = await Promise.all(configPromises);

    // Add valid config results directly to the flat context
    configResults.forEach(result => {
      if (result) {
        // Add config value with sanitized key
        // eslint-disable-next-line no-param-reassign
        context[result.key] = result.value;
      }
    });
  }

  /**
   * Add property parameters directly to the flat context
   */
  private addPropertyParametersToContext(
    context: Record<string, unknown>,
    properties: DataQueryProperty[],
    queryParams: Record<string, unknown>,
  ): void {
    properties.forEach(property => {
      try {
        // Look for property value in query parameters (with underscore sanitization)
        const sanitizedPropertyId = QueryParameterUtils.sanitizeParameterName(
          property.id,
        );
        const propertyValue =
          queryParams[sanitizedPropertyId] ?? property.defaultValue;

        QueryParameterUtils.validateQueryProperty(
          property as DataQueryProperty,
          propertyValue as string,
        );

        // Create a temporary property object with the override value for SQL generation
        const tempProperty = {
          ...property,
          defaultValue: propertyValue,
        };

        const sqlCode = String(tempProperty.defaultValue);

        const sanitizedKey = QueryParameterUtils.sanitizeParameterName(
          property.id,
        );
        context[sanitizedKey] = sqlCode;

        this.logger.debug('Added property parameter to context', {
          propertyId: property.id,
          propertyType: property.type,
          defaultValue: property.defaultValue,
          overrideValue: queryParams[sanitizedPropertyId],
          finalValue: propertyValue,
          sanitizedKey,
          sqlCode,
        });
      } catch (error) {
        this.logger.error('Failed to build SQL for property', {
          propertyId: property.id,
          propertyType: property.type,
          error: error instanceof Error ? error.message : String(error),
        });
        throw error;
      }
    });
  }

  /**
   * Parse BigQuery errors and create appropriate IctError with error type
   */
  private createBigQueryError(error: unknown, queryId: string): IctError {
    return BigQueryErrorUtils.createBigQueryError(error, queryId);
  }

  /**
   * Execute the query using BigQuery - routes to dry run or regular execution
   */
  private async executeQuery(
    sql: string,
    parameters: Record<string, unknown>,
    dataQuery: DataQuery,
    dryRun?: boolean,
  ): Promise<{rows: unknown[]; metadata: Record<string, unknown>}> {
    const executionStartTime = Date.now();

    if (dryRun) {
      return await this.executeDryRun(
        sql,
        parameters,
        dataQuery,
        executionStartTime,
      );
    }
    return await this.executeRegularQuery(
      sql,
      parameters,
      dataQuery,
      executionStartTime,
    );
  }

  /**
   * Execute a dry run validation of the query
   */
  private async executeDryRun(
    sql: string,
    parameters: Record<string, unknown>,
    dataQuery: DataQuery,
    executionStartTime: number,
  ): Promise<{rows: unknown[]; metadata: Record<string, unknown>}> {
    this.logger.debug('Executing BigQuery dry run', {
      queryId: dataQuery.id,
      queryLength: sql.length,
      parameterCount: Object.keys(parameters).length,
    });

    try {
      const job = await this.createBigQueryJob(
        sql,
        parameters,
        dataQuery,
        true,
      );
      const metadata = await this.collectJobMetadata(
        job,
        dataQuery,
        executionStartTime,
        true,
        0,
      );

      this.logger.info('BigQuery dry run validation passed', {
        queryId: dataQuery.id,
        jobId: metadata.jobId,
      });

      return {
        rows: [],
        metadata,
      };
    } catch (error) {
      this.logger.error('BigQuery dry run failed', {
        queryId: dataQuery.id,
        error,
      });
      throw this.createBigQueryError(error, dataQuery.id);
    }
  }

  /**
   * Execute a regular query and return results
   */
  private async executeRegularQuery(
    sql: string,
    parameters: Record<string, unknown>,
    dataQuery: DataQuery,
    executionStartTime: number,
  ): Promise<{rows: unknown[]; metadata: Record<string, unknown>}> {
    this.logger.debug('Executing BigQuery query', {
      queryId: dataQuery.id,
      queryLength: sql.length,
      parameterCount: Object.keys(parameters).length,
      dataset: this.contextService.datasetId,
    });

    try {
      const job = await this.createBigQueryJob(
        sql,
        parameters,
        dataQuery,
        false,
      );
      const [rows] = await job.getQueryResults();
      const metadata = await this.collectJobMetadata(
        job,
        dataQuery,
        executionStartTime,
        false,
        rows?.length || 0,
      );

      this.logger.info('BigQuery job completed successfully', {
        queryId: dataQuery.id,
        jobId: metadata.jobId,
        executionDurationMs: metadata.executionDurationMs,
        totalBytesBilled: metadata.totalBytesBilled,
        estimatedCostUsd: metadata.estimatedCostUsd,
        rowCount: metadata.rowCount,
        cacheHit: metadata.cacheHit,
      });

      return {
        rows: rows || [],
        metadata,
      };
    } catch (error) {
      this.logger.error('BigQuery execution failed', {
        queryId: dataQuery.id,
        error,
      });
      throw this.createBigQueryError(error, dataQuery.id);
    }
  }

  /**
   * Create a BigQuery job with common configuration
   */
  private async createBigQueryJob(
    sql: string,
    parameters: Record<string, unknown>,
    dataQuery: DataQuery,
    dryRun: boolean,
  ) {
    const bigQueryClient = new BigQuery({
      projectId: this.envService.edpBigQueryDb.projectId,
    });

    const sqlOptions = BigQueryUtils.createJobOptions(
      sql,
      parameters,
      {
        application: 'control-tower-data-query',
        tenant: this.contextService.datasetId,
        user: this.contextService.userId || 'unknown',
        dataqueryid: dataQuery.id,
      },
      dryRun,
    );

    const [job] = await bigQueryClient.createQueryJob(sqlOptions);
    return job;
  }

  /**
   * Collect job metadata for both dry run and regular execution
   */
  private async collectJobMetadata(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    job: any,
    dataQuery: DataQuery,
    executionStartTime: number,
    dryRun: boolean,
    rowCount: number,
  ): Promise<Record<string, unknown>> {
    const clientExecutionTime = Date.now() - executionStartTime;

    let jobMetadata: Record<string, unknown> = {
      rowCount,
      executedAt: new Date().toISOString(),
      clientExecutionTimeMs: clientExecutionTime,
      dryRun,
    };

    try {
      const [metadata] = await job.getMetadata();
      const stats = metadata.statistics;

      if (stats) {
        // Common metadata for both dry run and regular execution
        jobMetadata = {
          ...jobMetadata,
          jobId: metadata.jobReference?.jobId,
          jobCreationTime: stats?.creationTime,
          jobStartTime: stats?.startTime,
          jobEndTime: stats?.endTime,
        };

        if (dryRun) {
          jobMetadata.validationStatus = 'passed';
        } else {
          // Add execution-specific statistics
          jobMetadata = {
            ...jobMetadata,
            ...BigQueryUtils.extractExecutionStats(stats),
          };
        }
      }
    } catch (error) {
      this.logger.warn('Failed to capture detailed job metadata', {
        queryId: dataQuery.id,
        error,
      });
    }

    return jobMetadata;
  }

  /**
   * Format query results into the expected response format with validation
   */
  private formatQueryResults(
    queryResult: {rows: unknown[]; metadata: Record<string, unknown>},
    dataQuery: DataQuery,
  ): DataQueryResult {
    const {rows, metadata} = queryResult;
    const rawRowObjects = rows as Record<string, unknown>[];

    // Flatten BigQuery nested field values (e.g., timestamp: {value: "..."} -> timestamp: "...")
    const flattenedRows = rawRowObjects.map(row =>
      BigQueryUtils.flattenBigQueryRow(row),
    );

    // Map DataQuery types to DataQueryResult types
    const typeMapping: Record<string, DataQueryResultType> = {
      singleValue: 'singleValue',
      categorySeries: 'categorySeries',
      timeSeries: 'timeSeries',
      tabular: 'tabular',
    };

    const resultType = typeMapping[dataQuery.type];

    // Generate fields and validate based on type
    const {fields, validation} = this.generateFieldsAndValidate(
      flattenedRows,
      resultType,
      dataQuery,
    );

    return {
      type: resultType,
      fields,
      rows: flattenedRows,
      validation,
      metadata: {...metadata, ...dataQuery.metadata},
    };
  }

  /**
   * Generate field definitions and validate data structure matches expected type
   */
  private generateFieldsAndValidate(
    rows: Record<string, unknown>[],
    type: DataQueryResultType,
    dataQuery: DataQuery,
  ): {fields: DataQueryField[]; validation: DataQueryValidation} {
    if (rows.length === 0) {
      return {
        fields: [],
        validation: {isValid: true},
      };
    }

    const sampleRow = rows[0];
    const fieldNames = Object.keys(sampleRow);
    const fields: DataQueryField[] = [];

    // Generate fields based on first row
    for (const fieldName of fieldNames) {
      const sampleValue = sampleRow[fieldName];
      const fieldType = ValidationUtils.inferFieldType(sampleValue);

      fields.push({
        name: fieldName,
        type: fieldType,
        label: fieldName,
        unit: dataQuery.metadata?.unit,
      });
    }

    // Validate based on expected type
    const validation = ValidationUtils.validateDataStructure(
      rows,
      fields,
      type,
    );

    return {fields, validation};
  }
}
