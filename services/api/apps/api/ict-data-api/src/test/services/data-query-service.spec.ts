import {expect} from 'chai';
import sinon from 'sinon';
import {BigQuery} from '@google-cloud/bigquery';
import {
  WinstonLogger,
  ContextService,
  EnvironmentService,
  ConfigStore,
  IctError,
  Container,
} from 'ict-api-foundations';
import {DataQuery, DataQueryType} from 'ict-api-schema';
import {DataQueryService} from '../../services/data-query-service.js';
import {QueryParameterUtils} from '../../utils/query-parameter-utils.js';

describe('DataQueryService', () => {
  let service: DataQueryService;
  let mockLogger: sinon.SinonStubbedInstance<WinstonLogger>;
  let mockContextService: sinon.SinonStubbedInstance<ContextService>;
  let mockEnvService: sinon.SinonStubbedInstance<EnvironmentService>;
  let mockConfigStore: sinon.SinonStubbedInstance<ConfigStore>;
  let mockJob: {
    getQueryResults: sinon.SinonStub;
    getMetadata: sinon.SinonStub;
  };

  const mockDataQuery: DataQuery = {
    id: 'test-query-123',
    label: 'Test Query',
    description: 'Test description',
    query: 'SELECT value FROM {{edp}} WHERE date >= @start_date',
    type: 'singleValue',
    dataSources: [
      {
        id: 'edp',
        type: 'edp-bigquery',
        table: 'test_table',
      },
    ],
    parameters: {
      required: ['start_date'],
    },
    metadata: {
      unit: 'count',
      category: 'test',
    },
  } as DataQuery;

  beforeEach(() => {
    // Create mocks
    mockLogger = {
      info: sinon.stub(),
      debug: sinon.stub(),
      warn: sinon.stub(),
      error: sinon.stub(),
    } as sinon.SinonStubbedInstance<WinstonLogger>;

    mockContextService = {
      userId: 'test-user',
      datasetId: 'test-dataset',
    } as sinon.SinonStubbedInstance<ContextService>;

    mockEnvService = {
      edpBigQueryDb: {
        projectId: 'test-project',
      },
    } as sinon.SinonStubbedInstance<EnvironmentService>;

    mockConfigStore = {
      findMostRelevantSettingForUser: sinon.stub(),
    } as sinon.SinonStubbedInstance<ConfigStore>;

    // Mock BigQuery job
    mockJob = {
      getQueryResults: sinon.stub(),
      getMetadata: sinon.stub(),
    };

    // Mock BigQuery client
    const mockBigQueryClient = {
      createQueryJob: sinon.stub().resolves([mockJob]),
    };

    sinon
      .stub(BigQuery.prototype, 'createQueryJob')
      .callsFake(mockBigQueryClient.createQueryJob);

    // Set up container
    Container.set(WinstonLogger, mockLogger);
    Container.set(ContextService, mockContextService);
    Container.set(EnvironmentService, mockEnvService);
    Container.set(ConfigStore, mockConfigStore);

    service = new DataQueryService(
      mockLogger,
      mockContextService,
      mockEnvService,
      mockConfigStore,
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('executeDataQuery', () => {
    const queryParams = {
      start_date: '2023-01-01',
    };

    beforeEach(() => {
      // Mock successful query execution - single field for singleValue type
      mockJob.getQueryResults.resolves([[{value: 42}]]);
      mockJob.getMetadata.resolves([
        {
          jobReference: {jobId: 'test-job-123'},
          statistics: {
            creationTime: '2023-01-01T10:00:00Z',
            startTime: '2023-01-01T10:00:01Z',
            endTime: '2023-01-01T10:00:02Z',
            finalExecutionDurationMs: '1000',
            totalSlotMs: '2000',
            query: {
              totalBytesBilled: '1024',
              totalBytesProcessed: '2048',
              cacheHit: false,
            },
          },
        },
      ]);
    });

    it('should execute data query successfully', async () => {
      const result = await service.executeDataQuery(mockDataQuery, queryParams);

      expect(result).to.have.property('type');
      expect(result).to.have.property('fields');
      expect(result).to.have.property('rows');
      expect(result).to.have.property('validation');
      expect(result).to.have.property('metadata');
      expect(result).to.have.property('queryRequest');
      expect(result.type).to.equal('singleValue');
      expect(result.rows).to.have.length(1);
      expect(result.validation.isValid).to.be.true;
    });

    it('should handle dry run queries', async () => {
      const dryRunParams = {
        ...queryParams,
        dryRun: 'true',
      };

      const result = await service.executeDataQuery(
        mockDataQuery,
        dryRunParams,
      );

      expect(result.rows).to.have.length(0);
      expect(result.metadata).to.have.property('dryRun', true);
      expect(result.metadata).to.have.property('validationStatus', 'passed');
    });

    it('should process SQL template correctly', async () => {
      await service.executeDataQuery(mockDataQuery, queryParams);

      // Verify that the SQL was processed to replace placeholders
      const expectedSql =
        'SELECT value FROM test-project.test-dataset.test_table WHERE date >= @start_date';
      sinon.assert.calledOnce(
        BigQuery.prototype.createQueryJob as sinon.SinonStub,
      );
      const callArgs = (
        BigQuery.prototype.createQueryJob as sinon.SinonStub
      ).getCall(0).args[0];
      expect(callArgs.query).to.equal(expectedSql);
    });

    it('should validate required parameters', async () => {
      const invalidParams = {}; // Missing required start_date

      try {
        await service.executeDataQuery(mockDataQuery, invalidParams);
        expect.fail('Should have thrown validation error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).errorType).to.equal(
          'parameterValidationError',
        );
      }
    });

    it('should handle BigQuery errors correctly', async () => {
      const bigQueryError = new Error('Syntax error: Unexpected token');
      mockJob.getQueryResults.rejects(bigQueryError);

      try {
        await service.executeDataQuery(mockDataQuery, queryParams);
        expect.fail('Should have thrown BigQuery error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).errorType).to.equal('syntaxError');
      }
    });

    it('should handle unsupported data source types', async () => {
      const unsupportedDataQuery = {
        ...mockDataQuery,
        dataSources: [
          {
            id: 'unsupported',
            type: 'unsupported-type',
            table: 'test_table',
          },
        ],
      };

      try {
        await service.executeDataQuery(unsupportedDataQuery, queryParams);
        expect.fail('Should have thrown unsupported data source error');
      } catch (error) {
        expect(error).to.be.instanceOf(IctError);
        expect((error as IctError).errorType).to.equal('unsupportedDataSource');
      }
    });

    it('should process config parameters when specified', async () => {
      const dataQueryWithConfig = {
        ...mockDataQuery,
        config: ['test-config-key'],
      };

      mockConfigStore.findMostRelevantSettingForUser.resolves({
        id: 'test-config-key',
        name: 'test-config-key',
        dataType: 'string',
        value: 'test-config-value',
      });

      await service.executeDataQuery(dataQueryWithConfig, queryParams);

      sinon.assert.calledOnce(mockConfigStore.findMostRelevantSettingForUser);
      sinon.assert.calledWith(
        mockConfigStore.findMostRelevantSettingForUser,
        'test-user',
        undefined,
        'test-config-key',
      );
    });

    it('should handle missing config values gracefully', async () => {
      const dataQueryWithConfig = {
        ...mockDataQuery,
        config: ['missing-config-key'],
      };

      mockConfigStore.findMostRelevantSettingForUser.resolves(undefined);

      const result = await service.executeDataQuery(
        dataQueryWithConfig,
        queryParams,
      );

      expect(result).to.have.property('rows');
      sinon.assert.calledOnce(mockLogger.warn);
    });

    it('should format results correctly for different query types', async () => {
      // Test singleValue type
      const singleValueResult = await service.executeDataQuery(
        mockDataQuery,
        queryParams,
      );
      expect(singleValueResult.type).to.equal('singleValue');
      expect(singleValueResult.rows).to.have.length(1);
      expect(singleValueResult.validation.isValid).to.be.true;

      // Test timeSeries type
      const timeSeriesQuery = {
        ...mockDataQuery,
        type: DataQueryType.TIME_SERIES,
      };

      mockJob.getQueryResults.resolves([
        [
          {timestamp: '2023-01-01', value: 42},
          {timestamp: '2023-01-02', value: 43},
        ],
      ]);

      const timeSeriesResult = await service.executeDataQuery(
        timeSeriesQuery,
        queryParams,
      );
      expect(timeSeriesResult.type).to.equal('timeSeries');
      expect(timeSeriesResult.rows).to.have.length(2);
    });

    it('should handle empty result sets', async () => {
      mockJob.getQueryResults.resolves([[]]);

      const result = await service.executeDataQuery(mockDataQuery, queryParams);

      expect(result.rows).to.have.length(0);
      expect(result.fields).to.have.length(0);
      expect(result.validation.isValid).to.be.true;
    });

    it('should include query request in response', async () => {
      const result = await service.executeDataQuery(mockDataQuery, queryParams);

      expect(result.queryRequest).to.be.a('string');
      expect(result.queryRequest).to.include('DECLARE start_date');
      expect(result.queryRequest).to.include(
        'test-project.test-dataset.test_table',
      );
    });

    it('should flatten BigQuery nested {value: ...} objects', async () => {
      // Mock BigQuery response with nested timestamp object
      const nestedBigQueryResponse = [
        [
          {
            timestamp: {value: '2023-01-01T00:00:00.000Z'},
            count: 42,
            status: 'active',
          },
        ],
      ];

      mockJob.getQueryResults.resolves(nestedBigQueryResponse);

      const result = await service.executeDataQuery(mockDataQuery, queryParams);

      // Verify the nested timestamp object is flattened
      expect(result.rows).to.have.length(1);
      expect(result.rows[0]).to.have.property(
        'timestamp',
        '2023-01-01T00:00:00.000Z',
      );
      expect(result.rows[0]).to.have.property('count', 42);
      expect(result.rows[0]).to.have.property('status', 'active');

      // Ensure it's a flat string, not an object
      expect(result.rows[0].timestamp).to.be.a('string');
      expect(result.rows[0].timestamp).to.not.be.an('object');
    });

    it('should sanitize job labels correctly', async () => {
      await service.executeDataQuery(mockDataQuery, queryParams);

      const createJobCall = (
        BigQuery.prototype.createQueryJob as sinon.SinonStub
      ).getCall(0);
      const jobOptions = createJobCall.args[0];

      expect(jobOptions.labels).to.have.property(
        'application',
        'control-tower-data-query',
      );
      expect(jobOptions.labels).to.have.property('tenant', 'test-dataset');
      expect(jobOptions.labels).to.have.property('user', 'test-user');
      expect(jobOptions.labels).to.have.property(
        'dataqueryid',
        'test-query-123',
      );
    });
  });

  describe('error handling', () => {
    it('should re-throw IctError instances', async () => {
      const originalError = IctError.badRequest('Test error');
      originalError.errorType = 'testError';

      sinon
        .stub(QueryParameterUtils, 'validateRequiredParameters')
        .throws(originalError);

      try {
        await service.executeDataQuery(mockDataQuery, {});
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error).to.equal(originalError);
        expect((error as IctError).errorType).to.equal('testError');
      }
    });
  });

  describe('query properties functionality', () => {
    const mockDataQueryWithProperties: DataQuery = {
      ...mockDataQuery,
      queryProperties: [
        {
          id: 'time_period',
          type: 'time_bucket',
          defaultValue: 'DAY',
        },
        {
          id: 'sort_direction',
          type: 'sort',
          defaultValue: 'ASC',
        },
        {
          id: 'result_limit',
          type: 'limit',
          defaultValue: 100,
        },
        {
          id: 'group_field',
          type: 'group_by',
          defaultValue: 'category',
          groupableFields: ['category', 'location', 'department'],
        },
      ],
    };

    beforeEach(() => {
      mockJob.getQueryResults.resolves([[{value: 42}]]);
      mockJob.getMetadata.resolves([
        {
          jobReference: {jobId: 'test-job-123'},
          statistics: {
            creationTime: '2023-01-01T10:00:00Z',
            startTime: '2023-01-01T10:00:01Z',
            endTime: '2023-01-01T10:00:02Z',
          },
        },
      ]);
    });

    it('should use default property values when no overrides provided', async () => {
      const queryParams = {
        start_date: '2023-01-01',
      };

      await service.executeDataQuery(mockDataQueryWithProperties, queryParams);

      // Verify debug logs show default values were used (includes handlebars and other system debug calls)
      sinon.assert.callCount(mockLogger.debug, 9);

      // Check that debug was called with property information
      const debugCalls = mockLogger.debug.getCalls();
      const propertyDebugCalls = debugCalls.filter(
        call => call.args[0] === 'Added property parameter to context',
      );

      expect(propertyDebugCalls).to.have.length(4);

      // Verify default values were used
      expect(propertyDebugCalls[0].args[1]).to.include({
        propertyId: 'time_period',
        finalValue: 'DAY',
        overrideValue: undefined,
      });
      expect(propertyDebugCalls[1].args[1]).to.include({
        propertyId: 'sort_direction',
        finalValue: 'ASC',
        overrideValue: undefined,
      });
    });

    it('should use override values from query parameters', async () => {
      const queryParams = {
        start_date: '2023-01-01',
        time_period: 'MONTH',
        sort_direction: 'DESC',
        result_limit: '500',
        group_field: 'location',
      };

      await service.executeDataQuery(mockDataQueryWithProperties, queryParams);

      // Check that debug was called with override information
      const debugCalls = mockLogger.debug.getCalls();
      const propertyDebugCalls = debugCalls.filter(
        call => call.args[0] === 'Added property parameter to context',
      );

      expect(propertyDebugCalls).to.have.length(4);

      // Verify override values were used
      expect(propertyDebugCalls[0].args[1]).to.include({
        propertyId: 'time_period',
        finalValue: 'MONTH',
        overrideValue: 'MONTH',
      });
      expect(propertyDebugCalls[1].args[1]).to.include({
        propertyId: 'sort_direction',
        finalValue: 'DESC',
        overrideValue: 'DESC',
      });
      expect(propertyDebugCalls[2].args[1]).to.include({
        propertyId: 'result_limit',
        finalValue: '500',
        overrideValue: '500',
      });
      expect(propertyDebugCalls[3].args[1]).to.include({
        propertyId: 'group_field',
        finalValue: 'location',
        overrideValue: 'location',
      });
    });

    it('should handle mixed default and override values', async () => {
      const queryParams = {
        start_date: '2023-01-01',
        time_period: 'WEEK', // override
        // sort_direction not provided - should use default 'ASC'
        result_limit: '250', // override
        // group_field not provided - should use default 'category'
      };

      await service.executeDataQuery(mockDataQueryWithProperties, queryParams);

      const debugCalls = mockLogger.debug.getCalls();
      const propertyDebugCalls = debugCalls.filter(
        call => call.args[0] === 'Added property parameter to context',
      );

      // Verify mix of override and default values
      expect(propertyDebugCalls[0].args[1]).to.include({
        propertyId: 'time_period',
        finalValue: 'WEEK', // override
        overrideValue: 'WEEK',
      });
      expect(propertyDebugCalls[1].args[1]).to.include({
        propertyId: 'sort_direction',
        finalValue: 'ASC', // default
        overrideValue: undefined,
      });
      expect(propertyDebugCalls[2].args[1]).to.include({
        propertyId: 'result_limit',
        finalValue: '250', // override
        overrideValue: '250',
      });
      expect(propertyDebugCalls[3].args[1]).to.include({
        propertyId: 'group_field',
        finalValue: 'category', // default
        overrideValue: undefined,
      });
    });

    it('should handle properties with dashes in IDs (sanitization)', async () => {
      const dataQueryWithDashedProperty: DataQuery = {
        ...mockDataQuery,
        queryProperties: [
          {
            id: 'time-bucket-granularity',
            type: 'time_bucket',
            defaultValue: 'HOUR',
          },
        ],
      };

      const queryParams = {
        start_date: '2023-01-01',
        time_bucket_granularity: 'MINUTE', // underscore in param name
      };

      await service.executeDataQuery(dataQueryWithDashedProperty, queryParams);

      const debugCalls = mockLogger.debug.getCalls();
      const propertyDebugCalls = debugCalls.filter(
        call => call.args[0] === 'Added property parameter to context',
      );

      expect(propertyDebugCalls).to.have.length(1);
      expect(propertyDebugCalls[0].args[1]).to.include({
        propertyId: 'time-bucket-granularity',
        finalValue: 'MINUTE', // override found via sanitized key
        overrideValue: 'MINUTE',
        sanitizedKey: 'time_bucket_granularity',
      });
    });

    it('should generate correct SQL for each property type', async () => {
      const queryParams = {
        start_date: '2023-01-01',
        time_period: 'YEAR',
        sort_direction: 'DESC',
        result_limit: '1000',
        group_field: 'department',
      };

      await service.executeDataQuery(mockDataQueryWithProperties, queryParams);

      const debugCalls = mockLogger.debug.getCalls();
      const propertyDebugCalls = debugCalls.filter(
        call => call.args[0] === 'Added property parameter to context',
      );

      // Verify SQL code generation for each type
      expect(propertyDebugCalls[0].args[1]).to.include({
        propertyType: 'time_bucket',
        sqlCode: 'YEAR',
      });
      expect(propertyDebugCalls[1].args[1]).to.include({
        propertyType: 'sort',
        sqlCode: 'DESC',
      });
      expect(propertyDebugCalls[2].args[1]).to.include({
        propertyType: 'limit',
        sqlCode: '1000',
      });
      expect(propertyDebugCalls[3].args[1]).to.include({
        propertyType: 'group_by',
        sqlCode: 'department',
      });
    });

    it('should handle empty queryProperties array', async () => {
      const dataQueryWithEmptyProperties: DataQuery = {
        ...mockDataQuery,
        queryProperties: [],
      };

      const queryParams = {
        start_date: '2023-01-01',
      };

      await service.executeDataQuery(dataQueryWithEmptyProperties, queryParams);

      // Should not call property-related debug logs
      const debugCalls = mockLogger.debug.getCalls();
      const propertyDebugCalls = debugCalls.filter(
        call => call.args[0] === 'Added property parameter to context',
      );

      expect(propertyDebugCalls).to.have.length(0);
    });

    it('should handle missing queryProperties field', async () => {
      const dataQueryWithoutProperties: DataQuery = {
        ...mockDataQuery,
        // queryProperties field not defined
      };

      const queryParams = {
        start_date: '2023-01-01',
      };

      await service.executeDataQuery(dataQueryWithoutProperties, queryParams);

      // Should not call property-related debug logs
      const debugCalls = mockLogger.debug.getCalls();
      const propertyDebugCalls = debugCalls.filter(
        call => call.args[0] === 'Added property parameter to context',
      );

      expect(propertyDebugCalls).to.have.length(0);
    });
  });
});
