import {expect} from 'chai';
import {BigQueryUtils} from '../../utils/bigquery-utils.js';

describe('BigQueryUtils', () => {
  describe('generateFullQueryRequest', () => {
    it('should generate query with DECLARE statements for parameters', () => {
      const sql =
        'SELECT * FROM table WHERE date = @start_date AND id = @user_id';
      const parameters = {
        start_date: '2023-01-01',
        user_id: 123,
      };

      const result = BigQueryUtils.generateFullQueryRequest(sql, parameters);

      expect(result).to.include('DECLARE start_date TIMESTAMP DEFAULT');
      expect(result).to.include('DECLARE user_id NUMERIC DEFAULT 123');
      expect(result).to.include(
        'SELECT * FROM table WHERE date = start_date AND id = user_id',
      );
      expect(result).not.to.include('@');
    });

    it('should handle empty parameters', () => {
      const sql = 'SELECT * FROM table';
      const parameters = {};

      const result = BigQueryUtils.generateFullQueryRequest(sql, parameters);

      expect(result).to.equal('SELECT * FROM table');
    });

    it('should skip dryRun parameter', () => {
      const sql = 'SELECT * FROM table WHERE id = @user_id';
      const parameters = {
        user_id: 123,
        dryRun: true,
      };

      const result = BigQueryUtils.generateFullQueryRequest(sql, parameters);

      expect(result).to.include('DECLARE user_id NUMERIC DEFAULT 123');
      expect(result).not.to.include('dryRun');
      expect(result).not.to.include('DECLARE dryRun');
    });

    it('should handle different data types', () => {
      const sql = 'SELECT * WHERE str = @str_param AND bool = @bool_param';
      const parameters = {
        str_param: 'test',
        bool_param: true,
      };

      const result = BigQueryUtils.generateFullQueryRequest(sql, parameters);

      expect(result).to.include('DECLARE str_param STRING DEFAULT "test"');
      expect(result).to.include('DECLARE bool_param BOOL DEFAULT TRUE');
    });
  });

  describe('sanitizeLabelValue', () => {
    it('should sanitize label values correctly', () => {
      const result = BigQueryUtils.sanitizeLabelValue('Test-User@123');

      expect(result).to.equal('test-user_123');
    });

    it('should limit length to 63 characters', () => {
      const longString = 'a'.repeat(100);
      const result = BigQueryUtils.sanitizeLabelValue(longString);

      expect(result.length).to.be.lessThanOrEqual(63);
      expect(result).to.match(/^[a-z]/);
    });

    it('should remove trailing underscores and dashes', () => {
      const result = BigQueryUtils.sanitizeLabelValue('test-value___');

      expect(result).to.equal('test-value');
      expect(result).not.to.match(/[_-]$/);
    });
  });

  describe('createJobOptions', () => {
    it('should create job options with sanitized labels', () => {
      const sql = 'SELECT * FROM table';
      const parameters = {user_id: 123};
      const labels = {
        'user-id': 'Test@User',
      };

      const result = BigQueryUtils.createJobOptions(sql, parameters, labels);

      expect(result.query).to.equal(sql);
      expect(result.params).to.deep.equal(parameters);
      expect(result.dryRun).to.be.false;
      expect(result.labels).to.have.property('user-id');
      expect(result.labels!['user-id']).to.equal('test_user');
    });

    it('should handle dryRun parameter', () => {
      const sql = 'SELECT * FROM table';
      const parameters = {};
      const labels = {};

      const result = BigQueryUtils.createJobOptions(
        sql,
        parameters,
        labels,
        true,
      );

      expect(result.dryRun).to.be.true;
    });
  });

  describe('extractExecutionStats', () => {
    it('should extract basic execution stats', () => {
      const mockStats = {
        finalExecutionDurationMs: '1500',
        totalSlotMs: '2000',
        query: {
          totalBytesBilled: '1024',
          totalBytesProcessed: '2048',
          cacheHit: true,
        },
      };

      const result = BigQueryUtils.extractExecutionStats(mockStats);

      expect(result.executionDurationMs).to.equal(1500);
      expect(result.totalSlotMs).to.equal(2000);
      expect(result.totalBytesBilled).to.equal(1024);
      expect(result.totalBytesProcessed).to.equal(2048);
      expect(result.cacheHit).to.be.true;
    });

    it('should handle missing stats gracefully', () => {
      const mockStats = {};

      const result = BigQueryUtils.extractExecutionStats(mockStats);

      expect(result.executionDurationMs).to.be.null;
      expect(result.totalSlotMs).to.be.null;
    });

    it('should handle stats without query section', () => {
      const mockStats = {
        finalExecutionDurationMs: '1000',
        totalSlotMs: '1500',
      };

      const result = BigQueryUtils.extractExecutionStats(mockStats);

      expect(result.executionDurationMs).to.equal(1000);
      expect(result.totalSlotMs).to.equal(1500);
      expect(result.totalBytesBilled).to.be.undefined;
      expect(result.cacheHit).to.be.undefined;
    });
  });

  describe('flattenBigQueryRow', () => {
    it('should flatten nested {value: ...} objects', () => {
      const row = {
        id: {value: 123},
        name: {value: 'test'},
        timestamp: {value: '2023-01-01T12:00:00Z'},
        regularField: 'normal value',
      };

      const result = BigQueryUtils.flattenBigQueryRow(row);

      expect(result).to.deep.equal({
        id: 123,
        name: 'test',
        timestamp: '2023-01-01T12:00:00Z',
        regularField: 'normal value',
      });
    });

    it('should keep non-nested values unchanged', () => {
      const row = {
        id: 123,
        name: 'test',
        isActive: true,
        arrayField: [1, 2, 3],
      };

      const result = BigQueryUtils.flattenBigQueryRow(row);

      expect(result).to.deep.equal(row);
    });

    it('should handle objects with multiple properties', () => {
      const row = {
        nestedSingle: {value: 'extract this'},
        nestedMultiple: {value: 'keep this', otherProp: 'also keep'},
        simple: 'unchanged',
      };

      const result = BigQueryUtils.flattenBigQueryRow(row);

      expect(result).to.deep.equal({
        nestedSingle: 'extract this',
        nestedMultiple: {value: 'keep this', otherProp: 'also keep'},
        simple: 'unchanged',
      });
    });

    it('should handle null and undefined values', () => {
      const row = {
        nullValue: null,
        undefinedValue: undefined,
        nestedNull: {value: null},
      };

      const result = BigQueryUtils.flattenBigQueryRow(row);

      expect(result).to.deep.equal({
        nullValue: null,
        undefinedValue: undefined,
        nestedNull: null,
      });
    });
  });
});
