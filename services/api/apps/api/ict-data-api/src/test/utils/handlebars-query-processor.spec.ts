import {expect} from 'chai';
import sinon from 'sinon';
import {<PERSON><PERSON><PERSON><PERSON>} from 'ict-api-foundations';
import {DataQuery} from 'ict-api-schema';
import {HandlebarsQueryProcessor} from '../../utils/handlebars-query-processor.js';

describe('HandlebarsQueryProcessor', () => {
  let processor: HandlebarsQueryProcessor;
  let mockLogger: sinon.SinonStubbedInstance<WinstonLogger>;

  beforeEach(() => {
    mockLogger = sinon.createStubInstance(WinstonLogger);
    processor = new HandlebarsQueryProcessor(mockLogger);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('processQuery', () => {
    it('should process a simple Handlebars template', () => {
      const dataQuery: DataQuery = {
        id: 'test-query-1',
        query: 'SELECT * FROM {{tableName}} WHERE date = @date',
        label: 'Test Query',
        description: 'Test',
        type: 'singleValue',
        dataSources: [],
        filters: [],
        isDraft: false,
        metadata: {category: 'test'},
        parameters: {required: []},
      } as DataQuery;

      const context = {
        tableName: 'my_table',
        date: '2023-01-01',
      };

      const result = processor.processQuery(dataQuery, context);

      expect(result).to.equal('SELECT * FROM my_table WHERE date = @date');
    });

    it('should handle handlebars-helpers functions', () => {
      const dataQuery: DataQuery = {
        id: 'test-query-2',
        query: 'SELECT {{uppercase name}} FROM table',
        label: 'Test Query',
        description: 'Test',
        type: 'singleValue',
        dataSources: [],
        filters: [],
        isDraft: false,
        metadata: {category: 'test'},
        parameters: {required: []},
      } as DataQuery;

      const context = {
        name: 'test_column',
      };

      const result = processor.processQuery(dataQuery, context);

      expect(result).to.equal('SELECT TEST_COLUMN FROM table');
    });
  });

  describe('caching behavior', () => {
    it('should cache compiled templates based on query ID and SQL template hash', () => {
      const dataQuery: DataQuery = {
        id: 'cache-test-query',
        query: 'SELECT * FROM {{table}}',
        label: 'Cache Test',
        description: 'Test',
        type: 'singleValue',
        dataSources: [],
        filters: [],
        isDraft: false,
        metadata: {category: 'test'},
        parameters: {required: []},
      } as DataQuery;

      const context = {table: 'test_table'};

      // First call - should compile and cache
      const result1 = processor.processQuery(dataQuery, context);
      expect(result1).to.equal('SELECT * FROM test_table');
      expect(processor.getCacheSize()).to.equal(1);

      // Second call with same query - should use cache
      const result2 = processor.processQuery(dataQuery, context);
      expect(result2).to.equal('SELECT * FROM test_table');
      expect(processor.getCacheSize()).to.equal(1);
    });

    it('should create new cache entry when SQL template changes', () => {
      const baseQuery: DataQuery = {
        id: 'cache-test-query-2',
        query: 'SELECT * FROM {{table}}',
        label: 'Cache Test',
        description: 'Test',
        type: 'singleValue',
        dataSources: [],
        filters: [],
        isDraft: false,
        metadata: {category: 'test'},
        parameters: {required: []},
      } as DataQuery;

      const context = {table: 'test_table'};

      // First call with original query
      const result1 = processor.processQuery(baseQuery, context);
      expect(result1).to.equal('SELECT * FROM test_table');
      expect(processor.getCacheSize()).to.equal(1);

      // Modify the SQL template but keep same ID
      const modifiedQuery: DataQuery = {
        ...baseQuery,
        query: 'SELECT {{column}} FROM {{table}}',
      };

      const modifiedContext = {table: 'test_table', column: 'id'};

      // Second call with modified query - should create new cache entry
      const result2 = processor.processQuery(modifiedQuery, modifiedContext);
      expect(result2).to.equal('SELECT id FROM test_table');
      expect(processor.getCacheSize()).to.equal(2); // Both templates should be cached
    });

    it('should clear cache for specific query ID', () => {
      const query1: DataQuery = {
        id: 'query-1',
        query: 'SELECT * FROM table1',
        label: 'Query 1',
        description: 'Test',
        type: 'singleValue',
        dataSources: [],
        filters: [],
        isDraft: false,
        metadata: {category: 'test'},
        parameters: {required: []},
      } as DataQuery;

      const query2: DataQuery = {
        id: 'query-2',
        query: 'SELECT * FROM table2',
        label: 'Query 2',
        description: 'Test',
        type: 'singleValue',
        dataSources: [],
        filters: [],
        isDraft: false,
        metadata: {category: 'test'},
        parameters: {required: []},
      } as DataQuery;

      // Process both queries to cache them
      processor.processQuery(query1, {});
      processor.processQuery(query2, {});
      expect(processor.getCacheSize()).to.equal(2);

      // Clear cache for query-1 only
      processor.clearCacheForQuery('query-1');
      expect(processor.getCacheSize()).to.equal(1);

      // Verify query-2 is still cached by processing it again
      processor.processQuery(query2, {});
      expect(processor.getCacheSize()).to.equal(1);
    });

    it('should clear entire cache', () => {
      const dataQuery: DataQuery = {
        id: 'test-clear-all',
        query: 'SELECT * FROM {{table}}',
        label: 'Test',
        description: 'Test',
        type: 'singleValue',
        dataSources: [],
        filters: [],
        isDraft: false,
        metadata: {category: 'test'},
        parameters: {required: []},
      } as DataQuery;

      processor.processQuery(dataQuery, {table: 'test'});
      expect(processor.getCacheSize()).to.equal(1);

      processor.clearCache();
      expect(processor.getCacheSize()).to.equal(0);
    });
  });

  describe('buildTablePathsContext', () => {
    it('should build table paths for data sources', () => {
      const dataQuery: DataQuery = {
        id: 'table-path-test',
        query: 'SELECT * FROM {{source1}} JOIN {{source2}}',
        label: 'Table Path Test',
        description: 'Test',
        type: 'singleValue',
        dataSources: [
          {id: 'source1', type: 'bigquery', table: 'table_one'},
          {id: 'source2', type: 'bigquery', table: 'table_two'},
        ],
        filters: [],
        isDraft: false,
        metadata: {category: 'test'},
        parameters: {required: []},
      } as DataQuery;

      const buildTablePath = (dataSource: {
        id: string;
        type: string;
        table: string;
      }) => `project.dataset.${dataSource.table}`;

      const result = processor.buildTablePathsContext(
        dataQuery,
        buildTablePath,
      );

      expect(result).to.deep.equal({
        source1: 'project.dataset.table_one',
        source2: 'project.dataset.table_two',
      });
    });

    it('should handle empty data sources', () => {
      const dataQuery: DataQuery = {
        id: 'empty-sources-test',
        query: 'SELECT 1',
        label: 'Empty Sources Test',
        description: 'Test',
        type: 'singleValue',
        dataSources: [],
        filters: [],
        isDraft: false,
        metadata: {category: 'test'},
        parameters: {required: []},
      } as DataQuery;

      const buildTablePath = (dataSource: {
        id: string;
        type: string;
        table: string;
      }) => `project.dataset.${dataSource.table}`;

      const result = processor.buildTablePathsContext(
        dataQuery,
        buildTablePath,
      );

      expect(result).to.deep.equal({});
    });
  });

  describe('error handling', () => {
    it('should throw IctError for invalid Handlebars syntax', () => {
      const dataQuery: DataQuery = {
        id: 'invalid-syntax-test',
        query: 'SELECT * FROM {{unclosed',
        label: 'Invalid Syntax Test',
        description: 'Test',
        type: 'singleValue',
        dataSources: [],
        filters: [],
        isDraft: false,
        metadata: {category: 'test'},
        parameters: {required: []},
      } as DataQuery;

      expect(() => processor.processQuery(dataQuery, {})).to.throw(
        /Failed to process query template/,
      );
    });

    it('should handle context processing errors', () => {
      const dataQuery: DataQuery = {
        id: 'context-error-test',
        query: 'SELECT * FROM {{table.nonexistent.property}}',
        label: 'Context Error Test',
        description: 'Test',
        type: 'singleValue',
        dataSources: [],
        filters: [],
        isDraft: false,
        metadata: {category: 'test'},
        parameters: {required: []},
      } as DataQuery;

      // This should not throw - Handlebars handles missing properties gracefully
      const result = processor.processQuery(dataQuery, {table: {}});
      expect(result).to.equal('SELECT * FROM ');
    });
  });
});
