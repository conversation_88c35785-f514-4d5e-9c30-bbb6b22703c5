import {expect} from 'chai';
import {type DataQueryField} from '../../defs/index.ts';
import {ValidationUtils} from '../../utils/validation-utils.js';

describe('ValidationUtils', () => {
  describe('inferFieldType', () => {
    it('should infer number type', () => {
      const result = ValidationUtils.inferFieldType(123);
      expect(result).to.equal('number');
    });

    it('should infer boolean type', () => {
      const result = ValidationUtils.inferFieldType(true);
      expect(result).to.equal('boolean');
    });

    it('should infer timestamp type from ISO string', () => {
      const result = ValidationUtils.inferFieldType('2023-01-01T12:00:00Z');
      expect(result).to.equal('timestamp');
    });

    it('should infer timestamp type from date string', () => {
      const result = ValidationUtils.inferFieldType('2023-01-01');
      expect(result).to.equal('timestamp');
    });

    it('should infer string type for regular strings', () => {
      const result = ValidationUtils.inferFieldType('hello world');
      expect(result).to.equal('string');
    });

    it('should infer string type for non-timestamp strings', () => {
      const result = ValidationUtils.inferFieldType('not-a-timestamp');
      expect(result).to.equal('string');
    });

    it('should infer string type for null/undefined', () => {
      expect(ValidationUtils.inferFieldType(null)).to.equal('string');
      expect(ValidationUtils.inferFieldType(undefined)).to.equal('string');
    });
  });

  describe('validateDataStructure', () => {
    const createMockFields = (
      fields: Array<{name: string; type: string}>,
    ): DataQueryField[] =>
      fields.map(f => ({
        name: f.name,
        type: f.type as 'string' | 'number' | 'timestamp' | 'boolean',
        label: f.name,
      }));

    describe('singleValue validation', () => {
      it('should pass for single row and single field', () => {
        const rows = [{value: 123}];
        const fields = createMockFields([{name: 'value', type: 'number'}]);

        const result = ValidationUtils.validateDataStructure(
          rows,
          fields,
          'singleValue',
        );

        expect(result.isValid).to.be.true;
      });

      it('should fail for multiple rows', () => {
        const rows = [{value: 123}, {value: 456}];
        const fields = createMockFields([{name: 'value', type: 'number'}]);

        const result = ValidationUtils.validateDataStructure(
          rows,
          fields,
          'singleValue',
        );

        expect(result.isValid).to.be.false;
        expect(result.reason).to.include('2 rows, expected exactly 1');
      });

      it('should fail for multiple fields', () => {
        const rows = [{value: 123, name: 'test'}];
        const fields = createMockFields([
          {name: 'value', type: 'number'},
          {name: 'name', type: 'string'},
        ]);

        const result = ValidationUtils.validateDataStructure(
          rows,
          fields,
          'singleValue',
        );

        expect(result.isValid).to.be.false;
        expect(result.reason).to.include('2 fields, expected exactly 1');
      });
    });

    describe('timeSeries validation', () => {
      it('should pass for timestamp and value fields', () => {
        const rows = [
          {timestamp: '2023-01-01', value: 100},
          {timestamp: '2023-01-02', value: 200},
        ];
        const fields = createMockFields([
          {name: 'timestamp', type: 'timestamp'},
          {name: 'value', type: 'number'},
        ]);

        const result = ValidationUtils.validateDataStructure(
          rows,
          fields,
          'timeSeries',
        );

        expect(result.isValid).to.be.true;
      });

      it('should fail without timestamp field', () => {
        const rows = [{date: '2023-01-01', value: 100}];
        const fields = createMockFields([
          {name: 'date', type: 'string'},
          {name: 'value', type: 'number'},
        ]);

        const result = ValidationUtils.validateDataStructure(
          rows,
          fields,
          'timeSeries',
        );

        expect(result.isValid).to.be.false;
        expect(result.reason).to.include('must have a timestamp field');
      });

      it('should fail with only one field', () => {
        const rows = [{timestamp: '2023-01-01'}];
        const fields = createMockFields([
          {name: 'timestamp', type: 'timestamp'},
        ]);

        const result = ValidationUtils.validateDataStructure(
          rows,
          fields,
          'timeSeries',
        );

        expect(result.isValid).to.be.false;
        expect(result.reason).to.include('at least 2 fields');
      });
    });

    describe('categorySeries validation', () => {
      it('should pass for category and value fields only', () => {
        const rows = [
          {category: 'A', value: 100},
          {category: 'B', value: 200},
        ];
        const fields = createMockFields([
          {name: 'category', type: 'string'},
          {name: 'value', type: 'number'},
        ]);

        const result = ValidationUtils.validateDataStructure(
          rows,
          fields,
          'categorySeries',
        );

        expect(result.isValid).to.be.true;
      });

      it('should fail without category field', () => {
        const rows = [{name: 'A', value: 100}];
        const fields = createMockFields([
          {name: 'name', type: 'string'},
          {name: 'value', type: 'number'},
        ]);

        const result = ValidationUtils.validateDataStructure(
          rows,
          fields,
          'categorySeries',
        );

        expect(result.isValid).to.be.false;
        expect(result.reason).to.include('one column named "category"');
      });

      it('should fail without value field', () => {
        const rows = [{category: 'A', count: 100}];
        const fields = createMockFields([
          {name: 'category', type: 'string'},
          {name: 'count', type: 'number'},
        ]);

        const result = ValidationUtils.validateDataStructure(
          rows,
          fields,
          'categorySeries',
        );

        expect(result.isValid).to.be.false;
        expect(result.reason).to.include('one column named "value"');
      });

      it('should fail with more than 2 fields', () => {
        const rows = [{category: 'A', value: 100, extra: 'data'}];
        const fields = createMockFields([
          {name: 'category', type: 'string'},
          {name: 'value', type: 'number'},
          {name: 'extra', type: 'string'},
        ]);

        const result = ValidationUtils.validateDataStructure(
          rows,
          fields,
          'categorySeries',
        );

        expect(result.isValid).to.be.false;
        expect(result.reason).to.include('exactly 2 columns');
      });

      it('should fail with less than 2 fields', () => {
        const rows = [{category: 'A'}];
        const fields = createMockFields([{name: 'category', type: 'string'}]);

        const result = ValidationUtils.validateDataStructure(
          rows,
          fields,
          'categorySeries',
        );

        expect(result.isValid).to.be.false;
        expect(result.reason).to.include('one column named "value"');
      });
    });

    describe('tabular validation', () => {
      it('should always pass for tabular type', () => {
        const rows = [{a: 1, b: 2, c: 3}];
        const fields = createMockFields([
          {name: 'a', type: 'number'},
          {name: 'b', type: 'number'},
          {name: 'c', type: 'number'},
        ]);

        const result = ValidationUtils.validateDataStructure(
          rows,
          fields,
          'tabular',
        );

        expect(result.isValid).to.be.true;
      });
    });
  });
});
