import {Query} from '@google-cloud/bigquery';

/**
 * Utility class for BigQuery-specific operations
 */
export class BigQueryUtils {
  /**
   * Generate the full BigQuery request with DECLARE statements for parameters
   * This allows users to copy/paste the query into BigQuery for testing
   */
  static generateFullQueryRequest(
    sql: string,
    parameters: Record<string, unknown>,
  ): string {
    const declareStatements: string[] = [];

    // Generate DECLARE statements for each parameter (excluding dryRun)
    Object.entries(parameters).forEach(([key, value]) => {
      // Skip dryRun parameter as it's not needed in the query
      if (key === 'dryRun') {
        return;
      }

      const declareStatement = this.generateDeclareStatement(key, value);
      declareStatements.push(declareStatement);
    });

    // Remove @ symbols from parameter references in SQL
    const cleanedSql = sql.replace(/@(\w+)/g, '$1');

    // Combine DECLARE statements with the cleaned SQL query
    if (declareStatements.length > 0) {
      return `${declareStatements.join('\n')}\n\n${cleanedSql}`;
    }
    return cleanedSql;
  }

  /**
   * Generate a DECLARE statement for a specific parameter
   */
  private static generateDeclareStatement(key: string, value: unknown): string {
    if (Array.isArray(value)) {
      return this.generateArrayDeclareStatement(key, value);
    }

    // Special handling for date parameters
    if (
      (key === 'start_date' || key === 'end_date') &&
      typeof value === 'string'
    ) {
      // Assume the date string is in a format that can be converted to timestamp
      const escapedValue = value.replace(/"/g, '\\"');
      return `DECLARE ${key} TIMESTAMP DEFAULT TIMESTAMP('${escapedValue}');`;
    }

    if (typeof value === 'string') {
      const escapedValue = value.replace(/"/g, '\\"');
      return `DECLARE ${key} STRING DEFAULT "${escapedValue}";`;
    }

    if (typeof value === 'number') {
      return `DECLARE ${key} NUMERIC DEFAULT ${value};`;
    }

    if (typeof value === 'boolean') {
      return `DECLARE ${key} BOOL DEFAULT ${value ? 'TRUE' : 'FALSE'};`;
    }

    if (value === null || value === undefined) {
      return `DECLARE ${key} STRING DEFAULT NULL;`;
    }

    // For complex objects, serialize as JSON string
    const jsonValue = JSON.stringify(value).replace(/"/g, '\\"');
    return `DECLARE ${key} STRING DEFAULT "${jsonValue}";`;
  }

  /**
   * Generate DECLARE statement for array parameters
   */
  private static generateArrayDeclareStatement(
    key: string,
    value: unknown[],
  ): string {
    if (value.length === 0) {
      return `DECLARE ${key} ARRAY<STRING> DEFAULT [];`;
    }

    const firstElement = value[0];

    if (typeof firstElement === 'string') {
      const arrayValues = value
        .map(v => `"${String(v).replace(/"/g, '\\"')}"`)
        .join(', ');
      return `DECLARE ${key} ARRAY<STRING> DEFAULT [${arrayValues}];`;
    }

    if (typeof firstElement === 'number') {
      const arrayValues = value.join(', ');
      return `DECLARE ${key} ARRAY<NUMERIC> DEFAULT [${arrayValues}];`;
    }

    if (typeof firstElement === 'boolean') {
      const arrayValues = value.map(v => (v ? 'TRUE' : 'FALSE')).join(', ');
      return `DECLARE ${key} ARRAY<BOOL> DEFAULT [${arrayValues}];`;
    }

    // Fallback to string array for other types
    const arrayValues = value
      .map(v => `"${String(v).replace(/"/g, '\\"')}"`)
      .join(', ');
    return `DECLARE ${key} ARRAY<STRING> DEFAULT [${arrayValues}];`;
  }

  /**
   * Sanitize a string to be used as a BigQuery label value
   * BigQuery labels must contain only lowercase letters, numbers, underscores, and dashes
   * Must start with a lowercase letter and be 63 characters or less
   */
  static sanitizeLabelValue(value: string): string {
    if (!value) return 'unknown';

    // Convert to lowercase and replace invalid characters with underscores
    let sanitized = value
      .toLowerCase()
      .replace(/[^a-z0-9_-]/g, '_')
      .replace(/^[^a-z]+/, '') // Remove leading non-letters
      .substring(0, 63); // Limit to 63 characters

    // Ensure it starts with a letter
    if (!sanitized || !/^[a-z]/.test(sanitized)) {
      sanitized = `user_${sanitized}`;
    }

    // Remove trailing underscores/dashes and ensure it's not empty
    sanitized = sanitized.replace(/[_-]+$/, '') || 'unknown';

    return sanitized;
  }

  /**
   * Create BigQuery job options with common configuration
   */
  static createJobOptions(
    sql: string,
    parameters: Record<string, unknown>,
    labels: Record<string, string>,
    dryRun: boolean = false,
  ): Query {
    return {
      query: sql,
      params: parameters,
      dryRun,
      labels: Object.fromEntries(
        Object.entries(labels).map(([key, value]) => [
          key,
          this.sanitizeLabelValue(value),
        ]),
      ),
    };
  }

  /**
   * Extract execution statistics from BigQuery job stats
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static extractExecutionStats(stats: any): Record<string, unknown> {
    const executionStats: Record<string, unknown> = {
      executionDurationMs: stats.finalExecutionDurationMs
        ? parseInt(stats.finalExecutionDurationMs, 10)
        : null,
      totalSlotMs: stats.totalSlotMs ? parseInt(stats.totalSlotMs, 10) : null,
    };

    if (stats.query) {
      const queryStats = stats.query;

      Object.assign(executionStats, {
        totalBytesBilled: queryStats.totalBytesBilled
          ? parseInt(queryStats.totalBytesBilled, 10)
          : null,
        totalBytesProcessed: queryStats.totalBytesProcessed
          ? parseInt(queryStats.totalBytesProcessed, 10)
          : null,
        cacheHit: queryStats.cacheHit || false,
      });
    }

    return executionStats;
  }

  /**
   * Flatten BigQuery row data that may have nested {value: ...} objects
   */
  static flattenBigQueryRow(
    row: Record<string, unknown>,
  ): Record<string, unknown> {
    const flattenedRow: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(row)) {
      // Check if the value is an object with a single "value" property
      if (
        value &&
        typeof value === 'object' &&
        !Array.isArray(value) &&
        'value' in value &&
        Object.keys(value).length === 1
      ) {
        // Extract the nested value
        flattenedRow[key] = (value as {value: unknown}).value;
      } else {
        // Keep the original value
        flattenedRow[key] = value;
      }
    }

    return flattenedRow;
  }
}
