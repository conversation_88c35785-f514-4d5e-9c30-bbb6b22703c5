import Handlebars from 'handlebars';
import helpers from 'handlebars-helpers';
import {createHash} from 'crypto';
import {WinstonLogger, IctError} from 'ict-api-foundations';
import {DataQuery} from 'ict-api-schema';

/**
 * Interface for flat context passed to both BigQuery and Handlebars
 * Contains all parameters, configs, filters, and table paths in a single flat object
 */
export interface TemplateContext {
  /** All context values flattened into a single object */
  [key: string]: unknown;
}

/**
 * Utility class for processing SQL queries with Handlebars templating.
 * Provides template compilation, caching, and context processing for data queries.
 */
export class HandlebarsQueryProcessor {
  private readonly templateCache = new Map<
    string,
    HandlebarsTemplateDelegate
  >();
  private readonly handlebars: typeof Handlebars;

  constructor(private readonly logger: WinstonLogger) {
    // Create a new Handlebars instance to avoid global pollution
    this.handlebars = Handlebars.create();

    // Register handlebars-helpers for additional functionality
    helpers({handlebars: this.handlebars});

    this.logger.debug('HandlebarsQueryProcessor initialized with helpers');
  }

  /**
   * Process a SQL query template with the provided context
   * @param dataQuery The data query containing the SQL template
   * @param context The template context with tablePaths, configs, filters, etc.
   * @returns The processed SQL query string
   */
  processQuery(dataQuery: DataQuery, context: TemplateContext): string {
    const queryId = dataQuery.id;

    try {
      // Get or compile the template
      const template = this.getOrCompileTemplate(dataQuery.query, queryId);

      // Process the template with context
      const processedSql = template(context);

      this.logger.debug('Successfully processed Handlebars template', {
        queryId,
        originalLength: dataQuery.query.length,
        processedLength: processedSql.length,
        contextKeys: Object.keys(context),
      });

      return processedSql;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error('Failed to process Handlebars template', {
        queryId,
        error: errorMessage,
        stack: errorStack,
      });

      const ictError = IctError.badRequest(
        `Failed to process query template for query ${queryId}: ${errorMessage}`,
        error,
      );
      ictError.errorType = 'handlebarsTemplateError';
      throw ictError;
    }
  }

  /**
   * Build table paths context from data query data sources
   * @param dataQuery The data query with data sources
   * @param buildTablePath Function to build individual table paths
   * @returns Record of dataSource.id -> full table path
   */
  buildTablePathsContext(
    dataQuery: DataQuery,
    buildTablePath: (dataSource: {
      id: string;
      type: string;
      table: string;
    }) => string,
  ): Record<string, string> {
    const tablePaths: Record<string, string> = {};

    for (const dataSource of dataQuery.dataSources) {
      try {
        const tablePath = buildTablePath(dataSource);
        tablePaths[dataSource.id] = tablePath;

        this.logger.debug('Built table path for data source', {
          dataSourceId: dataSource.id,
          dataSourceType: dataSource.type,
          tableName: dataSource.table,
          tablePath,
        });
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        this.logger.error('Failed to build table path for data source', {
          dataSourceId: dataSource.id,
          error: errorMessage,
        });
        throw error;
      }
    }

    return tablePaths;
  }

  /**
   * Clear the template cache (useful for testing or memory management)
   */
  clearCache(): void {
    this.templateCache.clear();
    this.logger.debug('Handlebars template cache cleared');
  }

  /**
   * Clear cached templates for a specific query ID
   * Useful when a query is updated to remove old template versions
   */
  clearCacheForQuery(queryId: string): void {
    const keysToDelete: string[] = [];

    // Find all cache keys that start with the query ID
    for (const key of this.templateCache.keys()) {
      if (key.startsWith(`${queryId}:`)) {
        keysToDelete.push(key);
      }
    }

    // Delete the found keys
    for (const key of keysToDelete) {
      this.templateCache.delete(key);
    }

    this.logger.debug('Cleared cached templates for query', {
      queryId,
      deletedCount: keysToDelete.length,
      cacheSize: this.templateCache.size,
    });
  }

  /**
   * Get cache size (useful for monitoring)
   */
  getCacheSize(): number {
    return this.templateCache.size;
  }

  /**
   * Get or compile a template, using cache when possible
   * @param sqlTemplate The SQL template string
   * @param cacheKey Base key for caching (typically query ID)
   * @returns Compiled Handlebars template
   */
  private getOrCompileTemplate(
    sqlTemplate: string,
    cacheKey: string,
  ): HandlebarsTemplateDelegate {
    // Create a cache key that includes the SQL template content hash
    // This ensures cache invalidation when the template changes
    const templateHash = createHash('sha256')
      .update(sqlTemplate)
      .digest('hex')
      .substring(0, 12); // Use first 12 characters for brevity
    const fullCacheKey = `${cacheKey}:${templateHash}`;

    // Check cache first
    const cachedTemplate = this.templateCache.get(fullCacheKey);
    if (cachedTemplate) {
      this.logger.debug('Using cached Handlebars template', {
        cacheKey: fullCacheKey,
        originalKey: cacheKey,
        templateHash,
      });
      return cachedTemplate;
    }

    // Compile new template
    try {
      const template = this.handlebars.compile(sqlTemplate);

      // Cache the compiled template
      this.templateCache.set(fullCacheKey, template);

      this.logger.debug('Compiled and cached new Handlebars template', {
        cacheKey: fullCacheKey,
        originalKey: cacheKey,
        templateHash,
        templateLength: sqlTemplate.length,
        cacheSize: this.templateCache.size,
      });

      return template;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to compile Handlebars template', {
        cacheKey: fullCacheKey,
        originalKey: cacheKey,
        error: errorMessage,
        templatePreview: sqlTemplate.substring(0, 200),
      });

      throw new Error(`Template compilation failed: ${errorMessage}`);
    }
  }
}
