import {IctError} from 'ict-api-foundations';
import {
  DateTimeGranularity,
  DateTimeGranularityValues,
} from '@ict/sdk-foundations/types/defs/data-query-property-def.ts';
import {
  DataQuery,
  DataQueryProperty,
  isValidDataQueryProperty,
} from 'ict-api-schema';

/**
 * Utility class for Data API query parameter processing and validation. Note that we can't use typical
 * AJV validation because each Data Query has a different set of dynamic parameters.
 */
export class QueryParameterUtils {
  /**
   * Validate that all required parameters are present and non-empty
   */
  static validateRequiredParameters(
    dataQuery: DataQuery,
    queryParams: Record<string, unknown>,
  ): void {
    if (
      !dataQuery.parameters?.required ||
      dataQuery.parameters.required.length === 0
    ) {
      return;
    }

    const missingParams: string[] = [];
    const emptyParams: string[] = [];

    for (const requiredParam of dataQuery.parameters.required) {
      const paramValue = queryParams[requiredParam];

      if (paramValue === undefined || paramValue === null) {
        missingParams.push(requiredParam);
      } else if (typeof paramValue === 'string' && paramValue.trim() === '') {
        emptyParams.push(requiredParam);
      }
    }

    if (missingParams.length > 0 || emptyParams.length > 0) {
      const errorMessages: string[] = [];

      if (missingParams.length > 0) {
        errorMessages.push(
          `Missing required parameters: ${missingParams.join(', ')}`,
        );
      }

      if (emptyParams.length > 0) {
        errorMessages.push(
          `Empty required parameters: ${emptyParams.join(', ')}`,
        );
      }

      const ictError = IctError.badRequest(
        `Parameter validation failed for query ${dataQuery.id}: ${errorMessages.join('; ')}`,
      );
      ictError.errorType = 'parameterValidationError';
      throw ictError;
    }
  }

  /**
   * Process filter parameters from DataQuery.filters array
   * Splits pipe-delimited values and sanitizes parameter names
   */
  static processFilterParameters(
    dataQuery: DataQuery,
    queryParams: Record<string, unknown>,
    parameters: Record<string, unknown>,
  ): void {
    if (!dataQuery.filters || dataQuery.filters.length === 0) {
      return; // No filters to process
    }

    for (const filterName of dataQuery.filters) {
      const filterKey = filterName.replace(/-/g, '_');
      const filterValue = queryParams[filterKey];

      if (
        filterValue !== undefined &&
        filterValue !== null &&
        filterValue !== ''
      ) {
        // Convert to string and split on pipe delimiter
        const filterString = String(filterValue);
        const filterArray = filterString
          .split('|')
          .map(item => item.trim())
          .filter(item => item.length > 0);

        if (filterArray.length > 0) {
          // Sanitize parameter name: replace hyphens with underscores for BigQuery compatibility
          const sanitizedFilterName = filterName.replace(/-/g, '_');

          // Add the processed filter array to parameters
          // eslint-disable-next-line no-param-reassign
          parameters[sanitizedFilterName] = filterArray;
        }
      }
    }
  }

  /**
   * Sanitize parameter names for BigQuery compatibility
   * BigQuery does not support "-" in parameter names, so replace with "_"
   */
  static sanitizeParameterName(parameterName: string): string {
    return parameterName.replace(/-/g, '_');
  }

  /**
   * Add standard query parameters to the parameters object
   */
  static addStandardParameters(
    queryParams: Record<string, unknown>,
    parameters: Record<string, unknown>,
  ): void {
    Object.entries(queryParams).forEach(([key, value]) => {
      // Skip dryRun as it's handled separately
      if (key !== 'dryRun') {
        // eslint-disable-next-line no-param-reassign
        parameters[key] = value;
      }
    });
  }

  /**
   * Extract dry run flag from query parameters
   */
  static extractDryRunFlag(queryParams: Record<string, unknown>): boolean {
    return queryParams.dryRun === 'true' || queryParams.dryRun === true;
  }

  static validateQueryProperty(
    property: DataQueryProperty,
    propertyValue: string,
  ): void {
    if (!isValidDataQueryProperty(property)) {
      const ictError = IctError.badRequest(
        `Query property validation failed. ${JSON.stringify(property)} is not a valid DataQueryProperty`,
      );
      ictError.errorType = 'parameterValidationError';
      throw ictError;
    }

    // special cases beyond typechecking
    switch (property.type) {
      case 'time_bucket':
        if (
          !DateTimeGranularityValues.includes(
            propertyValue as DateTimeGranularity,
          )
        ) {
          const ictError = IctError.badRequest(
            `Query property validation failed for ${property.id}: ${propertyValue} is not a valid time bucket`,
          );
          ictError.errorType = 'parameterValidationError';
          throw ictError;
        }
        break;
      case 'group_by':
        if (!property.groupableFields.includes(propertyValue)) {
          const ictError = IctError.badRequest(
            `Query property validation failed for ${property.id}: ${propertyValue} is not a valid group by field`,
          );
          ictError.errorType = 'parameterValidationError';
          throw ictError;
        }
        break;
      case 'limit':
        if (isNaN(Number(propertyValue)) || Number(propertyValue) <= 0) {
          const ictError = IctError.badRequest(
            `Query property validation failed for ${property.id}: ${propertyValue} is not a valid limit`,
          );
          ictError.errorType = 'parameterValidationError';
          throw ictError;
        }
        break;
      default:
        return;
    }
  }
}
