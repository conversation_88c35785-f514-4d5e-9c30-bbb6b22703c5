import {
  type DataQueryField,
  type DataQueryResultType,
  type DataQueryValidation,
} from '../defs/index.ts';

/**
 * Utility class for data validation and field type inference
 */
export class ValidationUtils {
  /**
   * Infer field type from sample value
   */
  static inferFieldType(
    value: unknown,
  ): 'string' | 'number' | 'timestamp' | 'boolean' {
    if (typeof value === 'number') {
      return 'number';
    }
    if (typeof value === 'boolean') {
      return 'boolean';
    }
    if (typeof value === 'string') {
      // Check if it looks like a timestamp
      const timestampRegex =
        /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?)?$/;
      if (timestampRegex.test(value)) {
        return 'timestamp';
      }
    }
    return 'string';
  }

  /**
   * Validate that data structure matches the expected query type
   */
  static validateDataStructure(
    rows: Record<string, unknown>[],
    fields: DataQueryField[],
    type: DataQueryResultType,
  ): DataQueryValidation {
    switch (type) {
      case 'singleValue': {
        if (rows.length > 1) {
          return {
            isValid: false,
            reason: `Single value query returned ${rows.length} rows, expected exactly 1`,
          };
        }
        if (fields.length > 1) {
          return {
            isValid: false,
            reason: `Single value query returned ${fields.length} fields, expected exactly 1`,
          };
        }
        break;
      }

      case 'timeSeries': {
        const timestampField = fields.find(f => f.name === 'timestamp');
        if (!timestampField) {
          return {
            isValid: false,
            reason:
              'Time series query must have a timestamp field (named "timestamp" or with timestamp type)',
          };
        }
        if (fields.length < 2) {
          return {
            isValid: false,
            reason:
              'Time series query must have at least 2 fields (timestamp + value)',
          };
        }
        break;
      }

      case 'categorySeries': {
        const categoryField = fields.find(f => f.name === 'category');
        const valueField = fields.find(f => f.name === 'value');

        if (!categoryField) {
          return {
            isValid: false,
            reason:
              'Category series query must have exactly one column named "category"',
          };
        }

        if (!valueField) {
          return {
            isValid: false,
            reason:
              'Category series query must have exactly one column named "value"',
          };
        }

        if (fields.length !== 2) {
          return {
            isValid: false,
            reason: `Category series query must have exactly 2 columns, got ${fields.length}`,
          };
        }

        break;
      }

      case 'tabular':
        // Tabular is flexible, no specific validation needed
        break;

      default:
        // This should never happen due to TypeScript, but adding for completeness
        break;
    }

    return {isValid: true};
  }
}
