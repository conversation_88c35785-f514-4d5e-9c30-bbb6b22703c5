import {Get, Middlewares, OperationId, Route, Tags} from 'tsoa';
import {Container, ProtectedRouteMiddleware} from 'ict-api-foundations';
import {DataExplorerService} from '../services/dataexplorer-service.ts';
import {AimlHealthCheckResponse} from '../defs/data-explorer-aiml-healthcheck-def.ts';

@Route('/data-explorer/aiml-healthcheck')
@Middlewares([ProtectedRouteMiddleware.apiProtectedDataRoute()])
export class DataExplorerAimlHealthCheckController {
  static readonly exampleData: AimlHealthCheckResponse = {
    status: 'ready',
    checks: {
      postgres: {
        ok: true,
        detail: 'postgres ok',
      },
      bigquery: {
        ok: true,
        detail: 'bq ok',
      },
      rag_engine: {
        ok: true,
        detail: 'rag status: 200',
      },
    },
    total_ms: 680,
    version: '0.0.1',
  };

  @Get('')
  @OperationId('GetDataExplorerAimlHealthCheck')
  @Tags('data-explorer', 'aiml')
  public async getDataExplorerAimlHealthCheck(): Promise<AimlHealthCheckResponse> {
    const dataExplorerSerivce = Container.get(DataExplorerService);
    const response = await dataExplorerSerivce.getAimlHealthCheck();
    return response;
  }
}
