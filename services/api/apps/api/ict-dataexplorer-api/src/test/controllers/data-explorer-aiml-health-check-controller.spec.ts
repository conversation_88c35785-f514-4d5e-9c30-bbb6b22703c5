import sinon from 'sinon';
import request from 'supertest';
import {expect} from 'chai';
import {appSetup, Container} from 'ict-api-foundations';
import {DataExplorerAimlHealthCheckController} from '../../controllers/data-explorer-aiml-health-check-controller.ts';
import {DataExplorerService} from '../../services/dataexplorer-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';

describe(DataExplorerAimlHealthCheckController.name, () => {
  const url: string = '/data-explorer/aiml-healthcheck';

  const app = appSetup(RegisterRoutes);

  let dataExplorerServiceStub: sinon.SinonStubbedInstance<DataExplorerService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Data Explorer AIML Health Check Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);

      dataExplorerServiceStub.getAimlHealthCheck.rejects(
        new Error('Test error'),
      );
      const response = await request(app).get(url);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });
  });

  describe('Data Explorer AIML Health Check Controller Success scenarios', () => {
    beforeEach(() => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);
      dataExplorerServiceStub.getAimlHealthCheck.resolves(
        DataExplorerAimlHealthCheckController.exampleData,
      );
    });

    it('should return the expected AIML healthcheck data', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        DataExplorerAimlHealthCheckController.exampleData,
      );

      sinon.assert.called(dataExplorerServiceStub.getAimlHealthCheck);
    });
  });
});
