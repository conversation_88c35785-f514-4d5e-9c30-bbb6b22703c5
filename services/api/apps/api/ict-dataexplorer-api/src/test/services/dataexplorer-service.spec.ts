import {expect} from 'chai';
import {
  ConfigStore,
  ContextService,
  DatabaseProvider,
  EnvironmentService,
  PostgresDatabase,
  SecurityRoles,
} from 'ict-api-foundations';
import sinon from 'sinon';
import {DataSource, Repository} from 'typeorm';
import {EntityTypes, DataExplorerResultEntity} from 'ict-api-schema';
import {DataExplorerSearchAIResponse} from '../../defs/dataexplorer-search-ai-response.ts';
import {DataExplorerResult} from '../../defs/dataexplorer-search-result.ts';
import {Recommended} from '../../defs/dataexplorer-recommended.ts';
import {AIService} from '../../services/ai-service.ts';
import {DataExplorerService} from '../../services/dataexplorer-service.ts';
import {DataExplorerStore} from '../../stores/dataexplorer-store.ts';

describe(DataExplorerService.name, () => {
  let dataExplorerSearchAIStub: sinon.SinonStub;
  let contextService: ContextService;
  let configStore: ConfigStore;
  let aiService: AIService;
  let dataExplorerService: DataExplorerService;
  let dataExplorerStore: DataExplorerStore;
  let dbProvider: DatabaseProvider;
  let postgres: PostgresDatabase;
  let datasource: sinon.SinonStubbedInstance<DataSource>;
  let mockEnvService: EnvironmentService;
  let userRolesStub: sinon.SinonStub;
  const testDataset = 'test-dataset';
  const testProjectId = 'test-project-id';
  const testBqTables: unknown[] = [
    'fct_pick_activity',
    'fct_pick_order',
    'dim_operator',
  ];

  beforeEach(() => {
    dataExplorerSearchAIStub = sinon.stub();
    datasource = sinon.createStubInstance(DataSource);
    postgres = new PostgresDatabase(datasource, EntityTypes.DataExplorer);

    contextService = new ContextService();

    dbProvider = new DatabaseProvider();
    dbProvider.set(postgres.getType(), postgres);
    sinon.stub(contextService, 'dbProvider').get(() => dbProvider);
    configStore = new ConfigStore(contextService);
    dataExplorerStore = new DataExplorerStore(contextService);
    aiService = sinon.createStubInstance(AIService);
    sinon.stub(contextService, 'datasetId').get(() => testDataset);

    mockEnvService = new EnvironmentService();
    sinon.stub(mockEnvService, 'apiProject').get(() => {
      return {
        projectId: testProjectId,
      };
    });

    sinon.stub(mockEnvService, 'apiProject').get(() => {
      return {
        projectId: testProjectId,
      };
    });

    sinon.stub(mockEnvService, 'aimlDataDictionaryTables').get(() => {
      return testBqTables;
    });

    userRolesStub = sinon.stub(contextService, 'userRoles');
    userRolesStub.value([]);

    dataExplorerService = new DataExplorerService(
      dataExplorerStore,
      aiService,
      contextService,
      mockEnvService,
      configStore,
    );
    aiService.search = dataExplorerSearchAIStub;
  });

  afterEach(() => {
    sinon.restore();
    dataExplorerSearchAIStub.resetHistory();
  });

  describe('search', () => {
    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      // Stub value for when getting "DataExplorerDebugData"
      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves({value: true});
    });

    afterEach(() => {
      settingStub.restore();
    });

    const testQuestion = 'Is this a test question?';
    const mockResponse: DataExplorerSearchAIResponse = {
      response_code: 200,
      summary_response: 'The answer is 42',
      question: testQuestion,
      sql_query_output: [
        {
          answer: 42,
        },
      ],
      echart_code: null,
      debug_data: {
        Error: '',
        KnownDB: 'superior_uniform',
        ResponseCode: 200,
        echart_code:
          "option = {title: {text: 'Top 5 Items by Total Volume'},tooltip: {trigger: 'axis',axisPointer: {type: 'shadow'}},legend: {},grid: {left: '3%',right: '4%',bottom: '3%',containLabel: true},xAxis: {type: 'category',data: ['WMT5000BLOSFA','517OSFM','544OSFM','TBN3002DMOSFA','74756NS']},yAxis: {type: 'value',name: 'Total Quantity'},series: [{name: 'Total Quantity',type: 'bar',data: [234422019.0,193276259.0,161081799.0,107615046.0,100223380.0]}]};",
        generated_sql_query:
          " SELECT inv.sku, SUM(SAFE_CAST(inv.quantity AS BIGNUMERIC)) AS total_quantity FROM `edp-d-us-east2-etl.superior_uniform.gold_wms_inventory` AS inv WHERE inv.query_timestamp_utc > '1970-01-01' GROUP BY inv.sku ORDER BY total_quantity DESC LIMIT 5 ",
        question:
          'What are the top 5 items with the highest total volume in the warehouse?',
        sql_query_output:
          '[{"sku":"WMT5000BLOSFA","total_quantity":234422019.0},{"sku":"517OSFM","total_quantity":193276259.0},{"sku":"544OSFM","total_quantity":161081799.0},{"sku":"TBN3002DMOSFA","total_quantity":107615046.0},{"sku":"74756NS","total_quantity":100223380.0}]',
        summary_response:
          'The top 5 items with the highest total volume in the warehouse are:\n\n1.  **WMT5000BLOSFA** with a total quantity of 234,422,019.0\n2.  **517OSFM** with a total quantity of 193,276,259.0\n3.  **544OSFM** with a total quantity of 161,081,799.0\n4.  **TBN3002DMOSFA** with a total quantity of 107,615,046.0\n5.  **74756NS** with a total quantity of 100,223,380.0\n',
      },
    };
    const expectedResult: DataExplorerResult = {
      id: '1234',
      answer: mockResponse.summary_response,
      prompt: testQuestion,
      queryResults: mockResponse.sql_query_output,
      timestamp: new Date(),
      isRecommended: Recommended.notProvided,
      eChartCode: null,
      debugData: mockResponse.debug_data,
    };
    const mockSavedResponse: DataExplorerResultEntity = {
      id: expectedResult.id,
      description: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: 'testUser',
      question: testQuestion,
      answer: mockResponse.summary_response || '',
      jsonOutput: mockResponse.sql_query_output,
      isBookmarked: false,
      isRecommended: null,
      eChartCode: null,
      debugData: null,
    };

    it('should call AIService.search with the correct parameters', async () => {
      dataExplorerSearchAIStub.resolves(mockResponse);

      dataExplorerStore.saveSearchResult = sinon
        .stub()
        .resolves(mockSavedResponse);
      const result = await dataExplorerService.search(testQuestion);

      expect(result.answer).to.equal(expectedResult.answer);
      expect(result.prompt).to.equal(expectedResult.prompt);
      expect(result.queryResults).to.equal(expectedResult.queryResults);

      const expectedParams = {
        user_question: testQuestion,
        user_database: testDataset,
      };
      expect(dataExplorerSearchAIStub.calledOnce).to.be.true;
      expect(dataExplorerSearchAIStub.firstCall.args[0]).to.deep.equal(
        expectedParams,
      );
    });

    it('should return debug data if users roles includes "ct_engineer"', async () => {
      // Give user the ct_engineer role
      userRolesStub.value([SecurityRoles.CT_ENGINEERS]);

      dataExplorerSearchAIStub.resolves(mockResponse);

      dataExplorerStore.saveSearchResult = sinon
        .stub()
        .resolves(mockSavedResponse);

      const result = await dataExplorerService.search(testQuestion);

      expect(result.answer).to.equal(expectedResult.answer);
      expect(result.prompt).to.equal(expectedResult.prompt);
      expect(result.queryResults).to.equal(expectedResult.queryResults);
      expect(result.debugData).to.equal(expectedResult.debugData);

      expect(dataExplorerSearchAIStub.calledOnce).to.be.true;
      const expectedParams = {
        user_question: testQuestion,
        user_database: testDataset,
      };
      expect(dataExplorerSearchAIStub.firstCall.args[0]).to.deep.equal(
        expectedParams,
      );
    });

    it('should NOT return debug data if users roles does not includes "ct_engineer"', async () => {
      // Make sure user has no roles
      userRolesStub.value([]);

      dataExplorerSearchAIStub.resolves(mockResponse);

      dataExplorerStore.saveSearchResult = sinon
        .stub()
        .resolves(mockSavedResponse);

      const result = await dataExplorerService.search(testQuestion);

      expect(result.answer).to.equal(expectedResult.answer);
      expect(result.prompt).to.equal(expectedResult.prompt);
      expect(result.queryResults).to.equal(expectedResult.queryResults);
      expect(result.debugData).to.be.undefined;

      expect(dataExplorerSearchAIStub.calledOnce).to.be.true;
      const expectedParams = {
        user_question: testQuestion,
        user_database: testDataset,
      };
      expect(dataExplorerSearchAIStub.firstCall.args[0]).to.deep.equal(
        expectedParams,
      );
    });
  });

  describe('getResults', () => {
    it('should call dataExplorerSearchAI with the correct parameters', async () => {
      const fakeSettingRepo = sinon.createStubInstance(
        Repository<DataExplorerResult>,
      );
      fakeSettingRepo.findOneBy.resolves({
        id: '123',
        answer: '',
        createdAt: new Date(),
        description: '',
        isBookmarked: false,
        isRecommended: false,
        jsonOutput: {},
        question: '',
        updatedAt: new Date(),
        userId: 'auth0|abc',
      } as DataExplorerResultEntity);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });
      const result = await dataExplorerService.getResult('123');
      expect(result?.id).to.equal('123');
    });
  });

  describe('getResult', () => {
    it('should call DataExplorerStore to request a result', async () => {
      dataExplorerStore.getResult = sinon.stub().resolves('result');
      const result = await dataExplorerService.getResult('123');
      expect(result).to.equal(result);
    });
  });

  describe('getRecommendations', () => {
    const mockRecommendations = ['test recommendation'];

    it('should call aiService to request recommendations', async () => {
      aiService.getRecommendations = sinon.stub().resolves(mockRecommendations);
      const results = await dataExplorerService.getRecommendations();
      const expected = mockRecommendations.map(r => ({prompt: r}));
      expect(results).to.deep.equal(expected);
    });
    it('should return hardcoded questions if aiService returns no recommendations', async () => {
      aiService.getRecommendations = sinon.stub().resolves([]);
      const results = await dataExplorerService.getRecommendations();
      expect(results.length).to.equal(4);
    });
  });

  describe('getGoldQuestions', () => {
    const mockGoldQuestions = ['test gold question'];

    it('should call aiService to request gold questions', async () => {
      aiService.getGoldQuestions = sinon.stub().resolves(mockGoldQuestions);
      const results = await dataExplorerService.getGoldQuestions();
      const expected = mockGoldQuestions.map(q => ({prompt: q}));
      expect(results).to.deep.equal(expected);
    });

    it('should return hardcoded gold questions if aiService returns no questions', async () => {
      aiService.getGoldQuestions = sinon.stub().resolves([]);
      const results = await dataExplorerService.getGoldQuestions();
      expect(results.length).to.equal(4);
    });
  });
  describe('getAimlHealthCheck', () => {
    it('should call aiService to request AIML health check', async () => {
      const mockHealthCheckResponse = {
        status: 'ready',
        checks: {
          postgres: {ok: true, detail: 'postgres ok'},
          bigquery: {ok: true, detail: 'bq ok'},
          rag_engine: {ok: true, detail: 'rag status: 200'},
        },
        total_ms: 100,
        version: '1.0.0',
      };
      aiService.getAimlHealthCheck = sinon
        .stub()
        .resolves(mockHealthCheckResponse);
      const result = await dataExplorerService.getAimlHealthCheck();
      expect(result).to.deep.equal(mockHealthCheckResponse);
    });
  });
});
