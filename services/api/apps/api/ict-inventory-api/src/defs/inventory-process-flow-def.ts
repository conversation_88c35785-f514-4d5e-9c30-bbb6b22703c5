import {EdgeDirection, Metric} from 'ict-api-foundations';

export interface NodeAlert {
  color: string;
  value: string;
}

export interface Shuttle {
  id: string;
  name: string;
  metrics: Metric[];
}

export interface AisleLevel {
  id: string;
  label: string;
  shuttles: Shuttle[];
}

// Defines the structure for an area
export interface Area {
  id: string;
  alerts?: NodeAlert[];
  label: string;
  metrics: Metric[];
  position: AreaPosition;
  nodeType: 'Area' | 'Aisle' | 'Lift';
  aisleLevels?: AisleLevel[];
  view?: string;
}

// Defines the stucture returned after updating the position of a node
export interface UpdatedArea {
  identity: number;
  labels: string[];
  properties: {
    // name: string;
    // created_by: string;
    // views: string[];
    [key: string]: string | string[] | number; // need to this to account for the positions in different views (ex. multishuttle_x)
  };
  elementId: string;
}

export interface AreaUpdate {
  id: string;
  position: AreaPosition;
}

export interface GraphArea {
  id: string;
  alerts?: NodeAlert[];
  label: string;
  metrics: string[];
  position: AreaPosition;
  nodeType: 'Area' | 'Aisle' | 'Lift';
  aisleLevels?: AisleLevel[];
}

export interface AreaPosition {
  x: number;
  y: number;
}

// Defines the structure for a link between areas
export interface Edge {
  id: string;
  source: string;
  target: string;
  direction: EdgeDirection;
  metrics: Metric[];
}

export interface GraphEdge {
  id: string;
  source: string;
  target: string;
  direction: EdgeDirection;
  metrics: string[];
}

// Defines the overall structure of the API response
export interface ProcessFlowResponse {
  areas: Area[];
  edges: Edge[];
  lastProcessedTime: string;
}

export interface ProcessFlowGraphResponse {
  areas: GraphArea[];
  edges: GraphEdge[];
}

export interface ProcessFlowDetailsResponse {
  metricGroups: MetricGroup[];
}

export interface MetricGroup {
  title: string;
  metrics: Metric[];
}

export interface UpdatePositionPayload {
  id: string;
  position: {
    x: number;
    y: number;
  };
}

export interface ClearCacheRequest {
  scope: 'all' | 'metrics' | 'graph';
}

// Name for Redis keys that store previous Neo4j Graph responses (ie {nodes:[], edges:[]})
export const GRAPH_CACHE_NAME = 'process_flow_graph';

// Name for cache of area nodes that have been created in Neo4j.
// Used when processing and creating Neo4j area/aisle/shuttle/lift/station nodes.
export const GRAPH_AREA_CACHE_NAME = 'graph_area_cache';

// Name for cache of edges that have been created in Neo4j.
// Used when processing and creating Neo4j edges.
export const GRAPH_EDGE_CACHE_NAME = 'graph_edge_cache';

// Name for cache of metric nodes that have been created in Neo4j.
// Used when processing and creating Neo4j metric nodes.
export const GRAPH_METRIC_CACHE_NAME = 'graph_metric_cache';

// Name for cache of the last processed time.
export const PROCESSED_TIME_CACHE_NAME = 'last_processed_time';
