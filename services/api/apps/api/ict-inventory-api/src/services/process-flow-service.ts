import {
  ClearCacheResponse,
  DiService,
  Metric,
  MetricService,
} from 'ict-api-foundations';
import {
  Area,
  ClearCacheRequest,
  Edge,
  GraphArea,
  GraphEdge,
  MetricGroup,
  ProcessFlowDetailsResponse,
  ProcessFlowGraphResponse,
  ProcessFlowResponse,
  UpdatedArea,
  UpdatePositionPayload,
} from '../defs/inventory-process-flow-def.ts';
import {ProcessFlowStore} from '../stores/process-flow-store.ts';

/**
 * Service that handles applying business logic for Inventory to store queries.
 */
@DiService()
export class ProcessFlowService {
  constructor(
    private processFlowStore: ProcessFlowStore,
    private metricService: MetricService,
  ) {}

  /**
   * Retrieves the facility process flow graph from the database
   * @returns
   */
  async getAllAreas(view?: string): Promise<ProcessFlowResponse> {
    const graph = await this.processFlowStore.getAreaGraph(view);

    // We only care about metric ids that are part of our graph
    const ids = this.getUniqueMetricIds(graph);

    // Read the metric nodes with properties from Neo4j and then get the values from Redis
    let metricsResponse;
    if (ids.length > 0) {
      const metric_nodes = await this.processFlowStore.getMetricNodes(ids);
      if (metric_nodes.length > 0) {
        metricsResponse = await this.metricService.getMetrics(metric_nodes);
      }
    }

    // Get metric values stored in Redis for all metric_nodes stored in Neo4j.
    const areas = this.getGraphAreas(
      graph.areas,
      metricsResponse?.metrics ?? [],
    );
    const edges = this.getGraphEdges(
      graph.edges,
      metricsResponse?.metrics ?? [],
    );

    return {
      areas,
      edges,
      lastProcessedTime: metricsResponse?.lastProcessedTime ?? '',
    };
  }

  async getDetails(
    type: string,
    elementId: string,
    view?: string,
  ): Promise<ProcessFlowDetailsResponse> {
    const getMetricResponseForElement = async (metrics: string[]) => {
      const elementMetrics =
        await this.processFlowStore.getMetricNodes(metrics);
      return await this.metricService.getMetrics(elementMetrics);
    };

    // determine if an edge is bidirectional
    if (type === 'edge') {
      const biEdge =
        await this.processFlowStore.getBiDirectionalEdge(elementId);

      // bidirectional counterpart found, return them both
      if (biEdge.metrics.length > 0) {
        const biEdgeMetricsResponse = await getMetricResponseForElement(
          biEdge.metrics,
        );

        const edge = await this.processFlowStore.getElement(elementId, view);
        const edgeMetricsResponse = await getMetricResponseForElement(
          edge.metrics,
        );

        const groupedEdgeMetrics = this.groupMetricsByPanelGroup(
          edgeMetricsResponse.metrics,
        );
        const groupedBiEdgeMetrics = this.groupMetricsByPanelGroup(
          biEdgeMetricsResponse.metrics,
        );

        // append both counterpart metricresponses for the bidirectional edge
        const groupedMetrics = [...groupedBiEdgeMetrics, ...groupedEdgeMetrics];

        return {metricGroups: groupedMetrics};
      }
    }

    const element = await this.processFlowStore.getElement(elementId, view);
    const metricsResponse = await getMetricResponseForElement(element.metrics);

    // Group metrics by panelGroup
    const groupedMetrics = this.groupMetricsByPanelGroup(
      metricsResponse.metrics,
    );

    return {metricGroups: groupedMetrics};
  }

  /**
   * Updates an Area node in the database.
   * @param area
   * @returns
   */
  async updatePosition(
    payload: UpdatePositionPayload,
    view = 'facility',
  ): Promise<UpdatedArea> {
    return this.processFlowStore.updatePosition(payload, view);
  }

  /**
   * Updates an Edge in the database.
   * @param area
   * @returns
   */
  updateEdge(edge: Edge): Promise<Edge> {
    return this.processFlowStore.updateEdge(edge);
  }

  /**
   * Updates an Edge in the database.
   * @param area
   * @returns
   */
  updateMetric(metric: Metric): Promise<Metric> {
    return this.processFlowStore.updateMetric(metric);
  }

  /**
   * Clears metric data from Redis and/or graph data in Neo4j and Redis, depending on scope.
   * @returns ClearCacheResponse
   */
  async clearCache(
    clearCacheRequest: ClearCacheRequest,
  ): Promise<ClearCacheResponse> {
    // No matter what scope is, we are deleting SOMETHING from Redis.
    //   (graph related keys, metric related keys, or all keys)
    await this.processFlowStore.clearRedisMetricsCache(clearCacheRequest.scope);

    // If scope is all or graph, delete all nodes and edges from Neo4j.
    if (
      clearCacheRequest.scope === 'graph' ||
      clearCacheRequest.scope === 'all'
    ) {
      await this.processFlowStore.deleteGraph();
    }

    // TODO: add error handling
    return {
      success: true,
    };
  }

  /**
   * Get all unique metric ids from the graph data.
   * @param data
   * @returns
   */
  private getUniqueMetricIds(data: ProcessFlowGraphResponse): string[] {
    const allMetrics: string[] = [];

    data.areas.forEach(area => {
      allMetrics.push(...area.metrics);
    });
    data.edges.forEach(edge => {
      allMetrics.push(...edge.metrics);
    });

    // Create a Set to filter out duplicate metrics
    const uniqueMetrics = [...new Set(allMetrics)];
    return uniqueMetrics;
  }

  /**
   * Finds and filters metrics that are shown on the graph
   * @param metricIds
   * @param metrics
   * @returns
   */
  private findAndFilterGraphMetrics(
    metricIds: string[],
    metrics: Metric[],
  ): Metric[] {
    return metricIds
      .map(metricId => metrics.find(m => m.id === metricId))
      .filter((metric): metric is Metric => metric !== undefined);
  }

  // Helper function to group metrics by panelGroup
  private groupMetricsByPanelGroup(metrics: Metric[]): MetricGroup[] {
    const groupedMetrics: {[key: string]: Metric[]} = {};
    metrics.forEach(metric => {
      const group = metric.panelGroup || 'default';
      if (!groupedMetrics[group]) {
        groupedMetrics[group] = [];
      }
      groupedMetrics[group].push(metric);
    });

    // Convert the grouped metrics object into an array of MetricGroups
    return Object.entries(groupedMetrics).map(([title, metricList]) => ({
      title,
      metrics: metricList,
    }));
  }

  private getGraphAreas(areas: GraphArea[], metrics: Metric[]): Area[] {
    return areas.map(area => {
      const areaMetrics = this.findAndFilterGraphMetrics(area.metrics, metrics);
      return {
        ...area,
        metrics: areaMetrics,
      };
    });
  }

  private getGraphEdges(edges: GraphEdge[], metrics: Metric[]): Edge[] {
    return edges.map(edge => {
      const edgeMetrics = this.findAndFilterGraphMetrics(edge.metrics, metrics);
      return {
        ...edge,
        metrics: edgeMetrics,
      };
    });
  }
}
