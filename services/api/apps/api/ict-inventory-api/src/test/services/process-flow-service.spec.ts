import {expect} from 'chai';
import {EdgeDirection, Metric, MetricService} from 'ict-api-foundations';
import sinon from 'sinon';
import {
  Edge,
  ProcessFlowGraphResponse,
  UpdatedArea,
  UpdatePositionPayload,
} from '../../defs/inventory-process-flow-def.ts';
import {ProcessFlowService} from '../../services/process-flow-service.ts';
import {ProcessFlowStore} from '../../stores/process-flow-store.ts';

describe('ProcessFlowService', () => {
  let processFlowService: ProcessFlowService;
  let processFlowStoreStub: sinon.SinonStubbedInstance<ProcessFlowStore>;
  let metricServiceStub: sinon.SinonStubbedInstance<MetricService>;

  const mockGraph: ProcessFlowGraphResponse = {
    areas: [
      {
        id: 'area1',
        label: 'Area 1',
        metrics: ['metric1'],
        nodeType: 'Area',
        position: {
          x: 500,
          y: 250,
        },
      },
    ],
    edges: [
      {
        id: 'edge1',
        source: 'area1',
        direction: EdgeDirection.Downstream,
        target: 'area2',
        metrics: ['metric2'],
      },
    ],
  };

  const mockMetrics: Metric[] = [
    {
      id: 'metric1',
      description: 'Test metric 1 description',
      units: 'units/hr',
      type: 'normal',
      value: 100,
    },
    {
      id: 'metric2',
      description: 'Test metric 2 description',
      units: 'units/hr',
      type: 'normal',
      value: 200,
    },
  ];

  beforeEach(() => {
    processFlowStoreStub = sinon.createStubInstance(ProcessFlowStore);
    metricServiceStub = sinon.createStubInstance(MetricService);
    processFlowService = new ProcessFlowService(
      processFlowStoreStub,
      metricServiceStub,
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getAllAreas', () => {
    it('should return process flow response with areas and edges', async () => {
      // Setup stubs
      processFlowStoreStub.getAreaGraph.resolves(mockGraph);
      processFlowStoreStub.getMetricNodes.resolves(mockMetrics);
      metricServiceStub.getMetrics.resolves({
        metrics: mockMetrics,
        lastProcessedTime: '2024-01-01T00:00:00Z',
      });

      // Test the service method
      const result = await processFlowService.getAllAreas('facility');

      // Verify the result
      expect(result).to.deep.equal({
        areas: [
          {
            id: 'area1',
            label: 'Area 1',
            metrics: [mockMetrics[0]],
            nodeType: 'Area',
            position: {
              x: 500,
              y: 250,
            },
          },
        ],
        edges: [
          {
            id: 'edge1',
            source: 'area1',
            direction: EdgeDirection.Downstream,
            target: 'area2',
            metrics: [mockMetrics[1]],
          },
        ],
        lastProcessedTime: '2024-01-01T00:00:00Z',
      });

      // Verify stubs were called correctly
      sinon.assert.calledOnceWithExactly(
        processFlowStoreStub.getAreaGraph,
        'facility',
      );
      sinon.assert.calledOnceWithExactly(processFlowStoreStub.getMetricNodes, [
        'metric1',
        'metric2',
      ]);
      sinon.assert.calledOnceWithExactly(
        metricServiceStub.getMetrics,
        mockMetrics,
      );
    });

    it('should handle empty metrics gracefully', async () => {
      // Setup stubs with no metrics
      const emptyGraph = {
        areas: [],
        edges: [],
      };
      processFlowStoreStub.getAreaGraph.resolves(emptyGraph);

      // Test the service method
      const result = await processFlowService.getAllAreas('facility');

      // Verify the result
      expect(result).to.deep.equal({
        areas: [],
        edges: [],
        lastProcessedTime: '',
      });

      // Verify stubs were called correctly
      sinon.assert.calledOnceWithExactly(
        processFlowStoreStub.getAreaGraph,
        'facility',
      );
      sinon.assert.notCalled(processFlowStoreStub.getMetricNodes);
      sinon.assert.notCalled(metricServiceStub.getMetrics);
    });
  });

  describe('getDetails', () => {
    it('should return metric groups for a node element', async () => {
      // Setup stubs
      processFlowStoreStub.getElement.resolves({
        metrics: ['metric1', 'metric2'],
      });
      processFlowStoreStub.getMetricNodes.resolves(mockMetrics);
      metricServiceStub.getMetrics.resolves({
        metrics: mockMetrics,
        lastProcessedTime: '2024-01-01T00:00:00Z',
      });

      // Test the service method
      const result = await processFlowService.getDetails(
        'node',
        'element1',
        'facility',
      );

      // Verify the result
      expect(result).to.deep.equal({
        metricGroups: [
          {
            title: 'default',
            metrics: mockMetrics,
          },
        ],
      });

      // Verify stubs were called correctly
      sinon.assert.calledOnceWithExactly(
        processFlowStoreStub.getElement,
        'element1',
        'facility',
      );
      sinon.assert.calledOnceWithExactly(processFlowStoreStub.getMetricNodes, [
        'metric1',
        'metric2',
      ]);
      sinon.assert.calledOnceWithExactly(
        metricServiceStub.getMetrics,
        mockMetrics,
      );
    });

    it('should handle bidirectional edge with counterpart', async () => {
      // Setup stubs for bidirectional edge
      const biEdgeMetrics = ['metric3'];
      const edgeMetrics = ['metric1', 'metric2'];

      processFlowStoreStub.getBiDirectionalEdge.resolves({
        metrics: biEdgeMetrics,
      });
      processFlowStoreStub.getElement.resolves({metrics: edgeMetrics});

      const biEdgeMetricNodes = [
        {
          id: 'metric3',
          description: 'Test metric 3',
          units: 'units/hr',
          type: 'normal',
          value: 300,
        },
      ];
      processFlowStoreStub.getMetricNodes
        .withArgs(biEdgeMetrics)
        .resolves(biEdgeMetricNodes)
        .withArgs(edgeMetrics)
        .resolves(mockMetrics);

      metricServiceStub.getMetrics
        .withArgs(biEdgeMetricNodes)
        .resolves({
          metrics: biEdgeMetricNodes,
          lastProcessedTime: '2024-01-01T00:00:00Z',
        })
        .withArgs(mockMetrics)
        .resolves({
          metrics: mockMetrics,
          lastProcessedTime: '2024-01-01T00:00:00Z',
        });

      // Test the service method
      const result = await processFlowService.getDetails(
        'edge',
        'edge1',
        'facility',
      );

      // Verify the result
      expect(result).to.deep.equal({
        metricGroups: [
          {
            title: 'default',
            metrics: biEdgeMetricNodes,
          },
          {
            title: 'default',
            metrics: mockMetrics,
          },
        ],
      });

      // Verify stubs were called correctly
      sinon.assert.calledOnceWithExactly(
        processFlowStoreStub.getBiDirectionalEdge,
        'edge1',
      );
      sinon.assert.calledOnceWithExactly(
        processFlowStoreStub.getElement,
        'edge1',
        'facility',
      );
    });

    it('should handle edge without bidirectional counterpart', async () => {
      // Setup stubs for edge without bidirectional counterpart
      processFlowStoreStub.getBiDirectionalEdge.resolves({metrics: []});
      processFlowStoreStub.getElement.resolves({metrics: ['metric1']});
      processFlowStoreStub.getMetricNodes.resolves([mockMetrics[0]]);
      metricServiceStub.getMetrics.resolves({
        metrics: [mockMetrics[0]],
        lastProcessedTime: '2024-01-01T00:00:00Z',
      });

      // Test the service method
      const result = await processFlowService.getDetails(
        'edge',
        'edge1',
        'facility',
      );

      // Verify the result
      expect(result).to.deep.equal({
        metricGroups: [
          {
            title: 'default',
            metrics: [mockMetrics[0]],
          },
        ],
      });

      // Verify stubs were called correctly
      sinon.assert.calledOnceWithExactly(
        processFlowStoreStub.getBiDirectionalEdge,
        'edge1',
      );
      sinon.assert.calledOnceWithExactly(
        processFlowStoreStub.getElement,
        'edge1',
        'facility',
      );
    });
  });

  describe('updatePosition', () => {
    it('should update position and return updated area', async () => {
      const payload: UpdatePositionPayload = {
        id: 'area1',
        position: {x: 100, y: 200},
      };
      const updatedArea: UpdatedArea = {
        identity: 1,
        labels: ['Area'],
        properties: {
          name: 'Updated Area',
          x: 100,
          y: 200,
        },
        elementId: 'area1',
      };

      processFlowStoreStub.updatePosition.resolves(updatedArea);

      const result = await processFlowService.updatePosition(
        payload,
        'facility',
      );

      expect(result).to.deep.equal(updatedArea);
      sinon.assert.calledOnceWithExactly(
        processFlowStoreStub.updatePosition,
        payload,
        'facility',
      );
    });
  });

  describe('updateEdge', () => {
    it('should update edge and return updated edge', async () => {
      const edge: Edge = {
        id: 'edge1',
        source: 'area1',
        target: 'area2',
        direction: EdgeDirection.Downstream,
        metrics: [],
      };

      processFlowStoreStub.updateEdge.resolves(edge);

      const result = await processFlowService.updateEdge(edge);

      expect(result).to.deep.equal(edge);
      sinon.assert.calledOnceWithExactly(processFlowStoreStub.updateEdge, edge);
    });
  });

  describe('updateMetric', () => {
    it('should update metric and return updated metric', async () => {
      const metric: Metric = {
        id: 'metric1',
        description: 'Updated description',
        units: 'units/hr',
        type: 'normal',
        value: 150,
        panelGroup: 'test-group',
      };

      processFlowStoreStub.updateMetric.resolves(metric);

      const result = await processFlowService.updateMetric(metric);

      expect(result).to.deep.equal(metric);
      sinon.assert.calledOnceWithExactly(
        processFlowStoreStub.updateMetric,
        metric,
      );
    });
  });
});
