import {BaseHealthCheck} from 'ict-api-foundations';
import {Get, OperationId, Route, Tags} from 'tsoa';

@Route('/movements/healthcheck')
export class MovementsHealthCheckController {
  @Get()
  @OperationId('GetMovementsBasicHealthCheck')
  @Tags('movements')
  public getMovementsBasicHealthCheck() {
    return BaseHealthCheck.getBasicHealthCheck();
  }

  @Get('/full')
  @OperationId('GetMovementsFullHealthCheck')
  @Tags('movements')
  public getMovementsFullHealthCheck() {
    return BaseHealthCheck.getDeepHealthCheck(['auth0']);
  }
}
