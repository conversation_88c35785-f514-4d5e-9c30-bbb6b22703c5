export type DmsId = 'inventory' | 'shipping';

// DMS source group mappings
export const DMS_CONFIG = {
  inventory: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10'],
  shipping: ['11', '12'],
} as const;

export function getSourceGroupNames(dmsId: DmsId): string[] {
  return [...DMS_CONFIG[dmsId]];
}

export function isValidDmsId(dmsId: string): dmsId is DmsId {
  return dmsId in DMS_CONFIG;
}

export function getAllDmsIds(): DmsId[] {
  return Object.keys(DMS_CONFIG) as DmsId[];
}
