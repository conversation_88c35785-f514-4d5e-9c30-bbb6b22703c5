/**
 * Movement by hour data response structure
 */
export interface MovementByHourData {
  /** Hour of day (0-23) */
  hourOfDay: number;
  /** Count of retrieval movements */
  movementRetrieval: number;
  /** Count of storage movements */
  movementStorage: number;
  /** Count of bypass movements */
  movementByPass: number;
  /** Count of IAT movements */
  movementIat: number;
  /** Count of positioning movements */
  movementPositioning: number;
  /** Count of shuffle movements */
  movementShuffle: number;
}
