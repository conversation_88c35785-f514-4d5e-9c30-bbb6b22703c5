/**
 * Transformed movement location data response structure
 * Used for the new locationData format
 */
export type LocationValue = {
  value: number;
  name: string;
};

export interface MovementLocationData {
  locationData: Array<{
    [movementType: string]: LocationValue[];
  }>;
}

/**
 * Type definition for the scope parameter
 */
export type MovementLocationScope =
  | 'source_location'
  | 'destination_location'
  | 'source_type'
  | 'destination_type';
