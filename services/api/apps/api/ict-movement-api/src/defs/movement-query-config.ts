export interface LocationTypeRule {
  pattern: string;
  type: string;
  substringMatch?: {
    position: number;
    length: number;
    value: string;
  };
}

export interface MovementQueryConfig {
  locationTypeRules: LocationTypeRule[];
  baseWhereConditions: string[];
}

export const UNIVERSAL_MOVEMENT_CONFIG: MovementQueryConfig = {
  locationTypeRules: [
    {
      pattern: 'MSAI[0-9][0-9]..[0-9][0-9]',
      type: 'Shuttle',
      substringMatch: {position: 11, length: 2, value: 'SH'},
    },
    {
      pattern: 'MSAI[0-9][0-9]..[0-9][0-9]',
      type: 'Lift',
      substringMatch: {position: 11, length: 2, value: 'LO'},
    },
    {
      pattern: 'MSAI[0-9][0-9]..[0-9][0-9]',
      type: 'Drop Station',
      substringMatch: {position: 11, length: 2, value: 'DS'},
    },
    {
      pattern: 'MSAI[0-9][0-9]..[0-9][0-9]',
      type: 'Pick Station',
      substringMatch: {position: 11, length: 2, value: 'PS'},
    },
    {
      pattern: 'MSAI[0-9][0-9]..[0-9][0-9]',
      type: 'Rack Inbound',
      substringMatch: {position: 11, length: 2, value: 'RI'},
    },
    {
      pattern: 'MSAI[0-9][0-9]..[0-9][0-9]',
      type: 'Rack Outbound',
      substringMatch: {position: 11, length: 2, value: 'RO'},
    },
    {pattern: 'MSAI.*SH\\\\d{2}', type: 'Shuttle'},
    {pattern: 'MSAI.*LO\\\\d{2}', type: 'Lift'},
    {pattern: 'MSAI.*DS\\\\d{2}', type: 'Drop Station'},
    {pattern: 'MSAI.*PS\\\\d{2}$', type: 'Pick Station'},
    {pattern: 'MSAI.*RI\\\\d{2}', type: 'Rack Inbound'},
    {pattern: 'MSAI.*RO\\\\d{2}', type: 'Rack Outbound'},
    {pattern: 'MS[0-9][0-9]', type: 'Storage'},
  ],
  baseWhereConditions: [
    'movement_status_code = "OK"',
    "NOT (nullif('', shuttle_code) is null and nullif('', lift_code) is null)",
  ],
};

export function getMovementQueryConfig(): MovementQueryConfig {
  return UNIVERSAL_MOVEMENT_CONFIG;
}

export function buildDynamicSourceGroupFilter(): string {
  return `(
    (Source_Group_Name IS NOT NULL AND Source_Group_Name != '' AND Source_Group_Name IN UNNEST(@sourceGroupNames))
    OR
    (
      (Source_Group_Name IS NULL OR Source_Group_Name = '')
      AND (
        (REGEXP_CONTAINS(source_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]') AND SUBSTRING(source_location_code, 5, 2) IN UNNEST(@sourceGroupNames))
        OR
        (REGEXP_CONTAINS(source_location_code, r'MS[0-9][0-9]') AND SUBSTRING(source_location_code, 3, 2) IN UNNEST(@sourceGroupNames))
      )
    )
  )`;
}

export function buildDynamicFieldExtraction(): {
  sourceAisle: string;
  sourceLevel: string;
  destAisle: string;
  destLevel: string;
} {
  return {
    sourceAisle: `CASE
      WHEN Source_Group_Name IS NOT NULL AND Source_Group_Name != ''
        THEN Source_Group_Name
      WHEN REGEXP_CONTAINS(source_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
        THEN SUBSTRING(source_location_code, 5, 2)
      WHEN REGEXP_CONTAINS(source_location_code, r'MS[0-9][0-9]')
        THEN SUBSTRING(source_location_code, 3, 2)
      ELSE "N/A"
    END`,
    sourceLevel: `CASE
      WHEN Source_Subgroup_Name IS NOT NULL AND Source_Subgroup_Name != ''
        THEN Source_Subgroup_Name
      WHEN REGEXP_CONTAINS(source_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
        THEN SUBSTRING(source_location_code, 9, 2)
      ELSE "N/A"
    END`,
    destAisle: `CASE
      WHEN Destination_Group_Name IS NOT NULL AND Destination_Group_Name != ''
        THEN Destination_Group_Name
      WHEN REGEXP_CONTAINS(destination_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
        THEN SUBSTRING(destination_location_code, 5, 2)
      WHEN REGEXP_CONTAINS(destination_location_code, r'MS[0-9][0-9]')
        THEN SUBSTRING(destination_location_code, 3, 2)
      ELSE "N/A"
    END`,
    destLevel: `CASE
      WHEN Destination_Subgroup_Name IS NOT NULL AND Destination_Subgroup_Name != ''
        THEN Destination_Subgroup_Name
      WHEN REGEXP_CONTAINS(destination_location_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
        THEN SUBSTRING(destination_location_code, 9, 2)
      ELSE "N/A"
    END`,
  };
}

export function buildLocationTypeCaseExpression(
  fieldName: string,
  rules: LocationTypeRule[],
  defaultValue: string = '"Other"',
): string {
  const conditions = rules.map(rule => {
    if (rule.substringMatch) {
      return `WHEN REGEXP_CONTAINS(${fieldName}, r'${rule.pattern}') AND SUBSTRING(${fieldName}, ${rule.substringMatch.position}, ${rule.substringMatch.length}) = "${rule.substringMatch.value}" THEN "${rule.type}"`;
    }
    return `WHEN REGEXP_CONTAINS(${fieldName}, r'${rule.pattern}') THEN "${rule.type}"`;
  });

  return `CASE ${conditions.join(' ')} ELSE ${defaultValue} END`;
}
