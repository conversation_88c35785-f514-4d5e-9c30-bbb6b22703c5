/**
 * Movement time-series data response structure
 * Unified interface for both daily and hourly movement aggregations
 * Following the same structure as other movement endpoints
 */
export type TimeSeriesValue = {
  value: number;
  name: string;
};

export interface MovementTimeSeriesData {
  timeSeriesData: Array<{
    [movementType: string]: TimeSeriesValue[];
  }>;
}

/**
 * Granularity options for time-series movement data
 */
export type MovementTimeSeriesGranularity = 'day' | 'hour';
