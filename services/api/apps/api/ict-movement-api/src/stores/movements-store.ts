import {
  ContextService,
  DatabaseProvider,
  DiService,
  IctError,
  Pa<PERSON>atedResults,
  WinstonLog<PERSON>,
} from 'ict-api-foundations';

import {type DmsId, getSourceGroupNames} from '../defs/dms-config.ts';
import {MovementByTypeData, MovementSummaryRow} from '../defs/movement-by-type-data.ts';
import type {
  MovementLoadUnitsData,
  MovementUnitType,
} from '../defs/movement-load-units-data.ts';
import {
  MovementLocationData,
  MovementLocationScope,
} from '../defs/movement-location-data.ts';
import type {
  MovementTimeSeriesData,
  MovementTimeSeriesGranularity,
} from '../defs/movement-time-series-data.ts';

import {getMovementQueryConfig} from '../defs/movement-query-config.ts';
import {MovementQueryBuilder} from '../utils/query-builder.ts';

@DiService()
export class MovementsStore {
  constructor(
    private context: ContextService,
    private logger: WinstonLogger,
  ) {}

  get dbProvider(): DatabaseProvider {
    return this.context.dbProvider;
  }

  private extractDateTimeValue(value: unknown): string {
    if (typeof value === 'object' && value !== null && 'value' in value) {
      return (value as {value: string}).value;
    }
    return value as string;
  }

  async getMovementsSummary(
    startDate: string,
    endDate: string,
    dmsId: DmsId,
    unitType?: MovementUnitType,
    unitId?: string
  ): Promise<MovementByTypeData[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const queryConfig = getMovementQueryConfig();
    const queryBuilder = new MovementQueryBuilder(queryConfig);
    const goldMovementMultishuttleTable = bigQueryDb.getEdpFullTablePath(
      'gold_movement_multishuttle',
    );
    const sourceGroupNames = getSourceGroupNames(dmsId);
    const baseConditions = queryBuilder.getBaseWhereConditions();
    const dynamicSourceGroupFilter = queryBuilder.getDynamicSourceGroupFilter();
    const whereConditions = [
      'movement_end_timestamp_utc BETWEEN TIMESTAMP_SUB(TIMESTAMP(@startDate), INTERVAL 1 DAY) AND TIMESTAMP_ADD(TIMESTAMP(@endDate), INTERVAL 1 DAY)',
      'movement_end_date_time_local >= DATE(@startDate)',
      'movement_end_date_time_local < DATE_ADD(DATE(@endDate), INTERVAL 1 DAY)',
      ...baseConditions,
      dynamicSourceGroupFilter,
    ];

    // Add the unitId condition if provided
    if (unitId) {
      if (unitType === 'loadunit') {
        whereConditions.push('handling_unit_code = @unitId');
      } else if (unitType === 'sku') {
        whereConditions.push('item_sku = @unitId');
      }
    }

    const whereClause = whereConditions.join(' AND ');

    const sql = `
      WITH base_movements AS (
        SELECT
          TIMESTAMP_TRUNC(TIMESTAMP(movement_end_date_time_local), HOUR) as Date_Hour,
          IFNULL(movement_type_code, 'No Code Provided') as Movement_Type_Code
        FROM
          ${goldMovementMultishuttleTable}
        WHERE
          ${whereClause}
      ),
      main AS (
        SELECT
          Movement_Type_Code,
          COUNT(1) as Movements
        FROM base_movements
        GROUP BY
          Date_Hour,
          Movement_Type_Code
      ),
      -- Step 1: Aggregate the movements by type
      aggregated_counts AS (
        SELECT
          Movement_Type_Code,
          SUM(Movements) as Total_Movements
        FROM main
        GROUP BY
          Movement_Type_Code
      ),
      -- Step 2: Calculate the grand total to be used for percentages
      grand_total AS (
        SELECT
          SUM(Total_Movements) as Grand_Total_Movements
        FROM aggregated_counts
      )
      -- Step 3: Combine the individual types with the 'All' total row
      -- This creates the final table structure before formatting
      SELECT
        'All' AS movementType,
        grand_total.Grand_Total_Movements AS totalMovements,
        ROUND(100.0, 2) AS percentage
      FROM grand_total
      UNION ALL
      SELECT
        agg.Movement_Type_Code AS movementType,
        agg.Total_Movements AS totalMovements,
        ROUND(IFNULL(SAFE_DIVIDE(agg.Total_Movements, grand_total.Grand_Total_Movements), 0) * 100, 2) AS percentage
      FROM
        aggregated_counts agg,
        grand_total
      ORDER BY
        CASE
          WHEN movementType = 'All' THEN 0
          ELSE 1
        END,
        totalMovements DESC
    `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
        sourceGroupNames,
        ...(unitId ? { unitId } : {}),
      },
    };

    this.logger.info('Executing movements summary query', {
      dmsId,
      startDate,
      endDate,
      sourceGroupNames,
    });

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows || rows.length === 0) {
      this.logger.warn('No movements summary data found');
      throw IctError.noContent();
    }

    this.logger.info('getMovementsSummary results', {
      rowCount: rows.length,
    });

     return rows.map((row: MovementSummaryRow) => ({
      name: row.movementType,
      value: {
        totalMovements: row.totalMovements,
        percentage: row.percentage,
      },
    })) as MovementByTypeData[];
  }

  async getMovementsByLocation(
    startDate: string,
    endDate: string,
    dmsId: DmsId,
    scope: MovementLocationScope,
    unitType?: MovementUnitType,
    unitId?: string
  ): Promise<MovementLocationData> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const queryConfig = getMovementQueryConfig();
    const queryBuilder = new MovementQueryBuilder(queryConfig);
    const goldMovementMultishuttleTable = bigQueryDb.getEdpFullTablePath(
      'gold_movement_multishuttle',
    );
    const sourceGroupNames = getSourceGroupNames(dmsId);
    const dynamicFields = queryBuilder.getDynamicFieldExtractions();
    const {sourceTypeExpression, destTypeExpression} =
      queryBuilder.buildLocationTypeMappingExpressions();

    let selectClause: string;
    let groupByClause: string;
    let whereClause = '';

    switch (scope) {
      case 'source_location':
        selectClause = `
          (${dynamicFields.sourceAisle}) as locationAisle,
          Movement_Type_Code as movementType,
          COUNT(1) as totalMovements`;
        groupByClause = `(${dynamicFields.sourceAisle}), Movement_Type_Code`;
        break;

      case 'destination_location':
        selectClause = `
          (${dynamicFields.destAisle}) as locationAisle,
          Movement_Type_Code as movementType,
          COUNT(1) as totalMovements`;
        groupByClause = `(${dynamicFields.destAisle}), Movement_Type_Code`;
        whereClause = `AND (${dynamicFields.destAisle}) IS NOT NULL AND (${dynamicFields.destAisle}) != 'N/A'`;
        break;

      case 'source_type':
        selectClause = `
          Movement_Type_Code as movementType,
          Source_Location_Type as locationType,
          COUNT(1) as totalMovements`;
        groupByClause = 'Source_Location_Type, Movement_Type_Code';
        break;

      case 'destination_type':
        selectClause = `
          Dest_Location_Type as locationType,
          Movement_Type_Code as movementType,
          COUNT(1) as totalMovements`;
        groupByClause = 'Dest_Location_Type, Movement_Type_Code';
        break;

      default:
        throw IctError.badRequest(`Invalid scope: ${scope}`);
    }

    const baseConditions = queryBuilder.getBaseWhereConditions();
    const dynamicSourceGroupFilter = queryBuilder.getDynamicSourceGroupFilter();
    const whereConditions = [
      'movement_end_timestamp_utc BETWEEN TIMESTAMP_SUB(TIMESTAMP(@startDate), INTERVAL 1 DAY) AND TIMESTAMP_ADD(TIMESTAMP(@endDate), INTERVAL 1 DAY)',
      'movement_end_date_time_local >= DATE(@startDate)',
      'movement_end_date_time_local < DATE_ADD(DATE(@endDate), INTERVAL 1 DAY)',
      ...baseConditions,
      dynamicSourceGroupFilter,
    ];

    // Add the unitId condition if provided
    if (unitId) {
      if (unitType === 'loadunit') {
        whereConditions.push('handling_unit_code = @unitId');
      } else if (unitType === 'sku') {
        whereConditions.push('item_sku = @unitId');
      }
    }

    const whereClauseCondtions = whereConditions.join(' AND ');

    const sql = `
      WITH base_movements AS (
        SELECT
          IFNULL(movement_type_code, 'No Code Provided') as Movement_Type_Code,
          (${dynamicFields.sourceAisle}) as Source_Group_Name,
          (${dynamicFields.destAisle}) as Destination_Group_Name,
          ${sourceTypeExpression} as Source_Location_Type,
          ${destTypeExpression} as Dest_Location_Type,
          source_location_code,
          destination_location_code
        FROM
          ${goldMovementMultishuttleTable}
        WHERE
          ${whereClauseCondtions}
          ${whereClause}
      )
      SELECT
        ${selectClause}
      FROM
        base_movements
      GROUP BY
        ${groupByClause}
      ORDER BY
        -- This sorts by total movements (desc) to match existing behavior
        totalMovements DESC,
        ${scope === 'source_location' || scope === 'destination_location' ? 'locationAisle' : groupByClause.split(',')[0]}
    `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
        sourceGroupNames,
        ...(unitId ? { unitId } : {}),
      },
    };

    this.logger.info('Executing movements by location query', {
      startDate,
      endDate,
      dmsId,
      sourceGroupNames,
      scope,
    });

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows || rows.length === 0) {
      this.logger.warn('No movements by location data found', {
        startDate,
        endDate,
        dmsId,
        sourceGroupNames,
        scope,
      });
      throw IctError.noContent();
    }

    this.logger.info('getMovementsByLocation results', {
      rowCount: rows.length,
      dmsId,
      sourceGroupNames,
      scope,
    });

    // Transform the data into the required structure
    const movementTypeMap = new Map<
      string,
      Array<{value: number; name: string}>
    >();

    rows.forEach((row: Record<string, unknown>) => {
      const movementType = row.movementType as string;
      const value = row.totalMovements as number;
      // Use locationAisle for location scopes, locationType for type scopes
      const name = (row.locationAisle || row.locationType) as string;

      if (!movementTypeMap.has(movementType)) {
        movementTypeMap.set(movementType, []);
      }

      movementTypeMap.get(movementType)!.push({value, name});
    });

    // Convert map to the required object structure
    const taskTypeData = Object.fromEntries(movementTypeMap);

    return {locationData: [taskTypeData]};
  }

  async getMovementsByTimeSeries(
    startDate: string,
    endDate: string,
    dmsId: DmsId,
    granularity: MovementTimeSeriesGranularity,
    unitType?: MovementUnitType,
    unitId?: string
  ): Promise<MovementTimeSeriesData> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const queryConfig = getMovementQueryConfig();
    const queryBuilder = new MovementQueryBuilder(queryConfig);
    const goldMovementMultishuttleTable = bigQueryDb.getEdpFullTablePath(
      'gold_movement_multishuttle',
    );
    const sourceGroupNames = getSourceGroupNames(dmsId);
    let selectClause: string;
    let groupByClause: string;
    let orderByClause: string;

    switch (granularity) {
      case 'day':
        selectClause = `
          DATE(Date_Hour) as dayDate,
          Movement_Type_Code as movementType,
          COUNT(1) as totalMovements`;
        groupByClause = 'dayDate, movementType';
        orderByClause = 'dayDate, movementType';
        break;

      case 'hour':
        selectClause = `
          EXTRACT(HOUR FROM Date_Hour) as hourOfDay,
          Movement_Type_Code as movementType,
          COUNT(1) as totalMovements`;
        groupByClause = 'hourOfDay, Movement_Type_Code';
        orderByClause = 'hourOfDay, Movement_Type_Code';
        break;

      default:
        throw IctError.badRequest(`Invalid granularity: ${granularity}`);
    }

    const baseConditions = queryBuilder.getBaseWhereConditions();
    const dynamicSourceGroupFilter = queryBuilder.getDynamicSourceGroupFilter();
    const whereConditions = [
      'movement_end_timestamp_utc BETWEEN TIMESTAMP_SUB(TIMESTAMP(@startDate), INTERVAL 1 DAY) AND TIMESTAMP_ADD(TIMESTAMP(@endDate), INTERVAL 1 DAY)',
      'movement_end_date_time_local >= DATE(@startDate)',
      'movement_end_date_time_local < DATE_ADD(DATE(@endDate), INTERVAL 1 DAY)',
      ...baseConditions,
      dynamicSourceGroupFilter,
    ]

    // Add the unitId condition if provided
    if (unitId) {
      if (unitType === 'loadunit') {
        whereConditions.push('handling_unit_code = @unitId');
      } else if (unitType === 'sku') {
        whereConditions.push('item_sku = @unitId');
      }
    }

    const whereClause = whereConditions.join(' AND ');

    const sql = `
      WITH base_movements AS (
        SELECT
          TIMESTAMP_TRUNC(TIMESTAMP(movement_end_date_time_local), HOUR) as Date_Hour,
          IFNULL(movement_type_code, 'No Code Provided') as Movement_Type_Code
        FROM
          ${goldMovementMultishuttleTable}
        WHERE
          ${whereClause}
      )
      SELECT ${selectClause}
      FROM base_movements
      GROUP BY ${groupByClause}
      ORDER BY ${orderByClause}
    `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
        sourceGroupNames,
        ...(unitId ? { unitId } : {}),
      },
    };

    this.logger.info('Executing movements by time-series query', {
      granularity,
      dmsId,
      startDate,
      endDate,
    });

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows || rows.length === 0) {
      this.logger.warn('No movements by time-series data found', {
        granularity,
        dmsId,
        startDate,
        endDate,
      });
      return {timeSeriesData: [{}]};
    }

    this.logger.info('getMovementsByTimeSeries results', {
      rowCount: rows.length,
      granularity,
      dmsId,
    });

    // Transform the data into the required taskTypeData structure
    const movementTypeMap = new Map<
      string,
      Array<{value: number; name: string}>
    >();

    rows.forEach((row: Record<string, unknown>) => {
      const movementType = row.movementType as string;
      const value = row.totalMovements as number;

      let name: string;
      if (granularity === 'day') {
        const dateValue = this.extractDateTimeValue(row.dayDate);
        name = dateValue;
      } else {
        // hour granularity
        const hourOfDay = row.hourOfDay as number;
        name = `${hourOfDay}`;
      }

      if (!movementTypeMap.has(movementType)) {
        movementTypeMap.set(movementType, []);
      }

      movementTypeMap.get(movementType)!.push({value, name});
    });

    // Convert map to the required object structure
    const taskTypeData = Object.fromEntries(movementTypeMap);

    return {timeSeriesData: [taskTypeData]};
  }

  async getMovementsByLoadUnits(
    startDate: string,
    endDate: string,
    dmsId: DmsId,
    unitType: MovementUnitType,
    limit: number,
    page: number,
    unitId?: string,
  ): Promise<PaginatedResults<MovementLoadUnitsData>> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const queryConfig = getMovementQueryConfig();
    const queryBuilder = new MovementQueryBuilder(queryConfig);
    const goldMovementMultishuttleTable = bigQueryDb.getEdpFullTablePath('gold_movement_multishuttle');
    const offset = (page - 1) * limit;

    let selectClause: string;
    let groupByClause: string;
    let orderByClause: string;

    switch (unitType) {
      case 'loadunit':
        selectClause =
          'Load_Unit_Code as unitIdentifier, Movement_Type_Code as movementType, COUNT(1) as totalMovements';
        groupByClause = 'Load_Unit_Code, Movement_Type_Code';
        orderByClause = 'totalMovements DESC, unitIdentifier';
        break;
      case 'sku':
        selectClause =
          'Item_SKU as unitIdentifier, Movement_Type_Code as movementType, COUNT(1) as totalMovements';
        groupByClause = 'Item_SKU, Movement_Type_Code';
        orderByClause = 'totalMovements DESC, unitIdentifier';
        break;
      default:
        throw IctError.badRequest(`Invalid unit_type: ${unitType}`);
    }

    const sourceGroupNames = getSourceGroupNames(dmsId);
    const baseConditions = queryBuilder.getBaseWhereConditions();
    const dynamicSourceGroupFilter = queryBuilder.getDynamicSourceGroupFilter();
    const whereConditions = [
      'movement_end_timestamp_utc BETWEEN TIMESTAMP_SUB(TIMESTAMP(@startDate), INTERVAL 1 DAY) AND TIMESTAMP_ADD(TIMESTAMP(@endDate), INTERVAL 1 DAY)',
      'movement_end_date_time_local >= DATE(@startDate)',
      'movement_end_date_time_local < DATE_ADD(DATE(@endDate), INTERVAL 1 DAY)',
      ...baseConditions,
      dynamicSourceGroupFilter,
    ];

    // Add the unitId condition if provided
    if (unitId) {
      if (unitType === 'loadunit') {
        whereConditions.push('handling_unit_code = @unitId');
      } else if (unitType === 'sku') {
        whereConditions.push('item_sku = @unitId');
      }
    }

    const whereClause = whereConditions.join(' AND ');

    // Count query
    const countSql = `
      WITH base_movements AS (
        SELECT
          IFNULL(movement_type_code, 'No Code Provided') as Movement_Type_Code,
          IFNULL(CAST(handling_unit_code AS STRING), 'Null') as Load_Unit_Code,
          IFNULL(NULLIF(item_sku, ''), 'Null') as Item_SKU,
          Source_Group_Name
        FROM
          ${goldMovementMultishuttleTable}
        WHERE
          ${whereClause}
      ),
      aggregated_movements AS (
        SELECT
          ${unitType === 'loadunit' ? 'Load_Unit_Code' : 'Item_SKU'} as unit_value,
          Movement_Type_Code,
          COUNT(1) as movements
        FROM
          base_movements
        GROUP BY
          unit_value,
          Movement_Type_Code
      )
      SELECT COUNT(*) as total_count
      FROM aggregated_movements
    `;

    const countOptions = {
      query: countSql,
      params: {
        startDate,
        endDate,
        sourceGroupNames,
        ...(unitId ? { unitId } : {}),
      },
    };

    this.logger.info('Executing movements by load units count query', {
      unitType,
      dmsId,
      unitId,
    });
    const [countRows] = await bigQueryDb.executeMonitoredJob(countOptions);
    const totalItems = countRows?.[0]?.total_count || 0;

    // Data query
    const sql = `
      WITH base_movements AS (
        SELECT
          IFNULL(movement_type_code, 'No Code Provided') as Movement_Type_Code,
          IFNULL(CAST(handling_unit_code AS STRING), 'Null') as Load_Unit_Code,
          IFNULL(NULLIF(item_sku, ''), 'Null') as Item_SKU,
          Source_Group_Name
        FROM
          ${goldMovementMultishuttleTable}
        WHERE
          ${whereClause}
      ),
      aggregated_movements AS (
        SELECT
          ${selectClause}
        FROM
          base_movements
        GROUP BY
          ${groupByClause}
      )
      SELECT
        *
      FROM
        aggregated_movements
      ORDER BY
        ${orderByClause}
      LIMIT ${limit}
      OFFSET ${offset}
    `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
        sourceGroupNames,
        ...(unitId ? { unitId } : {}),
      },
    };

    this.logger.info('Executing movements by load units query', {
      unitType,
      dmsId,
      limit,
      page,
      offset,
      startDate,
      endDate,
      sourceGroupNames,
      unitId,
    });

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows || rows.length === 0) {
      this.logger.warn('No movements by load units data found', {
        unitType,
        dmsId,
        unitId,
      });

      return {
        list: {},
        totalResults: totalItems,
      };
    }

    this.logger.info('getMovementsByLoadUnits results', {
      rowCount: rows.length,
      unitType,
      totalItems,
      page,
    });

    // Transform the data into the required taskTypeData structure
    const movementTypeMap = new Map<
      string,
      Array<{value: number; name: string}>
    >();

    rows.forEach((row: Record<string, unknown>) => {
      const movementType = row.movementType as string;
      const value = row.totalMovements as number;
      const name = row.unitIdentifier as string;

      if (!movementTypeMap.has(movementType)) {
        movementTypeMap.set(movementType, []);
      }

      movementTypeMap.get(movementType)!.push({value, name});
    });

    // Convert map to the required object structure
    const taskTypeData = Object.fromEntries(movementTypeMap);

    return {
      list: taskTypeData,
      totalResults: totalItems,
    };
  }
}
