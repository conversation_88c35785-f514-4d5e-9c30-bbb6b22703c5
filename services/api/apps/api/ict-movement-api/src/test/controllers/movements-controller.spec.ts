import {expect} from 'chai';
import {Container, appSetup} from 'ict-api-foundations';
import sinon from 'sinon';
import request from 'supertest';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {MovementByTypeData} from '../../defs/movement-by-type-data.ts';
import {MovementLocationData} from '../../defs/movement-location-data.ts';
import {MovementTimeSeriesData} from '../../defs/movement-time-series-data.ts';
import {MovementsService} from '../../services/movements-service.ts';

describe('MovementsController', () => {
  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Movements Service. */
  let movementsServiceStub: sinon.SinonStubbedInstance<MovementsService>;

  const start_date = '2023-01-01T00:00:00.000Z';
  const end_date = '2023-01-02T00:00:00.000Z';
  const dms_id = 'inventory';

  afterEach(() => {
    sinon.restore();
  });

  describe('GET /movements/summary', () => {
    const url = '/movements/summary';

    describe('Error scenarios', () => {
      it('should reject request when required parameters are missing and return detailed validation errors', async () => {
        const response = await request(app).get(url);

        expect(response.status).to.equal(422);
        expect(response.body.message).to.equal('Validation Failed');
        expect(response.body.details.start_date.message).to.equal(
          "'start_date' is required",
        );
        expect(response.body.details.end_date.message).to.equal(
          "'end_date' is required",
        );
        expect(response.body.details.dms_id.message).to.equal(
          "'dms_id' is required",
        );
      });

      it('should reject request when dms_id parameter contains invalid value', async () => {
        const invalidDmsId = 'invalid_dms';
        const response = await request(app).get(url).query({
          start_date,
          end_date,
          dms_id: invalidDmsId,
        });

        expect(response.status).to.equal(422);
        expect(response.body.message).to.equal('Validation Failed');
        expect(response.body.details.dms_id.message).to.include(
          'should be one of the following',
        );
      });

      it('should reject request when date parameters have invalid ISO8601 format', async () => {
        const response = await request(app).get(url).query({
          start_date: '20-01-01',
          end_date: '01/01/2020 00:00:00',
          dms_id,
        });

        expect(response.status).to.equal(422);
        expect(response.body.message).to.equal('Validation Failed');
        expect(response.body.details.start_date.message).to.equal(
          'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss',
        );
        expect(response.body.details.end_date.message).to.equal(
          'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss',
        );
      });

      it('should reject request when start_date comes after end_date', async () => {
        const response = await request(app).get(url).query({
          start_date: '2023-01-02T00:00:00',
          end_date: '2023-01-01T00:00:00',
          dms_id,
        });

        expect(response.status).to.equal(400);
        expect(response.body.status).to.equal(400);
        expect(response.body.title).to.equal('Bad Request');
      });

      it('should handle service timeout errors gracefully', async () => {
        movementsServiceStub = sinon.createStubInstance(MovementsService);
        Container.set(MovementsService, movementsServiceStub);
        movementsServiceStub.getMovementsSummary.rejects(
          new Error('Request timeout'),
        );

        const response = await request(app).get(url).query({
          start_date,
          end_date,
          dms_id,
        });

        expect(response.status).to.equal(500);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body.status).to.equal(500);
        expect(response.body.title).to.equal('Internal Server Error');

        sinon.assert.calledWith(
          movementsServiceStub.getMovementsSummary,
          start_date,
          end_date,
          dms_id,
          undefined,
          undefined,
        );
      });

      it('should validate unit_type parameter when provided with invalid value', async () => {
        movementsServiceStub = sinon.createStubInstance(MovementsService);
        Container.set(MovementsService, movementsServiceStub);

        const response = await request(app).get(url).query({
          start_date,
          end_date,
          dms_id,
          unit_type: 'invalid_type',
        });

        expect(response.status).to.equal(422);
        expect(response.body.message).to.equal('Validation Failed');
      });
    });

    describe('Success scenarios', () => {
      beforeEach(() => {
        movementsServiceStub = sinon.createStubInstance(MovementsService);
        Container.set(MovementsService, movementsServiceStub);

        const mockSummaryData: MovementByTypeData[] = [
          { name: 'All', value: { totalMovements: 1000, percentage: 100.0 } },
          { name: 'RETRIEVAL', value: { totalMovements: 400, percentage: 40.0 } },
          { name: 'STORAGE', value: { totalMovements: 300, percentage: 30.0 } },
        ];

        movementsServiceStub.getMovementsSummary.resolves(mockSummaryData);
      });

      it('should successfully retrieve movements summary with aggregated data by type and percentages for inventory DMS', async () => {
        const response = await request(app).get(url).query({
          start_date,
          end_date,
          dms_id,
        });

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);

        // Structure validation
        expect(response.body).to.be.an('array');
        expect(response.body).to.have.lengthOf(3);

        // Content validation
        response.body.forEach((item: any) => {
          expect(item).to.have.property('name').that.is.a('string');
          expect(item.value)
            .to.have.property('totalMovements')
            .that.is.a('number')
            .greaterThan(0);
          expect(item.value)
            .to.have.property('percentage')
            .that.is.a('number')
            .within(0, 100);
        });

        // Business logic validation
        const allRecord = response.body.find((item: any) => item.name === 'All');
        expect(allRecord).to.exist;
        expect(allRecord.value.percentage).to.equal(100);
        expect(allRecord.value.totalMovements).to.equal(1000);

        sinon.assert.calledWith(
          movementsServiceStub.getMovementsSummary,
          start_date,
          end_date,
          dms_id,
          undefined, // unit_type (not provided)
          undefined, // unit_id (not provided)
        );
      });

      it('should successfully retrieve movements summary with unit-specific filtering when optional parameters are provided', async () => {
        const response = await request(app).get(url).query({
          start_date,
          end_date,
          dms_id,
          unit_type: 'loadunit',
          unit_id: 'LU123',
        });

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);

        // Structure validation
        expect(response.body).to.be.an('array');
        expect(response.body).to.have.lengthOf(3);

        // Content validation with comprehensive checks
        response.body.forEach((item: any) => {
          expect(item).to.have.property('name').that.is.a('string');
          expect(item.value)
            .to.have.property('totalMovements')
            .that.is.a('number')
            .greaterThan(0);
          expect(item.value)
            .to.have.property('percentage')
            .that.is.a('number')
            .within(0, 100);
        });

        // Verify service called with correct parameters
        sinon.assert.calledWith(
          movementsServiceStub.getMovementsSummary,
          start_date,
          end_date,
          dms_id,
          'loadunit', // unit_type provided
          'LU123', // unit_id provided
        );
      });

      it('should handle edge case with empty unit_id parameter', async () => {
        const response = await request(app).get(url).query({
          start_date,
          end_date,
          dms_id,
          unit_type: 'sku',
          unit_id: '',
        });

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body).to.be.an('array');

        sinon.assert.calledWith(
          movementsServiceStub.getMovementsSummary,
          start_date,
          end_date,
          dms_id,
          'sku',
          '',
        );
      });
    });
  });

  describe('GET /movements/location', () => {
    const url = '/movements/location';

    describe('Error scenarios', () => {
      it('should reject request when required parameters are missing', async () => {
        const response = await request(app).get(url);

        expect(response.status).to.equal(422);
        expect(response.body.message).to.equal('Validation Failed');
      });

      it('should reject request when group_by_column has invalid scope value', async () => {
        const response = await request(app).get(url).query({
          start_date,
          end_date,
          dms_id,
          group_by_column: 'invalid_scope',
        });

        expect(response.status).to.equal(422);
        expect(response.body.message).to.equal('Validation Failed');
      });
    });

    describe('Success scenarios', () => {
      beforeEach(() => {
        movementsServiceStub = sinon.createStubInstance(MovementsService);
        Container.set(MovementsService, movementsServiceStub);

        const mockLocationData: MovementLocationData = {
          locationData: [
            {
              RETRIEVAL: [{value: 150, name: '01'}],
              STORAGE: [{value: 100, name: '02'}],
            },
          ],
        };

        movementsServiceStub.getMovementsByLocation.resolves(mockLocationData);
      });

      it('should successfully retrieve movements aggregated by source location with proper data structure', async () => {
        const scope = 'source_location';
        const response = await request(app).get(url).query({
          start_date,
          end_date,
          dms_id,
          group_by_column: scope,
        });

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);

        // Structure validation
        expect(response.body).to.have.property('locationData');
        expect(response.body.locationData).to.be.an('array');
        expect(response.body.locationData).to.have.lengthOf(1);

        // Content validation
        const locationDataEntry = response.body.locationData[0];
        expect(locationDataEntry).to.have.property('RETRIEVAL');
        expect(locationDataEntry).to.have.property('STORAGE');
        expect(locationDataEntry.RETRIEVAL).to.be.an('array');
        expect(locationDataEntry.STORAGE).to.be.an('array');

        // Validate data structure of movement entries
        locationDataEntry.RETRIEVAL.forEach((item: any) => {
          expect(item).to.have.property('value').that.is.a('number');
          expect(item).to.have.property('name').that.is.a('string');
        });

        sinon.assert.calledWith(
          movementsServiceStub.getMovementsByLocation,
          start_date,
          end_date,
          dms_id,
          scope,
          undefined, // unit_type (not provided)
          undefined, // unit_id (not provided)
        );
      });

      it('should successfully retrieve movements by location with SKU-specific filtering when optional parameters are provided', async () => {
        const scope = 'source_location';
        const response = await request(app).get(url).query({
          start_date,
          end_date,
          dms_id,
          group_by_column: scope,
          unit_type: 'sku',
          unit_id: 'SKU456',
        });

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);

        // Structure validation
        expect(response.body).to.have.property('locationData');
        expect(response.body.locationData).to.be.an('array');
        expect(response.body.locationData).to.have.lengthOf(1);
        expect(response.body.locationData[0]).to.have.property('RETRIEVAL');
        expect(response.body.locationData[0]).to.have.property('STORAGE');

        sinon.assert.calledWith(
          movementsServiceStub.getMovementsByLocation,
          start_date,
          end_date,
          dms_id,
          scope,
          'sku', // unit_type provided
          'SKU456', // unit_id provided
        );
      });

      it('should handle different location scope types correctly', async () => {
        const scopes = [
          'source_location',
          'destination_location',
          'source_type',
          'destination_type',
        ];

        for (const scope of scopes) {
          const response = await request(app).get(url).query({
            start_date,
            end_date,
            dms_id,
            group_by_column: scope,
          });

          expect(response.status).to.equal(200);
          expect(response.body).to.have.property('locationData');
        }
      });
    });
  });

  describe('GET /movements/time-series', () => {
    const url = '/movements/time-series';

    describe('Error scenarios', () => {
      it('should reject request when required parameters are missing', async () => {
        const response = await request(app).get(url);

        expect(response.status).to.equal(422);
        expect(response.body.message).to.equal('Validation Failed');
      });

      it('should reject request when group_by_column has invalid granularity value', async () => {
        const response = await request(app).get(url).query({
          start_date,
          end_date,
          dms_id,
          group_by_column: 'invalid_granularity',
        });

        expect(response.status).to.equal(422);
        expect(response.body.message).to.equal('Validation Failed');
      });
    });

    describe('Success scenarios', () => {
      beforeEach(() => {
        movementsServiceStub = sinon.createStubInstance(MovementsService);
        Container.set(MovementsService, movementsServiceStub);

        const mockTimeSeriesData: MovementTimeSeriesData = {
          timeSeriesData: [
            {
              RETRIEVAL: [{value: 200, name: '2023-01-01'}],
              STORAGE: [{value: 150, name: '2023-01-01'}],
            },
          ],
        };

        movementsServiceStub.getMovementsByTimeSeries.resolves(
          mockTimeSeriesData,
        );
      });

      it('should successfully retrieve movements aggregated by day with time series data structure', async () => {
        const granularity = 'day';
        const response = await request(app).get(url).query({
          start_date,
          end_date,
          dms_id,
          group_by_column: granularity,
        });

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);

        // Structure validation
        expect(response.body).to.have.property('timeSeriesData');
        expect(response.body.timeSeriesData).to.be.an('array');
        expect(response.body.timeSeriesData).to.have.lengthOf(1);

        // Content validation
        const timeSeriesEntry = response.body.timeSeriesData[0];
        expect(timeSeriesEntry).to.have.property('RETRIEVAL');
        expect(timeSeriesEntry).to.have.property('STORAGE');
        expect(timeSeriesEntry.RETRIEVAL).to.be.an('array');
        expect(timeSeriesEntry.STORAGE).to.be.an('array');

        // Validate time series data points
        timeSeriesEntry.RETRIEVAL.forEach((item: any) => {
          expect(item).to.have.property('value').that.is.a('number');
          expect(item).to.have.property('name').that.is.a('string');
        });

        sinon.assert.calledWith(
          movementsServiceStub.getMovementsByTimeSeries,
          start_date,
          end_date,
          dms_id,
          granularity,
          undefined, // unit_type
          undefined, // unit_id
        );
      });

      it('should handle both day and hour granularity options correctly', async () => {
        const granularities = ['day', 'hour'];

        for (const granularity of granularities) {
          const response = await request(app).get(url).query({
            start_date,
            end_date,
            dms_id,
            group_by_column: granularity,
          });

          expect(response.status).to.equal(200);
          expect(response.body).to.have.property('timeSeriesData');
        }
      });
    });
  });

  describe('GET /movements/load-units', () => {
    const url = '/movements/load-units';

    describe('Error scenarios', () => {
      it('should reject request when required parameters are missing', async () => {
        const response = await request(app).get(url);

        expect(response.status).to.equal(422);
        expect(response.body.message).to.equal('Validation Failed');
      });

      it('should reject request when group_by_column has invalid unit type', async () => {
        const response = await request(app).get(url).query({
          start_date,
          end_date,
          dms_id,
          group_by_column: 'invalid_unit_type',
        });

        expect(response.status).to.equal(422);
        expect(response.body.message).to.equal('Validation Failed');
      });
    });

    describe('Success scenarios', () => {
      beforeEach(() => {
        movementsServiceStub = sinon.createStubInstance(MovementsService);
        Container.set(MovementsService, movementsServiceStub);

        const mockLoadUnitsResponse = {
          data: [
            {
              RETRIEVAL: [{value: 25, name: 'LU001'}],
              STORAGE: [{value: 15, name: 'LU002'}],
            },
          ],
          metadata: {
            page: 1,
            limit: 100,
            totalResults: 2,
          },
        };

        movementsServiceStub.getMovementsByLoadUnits.resolves(
          mockLoadUnitsResponse,
        );
      });

      it('should successfully retrieve movements aggregated by load units with pagination metadata', async () => {
        const unit_type = 'loadunit';
        const response = await request(app).get(url).query({
          start_date,
          end_date,
          dms_id,
          group_by_column: unit_type,
        });

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);

        // Structure validation
        expect(response.body).to.have.property('data');
        expect(response.body).to.have.property('metadata');
        expect(response.body.data).to.be.an('array');
        expect(response.body.data).to.have.lengthOf(1);

        // Content validation
        const loadUnitsData = response.body.data[0];
        expect(loadUnitsData).to.have.property('RETRIEVAL');
        expect(loadUnitsData).to.have.property('STORAGE');
        expect(loadUnitsData.RETRIEVAL).to.be.an('array');
        expect(loadUnitsData.STORAGE).to.be.an('array');

        // Validate load unit entries
        loadUnitsData.RETRIEVAL.forEach((item: any) => {
          expect(item).to.have.property('value').that.is.a('number');
          expect(item).to.have.property('name').that.is.a('string');
        });

        // Pagination metadata validation
        const metadata = response.body.metadata;
        expect(metadata).to.have.property('page').that.is.a('number');
        expect(metadata).to.have.property('limit').that.is.a('number');
        expect(metadata).to.have.property('totalResults').that.is.a('number');
        expect(metadata.page).to.equal(1);
        expect(metadata.limit).to.equal(100);
        expect(metadata.totalResults).to.equal(2);

        sinon.assert.calledWith(
          movementsServiceStub.getMovementsByLoadUnits,
          start_date,
          end_date,
          dms_id,
          unit_type,
          100, // default limit
          1, // default page
          undefined, // default unitId
        );
      });

      it('should handle custom pagination parameters correctly', async () => {
        const unit_type = 'sku';
        const customLimit = 50;
        const customPage = 2;

        const response = await request(app).get(url).query({
          start_date,
          end_date,
          dms_id,
          group_by_column: unit_type,
          limit: customLimit,
          page: customPage,
          unit_id: 'TEST_SKU',
        });

        expect(response.status).to.equal(200);
        expect(response.body).to.have.property('data');
        expect(response.body).to.have.property('metadata');

        sinon.assert.calledWith(
          movementsServiceStub.getMovementsByLoadUnits,
          start_date,
          end_date,
          dms_id,
          unit_type,
          customLimit,
          customPage,
          'TEST_SKU',
        );
      });

      it('should handle edge case pagination parameters (boundary values)', async () => {
        const unit_type = 'loadunit';

        // Test with limit=1 and page=1
        const response = await request(app).get(url).query({
          start_date,
          end_date,
          dms_id,
          group_by_column: unit_type,
          limit: 1,
          page: 1,
        });

        expect(response.status).to.equal(200);

        sinon.assert.calledWith(
          movementsServiceStub.getMovementsByLoadUnits,
          start_date,
          end_date,
          dms_id,
          unit_type,
          1, // min limit
          1, // first page
          undefined,
        );
      });
    });
  });
});
