import {expect} from 'chai';
import {appSetup} from 'ict-api-foundations';
import sinon from 'sinon';
import request from 'supertest';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';

describe('MovementsHealthCheckController', () => {
  const basicHealthUrl = '/movements/healthcheck';
  const fullHealthUrl = '/movements/healthcheck/full';

  const app = appSetup(RegisterRoutes);

  afterEach(() => {
    sinon.restore();
  });

  describe('GET /movements/healthcheck', () => {
    it('should return basic health check status', async () => {
      const response = await request(app).get(basicHealthUrl);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.have.property('sha');
      expect(response.body).to.have.property('status');
      expect(typeof response.body.sha).to.equal('string');
      expect([
        'ONLINE',
        'OFFLINE',
        'NOT_IN_USE',
        'UNHEALTHY',
        'UNKOWN',
      ]).to.include(response.body.status);
    });
  });

  describe('GET /movements/healthcheck/full', () => {
    it('should return full health check status with subsystems', async () => {
      const response = await request(app).get(fullHealthUrl);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.have.property('sha');
      expect(response.body).to.have.property('status');
      expect(response.body).to.have.property('subSystems');
      expect(response.body.subSystems).to.have.property('auth0');
      expect(typeof response.body.sha).to.equal('string');
      expect([
        'ONLINE',
        'OFFLINE',
        'NOT_IN_USE',
        'UNHEALTHY',
        'UNKOWN',
      ]).to.include(response.body.status);
      expect([
        'ONLINE',
        'OFFLINE',
        'NOT_IN_USE',
        'UNHEALTHY',
        'UNKOWN',
      ]).to.include(response.body.subSystems.auth0);
    });
  });
});
