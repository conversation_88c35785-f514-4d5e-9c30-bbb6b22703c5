import type {MovementQueryConfig} from '../defs/movement-query-config.ts';
import {
  buildDynamicFieldExtraction,
  buildDynamicSourceGroupFilter,
  buildLocationTypeCaseExpression,
} from '../defs/movement-query-config.ts';

export class MovementQueryBuilder {
  constructor(private config: MovementQueryConfig) {}
  buildLocationTypeMappingExpressions(): {
    sourceTypeExpression: string;
    destTypeExpression: string;
  } {
    const sourceTypeExpression = buildLocationTypeCaseExpression(
      'source_location_code',
      this.config.locationTypeRules,
      '"Other"',
    );

    const destTypeExpression = buildLocationTypeCaseExpression(
      'destination_location_code',
      this.config.locationTypeRules,
      '"N/A"',
    );

    return {sourceTypeExpression, destTypeExpression};
  }

  getDynamicFieldExtractions() {
    return buildDynamicFieldExtraction();
  }

  getDynamicSourceGroupFilter(): string {
    return buildDynamicSourceGroupFilter();
  }
  getBaseWhereConditions(): string[] {
    return [...this.config.baseWhereConditions];
  }
}
