import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
import {RegisterRoutes} from '../../../build/routes.ts';
import {OperatorService} from '../../services/operator-service.ts';
import {AreaStatus} from '../../defs/reassign-operator-def.ts';
import {OperatorStore} from '../../stores/operator-store.ts';

describe('OperatorController', () => {
  const url: string = '/operators/areas';

  const app = appSetup(RegisterRoutes);

  const buildAreaStatus = (options?: {
    areaId?: string;
    active_operators?: number;
  }): AreaStatus => ({
    areaId: options?.areaId || '',
    active_operators: options?.active_operators || 0,
    status: 'ok',
    alert: null,
    low_rec_operators: 0,
    high_rec_operators: 0,
    max_operators: 0,
    queued_orders: 0,
    pick_rate: 0,
  });

  const fakeAreas: AreaStatus[] = [
    buildAreaStatus({areaId: 'testArea1', active_operators: 2}),
    buildAreaStatus({areaId: 'testArea2', active_operators: 2}),
  ];

  const fromAreas = [
    {area_id: 'testArea1', number_to_move: 1},
    {area_id: 'testArea2', number_to_move: 1},
  ];

  const tooManyFromAreas = [
    {area_id: 'testArea1', number_to_move: 3},
    {area_id: 'testArea2', number_to_move: 1},
  ];

  /** Stub instance for the Operator Service. */
  let operatorServiceStub: sinon.SinonStubbedInstance<OperatorService>;
  let operatorStoreStub: sinon.SinonStubbedInstance<OperatorStore>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Operator Controller Error scenarios', () => {
    it('should validate that a request body contains from_areas and to_area_id passed', async () => {
      const response = await request(app).put(url);

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details['requestBody.from_areas'].message).to.equal(
        "'from_areas' is required",
      );
      expect(response.body.details['requestBody.to_area_id'].message).to.equal(
        "'to_area_id' is required",
      );
    });

    it('should error when an invalid area_id is passed', async () => {
      operatorServiceStub = sinon.createStubInstance(OperatorService);
      operatorStoreStub = sinon.createStubInstance(OperatorStore);
      Container.set(OperatorService, operatorServiceStub);
      Container.set(OperatorStore, operatorStoreStub);
      operatorStoreStub.getAreaStatus.resolves(fakeAreas);
      operatorServiceStub.putReassignOperator.resolves();

      const response = await request(app).put(url).send({
        from_areas: fromAreas,
        to_area_id: 'fakeAreaId',
      });

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details['requestBody.to_area_id'].message).to.equal(
        "'fakeAreaId' does not exist",
      );
    });

    it('should error too many operators are taken from an area', async () => {
      operatorServiceStub = sinon.createStubInstance(OperatorService);
      operatorStoreStub = sinon.createStubInstance(OperatorStore);
      Container.set(OperatorService, operatorServiceStub);
      Container.set(OperatorStore, operatorStoreStub);
      operatorStoreStub.getAreaStatus.resolves(fakeAreas);
      operatorServiceStub.putReassignOperator.resolves();

      const response = await request(app).put(url).send({
        from_areas: tooManyFromAreas,
        to_area_id: fakeAreas[0].areaId,
      });

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details['requestBody.from_areas'].message).to.equal(
        "There aren't enough active operators in the testArea1 area to move the specified amount.",
      );
    });

    it('should error when the service throws an error', async () => {
      operatorServiceStub = sinon.createStubInstance(OperatorService);
      operatorStoreStub = sinon.createStubInstance(OperatorStore);
      Container.set(OperatorService, operatorServiceStub);
      Container.set(OperatorStore, operatorStoreStub);
      operatorStoreStub.getAreaStatus.resolves(fakeAreas);
      operatorServiceStub.putReassignOperator.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app).put(url).send({
        from_areas: fromAreas,
        to_area_id: fakeAreas[0].areaId,
      });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        operatorServiceStub.putReassignOperator,
        fakeAreas,
        fromAreas,
        fakeAreas[0].areaId,
      );
    });
  });
  describe('Operator Controller success scenarios', () => {
    it('should call the service and return the data', async () => {
      operatorServiceStub = sinon.createStubInstance(OperatorService);
      operatorStoreStub = sinon.createStubInstance(OperatorStore);
      Container.set(OperatorService, operatorServiceStub);
      Container.set(OperatorStore, operatorStoreStub);
      operatorStoreStub.getAreaStatus.resolves(fakeAreas);
      operatorServiceStub.putReassignOperator.resolves();

      const response = await request(app).put(url).send({
        from_areas: fromAreas,
        to_area_id: fakeAreas[0].areaId,
      });

      expect(response.status).to.equal(200);

      sinon.assert.calledWith(
        operatorServiceStub.putReassignOperator,
        fakeAreas,
        fromAreas,
        fakeAreas[0].areaId,
      );
    });
  });
});
