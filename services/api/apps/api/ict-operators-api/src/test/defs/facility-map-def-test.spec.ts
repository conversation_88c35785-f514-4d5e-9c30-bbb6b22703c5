import {expect} from 'chai';
import {
  FacilityArea,
  FacilityMapStatusDefinition,
} from '../../defs/facility-map-def.ts';

describe('FacilityMapDef', () => {
  const testObjectPropertyType = (
    object: object,
    property: {type: string; name: string},
  ) => {
    it(`should have a ${property.type} property named "${property.name}"`, () => {
      const key = property.name as keyof object;
      expect(object[key]).not.to.be.undefined;
      if (property.type === 'array') {
        expect(Array.isArray(object[key])).to.be.true;
      } else {
        expect(typeof object[key]).to.be.equal(property.type);
      }
    });
  };

  describe('FacilityArea', () => {
    const testFacilityArea: FacilityArea = {
      active_operators: 1,
      message: 'alert',
      high_rec_operators: 3,
      id: 'id',
      identifier: 'Workstation 1',
      low_rec_operators: 2,
      max_operators: 4,
      name: 'name',
      pick_rate: 5,
      status: 'ok',
    };
    [
      {name: 'active_operators', type: 'number'},
      {name: 'message', type: 'string'},
      {name: 'high_rec_operators', type: 'number'},
      {name: 'id', type: 'string'},
      {name: 'identifier', type: 'string'},
      {name: 'low_rec_operators', type: 'number'},
      {name: 'max_operators', type: 'number'},
      {name: 'name', type: 'string'},
      {name: 'pick_rate', type: 'number'},
      {name: 'status', type: 'string'},
    ].forEach(property => testObjectPropertyType(testFacilityArea, property));
  });

  describe('FacilityMapStatusDefinition', () => {
    const testFacilityMapStatusDefinition: FacilityMapStatusDefinition = {
      areas: [],
      map_image_url: 'mapImageUrl',
      title: 'title',
    };
    [
      {name: 'areas', type: 'array'},
      {name: 'map_image_url', type: 'string'},
      {name: 'title', type: 'string'},
    ].forEach(property =>
      testObjectPropertyType(testFacilityMapStatusDefinition, property),
    );
  });
});
