import {expect} from 'chai';
import {AreaStatus, FromArea} from '../../defs/reassign-operator-def.ts';

describe('ReassignOperatorDef', () => {
  const testObjectPropertyType = (
    object: object,
    property: {type: string; name: string},
  ) => {
    it(`should have a ${property.type} property named "${property.name}"`, () => {
      const key = property.name as keyof object;
      expect(object[key]).not.to.be.undefined;
      expect(typeof object[key]).to.be.equal(property.type);
    });
  };

  describe('FromArea', () => {
    const testFromArea: FromArea = {
      area_id: 'area_id',
      number_to_move: 25,
    };
    [
      {name: 'area_id', type: 'string'},
      {name: 'number_to_move', type: 'number'},
    ].forEach(property => testObjectPropertyType(testFromArea, property));
  });

  describe('AreaStatus', () => {
    const testAreaStatus: AreaStatus = {
      active_operators: 1,
      areaId: 'areaId',
      alert: 'alert',
      high_rec_operators: 3,
      low_rec_operators: 4,
      max_operators: 5,
      pick_rate: 6,
      queued_orders: 7,
      status: 'ok',
    };
    [
      {name: 'active_operators', type: 'number'},
      {name: 'areaId', type: 'string'},
      {name: 'alert', type: 'string'},
      {name: 'high_rec_operators', type: 'number'},
      {name: 'low_rec_operators', type: 'number'},
      {name: 'max_operators', type: 'number'},
      {name: 'pick_rate', type: 'number'},
      {name: 'queued_orders', type: 'number'},
      {name: 'status', type: 'string'},
    ].forEach(property => testObjectPropertyType(testAreaStatus, property));
  });
});
