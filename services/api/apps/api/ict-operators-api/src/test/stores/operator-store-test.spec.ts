import {expect} from 'chai';
import sinon, {SinonStub} from 'sinon';
import {
  BigQueryDatabase,
  ConfigStore,
  ContextService,
  DatabaseProvider,
  IctError,
} from 'ict-api-foundations';
import {BigQuery} from '@google-cloud/bigquery';
import {AreaStatus} from '../../defs/reassign-operator-def.ts';
import {OperatorStore} from '../../stores/operator-store.ts';

// TODO: Test connection to bigquery with a fake database
describe('OperatorStore', () => {
  let operatorStore: OperatorStore;
  let configStore: ConfigStore;
  let queryStub: sinon.SinonStubbedInstance<BigQuery>;
  let dbProvider: DatabaseProvider;
  let contextService: ContextService;
  let executeMonitoredJobStub: SinonStub;
  let settingStub: sinon.SinonStub;

  beforeEach(() => {
    // Create a BigQuery client
    queryStub = sinon.createStubInstance(BigQuery);
    dbProvider = new DatabaseProvider();
    const bqDatabase = new BigQueryDatabase(queryStub, 'testDataset');
    dbProvider.set(bqDatabase.getType(), bqDatabase);

    contextService = new ContextService();
    sinon.stub(contextService, 'dbProvider').get(() => dbProvider);
    executeMonitoredJobStub = sinon.stub(
      dbProvider.bigQuery,
      'executeMonitoredJob',
    );
    configStore = new ConfigStore(contextService);
    operatorStore = new OperatorStore(contextService, configStore);
    settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
    settingStub.resolves(undefined);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getAreaStatus', () => {
    it('Should return an array of AreaStatus for valid areaIds', async () => {
      // Prepare test
      const areaIds = ['area1', 'area2'];
      const mockResponse: AreaStatus[] = [
        {
          areaId: 'area1',
          alert: '',
          status: 'test',
          active_operators: 5,
          low_rec_operators: 3,
          high_rec_operators: 10,
          max_operators: 20,
          pick_rate: 0.8,
          queued_orders: 0,
        },
        {
          areaId: 'area2',
          alert: '',
          status: 'test',
          active_operators: 4,
          low_rec_operators: 3,
          high_rec_operators: 10,
          max_operators: 20,
          pick_rate: 1.8,
          queued_orders: 0,
        },
      ];
      // Mock the query response
      executeMonitoredJobStub.resolves([mockResponse]);
      // Call the function
      const result = await operatorStore.getAreaStatus(areaIds);
      // Assertions
      expect(result).to.deep.equal(mockResponse);
    });
  });

  describe('getOperatorCountsAsync', () => {
    it('should retrieve operator counts for the specified date range', async () => {
      const startDate = '2023-03-03 11:15:00 UTC';
      const endDate = '2023-03-03 12:00:00 UTC';
      // Stub the query method to return a mock response
      const mockResponse = [[{activeOperators: 200}]]; // Adjusted to return a single object
      executeMonitoredJobStub.resolves(mockResponse);
      // Call the function and assert the results
      const operatorCount = await operatorStore.getOperatorCountsAsync(
        startDate,
        endDate,
      );

      // Assertions
      expect(operatorCount).to.be.an('object');
      expect(operatorCount)
        .to.have.property('activeOperators')
        .that.equals(200);
      expect(settingStub.called).to.be.false;
    });
  });

  describe('getOperatorChartData', () => {
    it('should call bigquery to retrieve operator chart data for the specified date range', async () => {
      const startDate = '2023-03-03 11:15:00 UTC';
      const endDate = '2023-03-03 12:00:00 UTC';
      const mockResponse = [[{data: 200}]];
      executeMonitoredJobStub.resolves(mockResponse);

      const operatorCount = await operatorStore.getOperatorChartData(
        startDate,
        endDate,
      );

      expect(operatorCount).to.be.an('array');
      expect(operatorCount).to.deep.eq(mockResponse[0]);
      expect(settingStub.called).to.be.false;
    });

    it('should throw a 204 if no data is found', async () => {
      const startDate = '2023-03-03 11:15:00 UTC';
      const endDate = '2023-03-03 12:00:00 UTC';
      const mockResponse: any = [[]];
      executeMonitoredJobStub.resolves(mockResponse);

      try {
        await operatorStore.getOperatorChartData(startDate, endDate);
      } catch (e) {
        expect(e).to.be.instanceOf(IctError);
        expect((e as IctError).statusCode).to.eq(204);
      }
    });
  });
});
