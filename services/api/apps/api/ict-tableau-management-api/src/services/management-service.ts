import {
  Container,
  ContextService,
  DiService,
  EnvironmentService,
  IctError,
  WinstonLogger,
} from 'ict-api-foundations';
import axios from 'axios';
import {AuthTicket} from '../defs/auth-ticket.ts';

@DiService()
export class ManagementService {
  constructor(
    private contextService: ContextService,
    private envService: EnvironmentService,
  ) {}

  public async retrieveTrustedTicketAsync(): Promise<AuthTicket> {
    const logger = Container.get(WinstonLogger);
    const trustedURL = `http://${this.envService.tableau.nodeIp}/trusted`;
    logger.info('Retrieving trusted ticket', {
      trustedURL,
    });
    const formData = new FormData();
    formData.append('username', this.contextService.userId);
    const tableauSite = this.contextService.organization.metadata.tableauSite;
    if (tableauSite) {
      formData.append('target_site', tableauSite);
    }
    logger.info('Form data', {
      username: this.contextService.userId,
      target_site: tableauSite,
    });

    const response = await axios.post<string>(trustedURL, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    logger.info('Response', {
      code: response.status,
      data: response.data,
    });
    if (
      response.data &&
      response.data.trim &&
      !response.data.trim().startsWith('-1')
    ) {
      return {
        userId: this.contextService.userId,
        ticket: response.data,
      };
    }
    throw IctError.unauthorized('Failed to retrieve trusted ticket.');
  }
}
