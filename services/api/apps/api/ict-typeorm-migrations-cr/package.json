{"name": "ict-typeorm-migrations-cr", "version": "1.0.0", "description": "Temporary hello world cloud run that includes running TypeORM migrations on startup.", "author": "Dematic", "license": "ISC", "type": "module", "main": "src/index.ts", "engines": {"node": ">=22.0.0"}, "dependencies": {"pg": "^8.12.0", "reflect-metadata": "^0.1.14", "typeorm": "^0.3.20"}, "scripts": {"build": "node ./esbuild.mjs", "test": "yarn run -T mocha", "test:watch": "mocha --watch", "start": "node --enable-source-maps ./build/index.cjs", "lint": "yarn run -T gts lint", "clean": "yarn run -T gts clean", "fix": "yarn run -T gts fix", "posttest": "yarn lint", "debug": "nodemon --exec \"node --inspect=0.0.0.0:9229 --loader ts-node/esm ./src/index.ts\"", "watch": "node --watch --no-warnings=ExperimentalWarning --loader ts-node/esm ./src/index.ts | yarn run -T pino-pretty"}}