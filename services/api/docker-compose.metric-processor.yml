services:
  # API Serivces - this includes all of the API services which will all
  # be run concurrently in the same container
  api:
    build:
      context: .
      dockerfile: ./Dockerfile
    volumes:
      - .:/src
      - '/src/node_modules'
      - $HOME/.config/gcloud/application_default_credentials.json:/gcp/creds.json
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/gcp/creds.json
      - REDISHOST=metric-processor-redis-1
      - REDISPORT=6379
      - POSTGRES_HOST=db
    env_file:
      - ./.env
    command: 'yarn run watch'
    networks:
      - control_tower_network
    depends_on:
      - db

  # nginx - this is the nginx server which will be used to act as a load balancer,
  # all traffic will be served on "8080" and then proxied to the API services
  nginx:
    image: nginx:latest@sha256:33e0bbc7ca9ecf108140af6288c7c9d1ecc77548cbfd3952fd8466a75edefe57
    ports:
      - '8080:80'
    networks:
      - control_tower_network
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf

  # Database - postgres database, used for configuration data for tenants and users
  db:
    image: postgres:17-alpine@sha256:1bc31b94cb037509ba25aed30f6f2903256ec3d122fcc2ae465ab988d09d3c80
    restart: always
    env_file:
      - ./.env
    networks:
      - control_tower_network
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
  cache:
    driver: local

networks:
  control_tower_network:
    external: true
