meta {
  name: GetMetricConfigSummary
  type: http
  seq: 8
}

headers {
  ict-facility-id: superior_uniform#eudoraar
}

get {
  url: {{api_host}}/config/process-flow/metric-configs
  body: none
  auth: inherit
}

params:query {
  metric_name: multishuttle_total_locations_available
  metric_id: 6a118b02-ee4d-4030-aea4-1d6ccfb1bd55
  fact_type: bin_utilization
  node_name: multishuttle
  active: true
  enabled: true
  config_type: node
}
