meta {
  name: PutMetricConfig
  type: http
  seq: 13
}

put {
  url: {{api_host}}/config/process-flow/metric-config
  body: json
  auth: inherit
}

headers {
  Content-Type: application/json
  ict-facility-id: superior_uniform#eudoraar
}

body:json {
  // Node Metric Configuration Example
  {
    "metricConfigName": "test_node_metric",
    "configType": "node",
    "views": ["facility", "multishuttle"],
    "matchConditions": {
      "eventType": "location_update",
      "area": "multishuttle"
    },
    "graphOperation": "area_node",
    "redisParams": {
      "rowHash": "{rowHash}"
    },
    "label": "Area",
    "parentNodes": ["facility"],
    "factType": "bin_utilization",
    "sourceSystem": "diq",
    "displayName": "Test Node Metric",
    "description": "Example metric configuration for testing",
    "acceptableLowRange": 0,
    "acceptableHighRange": 100,
    "enabled": true,
    "active": true,
    "isCustom": true,
    "nodeName": "test_node",
    "metricType": "stockTime",
    "timeWindow": "60m_set",
    "aggregation": "sum",
    "redisOperation": "event_set",
    "metricUnits": "/hr"
  }

  // Uncomment one of the examples below to test different config types:

  // Inbound Edge Metric Configuration Example
  /*
  {
    "metricConfigName": "test_inbound_edge_metric",
    "configType": "inbound-edge",
    "views": ["facility", "multishuttle"],
    "matchConditions": {
      "eventType": "handling_unit_arrival",
      "area": "multishuttle"
    },
    "graphOperation": "area_node",
    "redisParams": {
      "rowHash": "{rowHash}"
    },
    "factType": "throughput",
    "sourceSystem": "diq",
    "displayName": "Test Inbound Edge Metric",
    "description": "Example inbound edge metric configuration",
    "enabled": true,
    "active": true,
    "isCustom": true,
    "huId": "handlingUnitCode",
    "inboundArea": "multishuttle",
    "redisOperation": "event_set",
    "metricUnits": "units/hr",
    "inboundParentNodes": ["facility"]
  }
  */

  // Outbound Edge Metric Configuration Example
  /*
  {
    "metricConfigName": "test_outbound_edge_metric",
    "configType": "outbound-edge",
    "views": ["facility", "multishuttle"],
    "matchConditions": {
      "eventType": "handling_unit_departure",
      "area": "multishuttle"
    },
    "factType": "throughput",
    "sourceSystem": "diq",
    "displayName": "Test Outbound Edge Metric",
    "description": "Example outbound edge metric configuration",
    "enabled": true,
    "active": true,
    "isCustom": true,
    "outboundArea": "multishuttle",
    "huId": "handlingUnitCode",
    "units": "handlingUnit",
    "outboundParentNodes": ["facility"]
  }
  */

  // Complete Edge Metric Configuration Example
  /*
  {
    "metricConfigName": "test_complete_edge_metric",
    "configType": "complete-edge",
    "views": ["facility"],
    "matchConditions": {
      "eventType": "handling_unit_transfer"
    },
    "graphOperation": "area_node",
    "redisOperation": "event_set",
    "factType": "throughput",
    "sourceSystem": "diq",
    "displayName": "Test Complete Edge Metric",
    "description": "Example complete edge metric configuration",
    "enabled": true,
    "active": true,
    "isCustom": true,
    "inboundArea": "receiving",
    "outboundArea": "multishuttle",
    "outboundNodeLabel": "Area",
    "inboundParentNodes": ["facility"],
    "outboundParentNodes": ["facility"],
    "metricUnits": "units/hr"
  }
  */
}