import httpContext from 'express-http-context';
import {FacilityMap} from '@ict/sdk-foundations/types';
import {DiService} from '../di/type-di.ts';
import {DatabaseProvider} from '../db/database-provider.ts';
import {Organization} from '../defs/organization-def.ts';

/**
 * The ContextService wraps `express-http-context` and provides getters and
 * setters to all context data.
 */
@DiService()
export class ContextService {
  private readonly REQUEST_ID_KEY = 'requestId';
  private readonly DB_PROVIDER_KEY = 'dbProvider';
  private readonly USER_KEY = 'user';
  private readonly USER_EMAIL = 'userEmail';
  private readonly USER_ROLES = 'userRoles';
  private readonly ORGANIZATION_KEY = 'organization';
  private readonly TABLE_NAMES = 'tableNames';
  private readonly URL_KEY = 'url';
  private readonly GCP_PROJECT_ID = 'gcp_project_id';
  private readonly SELECTED_FACILITY_ID_KEY = 'selectedFacilityId';
  private readonly DATASET_ID_KEY = 'datasetId';
  private readonly FACILITY_MAPS_KEY = 'facilityMaps';
  private readonly DATABASE_ID_KEY = 'databaseId';
  private readonly AIML_DATASET_ID_KEY = 'aimlDatasetId';

  public set requestId(value: string) {
    httpContext.set(this.REQUEST_ID_KEY, value);
  }

  public get requestId(): string {
    return httpContext.get(this.REQUEST_ID_KEY);
  }

  public set url(value: string) {
    httpContext.set(this.URL_KEY, value);
  }

  public get url(): string {
    return httpContext.get(this.URL_KEY);
  }

  public set dbProvider(value: DatabaseProvider) {
    httpContext.set(this.DB_PROVIDER_KEY, value);
  }

  public get dbProvider(): DatabaseProvider {
    return httpContext.get(this.DB_PROVIDER_KEY);
  }

  public set organization(value: Organization) {
    httpContext.set(this.ORGANIZATION_KEY, value);
  }

  public get organization(): Organization {
    return httpContext.get(this.ORGANIZATION_KEY);
  }

  public set userId(value: string | null) {
    httpContext.set(this.USER_KEY, value);
  }

  public get userId(): string {
    return httpContext.get(this.USER_KEY);
  }

  public set userEmail(value: string | null) {
    httpContext.set(this.USER_EMAIL, value);
  }

  public get userEmail(): string {
    return httpContext.get(this.USER_EMAIL);
  }

  public get userRoles(): string[] {
    return httpContext.get(this.USER_ROLES);
  }

  public set userRoles(value: string[]) {
    httpContext.set(this.USER_ROLES, value);
  }

  public addTableName(tableName: string) {
    const currentTableName = httpContext.get(this.TABLE_NAMES) || '';

    if (currentTableName === '') {
      httpContext.set(this.TABLE_NAMES, tableName);
      return;
    }

    httpContext.set(this.TABLE_NAMES, `${currentTableName},${tableName}`);
  }

  public get tableNames(): string[] {
    return httpContext.get(this.TABLE_NAMES);
  }

  public set gcpProjectId(value: string | null) {
    httpContext.set(this.GCP_PROJECT_ID, value);
  }

  public get gcpProjectId(): string {
    return httpContext.get(this.GCP_PROJECT_ID);
  }

  public set selectedFacilityId(value: string | null) {
    httpContext.set(this.SELECTED_FACILITY_ID_KEY, value);
  }

  public get selectedFacilityId(): string | null {
    return httpContext.get(this.SELECTED_FACILITY_ID_KEY);
  }

  public set datasetId(value: string) {
    httpContext.set(this.DATASET_ID_KEY, value);
  }

  public get datasetId(): string {
    return httpContext.get(this.DATASET_ID_KEY);
  }

  public set facilityMaps(value: FacilityMap[]) {
    httpContext.set(this.FACILITY_MAPS_KEY, value);
  }

  public get facilityMaps(): FacilityMap[] {
    return httpContext.get(this.FACILITY_MAPS_KEY);
  }

  public set databaseId(value: string) {
    httpContext.set(this.DATABASE_ID_KEY, value);
  }

  public get databaseId(): string {
    return httpContext.get(this.DATABASE_ID_KEY);
  }

  public set aimlDatasetId(value: string | null) {
    httpContext.set(this.AIML_DATASET_ID_KEY, value);
  }

  public get aimlDatasetId(): string | null {
    return httpContext.get(this.AIML_DATASET_ID_KEY);
  }
}
