import express from 'express';
import {ApiMiddleware} from 'ict-api-foundations';

export function appSetup(
  registerRoutes: (app: express.Express) => void,
): express.Express {
  process.env.UNIT_TEST = 'true';
  const app = express();

  // Setup app for supertest
  app.use(
    express.urlencoded({
      extended: true,
    }),
  );
  app.use(express.json());
  registerRoutes(app);

  ApiMiddleware.applyErrorMiddlewares(app);

  return app;
}
