import api from '@opentelemetry/api';
import {ContextService} from '../context/context-service.ts';
import {redact} from './log-redact.ts';

// Map of npm output levels to Cloud Logging levels.
// See https://github.com/winstonjs/winston#logging-levels for more info.
const NPM_LEVEL_NAME_TO_CODE: {[name: string]: number} = {
  error: 3,
  warn: 4,
  info: 6,
  http: 6,
  verbose: 7,
  debug: 7,
  silly: 7,
};

declare enum _Severity {
  emergency = 0,
  alert = 1,
  critical = 2,
  error = 3,
  warning = 4,
  notice = 5,
  info = 6,
  debug = 7,
}
type SeverityNames = keyof typeof _Severity;

// Map of Cloud Logging levels.
const CLOUD_LOGGING_LEVEL_CODE_TO_NAME: {
  [key: number]: SeverityNames;
} = {
  0: 'emergency',
  1: 'alert',
  2: 'critical',
  3: 'error',
  4: 'warning',
  5: 'notice',
  6: 'info',
  7: 'debug',
};

export const logFormat =
  (contextService: ContextService) =>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ({level, message, ...metadata}: any) => {
    const updatedMetadata = {...metadata};
    const normalizedLevel = level?.toLowerCase() || level;

    const log: Record<string, unknown> = {
      timestamp: updatedMetadata.timestamp || new Date().toISOString(),
      level: normalizedLevel,
      requestId: contextService.requestId,
      message,
    };

    // If the metadata passed in includes an error object, log it with the stack trace
    // according to DataDog standards:
    //   https://docs.datadoghq.com/logs/error_tracking/backend/?tab=serilog
    const error = updatedMetadata.err || updatedMetadata.error;
    if (error) {
      log.error = {
        message: error?.message,
        stack: error?.stack,
      };

      // Remove so that we don't include the error in the log twice
      delete updatedMetadata.err;
      delete updatedMetadata.error;
    }

    // For GCP logging, we need to use the severity field instead of level
    if (NPM_LEVEL_NAME_TO_CODE[normalizedLevel] === undefined) {
      console.log(`unknown log level: ${normalizedLevel}`);
      log.message += ` - unknown log level: ${normalizedLevel}`;
      log.level = 'error';
    }

    // two-step translation
    const levelCode = NPM_LEVEL_NAME_TO_CODE[normalizedLevel];
    log.severity = CLOUD_LOGGING_LEVEL_CODE_TO_NAME[levelCode];

    const current_span = api.trace.getSpan(api.context.active());

    log.details =
      Object.keys(updatedMetadata).length > 0
        ? {...updatedMetadata}
        : undefined;
    if (contextService.gcpProjectId && current_span) {
      log['logging.googleapis.com/spanId'] = current_span.spanContext().spanId;
      // should be in the format projects/my-trace-project/traces/12345
      log['logging.googleapis.com/trace'] =
        `projects/${contextService.gcpProjectId}/traces/${current_span.spanContext().traceId}`;
      log['logging.googleapis.com/trace_sampled'] = true;
    }

    // Return the log message with sensitive info redacted
    return redact(log) as string;
  };
