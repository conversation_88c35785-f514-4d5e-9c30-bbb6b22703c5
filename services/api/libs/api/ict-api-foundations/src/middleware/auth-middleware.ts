import express from 'express';
import {auth} from 'express-oauth2-jwt-bearer';
import {DiService} from '../di/type-di.ts';
import {<PERSON><PERSON>og<PERSON>} from '../log/winston-logger.ts';
import {EnvironmentService} from '../services/environment-service.ts';

/**
 * This middleware wraps the Auth0 express-oauth-jwt-bearer middleware
 * and validates that the passed in token is valid. It also adds some
 * metadata about the user to "request.auth".
 */
@DiService()
export class AuthMiddleware {
  /**
   * Note that we don't want to invoke auth() every call because it creates
   * a new instance and prevents caching. Create it once and reuse per request.
   */
  private authFunction;

  constructor(
    private logger: WinstonLogger,
    private envService: EnvironmentService,
  ) {
    this.authFunction = auth({
      cacheMaxAge: 600000,
      timeoutDuration: 10000,
      audience: this.envService.app.authAudience ?? '',
      issuerBaseURL: `https://${this.envService.app.authDomain ?? ''}/`,
      tokenSigningAlg: 'RS256',
    });
  }

  public use = (
    req: express.Request,
    res: express.Response,
    next: express.NextFunction,
  ) => {
    if (req.path.includes('/healthcheck')) {
      this.logger.info('Skipping auth for health check');
      next();
    } else {
      return this.authFunction(req, res, next);
    }
  };
}
