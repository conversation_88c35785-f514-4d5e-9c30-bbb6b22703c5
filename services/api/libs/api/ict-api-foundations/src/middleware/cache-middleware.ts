import {NextFunction, Request, Response} from 'express';
import {EnvironmentService} from 'ict-api-foundations';
import {RedisClient} from '../cache/redis-client.ts';
import {ContextService} from '../context/context-service.ts';
import {Container, DiService} from '../di/type-di.ts';
import {WinstonLogger} from '../log/winston-logger.ts';

/**
 * Express Middleware that caches GET requests in Redis. The cache key is the Request URL
 */
@DiService()
export class CacheMiddleware {
  public static DEFAULT_CACHE_MINUTES = Number(
    process.env.DEFAULT_CACHE_MINUTES || 3,
  );

  public static use = (config: CacheConfig) => {
    const redisClient = Container.get(RedisClient);
    const logger = Container.get(WinstonLogger);
    const contextService = Container.get(ContextService);
    const envService = Container.get(EnvironmentService);

    return async (req: Request, res: Response, next: NextFunction) => {
      // Only cache GET requests and if Red<PERSON> is ready. Otherwise the
      // middleware and requests will continue as normal (without a cache)
      if (req.method !== 'GET' || !redisClient.isReady()) {
        return next();
      }

      // Cache Key is tenant:request_url - this ensures uniqueness across
      // tenants and requests
      const tenantId = contextService.datasetId;
      const cacheKey = `${tenantId}:${req.originalUrl}`;

      try {
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
          const ttl = await redisClient.ttl(cacheKey);
          // DO we need this?
          // logger.info('Cache hit', {cacheKey, ttl});
          const {body, status, contentType, tableNames} =
            JSON.parse(cachedData);
          res.type(contentType);
          res.setHeader('X-Cache-TTL', ttl.toString());
          if (!envService.app.isProd)
            res.setHeader('X-Table-Names', tableNames);
          res.status(status);
          return res.send(body);
        }
      } catch (e) {
        const error = e as Error;
        logger.error('Redis read error, skipping cache:', error);
      }

      const originalSend = res.send;

      // Override the `res.send` method to cache the response body before sending it.
      res.send = (body?: unknown) => {
        const contentType = res.get('Content-Type');
        const ttl = config.minutes * 60;

        // DO we need this?
        // logger.info('Cache miss', {cacheKey, ttl});

        const cachedBody: string = JSON.stringify({
          body,
          status: res.statusCode,
          contentType,
          ...(!envService.app.isProd
            ? {tableNames: contextService.tableNames}
            : {}),
        });

        redisClient.setEx(cacheKey, ttl, cachedBody).catch(e => {
          const error = e as Error;
          logger.error('Redis read error, skipping cache:', error);
        });
        res.setHeader('X-Cache-TTL', ttl.toString());
        return originalSend.call(res, body);
      };

      next();
    };
  };
}

export type CacheConfig = {
  /**
   * Cache TTL in minutes
   */
  minutes: number;
};
