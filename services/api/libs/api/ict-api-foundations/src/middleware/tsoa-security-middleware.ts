import * as express from 'express';
import {Container} from '../di/type-di.ts';
import {ContextService} from '../context/context-service.ts';
import {IctError} from '../errors/ict-error.ts';
import {TsoaSecurityAllRoles, TsoaSecurityAnyRoles} from '../roles/roles.ts';

export async function expressAuthentication(
  request: express.Request,
  securityName: string,
  scopes?: string[],
): Promise<unknown> {
  const context = Container.get(ContextService);

  switch (securityName) {
    case TsoaSecurityAllRoles: {
      if (scopes) {
        for (const scope of scopes) {
          if (!context.userRoles?.includes(scope)) {
            throw IctError.forbidden();
          }
        }
      }
      break;
    }
    case TsoaSecurityAnyRoles: {
      if (scopes) {
        for (const scope of scopes) {
          if (context.userRoles?.includes(scope)) {
            return;
          }
        }
        throw IctError.forbidden();
      }
      break;
    }
    default:
      break;
  }

  // need this as tsoa allows for values to be resolved from this promise as well
  return;
}
