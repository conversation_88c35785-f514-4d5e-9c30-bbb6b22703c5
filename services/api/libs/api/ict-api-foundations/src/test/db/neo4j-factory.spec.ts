import {expect} from 'chai';
import sinon from 'sinon';
import neo4j, {Driver} from 'neo4j-driver';
import Neo4jFactory from '../../db/neo4j-factory.ts';
import {EnvironmentService} from '../../services/environment-service.ts';
import {GCPSecretManager} from '../../secrets/gcp-secret-manager.ts';
import {Neo4jDatabase} from '../../db/neo4j.ts';
import {WinstonLogger} from '../../log/winston-logger.ts';

describe('Neo4jFactory', () => {
  let secretManager: sinon.SinonStubbedInstance<GCPSecretManager>;
  let envService: sinon.SinonStubbedInstance<EnvironmentService>;
  let factory: Neo4jFactory;
  let mockLogger: sinon.SinonStubbedInstance<WinstonLogger>;

  const mockSecret = JSON.stringify({
    NEO4J_URI: 'bolt://localhost:7687',
    NEO4J_USERNAME: 'neo4j',
    NEO4J_PASSWORD: 'password',
  });

  beforeEach(() => {
    // stub the "info" method of mockLogger
    mockLogger = sinon.createStubInstance(WinstonLogger);
    mockLogger.info.resolves();
    secretManager = sinon.createStubInstance(GCPSecretManager);
    envService = new EnvironmentService();
    sinon.stub(envService, 'apiProject').get(() => {
      return {
        projectId: 'test-project',
      };
    });

    // Ensure Neo4J has no URI so we don't attempt to connect to real db in tests
    sinon.stub(envService, 'neo4j').get(() => {
      return {
        uri: undefined,
      };
    });
    factory = new Neo4jFactory(secretManager, envService, mockLogger);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should return a cached instance if it exists', async () => {
    const datasetId = 'dataset1';
    const dbInstance = sinon.createStubInstance(Neo4jDatabase);
    factory['instances'].set(datasetId, dbInstance);

    const result = await factory.getDatabase(datasetId);

    expect(result).to.equal(dbInstance);
  });

  it('should throw an error if secret values are incomplete', async () => {
    const datasetId = 'dataset1';
    secretManager.get.resolves(JSON.stringify({}));

    try {
      await factory.getDatabase(datasetId);
      expect.fail('Expected error not thrown');
    } catch (err) {
      expect((err as Error).message).to.equal(
        'Neo4j configuration is incomplete. Please check the secret values for NEO4J_URI, NEO4J_USERNAME, NEO4J_PASSWORD.',
      );
    }
  });

  it('should create and cache a new instance if not cached', async () => {
    const datasetId = 'dataset1';
    secretManager.get.resolves(mockSecret);

    const dbInstance = sinon.createStubInstance(Neo4jDatabase);

    sinon.stub(neo4j, 'driver').returns(dbInstance as unknown as Driver);
    sinon.stub(Neo4jDatabase.prototype, 'initialize').resolves();
    const result = await factory.getDatabase(datasetId);
    expect(factory['instances'].has(datasetId)).to.be.true;
    expect(factory['instances'].get(datasetId)).to.equal(result);
  });
});
