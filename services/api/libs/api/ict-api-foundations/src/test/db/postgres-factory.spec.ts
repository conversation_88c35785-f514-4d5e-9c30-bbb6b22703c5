import {expect} from 'chai';
import sinon from 'sinon';
import {DataSource, DataSourceOptions} from 'typeorm';
import {EntityTypes} from 'ict-api-schema';
import {PostgresFactory} from '../../db/postgres-factory.ts';
import {PostgresDatabase} from '../../db/postgres.ts';
import {WinstonLogger} from '../../log/winston-logger.ts';

describe('PostgresFactory', () => {
  let mockLogger: sinon.SinonStubbedInstance<WinstonLogger>;
  let factory: PostgresFactory;

  beforeEach(() => {
    mockLogger = sinon.createStubInstance(WinstonLogger);
    mockLogger.info.resolves();
    factory = new PostgresFactory(mockLogger);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should return a cached instance if it exists', async () => {
    const database = 'test-db';
    const entityType = EntityTypes.Config;
    const key = `${database}:${entityType}`;
    const dbInstance = sinon.createStubInstance(PostgresDatabase);

    factory['instances'].set(key, dbInstance);

    const result = await factory.getDatabase(database, entityType);
    expect(result).to.equal(dbInstance);
  });

  it('should create and cache a new instance if not cached', async () => {
    const database = 'test-db';
    const entityType = EntityTypes.Config;
    const key = `${database}:${entityType}`;

    const mockDatasourceOptions: DataSourceOptions = {
      type: 'postgres',
      host: 'localhost',
      port: 5432,
      username: 'test',
      password: 'test',
      database,
    };

    sinon
      .stub(PostgresDatabase, 'generateDefaultPostgresOptions')
      .resolves(mockDatasourceOptions);
    sinon.stub(DataSource.prototype, 'initialize').resolves();
    sinon
      .stub(PostgresDatabase.prototype, 'datasource')
      .get(() => new DataSource(mockDatasourceOptions));

    const result = await factory.getDatabase(database, entityType);
    expect(factory['instances'].has(key)).to.be.true;
    expect(factory['instances'].get(key)).to.equal(result);
  });
});
