import {expect} from 'chai';
import sinon from 'sinon';
import {ContextService} from '../../context/context-service.ts';
import {Container} from '../../di/type-di.ts';
import {
  DatabaseOptionTemplates,
  PgPostgresDatabaseOptions,
} from '../../db/db-types.ts';
import {
  preparePgPostgresDatabaseOptions,
  systemAvailabilityDatabase,
} from '../../db/system-availability.ts';
import {ProtectedRouteMiddleware} from '../../middleware/protected-route-middleware.ts';

describe('System Availability Database', () => {
  describe('preparePgPostgresDatabaseOptions', () => {
    beforeEach(() => {
      const contextService = new ContextService();
      sinon.stub(contextService, 'datasetId').get(() => 'testDatasetId');
      Container.set(ContextService, contextService);

      process.env.SYSTEM_AVAILABILITY_PROJECT_ID = 'testProjectId';
    });

    afterEach(() => {
      delete process.env.SYSTEM_AVAILABILITY_PROJECT_ID;
      sinon.restore();
    });

    it('should prepare secretName correctly with databaseId template', () => {
      const dbOptions: PgPostgresDatabaseOptions = {
        secretName: `${DatabaseOptionTemplates.DatasetId}_ct_availability_database_json`,
      };
      const preparedOptions = preparePgPostgresDatabaseOptions(dbOptions);
      expect(preparedOptions?.secretName).to.equal(
        'testDatasetId_ct_availability_database_json',
      );
    });

    it('should not modify secretName if it does not contain datasetId template', () => {
      const dbOptions: PgPostgresDatabaseOptions = {
        secretName: 'some_other_secret',
      };
      const preparedOptions = preparePgPostgresDatabaseOptions(dbOptions);
      expect(preparedOptions?.secretName).to.equal('some_other_secret');
    });

    it('should prepare secretProjectId correctly with projectId template', () => {
      const dbOptions: PgPostgresDatabaseOptions = {
        secretProjectId: DatabaseOptionTemplates.Env_AvailabilityProject,
      };
      const preparedOptions = preparePgPostgresDatabaseOptions(dbOptions);
      expect(preparedOptions?.secretProjectId).to.equal('testProjectId');
    });

    it('should not modify secretProjectId if it does not contain projectId template', () => {
      const dbOptions: PgPostgresDatabaseOptions = {
        secretProjectId: 'some_other_secret',
      };
      const preparedOptions = preparePgPostgresDatabaseOptions(dbOptions);
      expect(preparedOptions?.secretProjectId).to.equal('some_other_secret');
    });
  });

  describe('systemAvailabilityDatabase', () => {
    beforeEach(() => {
      const contextService = new ContextService();
      sinon.stub(contextService, 'datasetId').get(() => 'testDatasetId');
      Container.set(ContextService, contextService);

      process.env.SYSTEM_AVAILABILITY_PROJECT_ID = 'testProjectId';
    });

    afterEach(() => {
      delete process.env.SYSTEM_AVAILABILITY_PROJECT_ID;
      sinon.restore();
    });

    it('should provide correct middleware options when prepared', () => {
      const middlewareOptions = systemAvailabilityDatabase();
      expect(middlewareOptions.options).to.exist;
      expect(middlewareOptions.options?.prepareOptions).to.exist;

      let preparedOptions = ProtectedRouteMiddleware['prepareDatabaseOptions'](
        middlewareOptions.options,
      );
      if (preparedOptions?.prepareOptions) {
        preparedOptions = preparedOptions.prepareOptions(preparedOptions);
      }
      expect(preparedOptions?.database).to.equal('testDatasetId_availability');

      const pgPreparedOptions = preparedOptions as PgPostgresDatabaseOptions;
      expect(pgPreparedOptions?.secretName).to.equal(
        'testDatasetId_ct_availability_database_json',
      );
      expect(pgPreparedOptions?.secretProjectId).to.equal('testProjectId');
    });
  });
});
