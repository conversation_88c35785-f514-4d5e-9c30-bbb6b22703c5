import {expect} from 'chai';
import {ApiResponse, ApiResponseArray} from '@ict/sdk-foundations/types';

describe('Api Response ', () => {
  it('should have a "metadata" property', () => {
    const apiResponse: ApiResponse<any, any> = {
      undefined,
      metadata: null,
    };
    expect(apiResponse).to.not.have.property('data');
    expect(apiResponse).to.have.property('metadata');
  });
});

describe('Api Response Array', () => {
  it('should have a "data" property and a "metadata" property', () => {
    const apiResponseArray: ApiResponseArray<any, any> = {
      data: null,
      metadata: null,
    };
    expect(apiResponseArray).to.have.property('data');
    expect(apiResponseArray).to.have.property('metadata');
  });
});
