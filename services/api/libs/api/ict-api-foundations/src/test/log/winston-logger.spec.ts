import {expect} from 'chai';
import sinon from 'sinon';
import {<PERSON><PERSON>og<PERSON>} from '../../log/winston-logger.ts';
import {ContextService} from '../../context/context-service.ts';

describe('WinstonLogger', () => {
  let winstonLogger: WinstonLogger;
  let defaultLoggerMock: any;
  const contextService = new ContextService();

  beforeEach(() => {
    winstonLogger = new WinstonLogger(contextService);

    // Mock the defaultLogger methods
    defaultLoggerMock = {
      debug: sinon.stub(),
      info: sinon.stub(),
      warn: sinon.stub(),
      error: sinon.stub(),
    };

    // Override the defaultLogger with our mock
    (winstonLogger as any).defaultLogger = defaultLoggerMock;
  });

  describe('debug', () => {
    it('should call debug with correct arguments', () => {
      const message = 'Test debug';
      const metadata = {key: 'value'};
      winstonLogger.debug(message, metadata);
      expect(defaultLoggerMock.debug.calledWith(message, metadata)).to.be.true;
    });
  });

  describe('info', () => {
    it('should call info with correct arguments', () => {
      const message = 'Test info';
      const metadata = {key: 'value'};
      winstonLogger.info(message, metadata);
      expect(defaultLoggerMock.info.calledWith(message, metadata)).to.be.true;
    });
  });

  describe('warn', () => {
    it('should call warn with correct arguments', () => {
      const message = 'Test warn';
      const metadata = {key: 'value'};
      winstonLogger.warn(message, metadata);
      expect(defaultLoggerMock.warn.calledWith(message, metadata)).to.be.true;
    });
  });

  describe('error', () => {
    it('should call error with correct arguments', () => {
      const message = 'Test error';
      const metadata = {key: 'value'};
      winstonLogger.error(message, metadata);
      expect(defaultLoggerMock.error.calledWith(message, metadata)).to.be.true;
    });
  });
});
