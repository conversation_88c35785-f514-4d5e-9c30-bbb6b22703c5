import {expect} from 'chai';
import {Request, Response} from 'express';
import sinon from 'sinon';
import {Container} from '../../di/type-di.ts';
import {WinstonLogger} from '../../log/winston-logger.ts';
import {CacheMiddleware} from '../../middleware/cache-middleware.ts';
import {RedisClient} from '../../cache/redis-client.ts';
import {ContextService} from '../../context/context-service.ts';

describe('CacheMiddleware', () => {
  let nextFunction: sinon.SinonStub;
  let mockLogger: sinon.SinonStubbedInstance<WinstonLogger>;
  let mockRedisClient: sinon.SinonStubbedInstance<RedisClient>;

  beforeEach(() => {
    nextFunction = sinon.stub();
    mockLogger = sinon.createStubInstance(WinstonLogger);
    mockRedisClient = sinon.createStubInstance(RedisClient);

    const contextService = Container.get(ContextService);
    sinon.stub(contextService, 'datasetId').get(() => 'testDataset');

    Container.set(<PERSON><PERSON><PERSON><PERSON>, mockLogger);
    Container.set(RedisClient, mockRedisClient);
  });

  afterEach(() => {
    sinon.restore();
    Container.reset();
  });

  describe('use', () => {
    it('should return if request method is not GET', () => {
      const postReq = {
        method: 'POST',
      } as Request;
      const res = {} as Response;

      CacheMiddleware.use({minutes: 1})(postReq, res, nextFunction);

      const putReq = {
        method: 'PUT',
      } as Request;
      CacheMiddleware.use({minutes: 1})(putReq, res, nextFunction);
      expect(nextFunction.calledTwice).to.be.true;
      expect(mockRedisClient.isReady.notCalled).to.be.true;
    });

    it('should return if redis is not ready', () => {
      mockRedisClient.isReady.returns(false);
      const req = {
        method: 'GET',
      } as Request;
      const res = {} as Response;

      CacheMiddleware.use({minutes: 1})(req, res, nextFunction);
      expect(nextFunction.calledOnce).to.be.true;
    });

    // it should return the redis cached data if it exists
    it('should return the cached data if it exists', async () => {
      const req: unknown = {
        method: 'GET',
        originalUrl: 'http://example.com',
        query: {
          start_date: '2023-04-15T12:34:56.789Z',
          end_date: '2023-04-15T12:49:56.789Z',
        },
      };
      const res: any = {
        type: sinon.stub(),
        setHeader: sinon.stub(),
        status: sinon.stub(),
        send: sinon.stub(),
      };
      const cachedData = {
        body: 'cached data',
        status: 200,
        contentType: 'application/json',
        tableNames: 'pick',
      };
      const ttl = 300;
      mockRedisClient.isReady.returns(true);
      mockRedisClient.get.resolves(JSON.stringify(cachedData));
      mockRedisClient.ttl.resolves(ttl);

      await CacheMiddleware.use({minutes: 5})(
        req as Request,
        res as Response,
        nextFunction,
      );

      expect(mockRedisClient.get.calledOnce).to.be.true;
      expect(mockRedisClient.ttl.calledOnce).to.be.true;

      expect(res.type.calledOnceWith(cachedData.contentType)).to.be.true;
      expect(res.setHeader.calledTwice).to.be.true;
      expect(res.status.calledOnceWith(cachedData.status)).to.be.true;
      expect(res.send.calledOnceWith(cachedData.body)).to.be.true;
    });

    // if the cache data does not exist, it should cache it
    it('should cache the data if it does not exist', async () => {
      mockRedisClient.setEx.resolves('');

      const req: any = {
        method: 'GET',
        originalUrl: 'http://example.com',
        query: {
          start_date: '2023-04-15T12:34:56.789Z',
          end_date: '2023-04-15T12:49:56.789Z',
        },
      };

      const resSend = sinon.stub();
      const res: any = {
        get: () => 'application/json',
        type: sinon.stub(),
        setHeader: sinon.stub(),
        status: sinon.stub(),
        send: resSend,
        statusCode: 200,
      };
      const ttl = 300;
      mockRedisClient.isReady.returns(true);
      mockRedisClient.get.resolves(null);
      mockRedisClient.ttl.resolves(ttl);

      await CacheMiddleware.use({minutes: 5})(
        req as Request,
        res as Response,
        nextFunction,
      );

      // After the cache middleware runs, the send function will be modified
      // to cache the response data. Call it so that we can check the cache
      res.send('data');

      expect(resSend.calledOnce).to.be.true;
      expect(mockRedisClient.setEx.calledOnce).to.be.true;

      const cacheKey = `testDataset:${req.originalUrl}`;
      expect(
        mockRedisClient.setEx.calledOnceWith(
          cacheKey,
          ttl,
          JSON.stringify({
            body: 'data',
            status: 200,
            contentType: 'application/json',
          }),
        ),
      ).to.be.true;

      expect(res.setHeader.calledOnceWith('X-Cache-TTL', ttl.toString())).to.be
        .true;
    });
  });
});
