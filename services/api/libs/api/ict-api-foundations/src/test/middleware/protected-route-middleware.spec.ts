import {assert} from 'chai';
import sinon from 'sinon';

import express, {Request, Response} from 'express';

import {BigQueryDatabase} from '../../db/bigquery.ts';
import {DatabaseProvider} from '../../db/database-provider.ts';
import {Database} from '../../db/database.ts';
import {DatabaseOptionTemplates, DatabaseTypes} from '../../db/db-types.ts';
import {
  Container,
  ContextService,
  ProtectedRouteMiddleware,
} from '../../index.ts';

class TestDatabase extends Database {
  public getType(): string {
    return 'postgres';
  }
}

describe('ProtectedRouteMiddleware', () => {
  let reqMock: Partial<express.Request>;
  let resMock: Partial<express.Response>;
  let nextMock: express.NextFunction;
  let nextFnMock: sinon.SinonExpectation;

  beforeEach(() => {
    nextFnMock = sinon.mock();
  });
  afterEach(() => {
    sinon.restore();
  });

  describe('apiProtectedDataRoute', () => {
    let sendStatusMock: sinon.SinonExpectation;
    let dataMiddlewareMock: sinon.SinonExpectation;
    let apiCreateDbSpy = sinon.spy(sinon.fake.resolves(undefined));

    beforeEach(() => {
      apiCreateDbSpy = sinon.spy(sinon.fake.resolves(undefined));
      sinon.replace(
        ProtectedRouteMiddleware,
        <any>'createDatabasesForUser',
        apiCreateDbSpy,
      );
      sendStatusMock = sinon.mock();
      dataMiddlewareMock = sinon.mock();

      reqMock = {};
      resMock = {
        locals: {},
        sendStatus: sendStatusMock,
      };
      nextMock = nextFnMock;
    });

    afterEach(() => {
      // Restore all previous behavior that was replaced
      sinon.restore();
    });

    it('should not send unauthorized in response', async () => {
      await ProtectedRouteMiddleware.apiProtectedDataRoute(dataMiddlewareMock)(
        <Request>reqMock,
        <Response>resMock,
        nextMock,
      );
      assert.isTrue(sendStatusMock.notCalled);
      assert.isTrue(apiCreateDbSpy.calledOnce);
      assert.exists(resMock.locals?.stores);
      assert.exists(resMock.locals?.services);
      assert.isTrue(nextFnMock.calledOnce);
      assert.isTrue(dataMiddlewareMock.calledOnce);
    });
  });

  describe('apiCreateDatabases', () => {
    const createUserDBFromTypeSpy = sinon.spy(
      sinon.fake.resolves(new TestDatabase()),
    );

    beforeEach(() => {
      sinon.replace(
        ProtectedRouteMiddleware,
        <any>'createDatabaseFromTypeForUser',
        createUserDBFromTypeSpy,
      );

      reqMock = {};
      resMock = {
        locals: {},
      };
      nextMock = nextFnMock;
    });

    afterEach(() => {
      // Restore all previous behavior that was replaced
      sinon.restore();
    });

    it('test database should be available in the db provider', async () => {
      await ProtectedRouteMiddleware.createDatabasesForUser(<Response>resMock);

      const dbProvider = resMock.locals?.databases as DatabaseProvider;
      assert.exists(dbProvider);

      const testDb = dbProvider.get(
        new TestDatabase().getType(),
      ) as TestDatabase;
      assert.exists(testDb);
    });
  });

  describe('apiAuth', () => {
    const dbOptionsSpy = sinon.spy(sinon.fake.resolves(undefined));
    const createConfigDBSpy = sinon.spy(
      sinon.fake.resolves(new TestDatabase()),
    );

    beforeEach(() => {
      sinon.replace(
        ProtectedRouteMiddleware,
        <any>'getDefaultDatabaseOptionsForUserAuthAsync',
        dbOptionsSpy,
      );
      sinon.replace(
        ProtectedRouteMiddleware,
        <any>'createConfigDatabaseFromType',
        createConfigDBSpy,
      );

      reqMock = {};
      resMock = {
        locals: {},
      };
      nextMock = nextFnMock;
    });

    after(() => {
      // Restore all previous behavior that was replaced
      sinon.restore();
    });
  });

  describe('createUserDatabaseFromType', () => {
    beforeEach(() => {
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'datasetId').get(() => 'testDataset');

      reqMock = {};
      resMock = {
        locals: {},
      };
      nextMock = nextFnMock;
    });

    it('should return a valid BigQueryDatabase', async () => {
      const testDb =
        (await ProtectedRouteMiddleware.createDatabaseFromTypeForUser({
          type: DatabaseTypes.BigQuery,
        })) as BigQueryDatabase;
      assert.exists(testDb);
      assert.equal(testDb.dataset, 'testDataset');
    });
  });

  describe('prepareDatabaseOptions', () => {
    beforeEach(() => {
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'databaseId').get(() => 'testDatabaseId');
    });

    it('should replace databaseId template with actual value', () => {
      const dbOptions = ProtectedRouteMiddleware['prepareDatabaseOptions']({
        database: `${DatabaseOptionTemplates.DatabaseId}_test_suffix`,
      });
      assert.equal(dbOptions?.database, 'testDatabaseId_test_suffix');
    });

    it('should not modify database if no template is present', () => {
      const dbOptions = ProtectedRouteMiddleware['prepareDatabaseOptions']({
        database: 'test_database',
      });
      assert.equal(dbOptions?.database, 'test_database');
    });
  });
});
