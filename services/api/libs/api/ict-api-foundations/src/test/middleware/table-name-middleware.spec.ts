import {expect} from 'chai';
import {Request, Response} from 'express';
import sinon from 'sinon';
import {Container, ContextService} from '../../index.ts';
import {RequestLoggerMiddleware} from '../../middleware/request-logger-middleware.ts';
import {TableNameMiddleware} from '../../middleware/table-name-middleware.ts';

describe('TableNameMiddleare', () => {
  let middleware: RequestLoggerMiddleware;
  let mockRequest: Partial<Request>;
  let mockResponse: any;
  let nextFunction: sinon.SinonStub;

  // eslint-disable-next-line no-empty-function
  before(() => {});

  beforeEach(() => {
    mockRequest = {
      ip: '127.0.0.1',
      method: 'GET',
      originalUrl: '/test-url',
      // eslint-disable-next-line no-empty-function
      get: sinon.stub().callsFake(() => {}),
      headers: {},
    };
    mockResponse = {
      on: sinon.stub(),
      get: sinon.stub(),
      setHeader: sinon.stub(),
      getHeader: sinon.stub(),
      writeHead: sinon.stub() as never,
    };
    nextFunction = sinon.stub();

    middleware = new TableNameMiddleware();
  });

  after(() => {
    sinon.restore();
  });

  it('should write tableNames to response headers', () => {
    const mockTableNames = 'table1,table2';
    const contextService = Container.get(ContextService);
    sinon.stub(contextService, 'tableNames').get(() => mockTableNames);

    middleware.use(
      mockRequest as Request,
      mockResponse as Response,
      nextFunction,
    );

    // Call writeHead() to invoke our hook
    mockResponse.writeHead();

    expect(mockResponse.setHeader.calledWith('x-table-names', mockTableNames))
      .to.be.true;
  });
});
