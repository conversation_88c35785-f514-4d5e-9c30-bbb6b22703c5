import express from 'express';
import {ValidationChain, query} from 'express-validator';
import sinon from 'sinon';
import {expect} from 'chai';
import {ValidationMiddleware} from '../../middleware/validation-middleware.ts';
import {ValidationError} from '../../errors/validation-error.ts';

describe(`${ValidationMiddleware.name}`, () => {
  after(() => {
    sinon.reset();
  });

  describe(`${ValidationMiddleware.validate.name}`, () => {
    let reqMock: Partial<express.Request>;
    let resMock: Partial<express.Response>;
    let nextMock: sinon.SinonStub;
    let statusMock: sinon.SinonStub;
    let sendMock: sinon.SinonStub;
    let testValidations: ValidationChain[];
    let testFunctionValidations: (req: express.Request) => ValidationChain[];

    beforeEach(() => {
      statusMock = sinon.stub().returnsThis();
      sendMock = sinon.stub().returnsThis();
      nextMock = sinon.stub();

      reqMock = {
        query: {
          test: 'test',
        },
      };
      resMock = {
        send: sendMock,
        status: statusMock,
      };

      testValidations = [
        query('test').exists().withMessage('test missing from query'),
      ];
      testFunctionValidations = (_req: express.Request) => [
        query('test').exists().withMessage('test missing from query'),
      ];
    });

    /** Reusable call to ValidationMiddleware.validate */
    const callValidate = async (
      validations:
        | ValidationChain[]
        | ((req: express.Request) => ValidationChain[]),
      {req = reqMock, res = resMock, next = nextMock} = {
        req: reqMock,
        res: resMock,
        next: nextMock,
      },
    ) => {
      // await is necessary
      await ValidationMiddleware.validate(validations)(
        <express.Request>req,
        <express.Response>res,
        <express.NextFunction>next,
      );
    };

    it('should call the next function if there are no errors', async () => {
      await callValidate(testValidations);

      expect(nextMock.called).to.be.true;
    });

    it('should call next with ValidationError if there are errros', async () => {
      delete reqMock.query!.test;
      await callValidate(testValidations);
      expect(nextMock.called).to.be.true;
      expect(nextMock.firstCall.firstArg).to.be.instanceOf(ValidationError);
      expect(nextMock.firstCall.firstArg.statusCode).to.equal(400);
    });

    it('should be able to handle functions that require the request object to create validations', async () => {
      await callValidate(testFunctionValidations);
      expect(nextMock.called).to.be.true;
    });
  });
});
