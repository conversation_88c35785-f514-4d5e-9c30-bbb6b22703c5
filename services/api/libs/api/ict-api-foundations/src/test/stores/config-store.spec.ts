import {expect} from 'chai';
import {
  AllowedSettingValue,
  EntityTypes,
  FacilitySetting,
  Setting,
  SettingLog,
  TenantSetting,
  UserSetting,
} from 'ict-api-schema';
import sinon, {SinonStub} from 'sinon';
import {DataSource, Repository} from 'typeorm';
import {AppConfigSettingSource} from '@ict/sdk-foundations/types/index.ts';
import {PostgresDatabase} from '../../db/postgres.ts';
import {DatabaseProvider} from '../../db/database-provider.ts';
import {ConfigStore} from '../../stores/config-store.ts';
import {Container, ContextService} from '../../index.ts';
import {PostgresFactory} from '../../db/postgres-factory.ts';

describe('ConfigStore', () => {
  let dbProvider: DatabaseProvider;
  let postgres: PostgresDatabase;
  let datasource: sinon.SinonStubbedInstance<DataSource>;
  let configStore: ConfigStore;
  let mockPostgresFactory: sinon.SinonStubbedInstance<PostgresFactory>;

  const testSetting = new Setting();
  testSetting.id = 'testSetting';
  testSetting.dataType = 'string';
  testSetting.group = 'groupname';
  testSetting.minValue = null;
  testSetting.maxValue = null;
  testSetting.defaultValueNumber = null;
  testSetting.defaultValueString = 'test value';
  testSetting.name = 'testSetting';
  testSetting.description = 'testSetting';
  testSetting.createdAt = new Date();
  testSetting.updatedAt = new Date();
  testSetting.tags = null;
  testSetting.parentSetting = 'testParentSetting';

  const testFalseSetting = new Setting();
  testFalseSetting.id = 'testFalseSetting';
  testFalseSetting.dataType = 'boolean';
  testFalseSetting.group = 'groupname';
  testFalseSetting.minValue = null;
  testFalseSetting.maxValue = null;
  testFalseSetting.defaultValueBoolean = false;
  testFalseSetting.defaultValueNumber = null;
  testFalseSetting.defaultValueString = null;
  testFalseSetting.name = 'testFalseSetting';
  testFalseSetting.description = 'testFalseSetting';
  testFalseSetting.createdAt = new Date();
  testFalseSetting.updatedAt = new Date();
  testFalseSetting.tags = null;

  const testAllowedSettingValue: AllowedSettingValue = {
    id: 'testAllowedSettingValue1',
    settingId: testSetting.id,
    value: 'testAllowedSettingValue1',
    createdAt: new Date(),
    updatedAt: new Date(),
    setting: testSetting,
    description: 'testAllowedSettingValue1',
  };

  const testUserSetting = new UserSetting();
  testUserSetting.id = 'testUserSetting';
  testUserSetting.userId = 'testUser';
  testUserSetting.settingId = testSetting.id;
  testUserSetting.valueNumber = null;
  testUserSetting.createdAt = new Date();
  testUserSetting.updatedAt = new Date();
  testUserSetting.setting = testSetting;
  testUserSetting.allowedSettingValue = testAllowedSettingValue;
  testUserSetting.description = 'testUserSetting';

  const testUserSetting2 = new UserSetting();
  testUserSetting2.id = 'testUserSetting2';
  testUserSetting2.userId = 'testUser';
  testUserSetting2.settingId = testSetting.id;
  testUserSetting2.valueNumber = null;
  testUserSetting2.createdAt = new Date();
  testUserSetting2.updatedAt = new Date();
  testUserSetting2.setting = testSetting;
  testUserSetting2.allowedSettingValue = testAllowedSettingValue;
  testUserSetting2.description = 'testUserSetting2';

  const testFalseUserSetting = new UserSetting();
  testFalseUserSetting.id = 'testFalseUserSetting';
  testFalseUserSetting.userId = 'testUser';
  testFalseUserSetting.settingId = testFalseSetting.id;
  testFalseUserSetting.valueBoolean = false;
  testFalseUserSetting.createdAt = new Date();
  testFalseUserSetting.updatedAt = new Date();
  testFalseUserSetting.setting = testFalseSetting;
  testFalseUserSetting.description = 'testFalseUserSetting';

  const testFacilitySetting = new FacilitySetting();
  testFacilitySetting.id = 'testFacilitySetting';
  testFacilitySetting.settingId = testSetting.id;
  testFacilitySetting.valueNumber = null;
  testFacilitySetting.createdAt = new Date();
  testFacilitySetting.updatedAt = new Date();
  testFacilitySetting.setting = testSetting;
  testFacilitySetting.allowedSettingValue = testAllowedSettingValue;
  testFacilitySetting.description = 'testFacilitySetting';
  testFacilitySetting.facilityId = 'testFacility';

  const testTenantSetting = new TenantSetting();
  testTenantSetting.id = 'testTenantSetting';
  testTenantSetting.settingId = testSetting.id;
  testTenantSetting.valueNumber = null;
  testTenantSetting.createdAt = new Date();
  testTenantSetting.updatedAt = new Date();
  testTenantSetting.setting = testSetting;
  testTenantSetting.allowedSettingValue = testAllowedSettingValue;
  testTenantSetting.description = 'testTenantSetting';

  const testTenantSetting2 = new TenantSetting();
  testTenantSetting2.id = 'testTenantSetting2';
  testTenantSetting2.settingId = testSetting.id;
  testTenantSetting2.valueNumber = null;
  testTenantSetting2.createdAt = new Date();
  testTenantSetting2.updatedAt = new Date();
  testTenantSetting2.setting = testSetting;
  testTenantSetting2.allowedSettingValue = testAllowedSettingValue;
  testTenantSetting2.description = 'testTenantSetting2';

  const testFalseTenantSetting = new TenantSetting();
  testFalseTenantSetting.id = 'testFalseTenantSetting';
  testFalseTenantSetting.settingId = testFalseSetting.id;
  testFalseTenantSetting.valueBoolean = false;
  testFalseTenantSetting.createdAt = new Date();
  testFalseTenantSetting.updatedAt = new Date();
  testFalseTenantSetting.setting = testFalseSetting;
  testFalseTenantSetting.description = 'testFalseTenantSetting';

  const testSettingLog = new SettingLog();
  testSettingLog.id = 'testSettingLog';
  testSettingLog.description = 'testSettingLog';
  testSettingLog.createdAt = new Date();
  testSettingLog.updatedAt = new Date();
  testSettingLog.settingId = 'testChangedSetting';
  testSettingLog.changedBy = 'testUser';
  testSettingLog.timestamp = new Date();
  testSettingLog.source = 'test';
  testSettingLog.newValue = 'testValue';

  beforeEach(() => {
    datasource = sinon.createStubInstance(DataSource);
    postgres = new PostgresDatabase(datasource, EntityTypes.Config);

    const contextService = new ContextService();

    // Create a proper stub instance for PostgresFactory
    mockPostgresFactory = sinon.createStubInstance(PostgresFactory);
    // Manually stub the getDatabase method to return a resolved promise
    (
      mockPostgresFactory.getDatabase as sinon.SinonStub<
        [string, EntityTypes],
        Promise<PostgresDatabase>
      >
    ).resolves(postgres);

    Container.set(PostgresFactory, mockPostgresFactory as PostgresFactory);
    dbProvider = new DatabaseProvider();
    dbProvider.set(postgres.getType(), postgres);
    sinon.stub(contextService, 'dbProvider').get(() => dbProvider);
    configStore = new ConfigStore(contextService);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('findSetting', () => {
    it('should return a setting if found', async () => {
      const fakeSettingRepo = sinon.createStubInstance(Repository<Setting>);
      fakeSettingRepo.findOneBy.resolves(testSetting);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const setting = await configStore.findSetting(testSetting.id);
      expect(setting).to.deep.equal(testSetting);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.findOneBy.calledOnce).to.be.true;
    });
  });

  describe('findSettingByName', () => {
    it('should return a setting if found by name', async () => {
      const fakeSettingRepo = sinon.createStubInstance(Repository<Setting>);
      fakeSettingRepo.findOneBy.resolves(testSetting);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const setting = await configStore.findSettingByName(testSetting.name);
      expect(setting).to.deep.equal(testSetting);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.findOneBy.calledOnce).to.be.true;
    });
  });

  describe('findAllSettings', () => {
    it('should return a list of settings if found', async () => {
      const fakeSettingRepo = sinon.createStubInstance(Repository<Setting>);
      fakeSettingRepo.find.resolves([testSetting]);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const settingList = await configStore.findAllSettings();
      expect(settingList).to.deep.equal([testSetting]);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.find.calledOnce).to.be.true;
    });
  });

  describe('saveSetting', () => {
    it('should save a setting', async () => {
      const fakeSettingRepo = sinon.createStubInstance(Repository<Setting>);
      fakeSettingRepo.save.resolves(testSetting);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const setting = await configStore.saveSetting(testSetting);
      expect(setting).to.deep.equal(testSetting);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.save.calledOnce).to.be.true;
    });
  });

  describe('findAllowedValuesForSetting', () => {
    it('should find all allowed values for setting', async () => {
      const testAllowedSettingsValues: AllowedSettingValue[] = [
        testAllowedSettingValue,
        {
          id: 'testAllowedSettingValue2',
          settingId: testSetting.id,
          value: 'testAllowedSettingValue2',
          createdAt: new Date(),
          updatedAt: new Date(),
          setting: testSetting,
          description: 'testAllowedSettingValue2',
        },
      ];

      const fakeSettingRepo = sinon.createStubInstance(
        Repository<AllowedSettingValue>,
      );
      fakeSettingRepo.find.resolves(testAllowedSettingsValues);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const allowedValues = await configStore.findAllowedValuesForSetting(
        testSetting.id,
      );
      expect(allowedValues).to.deep.equal(testAllowedSettingsValues);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.find.calledOnce).to.be.true;
    });
  });

  describe('findAllowedSettingValue', () => {
    it('should return an allowed setting value if found', async () => {
      const fakeSettingRepo = sinon.createStubInstance(
        Repository<AllowedSettingValue>,
      );
      fakeSettingRepo.findOneBy.resolves(testAllowedSettingValue);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const allowedSettingValue = await configStore.findAllowedSettingValue(
        testAllowedSettingValue.id,
      );
      expect(allowedSettingValue).to.deep.equal(testAllowedSettingValue);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.findOneBy.calledOnce).to.be.true;
    });
  });

  describe('saveAllowedSettingValue', () => {
    it('should save an allowed setting value', async () => {
      const fakeSettingRepo = sinon.createStubInstance(
        Repository<AllowedSettingValue>,
      );
      fakeSettingRepo.save.resolves(testAllowedSettingValue);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const allowedSettingValue = await configStore.saveAllowedSettingValue(
        testAllowedSettingValue,
      );
      expect(allowedSettingValue).to.deep.equal(testAllowedSettingValue);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.save.calledOnce).to.be.true;
    });
  });

  describe('findUserSetting', () => {
    it('should return an allowed setting value if found', async () => {
      const fakeSettingRepo = sinon.createStubInstance(Repository<UserSetting>);
      fakeSettingRepo.findOneBy.resolves(testUserSetting);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const userSetting = await configStore.findUserSetting(
        testUserSetting.userId,
        testUserSetting.settingId,
      );
      expect(userSetting).to.deep.equal(testUserSetting);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.findOneBy.calledOnce).to.be.true;
    });
  });

  describe('findAllUserSettings', () => {
    it('should return all user settings found', async () => {
      const testUserSettings: UserSetting[] = [
        testUserSetting,
        testUserSetting2,
      ];

      const fakeSettingRepo = sinon.createStubInstance(Repository<UserSetting>);
      fakeSettingRepo.find.resolves(testUserSettings);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const userSettings = await configStore.findAllUserSettings(
        testUserSetting.userId,
      );
      expect(userSettings).to.deep.equal(testUserSettings);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.find.calledOnce).to.be.true;
    });
  });

  describe('saveUserSetting', () => {
    it('should save a user setting', async () => {
      const fakeSettingRepo = sinon.createStubInstance(Repository<UserSetting>);
      fakeSettingRepo.save.resolves(testUserSetting);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const userSetting = await configStore.saveUserSetting(testUserSetting);
      expect(userSetting).to.deep.equal(testUserSetting);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.save.calledOnce).to.be.true;
    });
  });

  describe('findTenantSetting', () => {
    it('should return a tenant setting if found', async () => {
      const fakeSettingRepo = sinon.createStubInstance(
        Repository<TenantSetting>,
      );
      fakeSettingRepo.findOneBy.resolves(testTenantSetting);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const tenantSetting = await configStore.findTenantSetting(testSetting.id);
      expect(tenantSetting).to.deep.equal(testTenantSetting);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.findOneBy.calledOnce).to.be.true;
    });
  });

  describe('findAllTenantSettings', () => {
    it('should return all tenant settings found', async () => {
      const testTenantSettings: TenantSetting[] = [
        testTenantSetting,
        testTenantSetting2,
      ];

      const fakeSettingRepo = sinon.createStubInstance(
        Repository<TenantSetting>,
      );
      fakeSettingRepo.find.resolves(testTenantSettings);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const tenantSettings = await configStore.findAllTenantSettings();
      expect(tenantSettings).to.deep.equal(testTenantSettings);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.find.calledOnce).to.be.true;
    });
  });

  describe('saveTenantSetting', () => {
    it('should save a tenant setting', async () => {
      const fakeSettingRepo = sinon.createStubInstance(Repository<UserSetting>);
      fakeSettingRepo.save.resolves(testTenantSetting);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const tenantSetting =
        await configStore.saveTenantSetting(testTenantSetting);
      expect(tenantSetting).to.deep.equal(testTenantSetting);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.save.calledOnce).to.be.true;
    });
  });

  describe('findSpecificSettingValue', () => {
    let findSettingStub: SinonStub;
    let findUserSettingStub: SinonStub;
    let findTenantSettingStub: SinonStub;

    beforeEach(() => {
      findSettingStub = sinon.stub(configStore, 'findSetting');
      findUserSettingStub = sinon.stub(configStore, 'findUserSetting');
      findTenantSettingStub = sinon.stub(configStore, 'findTenantSetting');
      sinon.reset();
    });

    after(() => {
      sinon.restore();
    });

    it('should return undefined if the setting doesnt exist', async () => {
      findSettingStub.resolves(undefined);

      const specificSetting = await configStore.findSpecificSettingValue(
        AppConfigSettingSource.default,
        'non-existent-id',
      );
      expect(specificSetting).to.be.undefined;
    });

    it('should return a default setting if one exists', async () => {
      findSettingStub.resolves(testSetting);

      const specificSetting = await configStore.findSpecificSettingValue(
        AppConfigSettingSource.default,
        testSetting.id,
      );
      expect(specificSetting).to.deep.equal({
        dataType: testSetting.dataType,
        id: testSetting.id,
        name: testSetting.name,
        group: testSetting.group,
        description: testSetting.description,
        source: AppConfigSettingSource.default,
        value: testSetting.defaultValue,
        tags: testSetting.tags,
        parentSetting: testSetting.parentSetting,
      });
    });

    it('should return a tenant stored setting if one exists', async () => {
      findSettingStub.resolves(testSetting);
      findTenantSettingStub.resolves(testTenantSetting);

      const specificSetting = await configStore.findSpecificSettingValue(
        AppConfigSettingSource.tenant,
        testSetting.id,
      );
      expect(specificSetting).to.deep.equal({
        dataType: testTenantSetting.setting.dataType,
        id: testTenantSetting.setting.id,
        name: testTenantSetting.setting.name,
        group: testTenantSetting.setting.group,
        description: testSetting.description,
        source: AppConfigSettingSource.tenant,
        value: testTenantSetting.value,
        tags: testTenantSetting.setting.tags,
        parentSetting: testSetting.parentSetting,
      });
    });

    it('should return a user stored setting if one exists', async () => {
      findSettingStub.resolves(testSetting);
      findUserSettingStub.resolves(testUserSetting);

      const specificSetting = await configStore.findSpecificSettingValue(
        AppConfigSettingSource.user,
        testSetting.id,
        undefined,
        testUserSetting.userId,
      );
      expect(specificSetting).to.deep.equal({
        dataType: testUserSetting.setting.dataType,
        id: testUserSetting.setting.id,
        group: testUserSetting.setting.group,
        description: testSetting.description,
        name: testUserSetting.setting.name,
        source: AppConfigSettingSource.user,
        value: testUserSetting.value,
        tags: testUserSetting.setting.tags,
        parentSetting: testSetting.parentSetting,
      });
    });
  });

  describe('findMostRelevantSettingForUser', () => {
    afterEach(() => {
      sinon.restore();
    });

    it('should return a default value of setting', async () => {
      sinon.stub(configStore, 'findSetting').resolves(testSetting);
      sinon.stub(configStore, 'findUserSetting').resolves(undefined);
      sinon.stub(configStore, 'findFacilitySetting').resolves(undefined);
      sinon.stub(configStore, 'findTenantSetting').resolves(undefined);

      const relevantSetting = await configStore.findMostRelevantSettingForUser(
        'testUser',
        testSetting.id,
      );
      expect(relevantSetting).to.deep.equal({
        id: testSetting.id,
        dataType: testSetting.dataType,
        name: testSetting.name,
        group: testSetting.group,
        description: testSetting.description,
        source: AppConfigSettingSource.default,
        value: testSetting.defaultValue,
        tags: testSetting.tags,
        parentSetting: testSetting.parentSetting,
      });
    });
    it('should return a tenant value', async () => {
      sinon.stub(configStore, 'findSetting').resolves(testSetting);
      sinon.stub(configStore, 'findUserSetting').resolves(undefined);
      sinon.stub(configStore, 'findFacilitySetting').resolves(undefined);
      sinon.stub(configStore, 'findTenantSetting').resolves(testTenantSetting);

      const relevantSetting = await configStore.findMostRelevantSettingForUser(
        'testUser',
        testSetting.id,
      );
      expect(relevantSetting).to.deep.equal({
        id: testSetting.id,
        dataType: testSetting.dataType,
        group: testSetting.group,
        description: testSetting.description,
        name: testSetting.name,
        source: AppConfigSettingSource.tenant,
        value: testTenantSetting.value,
        tags: testTenantSetting.setting.tags,
        parentSetting: testSetting.parentSetting,
      });
    });

    it('should return a tenant value when the value is false', async () => {
      sinon.stub(configStore, 'findSetting').resolves(testFalseSetting);
      sinon.stub(configStore, 'findUserSetting').resolves(undefined);
      sinon.stub(configStore, 'findFacilitySetting').resolves(undefined);
      sinon
        .stub(configStore, 'findTenantSetting')
        .resolves(testFalseTenantSetting);

      const relevantSetting = await configStore.findMostRelevantSettingForUser(
        'testUser',
        testFalseSetting.id,
      );
      expect(relevantSetting).to.deep.equal({
        id: testFalseSetting.id,
        dataType: testFalseSetting.dataType,
        group: testFalseSetting.group,
        description: testFalseSetting.description,
        name: testFalseSetting.name,
        source: AppConfigSettingSource.tenant,
        value: testFalseTenantSetting.value,
        tags: testFalseTenantSetting.setting.tags,
        parentSetting: testFalseSetting.parentSetting,
      });

      it('should return a facility setting', async () => {
        sinon.stub(configStore, 'findSetting').resolves(testSetting);
        sinon.stub(configStore, 'findUserSetting').resolves(undefined);
        sinon
          .stub(configStore, 'findFacilitySetting')
          .resolves(testFacilitySetting);
        sinon
          .stub(configStore, 'findTenantSetting')
          .resolves(testTenantSetting);

        const facilitySetting =
          await configStore.findMostRelevantSettingForUser(
            'testUser',
            testSetting.id,
          );
        expect(facilitySetting).to.deep.equal({
          id: testSetting.id,
          dataType: testSetting.dataType,
          group: testSetting.group,
          description: testSetting.description,
          name: testSetting.name,
          source: AppConfigSettingSource.user,
          value: testFacilitySetting.value,
          tags: testFacilitySetting.setting.tags,
          parentSetting: testSetting.parentSetting,
        });
      });
    });

    it('should return a user setting', async () => {
      sinon.stub(configStore, 'findSetting').resolves(testSetting);
      sinon.stub(configStore, 'findUserSetting').resolves(testUserSetting);
      sinon
        .stub(configStore, 'findFacilitySetting')
        .resolves(testFacilitySetting);
      sinon.stub(configStore, 'findTenantSetting').resolves(testTenantSetting);

      const relevantSetting = await configStore.findMostRelevantSettingForUser(
        'testUser',
        testSetting.id,
      );
      expect(relevantSetting).to.deep.equal({
        id: testSetting.id,
        dataType: testSetting.dataType,
        group: testSetting.group,
        description: testSetting.description,
        name: testSetting.name,
        source: AppConfigSettingSource.user,
        value: testUserSetting.value,
        tags: testUserSetting.setting.tags,
        parentSetting: testSetting.parentSetting,
      });
    });
  });

  it('should return a user setting when the value is false', async () => {
    sinon.stub(configStore, 'findSetting').resolves(testFalseSetting);
    sinon.stub(configStore, 'findUserSetting').resolves(testFalseUserSetting);
    sinon
      .stub(configStore, 'findFacilitySetting')
      .resolves(testFacilitySetting);
    sinon
      .stub(configStore, 'findTenantSetting')
      .resolves(testFalseTenantSetting);

    const relevantSetting = await configStore.findMostRelevantSettingForUser(
      'testUser',
      testFalseSetting.id,
    );
    expect(relevantSetting).to.deep.equal({
      id: testFalseSetting.id,
      dataType: testFalseSetting.dataType,
      group: testFalseSetting.group,
      description: testFalseSetting.description,
      name: testFalseSetting.name,
      source: AppConfigSettingSource.user,
      value: testFalseUserSetting.value,
      tags: testFalseUserSetting.setting.tags,
      parentSetting: testFalseSetting.parentSetting,
    });
  });

  describe('deleteSetting', () => {
    afterEach(() => {
      sinon.restore();
    });

    it('should return the correct number of records that were deleted', async () => {
      const fakeSettingRepo = sinon.createStubInstance(Repository<Setting>);

      fakeSettingRepo.findBy.resolves([testSetting]);
      fakeSettingRepo.remove.resolves([testSetting]);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const numSettingsDeleted = await configStore.deleteSetting(
        testSetting.id,
      );
      expect(numSettingsDeleted).to.equal(1);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.remove.calledOnce).to.be.true;
    });
  });

  describe('deleteTenantSetting', () => {
    afterEach(() => {
      sinon.restore();
    });

    it('should return the correct number of records that were deleted', async () => {
      const fakeSettingRepo = sinon.createStubInstance(
        Repository<TenantSetting>,
      );

      fakeSettingRepo.findBy.resolves([testTenantSetting]);
      fakeSettingRepo.remove.resolves([testTenantSetting]);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const numSettingsDeleted = await configStore.deleteTenantSetting(
        testSetting.id,
      );
      expect(numSettingsDeleted).to.equal(1);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.remove.calledOnce).to.be.true;
    });
  });

  describe('deleteUserSetting', () => {
    afterEach(() => {
      sinon.restore();
    });

    it('should return the correct number of records that were deleted', async () => {
      const fakeSettingRepo = sinon.createStubInstance(Repository<UserSetting>);

      fakeSettingRepo.findBy.resolves([testUserSetting]);
      fakeSettingRepo.remove.resolves([testUserSetting]);
      datasource.getRepository.callsFake(() => {
        return fakeSettingRepo;
      });

      const numSettingsDeleted = await configStore.deleteUserSetting(
        testSetting.id,
        'userid',
      );
      expect(numSettingsDeleted).to.equal(1);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingRepo.remove.calledOnce).to.be.true;
    });
  });

  describe('findSettingLogs', () => {
    it('should return correct number of logs', async () => {
      const fakeSettingLogRepo = sinon.createStubInstance(
        Repository<SettingLog>,
      );

      fakeSettingLogRepo.find.resolves([testSettingLog]);
      datasource.getRepository.callsFake(() => {
        return fakeSettingLogRepo;
      });

      const settingLogs = await configStore.findSettingLogs(
        'testUser',
        'testChangedSetting',
        5,
        'testFacility',
      );
      expect(settingLogs!.length).to.equal(1);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingLogRepo.find.calledOnce).to.be.true;
      expect(
        fakeSettingLogRepo.find.calledWith({
          where: [
            {settingId: 'testChangedSetting', source: 'tenant'},
            {
              settingId: 'testChangedSetting',
              source: 'facility',
              facilityId: 'testFacility',
            },
            {
              settingId: 'testChangedSetting',
              source: 'user',
              changedBy: 'testUser',
            },
          ],
          order: {timestamp: 'DESC'},
          take: 5,
        }),
      ).to.be.true;
    });

    it('should return no logs if none are present', async () => {
      const fakeSettingLogRepo = sinon.createStubInstance(
        Repository<SettingLog>,
      );

      fakeSettingLogRepo.find.resolves([]);
      datasource.getRepository.callsFake(() => {
        return fakeSettingLogRepo;
      });

      const settingLogs = await configStore.findSettingLogs(
        'testUser',
        'testChangedSetting',
        0,
        'testFacility',
      );
      expect(settingLogs.length).to.equal(0);
      expect(datasource.getRepository.calledOnce).to.be.true;
      expect(fakeSettingLogRepo.find.calledOnce).to.be.true;
      expect(
        fakeSettingLogRepo.find.calledWith({
          where: [
            {settingId: 'testChangedSetting', source: 'tenant'},
            {
              settingId: 'testChangedSetting',
              source: 'facility',
              facilityId: 'testFacility',
            },
            {
              settingId: 'testChangedSetting',
              source: 'user',
              changedBy: 'testUser',
            },
          ],
          order: {timestamp: 'DESC'},
          take: 0,
        }),
      ).to.be.true;
    });
  });
});
