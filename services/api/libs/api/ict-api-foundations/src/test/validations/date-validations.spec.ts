import {expect} from 'chai';
import express from 'express';
import {body, query, validationResult} from 'express-validator';
import {
  dateBodyValidationChain,
  dateQueryValidationChain,
  startEndDateBodyValidations,
  startEndDateQueryValidations,
} from '../../index.ts';
import {testStartEndDateQueryValidations} from '../../helpers/tests/validations/start-end-date-test-helpers.ts';

describe('DateValidations', () => {
  let reqMock: Partial<express.Request>;

  beforeEach(() => {
    reqMock = {
      query: {},
    };
  });

  describe('dateQueryValidationChain', () => {
    it('should return a validation chain', () => {
      const chain = dateQueryValidationChain(['test']);
      const expected = query('test');
      expect(Object.keys(expected)).to.eql(Object.keys(chain));
    });
    it('should check if query parameter exists', async () => {
      const chain = dateQueryValidationChain(['test']);
      await chain.run(reqMock);

      const results = validationResult(reqMock).mapped();
      expect(results.test.msg).to.equal('"test" missing from query parameters');
    });
    it('should check if query parameter is a string', async () => {
      reqMock = {
        query: {
          test: 1 as unknown as string,
        },
      };
      const chain = dateQueryValidationChain(['test']);
      await chain.run(reqMock);

      const results = validationResult(reqMock).mapped();
      expect(results.test.msg).to.equal('"test" must be a string');
    });
    it('should check if query parameter is empty', async () => {
      reqMock = {
        query: {
          test: ' ',
        },
      };
      const chain = dateQueryValidationChain(['test']);
      await chain.run(reqMock);

      const results = validationResult(reqMock).mapped();
      expect(results.test.msg).to.equal('"test" must not be empty');
    });
    it('should check if query parameter is an ISO 8601 date', async () => {
      reqMock = {
        query: {
          test: 'Not an ISO 8601 date',
        },
      };
      const chain = dateQueryValidationChain(['test']);
      await chain.run(reqMock);

      const results = validationResult(reqMock).mapped();
      expect(results.test.msg).to.equal('"test" must be an ISO 8601 date');
    });
    it('should not have any errors if parameter is valid', async () => {
      reqMock = {
        query: {
          test: '2021-11-30T00:00:01',
        },
      };
      const chain = dateQueryValidationChain(['test']);
      await chain.run(reqMock);

      const results = validationResult(reqMock).mapped();
      expect(results.test).to.be.undefined;
    });
    it('should be able to apply multiple parameters to the chain', async () => {
      const chain = dateQueryValidationChain(['test', 'test2']);
      await chain.run(reqMock);

      const results = validationResult(reqMock).mapped();
      expect(results.test.msg).to.equal('"test" missing from query parameters');
      expect(results.test2.msg).to.equal(
        '"test2" missing from query parameters',
      );
    });
  });

  describe('dateBodyValidationChain', () => {
    it('should return a validation chain', () => {
      const chain = dateBodyValidationChain(['test']);
      const expected = body('test');
      expect(Object.keys(expected)).to.eql(Object.keys(chain));
    });
    it('should check if body parameter exists', async () => {
      const chain = dateBodyValidationChain(['test']);
      await chain.run(reqMock);

      const results = validationResult(reqMock).mapped();
      expect(results.test.msg).to.equal('"test" missing from body properties');
    });
    it('should check if body parameter is a string', async () => {
      reqMock = {
        body: {
          test: 1 as unknown as string,
        },
      };
      const chain = dateBodyValidationChain(['test']);
      await chain.run(reqMock);

      const results = validationResult(reqMock).mapped();
      expect(results.test.msg).to.equal('"test" must be a string');
    });
    it('should check if body parameter is empty', async () => {
      reqMock = {
        body: {
          test: ' ',
        },
      };
      const chain = dateBodyValidationChain(['test']);
      await chain.run(reqMock);

      const results = validationResult(reqMock).mapped();
      expect(results.test.msg).to.equal('"test" must not be empty');
    });
    it('should check if body parameter is an ISO 8601 date', async () => {
      reqMock = {
        body: {
          test: 'Not an ISO 8601 date',
        },
      };
      const chain = dateBodyValidationChain(['test']);
      await chain.run(reqMock);

      const results = validationResult(reqMock).mapped();
      expect(results.test.msg).to.equal('"test" must be an ISO 8601 date');
    });
    it('should not have any errors if parameter is valid', async () => {
      reqMock = {
        body: {
          test: '2021-11-30T00:00:01',
        },
      };
      const chain = dateBodyValidationChain(['test']);
      await chain.run(reqMock);

      const results = validationResult(reqMock).mapped();
      expect(results.test).to.be.undefined;
    });
    it('should be able to apply multiple parameters to the chain', async () => {
      const chain = dateBodyValidationChain(['test', 'test2']);
      await chain.run(reqMock);

      const results = validationResult(reqMock).mapped();
      expect(results.test.msg).to.equal('"test" missing from body properties');
      expect(results.test2.msg).to.equal(
        '"test2" missing from body properties',
      );
    });
  });

  describe('startEndDateQueryValidations', () => {
    it('should return a validation chain array', () => {
      const validations = startEndDateQueryValidations(
        <express.Request>reqMock,
      );
      validations.forEach(validation => {
        expect(Object.keys(validation)).to.eql(Object.keys(query()));
      });
    });
    it('should check for default start and end date query parameters', async () => {
      const validations = startEndDateQueryValidations(
        <express.Request>reqMock,
      );
      for (const validation of validations) {
        await validation.run(reqMock);
      }
      const result = validationResult(reqMock).mapped();
      expect(result.start_date).to.exist;
      expect(result.end_date).to.exist;
    });
    it('should check that start date is before end date', async () => {
      const start_date: string = '2021-11-30T23:59:59';
      const end_date: string = '2021-11-30T00:00:01';
      reqMock = {
        query: {
          start_date,
          end_date,
        },
      };
      const validations = startEndDateQueryValidations(
        <express.Request>reqMock,
      );
      for (const validation of validations) {
        await validation.run(reqMock);
      }
      const result = validationResult(reqMock).mapped();
      expect(result.start_date.msg).to.equal(
        `start_date ("${new Date(start_date).toISOString()}") must be before or equal to end_date ("${new Date(end_date).toISOString()}")`,
      );
    });
    it('should not have any errors if dates are valid', async () => {
      reqMock = {
        query: {
          start_date: '2021-11-30T00:00:01',
          end_date: '2021-11-30T23:59:59',
        },
      };
      const validations = startEndDateQueryValidations(
        <express.Request>reqMock,
      );
      for (const validation of validations) {
        await validation.run(reqMock);
      }
      const result = validationResult(reqMock).mapped();
      expect(result.start_date).to.not.exist;
      expect(result.end_date).to.not.exist;
    });
    it('should be able to change query parameter names to check', async () => {
      const validations = startEndDateQueryValidations(
        <express.Request>reqMock,
        'startDate',
        'endDate',
      );
      for (const validation of validations) {
        await validation.run(reqMock);
      }
      const result = validationResult(reqMock).mapped();
      expect(result.startDate.msg).to.equal(
        '"startDate" missing from query parameters',
      );
      expect(result.endDate.msg).to.equal(
        '"endDate" missing from query parameters',
      );
    });
  });

  describe('startEndDateBodyValidations', () => {
    it('should return a validation chain array', () => {
      const validations = startEndDateBodyValidations(<express.Request>reqMock);
      validations.forEach(validation => {
        expect(Object.keys(validation)).to.eql(Object.keys(body()));
      });
    });
    it('should check for default start and end date body parameters', async () => {
      const validations = startEndDateBodyValidations(<express.Request>reqMock);
      for (const validation of validations) {
        await validation.run(reqMock);
      }
      const result = validationResult(reqMock).mapped();
      expect(result.start_date).to.exist;
      expect(result.end_date).to.exist;
    });
    it('should check that start date is before end date', async () => {
      const start_date: string = '2021-11-30T23:59:59';
      const end_date: string = '2021-11-30T00:00:01';
      reqMock = {
        body: {
          start_date,
          end_date,
        },
      };
      const validations = startEndDateBodyValidations(<express.Request>reqMock);
      for (const validation of validations) {
        await validation.run(reqMock);
      }
      const result = validationResult(reqMock).mapped();
      expect(result.start_date.msg).to.equal(
        `start_date ("${new Date(start_date).toISOString()}") must be before or equal to end_date ("${new Date(end_date).toISOString()}")`,
      );
    });
    it('should not have any errors if dates are valid', async () => {
      reqMock = {
        body: {
          start_date: '2021-11-30T00:00:01',
          end_date: '2021-11-30T23:59:59',
        },
      };
      const validations = startEndDateBodyValidations(<express.Request>reqMock);
      for (const validation of validations) {
        await validation.run(reqMock);
      }
      const result = validationResult(reqMock).mapped();
      expect(result.start_date).to.not.exist;
      expect(result.end_date).to.not.exist;
    });
    it('should be able to change body parameter names to check', async () => {
      const validations = startEndDateBodyValidations(
        <express.Request>reqMock,
        'startDate',
        'endDate',
      );
      for (const validation of validations) {
        await validation.run(reqMock);
      }
      const result = validationResult(reqMock).mapped();
      expect(result.startDate.msg).to.equal(
        '"startDate" missing from body properties',
      );
      expect(result.endDate.msg).to.equal(
        '"endDate" missing from body properties',
      );
    });
  });

  testStartEndDateQueryValidations(startEndDateQueryValidations);
});
