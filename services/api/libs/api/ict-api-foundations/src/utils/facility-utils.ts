import {ContextService} from '../context/context-service.ts';
import {IctError} from '../errors/ict-error.ts';
import {Winston<PERSON>ogger} from '../log/winston-logger.ts';

/**
 * Utility class for facility-related operations
 */
export class FacilityUtils {
  /**
   * Resolves the actual facility ID from the facility map based on the selected facility ID
   * @param context The context service containing facility information
   * @param logger The logger for error reporting
   * @returns The resolved facility ID from the facility map
   * @throws IctError.internalServerError if no facility map is found for the selected facility
   * @throws IctError.badRequest if no facility ID is found in the facility map
   */
  public static resolveFacilityId(
    context: ContextService,
    logger: WinstonLogger,
  ): string {
    const selectedFacilityId = context.selectedFacilityId;

    if (!selectedFacilityId) {
      logger.error('No selected facility ID found in context');
      throw IctError.badRequest(
        'No facility ID provided. Please ensure the ict-facility-id header is set.',
      );
    }

    const currentFacilityMap = context.facilityMaps?.find(
      facility => facility.id === selectedFacilityId,
    );

    if (!currentFacilityMap) {
      logger.error('No facility map found for selected facility', {
        selectedFacilityId,
        availableFacilityMaps: context.facilityMaps?.map(f => f.id),
      });
      throw IctError.internalServerError(
        `No facility map found for selected facility ${selectedFacilityId}`,
      );
    }

    const facilityId = currentFacilityMap.facilityId;

    if (!facilityId) {
      logger.error('No facility ID found in facility map', {
        selectedFacilityId,
        facilityMap: currentFacilityMap,
      });
      throw IctError.badRequest(
        'No facility ID provided. Please ensure the ict-facility-id header is set and a facility map is configured.',
      );
    }

    logger.debug('Successfully resolved facility ID', {
      selectedFacilityId,
      resolvedFacilityId: facilityId,
    });

    return facilityId;
  }

  /**
   * Resolves the actual tenant ID from the facility map based on the selected facility ID
   * @param context The context service containing facility information
   * @param logger The logger for error reporting
   * @returns The resolved tenant ID from the facility map
   * @throws IctError.internalServerError if no facility map is found for the selected facility
   * @throws IctError.badRequest if no tenant ID is found in the facility map
   */
  public static resolveTenantId(
    context: ContextService,
    logger: WinstonLogger,
  ): string {
    const selectedFacilityId = context.selectedFacilityId;

    if (!selectedFacilityId) {
      logger.error('No selected facility ID found in context');
      throw IctError.badRequest(
        'No facility ID provided. Please ensure the ict-facility-id header is set.',
      );
    }

    const currentFacilityMap = context.facilityMaps?.find(
      facility => facility.id === selectedFacilityId,
    );

    if (!currentFacilityMap) {
      logger.error('No facility map found for selected facility', {
        selectedFacilityId,
        availableFacilityMaps: context.facilityMaps?.map(f => f.id),
      });
      throw IctError.internalServerError(
        `No facility map found for selected facility ${selectedFacilityId}`,
      );
    }

    const tenantId = currentFacilityMap.tenantId;

    if (!tenantId) {
      logger.error('No tenant ID found in facility map', {
        selectedFacilityId,
        facilityMap: currentFacilityMap,
      });
      throw IctError.badRequest(
        'No facility ID provided. Please ensure the ict-facility-id header is set and a facility map is configured.',
      );
    }

    logger.debug('Successfully resolved tenant ID', {
      selectedFacilityId,
      resolvedTenantId: tenantId,
    });

    return tenantId;
  }
}
