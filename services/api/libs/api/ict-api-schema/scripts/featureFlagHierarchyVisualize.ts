import {FEATURE_FLAGS} from '../src/default-data/config/feature-flags.ts';

interface Node {
  name: string;
  children?: Node[];
  parentName?: string;
}

const features: Map<string, Node> = new Map();
FEATURE_FLAGS.forEach(f => {
  features.set(f.name, {
    name: f.name,
    parentName: f.parentSetting,
  });
});

// Add children to parent nodes
features.forEach(f => {
  if (f.parentName) {
    const parent = features.get(f.parentName);
    if (parent) {
      if (parent.children === undefined) {
        parent.children = [];
      }
      parent.children.push(f);
    }
  }
});

// Get top level features, for a tree like structure
const topLevelFeatures = Array.from(features.values()).filter(
  f => !f.parentName,
);

// Remove uneccesary info for visualization
const clean = (node: Node) => ({
  name: node.name,
  children: node.children?.map(clean),
});

const cleaned = topLevelFeatures.map(clean);

// Visualize
console.log(JSON.stringify(cleaned, null, 2));
