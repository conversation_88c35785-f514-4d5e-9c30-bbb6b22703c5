import {DefaultSettingDefinition} from '../../default-setting-definition.ts';
import {DataQuery, DataQueryType} from '../types/data-query-def.ts';

export const ActiveOperatorsDataPointConfig: DefaultSettingDefinition<DataQuery> =
  {
    name: 'active-operators',
    dataType: 'json',
    description: 'The number of active operators',
    defaultValueJson: {
      id: 'active-operators',
      label: 'Active Operators',
      type: DataQueryType.SINGLE_VALUE,
      query:
        '\nSELECT\n  online_operators AS value\nFROM {{edp}}\nWHERE etl_create_timestamp_utc <= @end_date\nORDER BY etl_create_timestamp_utc DESC  \nLIMIT 1',
      config: [],
      filters: [],
      isDraft: false,
      metadata: {
        unit: 'points',
        category: 'operators',
      },
      parameters: {
        required: ['start_date', 'end_date'],
      },
      dataSources: [
        {
          id: 'edp',
          type: 'edp-bigquery',
          table: 'platinum_operators_online',
        },
      ],
      description: 'The number of active operators',
    },
    group: 'data-single-value',
  };
