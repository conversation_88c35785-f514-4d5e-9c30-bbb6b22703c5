import {
  DateTimeGranularity,
  DateTimeGranularityValues,
} from '@ict/sdk-foundations/types/defs/data-query-property-def.ts';

export type DataQuery = {
  id: string;
  type: DataQueryType;
  label: string;
  description: string;
  filters: string[];
  isDraft: boolean;
  metadata: {
    unit?: string;
    category: string;
    isCategoryFilter?: boolean;
  };
  dataSources: DataQuerySource[];
  config?: string[];
  parameters: DataQueryParameters;
  query: string;
  queryProperties?: DataQueryProperty[];
};

export enum DataQueryType {
  SINGLE_VALUE = 'singleValue',
  CATEGORY_SERIES = 'categorySeries',
  TIME_SERIES = 'timeSeries',
  TABULAR = 'tabular',
  FILTER = 'filter',
}

export type DataQuerySource = {
  id: string;
  type: string;
  table: string;
};

export type DataQueryParameters = {
  required: string[];
};

export type QueryPropertyType = 'sort' | 'group_by' | 'time_bucket' | 'limit';
type BaseQueryProperty = {
  id: string;
  type: QueryPropertyType;
};

export type TimeBucket = {
  type: 'time_bucket';
  defaultValue: (typeof DateTimeGranularityValues)[number];
};

type SortOrder = 'ASC' | 'DESC';
export const SortOrderValues: SortOrder[] = ['ASC', 'DESC'];
export type Sort = {
  type: 'sort';
  defaultValue: (typeof SortOrderValues)[number];
};

export type Limit = {
  type: 'limit';
  defaultValue: number;
};

export type GroupBy = {
  type: 'group_by';
  groupableFields: string[];
  defaultValue: string;
};

/**
 * This will be a list of properties that are SQL keywords. They need to use handlebars
 * because they are keywords that can't be injected with parameters.  Current list is:
 *  - Time period aggregation:
 * Example: {
 *   id: "time_period_aggregation",
 *   type: "time_bucket",
 *   defaultValue: "MONTH",
 * }
 */
export type DataQueryProperty = BaseQueryProperty &
  (TimeBucket | Sort | Limit | GroupBy); // this is what's used for the find and replace

/**
 * Type guard to validate if an unknown object is a valid DataQueryProperty
 */
export const isValidDataQueryProperty = (
  obj: unknown,
): obj is DataQueryProperty => {
  if (!obj || typeof obj !== 'object') {
    return false;
  }

  const property = obj as Record<string, unknown>;

  // Must have an id property that's a string
  if (typeof property.id !== 'string') {
    return false;
  }

  // Must have a type property that matches one of the valid values
  if (
    typeof property.type !== 'string' ||
    !['time_bucket', 'sort', 'limit', 'group_by'].includes(
      property.type as QueryPropertyType,
    )
  ) {
    return false;
  }

  // Validate based on type
  switch (property.type as QueryPropertyType) {
    case 'time_bucket':
      return (
        typeof property.defaultValue === 'string' &&
        DateTimeGranularityValues.includes(
          property.defaultValue as DateTimeGranularity,
        )
      );
    case 'sort':
      return (
        typeof property.defaultValue === 'string' &&
        SortOrderValues.includes(property.defaultValue as SortOrder)
      );

    case 'limit':
      return (
        typeof property.defaultValue === 'number' &&
        Number.isInteger(property.defaultValue) &&
        property.defaultValue > 0
      );

    case 'group_by':
      return (
        Array.isArray(property.groupableFields) &&
        property.groupableFields.every(field => typeof field === 'string') &&
        typeof property.defaultValue === 'string'
      );

    default:
      return false;
  }
};
