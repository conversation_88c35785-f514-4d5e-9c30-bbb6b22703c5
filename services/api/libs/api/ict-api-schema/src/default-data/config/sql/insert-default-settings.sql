-- ****THIS FILE IS AUTO-GENERATED.  DO NOT EDIT IT DIRECTLY******
 INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('facility-maps', 'Array of Facility Map Definitions. Each facility can optionally specify a tableauProject field to filter Tableau workbooks by facility.', 'json', 'facility-maps', '[]', '{"$schema":"http://json-schema.org/draft-07/schema#","title":"facilities","type":"array","items":{"type":"object","properties":{"id":{"type":"string"},"name":{"type":"string"},"default":{"type":"boolean"},"dataset":{"type":"string"},"facilityId":{"type":"string","description":"The facility_id used by EDP in Pub/Sub attributes and silver table fields (ex. \"jef<PERSON>onga\")"},"tenantId":{"type":"string","description":"The tenant_id used by EDP in Pub/Sub attributes and silver table fields (ex. \"acehardware\")"},"tableauProject":{"type":"string","description":"Optional Tableau project name for this facility"}},"required":["id","name","dataset"],"additionalProperties":false}}', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueString", "tags", "parentSetting") 
    VALUES ('selected-facility-id', 'The default selected facility id for a user.', 'string', null, null, null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueString" = EXCLUDED."defaultValueString",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('menu', 'The menu configuration for the UI', 'json', null, '[{"id":"operations-visibility","label":"menu.operationsVisibility","icon":"ChartBar","link":"/ict-page-not-implemented","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility","children":[{"id":"outbound-overview","label":"menu.outboundOverview","link":"/ict-outbound-overview","viewType":"dashboard","viewConfigId":"outbound-overview-dashboard","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Outbound-Overview"},{"id":"workstation","label":"menu.workstation","link":"/ict-page-not-implemented","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Workstation","children":[{"id":"workstation-overview-dashboard","label":"menu.workstationOverview","link":"/ict-workstation-overview","viewType":"dashboard","viewConfigId":"workstation-overview-dashboard","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Workstation-Overview"},{"id":"workstation-active-orders","label":"menu.workstationActiveOrders","link":"/ict-workstation-active-orders","viewType":"workstation-view","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Workstation-Overview/Active-Orders"}]},{"id":"inbound-overview","label":"menu.inboundOverview","link":"/ict-inbound-overview","viewType":"dashboard","viewConfigId":"inbound-overview-dashboard","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Inbound-Overview"},{"id":"inventory","label":"menu.inventory","link":"/ict-page-not-implemented","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Inventory","children":[{"id":"inventory-overview","label":"menu.inventoryOverview","link":"/ict-inventory-overview","viewType":"dashboard","viewConfigId":"inventory-overview-dashboard","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Inventory/Inventory-Overview"},{"id":"inventory-list","label":"menu.inventoryList","link":"/ict-inventory-list","viewType":"inventory-list","viewConfigId":"inventory-list-dashboard","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Inventory/Inventory-List"},{"id":"container-list","label":"menu.containerList","link":"/ict-container-list","viewType":"container-list","viewConfigId":"container-list-dashboard","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Inventory/Container-List"},{"id":"replenishment-details","label":"menu.replenishmentDetails","link":"/ict-replenishment-details","viewType":"dashboard","viewConfigId":"inventory-replenishment-dashboard","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Inventory/Replenishment-Details"}]},{"id":"picking-buffer-area-details","label":"menu.pickingBufferAreaDetails","link":"/ict-picking-buffer-area-details","viewType":"dashboard","viewConfigId":"picking-buffer-area-details-dashboard","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Picking-Buffer-Area-Details"},{"id":"Daily Performance Report","label":"menu.dailyPerformanceReport","link":"/ict-reports","viewType":"daily-performance-report","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Daily-Performance-report"},{"id":"dms-movements-overview","link":"/ict-dms-movements-overview","label":"Movements Overview","visible":true,"helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Picking-Buffer-Area-Details","viewType":"dashboard","viewConfigId":"dms-movements-overview-dashboard"}]},{"id":"automation-visualization","label":"menu.automationVisualization","icon":"BatteryCharging","link":"/ict-page-not-implemented","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Automation-Visualization","children":[{"id":"functional-areas","label":"menu.functionalAreas","link":"/ict-functional-areas","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Automation-Visualization/Functional-Areas"},{"id":"alarms","label":"menu.alarms","link":"/ict-alarms","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Automation-Visualization/Alarms"},{"id":"events","label":"menu.events","link":"ict-events","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Automation-Visualization/Events"},{"id":"controls","label":"menu.controls","link":"/ict-controls","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Automation-Visualization/Controls"},{"id":"system-health","label":"menu.systemHealth","link":"/ict-system-health","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Automation-Visualization/System-Health"}]},{"id":"process-flow-visualization","label":"menu.processFlowVisualization","icon":"Flow","link":"/ict-page-not-implemented","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Process-Flow-Visualization","children":[{"id":"facility-process-flow","label":"menu.facilityProcessFlow","link":"/ict-facility-process-flow","viewType":"facility-process-flow","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Process-Flow-Visualization/Picking-Process-Flow"}]},{"id":"advanced-orchestration","label":"menu.advancedOrchestration","icon":"ColumnDependency","link":"/ict-page-not-implemented","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Advanced-Orchestration","children":[{"id":"inventory-forecast","label":"menu.inventoryForecast","link":"/ict-inventory-forecast","viewType":"inventory-forecast","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Advanced-Orchestration/Inventory-Forecast"},{"id":"file-upload","label":"menu.fileUpload","link":"/ict-file-upload","viewType":"file-upload","viewConfigId":"file-upload-dashboard","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Advanced-Orchestration/File-Upload"}]},{"id":"operational-alerting","label":"menu.operationalAlerting","icon":"Notification","link":"/ict-page-not-implemented","roles":["ct_engineers","ct_configurator"],"helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operational-Alerting","children":[{"id":"configured-alerts","label":"menu.configuredAlerts","link":"/ict-tableau-alerts","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Operational-Alerting/Configured-Alerts","viewType":"configured-alerts"}]},{"id":"application-health","label":"menu.applicationHealth","icon":"CloudMonitoring","link":"/ict-page-not-implemented","roles":["ct_engineers"],"helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Application-Health","children":[{"id":"curated-data","label":"menu.curatedData","icon":"IconAnalytics","link":"/ict-curated-data","viewType":"curated-data"}]},{"id":"scenario-modeling","label":"menu.scenarioModeling","icon":"Analytics","link":"/ict-page-not-implemented","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Scenario-Modeling","children":[{"id":"simulation","label":"menu.simulation","link":"/ict-simulation","viewType":"simulation"}]},{"id":"performance-analysis","label":"menu.performanceAnalysis","link":"/ict-page-not-implemented","icon":"Template","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Performance-Analysis"},{"id":"ict-data-explorer","label":"menu.dataExplorer","icon":"Keyboard","link":"/ict-data-explorer","viewType":"data-explorer","helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Data-Explorer"},{"id":"ict-dematic-chat","label":"menu.dematicChat","icon":"IbmWatsonDiscovery","link":"/ict-dematic-chat","viewType":"dematic-chat"},{"id":"administration","label":"menu.administration","icon":"SecurityServices","link":"/ict-page-not-implemented","roles":["ct_engineers","ct_configurator"],"helpLink":"/r/Dematic-Control-Tower/User-Guide/en-US/Administration","children":[{"id":"user-management","label":"menu.userManagement","link":"/ict-user-management","viewType":"user-management"}]},{"id":"config-administration","label":"menu.dematicInternal","visible":true,"icon":"Password","link":"/ict-page-not-implemented","roles":["ct_engineers","ct_configurator"],"allowedParentIds":[],"children":[{"id":"debug-info","label":"menu.debugInfo","link":"/debug-info","viewType":"debug-info","allowedParentIds":["config-administration"],"visible":true},{"id":"feature-flag-config","label":"menu.featureFlags","link":"/feature-flags","viewType":"feature-flags","allowedParentIds":["config-administration"],"visible":true},{"id":"app-config-settings","label":"menu.applicationConfig","link":"/app-config-settings","viewType":"app-config-settings","allowedParentIds":["config-administration"],"visible":true},{"id":"menu-manager","label":"menu.menuManager","link":"/menu-manager","viewType":"menu-manager","allowedParentIds":["config-administration"],"visible":true},{"id":"mfe-manager","label":"menu.mfeManager","link":"/mfe-manager","viewType":"mfe-manager","allowedParentIds":["config-administration"],"visible":true},{"id":"playground","label":"menu.playground","link":"/playground","viewType":"playground","allowedParentIds":["config-administration"],"localOnly":true,"visible":true},{"id":"examples","label":"menu.examples","localOnly":true,"allowedParentIds":["config-administration"],"children":[{"id":"routable-view","label":"menu.routableView","link":"/routable-view","viewType":"routable-view","allowedParentIds":["config-administration","examples"]},{"id":"options-view","label":"menu.optionsView","link":"/options-view","viewType":"options-view","allowedParentIds":["config-administration","examples"]},{"id":"static-view","label":"menu.staticView","link":"/static-view","viewType":"static-view","allowedParentIds":["config-administration","examples"]}]}]}]', '{"$schema":"http://json-schema.org/draft-07/schema#","title":"Menu Configuration","type":"array","items":{"type":"object","properties":{"id":{"type":"string"},"label":{"type":"string"},"icon":{"type":"string"},"viewType":{"type":"string"},"viewConfigId":{"type":"string"},"link":{"type":"string"},"helpLink":{"type":"string"},"children":{"type":"array","items":{"type":"object"}},"roles":{"type":"array","items":{"type":"string"}},"localOnly":{"type":"boolean"},"visible":{"type":"boolean"},"allowedParentIds":{"type":"array","items":{"type":"string"},"description":"List of parent IDs this menu item can be moved under"}},"required":["id","label"],"additionalProperties":true}}', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('shift-start-end-times', 'The shift schedule in 24 Hour format, must include start and end times per shift.', 'json', null, '{"first_startTime":"7:00","first_endTime":"15:29","second_startTime":"15:30","second_endTime":"23:59","third_startTime":"00:00","third_endTime":"06:59"}', null, null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueString", "tags", "parentSetting") 
    VALUES ('site-time-zone', 'The facility time zone', 'string', null, 'America/New_York', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueString" = EXCLUDED."defaultValueString",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('user-favorites', 'User-specific favorite pages stored as route IDs', 'json', 'user-writable', '{"favorites":[]}', '{"$schema":"http://json-schema.org/draft-07/schema#","title":"Schema for user favorite pages","type":"object","properties":{"favorites":{"type":"array","items":{"type":"string","description":"Route ID of the favorited page"}}},"required":["favorites"]}', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueString", "tags", "parentSetting") 
    VALUES ('home-page-url', 'The URI of the home page to use when a user logs in.', 'string', null, '', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueString" = EXCLUDED."defaultValueString",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueString", "tags", "parentSetting") 
    VALUES ('site-fact', 'The type of facts returned by the site (standard, autostore, horizon)', 'string', null, 'standard', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueString" = EXCLUDED."defaultValueString",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-edge-animation-effect', 'Edges will conditionally render as animated dashed lines based on a status value.', 'boolean', 'feature-flags', false, '"page"=>"Facility Process Flow","type"=>"Style"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-node-alerts', 'Alerts will be shown within nodes in Facility Process Flow.', 'boolean', 'feature-flags', false, '"page"=>"Facility Process Flow","type"=>"Style"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-polling', 'Facility Process Flow page will poll for new data on a configured time interval.', 'boolean', 'feature-flags', false, '"page"=>"Facility Process Flow","type"=>"Action"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('data-explorer-debug-data', 'The debug data section in the results for the Data Explorer page', 'boolean', 'feature-flags', false, '"type"=>"Individual component"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-node-drilldown-button', 'Nodes will have a button that when clicked, will take the user to a more detailed view of the process flow within that area.', 'boolean', 'feature-flags', false, '"page"=>"Facility Process Flow"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-node-metrics', 'KPI metrics will be displayed within nodes in the Facility Process Flow Chart.', 'boolean', 'feature-flags', false, '"page"=>"Facility Process Flow","type"=>"Style"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-edge-label-status-indicators', 'Edge label text will visually indicate that the value is outside of the expected range.', 'boolean', 'feature-flags', false, '"page"=>"Facility Process Flow","type"=>"Style"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-edge-label-status-indicators', 'Edge label text will visually indicate that the value is outside of the expected range.', 'boolean', 'feature-flags', false, '"page"=>"Facility Process Flow","type"=>"Style"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-edge-label-alerts', 'Edge alerts shown in edge label badges will be displayed if present in the data.', 'boolean', 'feature-flags', false, '"page"=>"Facility Process Flow","type"=>"Style"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-node-metrics', 'KPI metrics will be displayed within nodes in the Facility Process Flow Chart.', 'boolean', 'feature-flags', false, '"page"=>"Facility Process Flow","type"=>"Style"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-detail-panel', 'Detail panel will open when an area node or edge is clicked and put into the selected state.', 'boolean', 'feature-flags', false, '"page"=>"Facility Process Flow","type"=>"Action"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-config-management', 'Facility Process Flow configuration management will be available in the admin controls menu. This will allow users to configure and customize functionality within the graph and detail panels.', 'boolean', 'feature-flags', false, '"page"=>"Facility Process Flow","type"=>"Action"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-admin-controls', 'If enabled, the button to open the admin controls modal will be shown in Process Flow when interactive mode is enabled.', 'boolean', 'feature-flags', false, '"page"=>"Facility Process Flow","type"=>"Action"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-edge-labels', 'If enabled, edges within Facility Process Flow will display edge labels.', 'boolean', 'feature-flags', false, '"page"=>"Facility Process Flow","type"=>"Style"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('ict-nav-help-content', 'The help icon content on the header bar', 'boolean', 'feature-flags', false, '"type"=>"Action"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('ict-data-integration-configuration', 'Turns on the Data Integration Configuration view and API', 'boolean', 'feature-flags', false, '"type"=>"Individual component"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('fault-tracking-manual-entry-button', 'Controls the visibility of the Add Manual Entry button in the Fault Tracking view.', 'boolean', 'feature-flags', false, '"page"=>"Fault Tracking","type"=>"Action"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('fault-tracking-edit-timings-button', 'Controls the visibility of the Edit Timings button in the Fault Tracking view.', 'boolean', 'feature-flags', false, '"page"=>"Fault Tracking","type"=>"Action"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES ('fault-tracking-calculation-status-button', 'Controls the visibility of the Edit Calculation Status button in the Fault Tracking view.', 'boolean', 'feature-flags', false, '"page"=>"Fault Tracking","type"=>"Action"' :: hstore, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('inbound-overview-dashboard', 'Defines the UI dashboard configuration of the Inbound Overview page in the Operations Visibility package', 'json', 'dashboard-configurations', '{"title":"Inbound Overview","content":[{"h":2,"w":3,"x":3,"y":3,"content":{"id":"inventory-advices-outstanding","type":"kpi","options":{"type":"inventory-advices-outstanding","title":"Outstanding Advices","bgColor":"#ffffff","description":"Number of outstanding advices"}}},{"h":2,"w":3,"x":0,"y":3,"content":{"id":"inventory-advices-in-progress","type":"kpi","options":{"type":"inventory-advices-in-progress","title":"In Progress Advices","bgColor":"#ffffff","description":"Number of advices currently in progress"}}},{"h":2,"w":3,"x":6,"y":3,"content":{"id":"inventory-advices-finished","type":"kpi","options":{"type":"inventory-advices-finished","title":"Finished Advices","bgColor":"#ffffff","description":"Number of finished advices"}}},{"h":2,"w":3,"x":9,"y":3,"content":{"id":"inventory-advices-cycle-time","type":"kpi","options":{"type":"inventory-advices-cycle-time","title":"Advice Cycle Time","bgColor":"#ffffff","description":"Average cycle time for advices"}}},{"h":5,"w":12,"x":0,"y":5,"content":{"id":"advice-list","name":"Advice List","type":"advice-list","options":{}}}]}', null, null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('outbound-overview-dashboard', 'Defines the UI dashboard configuration of the Outbound Overview page in the Operations Visibility package', 'json', 'dashboard-configurations', '{"title":"Outbound Overview","content":[{"h":2,"w":3,"x":0,"y":0,"content":{"id":"facility-orders-shipped","type":"kpi","options":{"type":"facility-orders-shipped","title":"Facility Orders Shipped","bgColor":"#ffffff","description":"Number of orders shipped vs total","unit":true,"precision":0}}},{"h":2,"w":3,"x":3,"y":0,"content":{"id":"facility-orders-progress","type":"kpi","options":{"type":"facility-orders-progress","unit":true,"title":"Facility Order Progress","bgColor":"#ffffff","precision":1,"description":"Percentage of facility order progress"}}},{"h":2,"w":3,"x":6,"y":0,"content":{"id":"facility-orders-throughput-rate","type":"kpi","filters":{"datePeriodRange":"today"},"options":{"type":"facility-orders-throughput-rate","title":"Facility Throughput Rate","precision":1,"unit":true}}},{"h":2,"w":3,"x":9,"y":0,"content":{"id":"facility-orders-cycle-time","type":"kpi","options":{"type":"facility-orders-cycle-time","unit":true,"title":"Facility Order Cycle Time","bgColor":"#ffffff","description":"Average facility order cycle time","valueSuffix":"minutes","precision":0}}},{"h":2,"w":4,"x":0,"y":2,"content":{"id":"orders-facility-estimated-completion","type":"kpi","filters":{"datePeriodRange":"today"},"options":{"type":"orders-facility-estimated-completion","title":"Facility Estimated Completion","unit":true,"precision":0}}},{"h":2,"w":4,"x":4,"y":2,"content":{"id":"operators-active","type":"kpi","options":{"type":"operators-active","title":"Active Operators","bgColor":"#ffffff","showDelta":true,"showTarget":false,"description":"Number of active operators","targetValue":5,"targetDirection":"above","targetDisplayStyle":"arrow","precision":0}}},{"h":2,"w":4,"x":8,"y":2,"content":{"id":"facility-orders-lines-progress","type":"kpi","options":{"type":"facility-orders-lines-progress","unit":true,"title":"Facility Order Line Progress","bgColor":"#ffffff","description":"Percentage of facility order line progress","precision":1}}},{"h":6,"w":12,"x":0,"y":4,"content":{"id":"e43bb72c-a619-45c8-acb3-d01c418829fd","type":"outbound-overview-areas","filters":{"datePeriodRange":"thisWeek"},"options":{"areas":[{"key":"shipping","label":"Shipping Area"},{"key":"picking","label":"Picking Area"}]}}}],"showDateRange":true,"defaultDatePeriodRange":"today"}', null, null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('inventory-overview-dashboard', 'Defines the UI dashboard configuration of the Inventory Overview page in the Operations Visibility package', 'json', 'dashboard-configurations', '{"title":"Inventory Overview","content":[{"h":2,"w":3,"x":6,"y":0,"content":{"id":"orders-fulfillment-outstanding","name":"Fulfillment Orders Outstanding","type":"kpi","options":{"type":"orders-fulfillment-outstanding","title":"Fulfillment Orders Outstanding","bgColor":"#ffffff","metricType":"orders-fulfillment-outstanding","description":"Number of fulfillment orders outstanding"}}},{"h":2,"w":3,"x":0,"y":0,"content":{"id":"facility-projected-orders-fulfillment","name":"Projected FacilityOrder Fulfillment","type":"kpi","options":{"type":"facility-orders-projected-fulfillment","title":"Projected Facility Order Fulfillment","bgColor":"#ffffff","metricType":"facility-orders-projected-fulfillment","description":"Projected facility order fulfillment rate"}}},{"h":2,"w":3,"x":3,"y":0,"content":{"id":"orders-cycletime","name":"Order Cycle Time","type":"kpi","options":{"type":"orders-projected-fulfillment","unit":true,"title":"Projected Order Fulfillment","bgColor":"#ffffff","metricType":"orders-cycle-time","description":"Average order cycle time","valueSuffix":"minutes"}}},{"h":2,"w":3,"x":9,"y":0,"content":{"id":"orders-storage-utilization","name":"Storage Utilization","type":"kpi","options":{"type":"inventory-storage-utilization","unit":true,"title":"Storage Utilization","bgColor":"#ffffff","precision":1,"metricType":"inventory-storage-utilization","description":"Storage utilization percentage"}}},{"h":3,"w":6,"x":0,"y":2,"content":{"id":"at-inventory","name":"At Inventory","type":"kpi-chart","options":{"type":"inventory-stock-distribution-at-percentage","title":"At Inventory","colorId":null,"showUnit":true,"precision":1,"dateFormat":"hh:mm a","showMaxKpi":false,"showMinKpi":false,"valueLabel":"","targetValue":55,"valueFormat":"%","showTotalKpi":false,"showAverageKpi":false,"showCurrentKpi":true,"showTargetLine":false,"showAverageLine":false}}},{"h":3,"w":6,"x":0,"y":5,"content":{"id":"under-inventory","name":"Under Inventory","type":"kpi-chart","options":{"type":"inventory-stock-distribution-over-percentage","title":"Over Inventory","colorId":"teal","showUnit":true,"lineColor":"#007d79","precision":1,"dateFormat":"hh:mm a","valueLabel":"","targetValue":55,"valueFormat":"%","showCurrentKpi":true,"showTargetLine":false,"showAverageLine":false}}},{"h":3,"w":6,"x":6,"y":2,"content":{"id":"over-inventory","name":"Over Inventory","type":"kpi-chart","options":{"type":"inventory-stock-distribution-no-percentage","title":"No Inventory","colorId":"cyan","showUnit":true,"lineColor":"#33b1ff","precision":1,"dateFormat":"hh:mm a","valueLabel":"","targetValue":55,"valueFormat":"%","showCurrentKpi":true,"showTargetLine":false,"showAverageLine":false}}},{"h":3,"w":6,"x":6,"y":5,"content":{"id":"no-inventory","name":"No Inventory","type":"kpi-chart","options":{"type":"inventory-stock-distribution-under-percentage","title":"Under Inventory","colorId":"magenta","showUnit":true,"lineColor":"#ff7eb6","precision":1,"dateFormat":"hh:mm a","valueLabel":"","targetValue":55,"valueFormat":"%","showCurrentKpi":true,"showTargetLine":false,"showAverageLine":false}}}],"showDateRange":true,"defaultDatePeriodRange":"today","availableDateRanges":["today","last7days","last14days","last30days"]}', null, null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('workstation-overview-dashboard', 'Defines the UI dashboard configuration of the Workstation Overview page in the Operations Visibility package', 'json', 'dashboard-configurations', '{"title":"Workstation Overview","autoRefreshEnabled":true,"autoRefreshInterval":60,"content":[{"h":3,"w":4,"x":0,"y":0,"content":{"id":"total-stations","type":"total-stations","options":{}}},{"h":3,"w":4,"x":4,"y":0,"content":{"id":"logged-in-operators","type":"logged-in-operators","options":{}}},{"h":3,"w":4,"x":8,"y":0,"content":{"id":"workstation-order-status","type":"workstation-order-status","options":{}}},{"h":2,"w":4,"x":0,"y":4,"content":{"id":"station-performance","type":"station-performance","options":{}}},{"h":2,"w":4,"x":4,"y":3,"content":{"id":"station-health","type":"station-health","options":{}}},{"h":6,"w":12,"x":0,"y":5,"content":{"id":"workstation-list","type":"workstation-list","options":{}}}]}', null, null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('picking-buffer-area-details-dashboard', 'Defines the UI dashboard configuration of the Picking Buffer Area Details page in the Operations Visibility package', 'json', 'dashboard-configurations', '{"title":"Picking Buffer Area Details","content":[{"h":4,"w":6,"x":0,"y":3,"content":{"id":"99104560-45ad-4cd1-9e58-8e9f6b539571","type":"bar-chart","filters":{"datePeriodRange":"today"},"options":{"type":"faults","title":"By Aisle","sortBy":"ascending","filters":{"groupBy":"aisle"},"groupBy":"","sortByName":"none","orientation":"horizontal"}}},{"h":4,"w":6,"x":6,"y":3,"content":{"id":"b4809517-4276-4aa9-bcb3-a1449ba56de3","type":"bar-chart","filters":{"datePeriodRange":"today"},"options":{"type":"faults","title":"By Level","sortBy":"ascending","colorId":"cyan","filters":{"groupBy":"level"},"groupBy":"","barColor":"#33b1ff","sortByName":"none","orientation":"horizontal"}}},{"h":4,"w":6,"x":0,"y":7,"content":{"id":"b51a1852-75c0-4c19-80b3-506c4a21edbb","type":"bar-chart","filters":{"datePeriodRange":"today"},"options":{"type":"faults","title":"By Device Type","sortBy":"ascending","colorId":"teal","filters":{"groupBy":"device_functional_type"},"groupBy":"","barColor":"#007d79","sortByName":"none","orientation":"horizontal"}}},{"h":4,"w":6,"x":6,"y":7,"content":{"id":"1d476fdc-3a09-4de7-998d-eee70ac3ecca","type":"bar-chart","filters":{"datePeriodRange":"today"},"options":{"type":"faults","title":"By Device ID","sortBy":"ascending","colorId":"magenta","filters":{"groupBy":"device_code"},"groupBy":"","barColor":"#ff7eb6","sortByName":"none","orientation":"horizontal","displayLimit":30}}},{"h":3,"w":6,"x":0,"y":11,"content":{"id":"810f60c0-1340-4d83-a76e-7f6172735f63","type":"bar-chart","filters":{"datePeriodRange":"today"},"options":{"type":"faults","title":"By Status","sortBy":"ascending","colorId":"red","filters":{"groupBy":"reason_name"},"groupBy":"","barColor":"#fa4d56","sortByName":"none","orientation":"horizontal"}}},{"h":3,"w":6,"x":6,"y":11,"content":{"id":"9fb6914e-974c-4170-8a2e-bc9ca4683722","type":"pie-chart","filters":{"datePeriodRange":"today"},"options":{"type":"faults","title":"By Status","filters":{"groupBy":"reason_name"},"groupBy":"","pieType":"donut","showUnit":true,"showPercentage":true}}}],"showDateRange":true}', null, null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('dms-movements-overview-dashboard', 'Defines the UI dashboard configuration of the Movements Overview page in the Operations Visibility package', 'json', 'dashboard-configurations', '{"title":"Movements Overview","content":[{"h":4,"w":6,"x":0,"y":10,"content":{"id":"87c21e2a-025e-4035-a9ac-c051b804dc17","type":"combo-chart-ext","filters":{"datePeriodRange":"last30days"},"options":{"type":"dms-movements-by-location","dmsId":"","title":"Source Location Aisle","filters":{"dmsId":"inventory","groupBy":"source_location"},"groupBy":"","chartStyle":"stacked-row"}}},{"h":2,"w":12,"x":0,"y":0,"content":{"id":"12df5b75-6518-4859-8588-682f784efdb9","type":"dashboard-summary","filters":{"datePeriodRange":"last30days"},"options":{}}},{"h":4,"w":12,"x":0,"y":2,"content":{"id":"b81b3c38-4b62-46d2-b2c3-9096fcdd9ef3","type":"combo-chart-ext","filters":{"datePeriodRange":"last30days"},"options":{"type":"dms-movements-by-day","dmsId":"","title":"DMS Movements by Day","filters":{"dmsId":"inventory","groupBy":"day"},"groupBy":"","chartStyle":"stacked-column"}}},{"h":4,"w":6,"x":0,"y":6,"content":{"id":"182329ad-efc2-4d06-ab55-7ea2e0a537e8","type":"combo-chart-ext","filters":{"datePeriodRange":"last7days"},"options":{"type":"dms-movements-by-location","dmsId":"","title":"Source Location Type","filters":{"dmsId":"inventory","groupBy":"source_type"},"groupBy":"","chartStyle":"stacked-row"}}},{"h":4,"w":6,"x":6,"y":6,"content":{"id":"4f97c451-9d60-49b2-bcb2-6cdec497cc84","type":"combo-chart-ext","filters":{"datePeriodRange":"last7days"},"options":{"type":"dms-movements-by-location","dmsId":"","title":"Destination Location Type","filters":{"dmsId":"inventory","groupBy":"destination_type"},"groupBy":"","chartStyle":"stacked-row"}}},{"h":4,"w":6,"x":6,"y":10,"content":{"id":"dbd52f03-b295-42e4-8270-e454cefe4c46","type":"combo-chart-ext","filters":{"datePeriodRange":"last7days"},"options":{"type":"dms-movements-by-location","dmsId":"","title":"Destination Location Aisle","filters":{"dmsId":"inventory","groupBy":"destination_location"},"groupBy":"","chartStyle":"stacked-row"}}},{"h":6,"w":6,"x":0,"y":14,"content":{"id":"e970e33e-675a-410f-95a6-69a33b6146d8","type":"combo-chart-ext","filters":{"datePeriodRange":"last7days"},"options":{"type":"dms-movements-by-load-unit","dmsId":"","title":"DMS Movements by Load Unit","filters":{"dmsId":"inventory","groupBy":"sku"},"groupBy":"","chartStyle":"stacked-row"}}},{"h":6,"w":6,"x":6,"y":14,"content":{"id":"0fddeac7-ce1a-41b8-b56c-8ba3bdb03923","type":"combo-chart-ext","filters":{"datePeriodRange":"last7days"},"options":{"type":"dms-movements-by-day","dmsId":"","title":"DMS Movements by Hour","filters":{"dmsId":"inventory","groupBy":"hour"},"groupBy":""}}}],"showDateRange":true,"autoRefreshEnabled":false,"autoRefreshInterval":60,"availableDateRanges":["last30days","last14days","last7days","lastMonth","lastWeek","thisMonth","thisWeek","today","yesterday"],"defaultDatePeriodRange":"last30days"}', null, null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('inventory-replenishment-dashboard', 'Defines the UI dashboard configuration of the Inventory Replenishment page in the Operations Visibility package', 'json', 'dashboard-configurations', '{"title":"Replenishment Details","content":[{"h":3,"w":4,"x":0,"y":0,"content":{"id":"3f5a0917-6b23-430a-b5bc-9e6907fedb7c","type":"kpi-chart","filters":{"datePeriodRange":"today"},"options":{"type":"daily-replenishments","title":"Daily Replenishments","showUnit":true,"precision":0,"showLegend":false,"showAverageKpi":true}}},{"h":3,"w":4,"x":4,"y":0,"content":{"id":"8e961b48-c55c-4733-9a33-a7bba98c12bd","type":"kpi-chart","filters":{"datePeriodRange":"today"},"options":{"type":"daily-cycle-times","title":"Daily Cycle Time","showUnit":true,"precision":0,"showLegend":false,"showAverageKpi":true}}},{"h":3,"w":4,"x":8,"y":0,"content":{"id":"96112246-49f9-4c85-b5cb-72f25864e86f","type":"kpi-chart","filters":{"datePeriodRange":"today"},"options":{"type":"daily-pending-orders","title":"Daily Pending Orders","showUnit":true,"precision":0,"showLegend":false,"showAverageKpi":true}}},{"h":3,"w":12,"x":0,"y":3,"content":{"id":"cf72feb5-d58a-42d1-b6f5-ca602d8bcbf9","type":"combo-chart","filters":{"datePeriodRange":"lastWeek"},"options":{"type":"daily-replenishments-by-shift","title":"Daily Replenishments by Shift","chartStyle":"area"}}},{"h":4,"w":12,"x":0,"y":6,"content":{"id":"dc665515-23de-44b4-9503-5aac8b4afc45","type":"combo-chart","filters":{"datePeriodRange":"lastWeek"},"options":{"type":"replenishment-task-type-data","title":"Daily Replenishments by Type","chartStyle":"stacked-column"}}}],"showDateRange":true,"defaultDatePeriodRange":"last7days","availableDateRanges":["last7days","last14days","last30days"]}', null, null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('file-upload-dashboard', 'Defines the UI dashboard configuration of the File Upload page in the Advanced Orchestration package', 'json', 'dashboard-configurations', '{"sapOtherFiles":false,"sapOrderDetails":true,"sapOrderManagement":true}', null, null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('container-list-dashboard', 'Defines the UI dashboard configuration of the Container List page in the Operations Visibility package', 'json', 'dashboard-configurations', '{"dataSource":"standard"}', null, null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('inventory-list-dashboard', 'Defines the UI dashboard configuration of the Inventory List page in the Operations Visibility package', 'json', 'dashboard-configurations', '{"dataSource":"facility"}', null, null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('cycle-time-state-range', 'Defines the start and end states that are used to determine an order cycle', 'json', null, '{"startStates":["RELEASED","CREATED"],"endStates":["COMPLETE","COMPLETED","PICK_COMPLETE"]}', '{"$schema":"http://json-schema.org/draft-07/schema#","title":"Generated schema for Root","type":"object","properties":{"startStates":{"type":"array","items":{"type":"string"},"minItems":1},"endStates":{"type":"array","items":{"type":"string"},"minItems":1}},"required":["startStates","endStates"]}', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('order-complete-event-codes', 'An array of the event codes that should signify that an order is complete. These codes are related to the gold_pick_task.event_code field.', 'json', null, '{"codes":["COMPLETE","COMPLETED","PICK_COMPLETE"]}', '{"$schema":"http://json-schema.org/draft-07/schema#","title":"Generated schema for Root","type":"object","properties":{"codes":{"type":"array","items":{"type":"string"},"minItems":1}},"required":["codes"]}', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueNumber", "tags", "parentSetting") 
    VALUES ('inventory-high-impact-sku-percentage', 'The percentage of high impact SKUs to display on the inventory overview page', 'number', null, 10, null, null)
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueNumber" = EXCLUDED."defaultValueNumber",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('inventory-work-area-code-filter-list', 'The list of work area codes that the inventory overview page components should be filtered by.  This is used to query fct_inventory and hst_item_by_zone and filter by the work_area_code (joined to dim_work_area). This list should either be empty or contain a list of valid work_area_codes from dim_work_area.', 'json', null, '{"areaCodes":[]}', '{"$schema":"http://json-schema.org/draft-07/schema#","title":"Generated schema for Root","type":"object","properties":{"areaCodes":{"type":"array","items":{"type":"string"}}},"required":["areaCodes"]}', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('inventory-sku-list-column-order', 'Defines the order of the fields in the inventory sku list table on the inventory overview page', 'json', null, '["sku","quantityAvailable","quantityAllocated","maxContainers","skuPositions","contOverage","daysOnHand","averageDailyQuantity","averageDailyOrders","latestActivityDateTimestamp","latestCycleCountTimestamp","description","targetMultiplicity","velocityClassification"]', '{"$schema":"http://json-schema.org/draft-07/schema#","title":"Generated schema for Root","type":"array","items":{"type":"string"}}', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('inventory-zone-mapping', 'Defines the intended use for the inventory zone areas. Can be defined by code or specific zone name (ex GTP or GTP02)', 'json', null, '{"reserve_storage":["VNA","CNV","ALT","RCV"],"forward_pick":["DMS","GTP","ASRS"]}', null, null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('inventory-storage-utilization-area-filter-list', 'The list of areas that the storage utilization KPI is filtered by. This list should either be empty or contain a list of valid areas from the area column of fct_bin_utilization.', 'json', null, '{"areaCodes":[]}', '{"$schema":"http://json-schema.org/draft-07/schema#","title":"Generated schema for Root","type":"object","properties":{"areaCodes":{"type":"array","items":{"type":"string"}}},"required":["areaCodes"]}', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('inventory-storage-utilization-aisle-filter-list', 'The list of aisles that the storage utilization KPI is filtered by. This list should either be empty or contain a list of valid aisles from the aisle column of fct_bin_utilization.', 'json', null, '{"aisleCodes":[]}', '{"$schema":"http://json-schema.org/draft-07/schema#","title":"Generated schema for Root","type":"object","properties":{"areaCodes":{"type":"array","items":{"type":"string"}}},"required":["aisleCodes"]}', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('excluded-workstation-list', 'Defines the listing of workstations to remove from workstation/recontainerization dashboard', 'json', null, '{"workstations":["10000000002","10000000003","10000000004","10000000006","10000000007"]}', null, null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueNumber", "tags", "parentSetting") 
    VALUES ('workstation-count', 'Count of workstations at the facility', 'number', null, 99, null, null)
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueNumber" = EXCLUDED."defaultValueNumber",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('workstation-destination-locations-suffixes', 'List of workstation destination locations suffixes, used in filtering for order status', 'json', null, '{"suffixes":["B1","B2","B3","B4","B5","F1","F2","F3","F4","F5","F6"]}', '{"$schema":"http://json-schema.org/draft-07/schema#","title":"Generated schema for Root","type":"object","properties":{"suffixes":{"type":"array","items":{"type":"string"}}},"required":["suffixes"]}', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueNumber", "tags", "parentSetting") 
    VALUES ('workstation-order-delayed-time', 'Amount of time, in minutes, after an ''Active'' event, without a ''Release'' event, that an order is considered delayed', 'number', null, 45, null, null)
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueNumber" = EXCLUDED."defaultValueNumber",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('workstation-container-departure-events', 'List of event codes that indicate a container has departed a workstation', 'json', null, '["release","departure"]', '{"$schema":"http://json-schema.org/draft-07/schema#","type":"array","items":{"type":"string"}}', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-graph-config', 'Stores configuration values for how to render the nodes, and edges within the graph and customize functionality within the graph.', 'json', null, '{}', '{"$schema":"http://json-schema.org/draft-07/schema#","title":"Process Flow Graph Configuration Settings","type":"object","additionalProperties":{"type":"object","additionalProperties":{"type":"object","properties":{"metrics":{"type":"array","items":{"type":"string"}},"hasChildren":{"type":"boolean"}},"required":["metrics","hasChildren"],"additionalProperties":false}}}', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueNumber", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-polling-interval', 'The time interval in milliseconds to poll the ICT facility process flow page', 'number', null, 60000, null, null)
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueNumber" = EXCLUDED."defaultValueNumber",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('ict-facility-process-flow-detail-panel-layout', 'The layout of metrics and metric groups in the process flow detail panel.', 'json', null, '{}', '{"$schema":"http://json-schema.org/draft-07/schema#","type":"object","additionalProperties":{"type":"array","items":{"type":"object","properties":{"name":{"type":"string"},"metrics":{"type":"array","items":{"type":"string"}}},"required":["name","metrics"],"additionalProperties":false}}}', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueString", "tags", "parentSetting") 
    VALUES ('tableau-top-level-project-name', 'The Tableau project name to act as the top level project for the UI navigation menu. Only workbooks in the subtree of this project will be shown in the UI. When not set or if the name is not found in the list of retrieved projects, workbooks from all projects will be shown. Project name is case-insensitive.', 'string', null, '', null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueString" = EXCLUDED."defaultValueString",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueNumber", "tags", "parentSetting") 
    VALUES ('data-explorer-default-recommendations', 'The number of recommended questions shown on the data explorer page', 'number', null, 4, null, null)
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueNumber" = EXCLUDED."defaultValueNumber",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;
INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES ('active-operators', 'The number of active operators', 'json', 'data-single-value', '{"id":"active-operators","label":"Active Operators","type":"singleValue","query":"\nSELECT\n  online_operators AS value\nFROM {{edp}}\nWHERE etl_create_timestamp_utc <= @end_date\nORDER BY etl_create_timestamp_utc DESC  \nLIMIT 1","config":[],"filters":[],"isDraft":false,"metadata":{"unit":"points","category":"operators"},"parameters":{"required":["start_date","end_date"]},"dataSources":[{"id":"edp","type":"edp-bigquery","table":"platinum_operators_online"}],"description":"The number of active operators"}', null, null, null) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = null
      ;