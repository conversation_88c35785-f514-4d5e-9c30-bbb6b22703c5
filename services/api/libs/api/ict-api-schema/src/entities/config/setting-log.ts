import {Column, Entity} from 'typeorm';
import {ICTEntity} from '../ict-entity.ts';

/**
 * Entity that logs changes/transactions made to a setting
 */
@Entity({name: 'setting_log'})
export class SettingLog extends ICTEntity {
  @Column()
  settingId!: string;

  // indicates the username that changed the setting
  @Column()
  changedBy!: string;

  // indicates the relevant facilityId that the setting was changed for
  @Column({nullable: true})
  facilityId?: string;

  // date when change was added to table (including timezone)
  @Column({default: () => 'CURRENT_TIMESTAMP'})
  timestamp!: Date;

  // source/setting value type (default value, tenant, or user)
  @Column()
  source!: string;

  @Column()
  newValue?: string;
}
