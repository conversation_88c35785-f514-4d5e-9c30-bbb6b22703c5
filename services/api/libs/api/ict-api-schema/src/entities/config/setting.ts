import {
  BeforeI<PERSON>rt,
  BeforeUpdate,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import {ICTNamedEntity} from '../ict-named-entity.ts';
import {ICTEntity} from '../ict-entity.ts';
import {JsonUtils} from '../../utils/json-utils.ts';

/**
 * Entity that allows tenants and users to store settings.
 */
@Entity({name: 'setting'})
export class Setting extends ICTNamedEntity {
  @Column()
  dataType!: 'string' | 'number' | 'json' | 'boolean';

  @Column({type: 'float', nullable: true})
  minValue: number | null = null;

  @Column({type: 'float', nullable: true})
  maxValue: number | null = null;

  @Column({type: 'float', nullable: true})
  defaultValueNumber!: number | null;

  @Column({type: 'varchar', nullable: true})
  defaultValueString!: string | null;

  @Column({type: 'jsonb', nullable: true})
  defaultValueJson!: unknown | null;

  @Column({type: 'boolean', nullable: true})
  defaultValueBoolean: boolean = true;

  @Column({type: 'jsonb', nullable: true})
  jsonSchema: unknown | null = null;

  @Column({type: 'varchar', nullable: true})
  group: string | null = null;

  @Column({type: 'hstore', nullable: true, hstoreType: 'object'})
  tags: {[key: string]: string} | null = null;

  @Column({type: 'varchar', nullable: true})
  parentSetting: string | null = null;

  @ManyToOne(() => Setting, setting => setting.name, {nullable: true})
  @JoinColumn({name: 'parentSetting', referencedColumnName: 'name'})
  parentSettingEntity: Setting | null = null;

  @OneToMany(
    () => AllowedSettingValue,
    allowedSetting => allowedSetting.setting,
  )
  allowedSettings?: AllowedSettingValue[];

  get defaultValue(): number | string | null | unknown {
    switch (this.dataType) {
      case 'string':
        return this.defaultValueString;
      case 'number':
        return this.defaultValueNumber;
      case 'json':
        return this.defaultValueJson;
      case 'boolean':
        return this.defaultValueBoolean;
      default:
        return this.defaultValueString;
    }
  }

  set defaultValue(value: string | number | unknown) {
    switch (this.dataType) {
      case 'string':
        this.defaultValueString = value as string;
        break;
      case 'number':
        this.defaultValueNumber = value as number;
        break;
      case 'json':
        this.defaultValueJson = value;
        break;
      case 'boolean':
        this.defaultValueBoolean = value as boolean;
        break;
      default:
        this.defaultValueString = value as string;
        break;
    }
  }

  @BeforeInsert()
  @BeforeUpdate()
  checkJsonSchema() {
    if (this.dataType !== 'json') return;
    if (!this.jsonSchema || this.jsonSchema === null) return;
    if (this.defaultValueJson === null) return;

    let isJsonValid = false;
    if (this.defaultValueJson) {
      isJsonValid = JsonUtils.validateSchema(
        this.defaultValueJson,
        this.jsonSchema,
      );
    }

    if (!isJsonValid) {
      throw new Error('Error validating new json value against schema!');
    }
  }
}

@Entity({name: 'allowed_setting_value'})
export class AllowedSettingValue extends ICTEntity {
  @Column()
  settingId!: string;

  @ManyToOne(() => Setting, setting => setting.allowedSettings)
  setting!: Setting;

  @Column()
  value!: string;
}
