import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  JoinColumn,
  ManyToOne,
  OneToOne,
} from 'typeorm';
import {ICTEntity} from '../ict-entity.ts';
import {AllowedSettingValue, Setting} from './setting.ts';
import {JsonUtils} from '../../utils/json-utils.ts';

/**
 * Entity that allows tenants and users to store settings.
 */
export abstract class StoredSetting extends ICTEntity {
  @Column()
  settingId!: string;

  @ManyToOne(() => Setting, {eager: true})
  @JoinColumn()
  setting!: Setting;

  @OneToOne(() => AllowedSettingValue, {eager: true})
  @JoinColumn()
  allowedSettingValue: AllowedSettingValue | null = null;

  @Column({type: 'float', nullable: true})
  valueNumber: number | null = null;

  @Column({type: 'varchar', nullable: true})
  valueString!: string | null;

  @Column({type: 'jsonb', nullable: true})
  valueJson: unknown | null = null;

  @Column({type: 'boolean', nullable: true})
  valueBoolean: boolean = true;

  @Column({default: 'unknown'})
  lastChangedBy!: string;

  @BeforeUpdate()
  @BeforeInsert()
  checkJsonSchema() {
    if (this.setting.dataType !== 'json') return;
    if (!this.setting.jsonSchema || this.setting.jsonSchema === null) return;
    if (this.valueJson === null) return;

    let isJsonValid = false;
    if (this.valueJson) {
      isJsonValid = JsonUtils.validateSchema(
        this.valueJson,
        this.setting.jsonSchema,
      );
    }

    if (!isJsonValid) {
      throw new Error('Error validating new json value against schema!');
    }
  }

  get value(): number | string | null | unknown {
    switch (this.setting.dataType) {
      case 'string':
        return this.allowedSettingValue?.value ?? this.valueString ?? null;
      case 'number':
        return this.valueNumber;
      case 'json':
        return this.valueJson;
      case 'boolean':
        return this.valueBoolean;
      default:
        return null;
    }
  }

  set value(val: number | string | unknown) {
    switch (this.setting.dataType) {
      case 'string':
        // if a setting is linked to allowed_settings, then check to ensure the value is in the list.
        // if there are no allowed_settings for that setting, then just set the valueString
        if (this.setting.allowedSettings) {
          const allowedSetting = this.setting.allowedSettings.find(
            allowedSettingVal => allowedSettingVal.value === val,
          );
          if (allowedSetting) {
            this.allowedSettingValue = allowedSetting;
          } else {
            throw new Error(
              `Updating setting ${this.setting.name} failed because the value ${val} isn't allowed.`,
            );
          }
        } else {
          this.valueString = val as string;
          break;
        }
        break;
      case 'number':
        this.valueNumber = val as number;
        break;
      case 'json':
        this.valueJson = val;
        break;
      case 'boolean':
        this.valueBoolean = val as boolean;
        break;
      default:
        break;
    }
  }
}
