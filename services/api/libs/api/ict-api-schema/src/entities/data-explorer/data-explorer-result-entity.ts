import {Entity, Column} from 'typeorm';
import {ICTEntity} from '../ict-entity.ts';

/**
 * Entity that allows attaching the data explorer response to the user.
 */
@Entity({name: 'results'})
export class DataExplorerResultEntity extends ICTEntity {
  /**
   * The user id.
   */
  @Column()
  userId!: string;

  /**
   * Question the user asks.
   */
  // @TODO Need to align with the AI team
  @Column({length: 1024})
  question!: string;

  /**
   * Answer provided to the user.
   */
  // @TODO Need to align with the AI team
  @Column({length: 1024})
  answer!: string;

  /**
   * JSON output.
   */
  @Column({type: 'jsonb'})
  jsonOutput!: unknown[];

  /**
   * Boolean to indicate if search result it bookmarked
   */
  @Column()
  isBookmarked!: boolean;

  /**
   * Indicates if user has recommended the response.
   * True = Thumbs up, False = Thumbs down, Null = No vote
   */
  @Column({type: 'boolean', nullable: true})
  isRecommended: boolean | null = null;

  /**
   * JSON Code in string format
   */
  @Column({type: 'jsonb', nullable: true})
  eChartCode: unknown | null = null;

  /**
   * Full AIML response for debugging purposes
   */
  @Column({type: 'jsonb', nullable: true})
  debugData: unknown | null = null;
}
