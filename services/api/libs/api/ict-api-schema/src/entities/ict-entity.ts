import {
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Column,
} from 'typeorm';

/**
 * An abstract class that represents a Control Tower entity.
 */
export abstract class ICTEntity {
  /**
   * Primary key id of the entity, set to be generated to a UUID.
   */
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  /**
   * Optional description of the entity.
   */
  @Column({type: 'varchar', nullable: true})
  description: string | null = null;

  /**
   * Date and time the entity was created.
   */
  @CreateDateColumn()
  createdAt!: Date;

  /**
   * Date and time the entity was last updated.
   */
  @UpdateDateColumn()
  updatedAt!: Date;
}
