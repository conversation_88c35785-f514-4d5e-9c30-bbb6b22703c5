import {
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  RemoveEvent,
  UpdateEvent,
} from 'typeorm';
import {SettingLog} from '../entities/config/setting-log.ts';
import {StoredSetting} from '../entities/config/stored-setting.ts';
import {TenantSetting} from '../entities/config/tenant-setting.ts';
import {FacilitySetting} from '../entities/config/facility-setting.ts';
import {UserSetting} from '../entities/config/user-setting.ts';

/**
 * EventSubscriber that automatically adds and updates entries
 * to the SettingLog when a setting is changed
 */
@EventSubscriber()
export class SettingLogSubscriber implements EntitySubscriberInterface {
  listenTo(): typeof StoredSetting | string {
    return StoredSetting;
  }

  afterInsert(event: InsertEvent<StoredSetting>) {
    if (event.entity)
      this.createNewLog(event, event.entity.createdAt, event.entity.value);
    else
      console.log(
        "InsertEvent did not have an entity, setting_log didn't record change",
      );
  }

  afterUpdate(event: UpdateEvent<StoredSetting>) {
    if (event.entity)
      this.createNewLog(event, event.entity.updatedAt, event.entity.value);
    else
      console.log(
        "UpdateEvent did not have an entity, setting_log didn't record change",
      );
  }

  beforeRemove(event: RemoveEvent<StoredSetting>) {
    if (event.entity) this.createNewLog(event, null, 'DELETE');
    else
      console.log(
        "RemoveEvent did not have an entity, setting_log didn't record change",
      );
  }

  private createNewLog(
    event:
      | InsertEvent<StoredSetting>
      | UpdateEvent<StoredSetting>
      | RemoveEvent<StoredSetting>,
    timestamp: Date | null,
    setting: unknown,
  ) {
    // exits attempt for NewLog if queryRunner has already been run
    if (event.queryRunner.isReleased) return;

    // get values needed for function
    const entity = event.entity!;
    const entry = new SettingLog();
    let source: string;
    if (entity instanceof TenantSetting) source = 'tenant';
    else if (entity instanceof FacilitySetting) source = 'facility';
    else if (entity instanceof UserSetting) source = 'user';
    else if (entity instanceof StoredSetting) source = 'stored-setting';
    else source = 'unknown';

    // set values and insert into database
    entry.settingId = entity.settingId;
    entry.changedBy = entity.lastChangedBy;
    if (timestamp) entry.timestamp = timestamp;
    entry.source = source;
    if (entity instanceof FacilitySetting) entry.facilityId = entity.facilityId;
    entry.newValue = JSON.stringify(setting);

    event.manager.getRepository(SettingLog).save(entry);
  }
}
