import * as fs from 'fs';
import {DefaultConfigSettings} from '../default-data/config/default-configs/index.ts';
import {Setting} from '../entities/config/setting.ts';
import {DefaultSettingDefinition} from '@ict/sdk-foundations/types/index.ts';
import {AppConfigSettingTag} from '@ict/sdk-foundations/types/index.ts';

// create an insert query for each config setting (including feature flags) based on its data type
const insertCommands = DefaultConfigSettings.map(setting => {
  const parsedSetting = parseSettingData(setting as DefaultSettingDefinition);

  // do not add the setting if it has no data type
  if (!parsedSetting.dataType) {
    console.warn(
      `Setting ${setting.name} has no data type and will not be added.`,
    );
    return '';
  }

  switch (parsedSetting.dataType) {
    case 'string':
      return generateStringTypeInsert(parsedSetting);
    case 'number':
      return generateNumberTypeInsert(parsedSetting);
    case 'boolean':
      return generateBooleanTypeInsert(parsedSetting);
    case 'json':
      return generateJsonTypeInsert(parsedSetting);
    default:
      // do not add the setting if the data type is invalid
      console.warn(
        `Setting ${setting.name} has an invalid data type of ${setting.dataType} and will not be added.`,
      );
      return '';
  }
});

const sql = insertCommands.join('\n');

// update the sql file with the updated list of inserts
fs.writeFileSync(
  'src/default-data/config/sql/insert-default-settings.sql',
  `-- ****THIS FILE IS AUTO-GENERATED.  DO NOT EDIT IT DIRECTLY******\n ${sql}`,
);

console.log('Default config sql generation complete.');

function generateBooleanTypeInsert(setting: Setting) {
  return `INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueBoolean", "tags", "parentSetting") 
    VALUES (${setting.name}, ${setting.description}, '${setting.dataType}', ${setting.group}, ${setting.defaultValueBoolean}, ${generateTagsSql(setting)}, ${setting.parentSetting}) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueBoolean" = EXCLUDED."defaultValueBoolean",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = ${setting.parentSetting}
      ;`;
}

function generateStringTypeInsert(setting: Setting) {
  return `INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueString", "tags", "parentSetting") 
    VALUES (${setting.name}, ${setting.description}, '${setting.dataType}', ${setting.group}, ${setting.defaultValueString}, ${generateTagsSql(setting)}, ${setting.parentSetting}) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueString" = EXCLUDED."defaultValueString",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = ${setting.parentSetting}
      ;`;
}

function generateNumberTypeInsert(setting: Setting) {
  return `INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueNumber", "tags", "parentSetting") 
    VALUES (${setting.name}, ${setting.description}, '${setting.dataType}', ${setting.group}, ${setting.defaultValueNumber}, ${generateTagsSql(setting)}, ${setting.parentSetting})
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueNumber" = EXCLUDED."defaultValueNumber",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = ${setting.parentSetting}
      ;`;
}

function generateJsonTypeInsert(setting: Setting) {
  return `INSERT INTO "config"."setting" ("name", "description", "dataType",  "group", "defaultValueJson", "jsonSchema", "tags", "parentSetting") 
    VALUES (${setting.name}, ${setting.description}, '${setting.dataType}', ${setting.group}, ${setting.defaultValueJson}, ${setting.jsonSchema}, ${generateTagsSql(setting)}, ${setting.parentSetting}) 
    ON CONFLICT(name) DO UPDATE
    SET 
      "description" = EXCLUDED.description,
      "group" = EXCLUDED.group,
      "defaultValueJson" = EXCLUDED."defaultValueJson",
      "jsonSchema" = EXCLUDED."jsonSchema",
      "tags" = EXCLUDED."tags",
      "dataType" = EXCLUDED."dataType",
      "parentSetting" = ${setting.parentSetting}
      ;`;
}

function generateTagsSql(setting: Setting) {
  if (!setting.tags) return null;

  let tagsSql = (setting.tags as unknown as AppConfigSettingTag[])
    .map(tag => {
      return `"${tag.key}"=>"${tag.value}"`;
    })
    .toString();
  tagsSql = `'${tagsSql}' :: hstore`;

  return tagsSql;
}
/**
 * Add single quotes around string values so they appear correctly in the insert statement.
 * Non-string values should NOT have quotes around them.
 * Set properties to null if they are not defined
 * */
function parseSettingData(defaultsetting: DefaultSettingDefinition) {
  const setting = new Setting();
  setting.name =
    defaultsetting.name !== undefined ? `'${defaultsetting.name}'` : '';
  setting.description = defaultsetting.description
    ? /** Need to escape any single quotes in descriptions, use double single quotes to escape single quotes */
      `'${defaultsetting.description.replaceAll("'", "''")}'`
    : null;
  setting.dataType = defaultsetting.dataType || null;
  setting.group = defaultsetting.group ? `'${defaultsetting.group}'` : null;
  setting.defaultValueBoolean =
    defaultsetting.defaultValueBoolean !== undefined
      ? defaultsetting.defaultValueBoolean
      : false;

  // allow empty string to be added as a valid setting value
  setting.defaultValueString =
    defaultsetting.defaultValueString !== undefined &&
    defaultsetting.defaultValueString !== null
      ? `'${defaultsetting.defaultValueString}'`
      : null;
  setting.defaultValueNumber =
    defaultsetting.defaultValueNumber !== undefined
      ? defaultsetting.defaultValueNumber
      : null;
  setting.defaultValueJson = defaultsetting.defaultValueJson
    ? `'${JSON.stringify(defaultsetting.defaultValueJson)}'`
    : null;
  setting.jsonSchema = defaultsetting.jsonSchema
    ? `'${JSON.stringify(defaultsetting.jsonSchema)}'`
    : null;
  setting.tags =
    (defaultsetting.tags as unknown as {[key: string]: string}) ?? null;
  setting.parentSetting = defaultsetting.parentSetting
    ? `'${defaultsetting.parentSetting}'`
    : null;
  return setting;
}
