import path from 'path';
import shell from 'shelljs';
import fs from 'fs';

const BUILD_DIR = path.resolve('./');
const SDK_DIR = path.resolve(BUILD_DIR, 'sdk-foundations');
const ROOT_SDK_DIR = path.resolve(
  BUILD_DIR,
  '../../../../../libs/shared/ict-sdk/generated'
);

const OPEN_API_FILE = 'control-tower-openapi.yaml';
const OPEN_API_FILE_LOCATION = path.resolve(`../../../docs/${OPEN_API_FILE}`);
const TYPES_DIR = path.resolve('./types');

copyFilesToSdk();

/**
 * Runs various commands to get our shared SDK package setup. This function
 * will generate types, slim down our OpenAPI file and convert to JSON, and copy over
 * the OpenAPI YAML
 */
function copyFilesToSdk() {
  console.log('Generating SDK files...');
  console.log('SDK folder               : ', SDK_DIR);
  console.log('Open API Spec location   : ', OPEN_API_FILE_LOCATION);
  console.log('types folder : ', TYPES_DIR);
  console.log('root sdk folder : ', ROOT_SDK_DIR);

  // Ensure our SDK lib folder exists and clean...
  if (shell.test('-e', SDK_DIR)) {
    shell.rm('-r', SDK_DIR);
    shell.rm('-r', ROOT_SDK_DIR);
  }

  shell.mkdir('-p', SDK_DIR);
  shell.mkdir('-p', ROOT_SDK_DIR);

  // Copy the open api spec to the package folder...
  const openApiFile = path.resolve(`${SDK_DIR}`, OPEN_API_FILE);
  const rootOpenApiFile = path.resolve(`${ROOT_SDK_DIR}`, OPEN_API_FILE);
  shell.cp(OPEN_API_FILE_LOCATION, openApiFile);
  shell.cp(OPEN_API_FILE_LOCATION, rootOpenApiFile);

  // Generate SDK Types using openapi-typescript instead of openapi typegen
  shell.exec(
    `yarn exec openapi-typescript ${openApiFile} -o ${SDK_DIR}/sdk-types.d.ts`
  );
  shell.cp(
    path.resolve(`${SDK_DIR}`, 'sdk-types.d.ts'),
    path.resolve(`${ROOT_SDK_DIR}`, 'sdk-types.d.ts')
  );

  // Generate the "OpenAPI Runtime" which is a slimmed down file that
  // is used by https://openapistack.co/
  shell.exec(
    `yarn exec openapi read --strip openapi_client_axios --format json ${openApiFile} > ${SDK_DIR}/openapi-runtime.json`
  );
  shell.cp(
    path.resolve(`${SDK_DIR}`, 'openapi-runtime.json'),
    path.resolve(`${ROOT_SDK_DIR}`, 'openapi-runtime.json')
  );

  // Generate the OpenAPI Typescript Types - used by UI Carbon SDK
  shell.exec(
    `yarn exec openapi-typescript ${openApiFile} -o ${SDK_DIR}/openapi-react-query.d.ts`
  );
  shell.cp(
    path.resolve(`${SDK_DIR}`, 'openapi-react-query.d.ts'),
    path.resolve(`${ROOT_SDK_DIR}`, 'openapi-react-query.d.ts')
  );

  // Copy files to shared defs into SDK directory...
  shell.cp('-R', TYPES_DIR, SDK_DIR);
  shell.cp('-R', TYPES_DIR, ROOT_SDK_DIR);
  cleanUpTsExtensions(path.resolve(SDK_DIR, 'types'));
  cleanUpTsExtensions(path.resolve(ROOT_SDK_DIR, 'types'));
  console.log('Generating SDK files... Done!');
}

// Remove the .ts extension from the imports.
function cleanUpTsExtensions(dir) {
  const files = fs.readdirSync(dir);
  files.forEach(file => {
    const fileName = path.resolve(dir, file);
    if (fs.statSync(fileName).isDirectory()) {
      cleanUpTsExtensions(fileName);
    } else {
      const contents = fs.readFileSync(fileName, {encoding: 'utf8'});
      if (contents.includes('.ts')) {
        console.log(`removed .ts from imports: ${path.resolve(dir, file)}`);
        const updatedContents = contents.replaceAll('.ts', '');
        fs.writeFileSync(path.resolve(dir, file), updatedContents);
      }
    }
  });
}
