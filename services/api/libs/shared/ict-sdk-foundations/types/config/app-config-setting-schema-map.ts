import Ajv from 'ajv';
import {AppConfigSettingName} from './app-config-setting-name.ts';
import {FacilityMapSchema} from './system/facility-map-def.ts';
import {DefaultMenuConfig} from './system/menu-config.ts';

// NOTE: If you need to get access to a schema that is not listed then you may need too
// move the schema from the ict-api-schema project and create a type to go with it if
// one does not exists already.  zod is a good choice for this as well.
const AppConfigSettingSchemaMap: Map<AppConfigSettingName, object> = new Map<
  AppConfigSettingName,
  object
>([
  ['facility-maps', FacilityMapSchema],
  ['menu', DefaultMenuConfig],
]);

/**
 * Result type for schema validation
 */
export type ValidationResult = {
  valid: boolean;
  errors?: string[];
};

/**
 * Validates an object against a given JSON Schema.
 * @param data The object to validate.
 * @param schema The JSON Schema to validate against.
 * @returns ValidationResult with success status and errors if validation fails.
 */
export function validateAppConfigSettingSchema(
  settingName: AppConfigSettingName,
  data: unknown,
): ValidationResult {
  const schema = AppConfigSettingSchemaMap.get(settingName);
  if (!schema) {
    return {
      valid: false,
      errors: [`No schema defined for setting: ${settingName}`],
    };
  }

  const ajv = new Ajv();
  const validate = ajv.compile(schema);
  const isValid = validate(data);

  if (isValid) {
    return {
      valid: true,
    };
  } else {
    const errors =
      validate.errors?.map(error =>
        `${error.instancePath} ${error.message}`.trim(),
      ) || [];

    return {
      valid: false,
      errors,
    };
  }
}
