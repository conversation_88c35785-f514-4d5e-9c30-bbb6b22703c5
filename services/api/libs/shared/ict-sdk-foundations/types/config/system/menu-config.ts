// This is the default menu configuration for the UI. It contains
// the "core" menu items that are default to the application. ICT
// admins can hide/show/modify these items at runtime.

import {MenuConfig} from '../../defs/menu-config-def.ts';

export const DefaultMenuConfig: MenuConfig = [
  {
    id: 'operations-visibility',
    label: 'menu.operationsVisibility',
    icon: 'ChartBar',
    link: '/ict-page-not-implemented',
    helpLink: '/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility',
    children: [
      {
        id: 'outbound-overview',
        label: 'menu.outboundOverview',
        link: '/ict-outbound-overview',
        viewType: 'dashboard',
        viewConfigId: 'outbound-overview-dashboard',
        helpLink:
          '/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Outbound-Overview',
      },
      {
        id: 'workstation',
        label: 'menu.workstation',
        link: '/ict-page-not-implemented',
        helpLink:
          '/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Workstation',
        children: [
          {
            id: 'workstation-overview-dashboard',
            label: 'menu.workstationOverview',
            link: '/ict-workstation-overview',
            viewType: 'dashboard',
            viewConfigId: 'workstation-overview-dashboard',
            helpLink:
              '/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Workstation-Overview',
          },
          {
            id: 'workstation-active-orders',
            label: 'menu.workstationActiveOrders',
            link: '/ict-workstation-active-orders',
            viewType: 'workstation-view',
            helpLink:
              '/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Workstation-Overview/Active-Orders',
          },
        ],
      },
      {
        id: 'inbound-overview',
        label: 'menu.inboundOverview',
        link: '/ict-inbound-overview',
        viewType: 'dashboard',
        viewConfigId: 'inbound-overview-dashboard',
        helpLink:
          '/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Inbound-Overview',
      },
      {
        id: 'inventory',
        label: 'menu.inventory',
        link: '/ict-page-not-implemented',
        helpLink:
          '/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Inventory',
        children: [
          {
            id: 'inventory-overview',
            label: 'menu.inventoryOverview',
            link: '/ict-inventory-overview',
            viewType: 'dashboard',
            viewConfigId: 'inventory-overview-dashboard',
            helpLink:
              '/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Inventory/Inventory-Overview',
          },
          {
            id: 'inventory-list',
            label: 'menu.inventoryList',
            link: '/ict-inventory-list',
            viewType: 'inventory-list',
            viewConfigId: 'inventory-list-dashboard',
            helpLink:
              '/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Inventory/Inventory-List',
          },
          {
            id: 'container-list',
            label: 'menu.containerList',
            link: '/ict-container-list',
            viewType: 'container-list',
            viewConfigId: 'container-list-dashboard',
            helpLink:
              '/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Inventory/Container-List',
          },
          {
            id: 'replenishment-details',
            label: 'menu.replenishmentDetails',
            link: '/ict-replenishment-details',
            viewType: 'dashboard',
            viewConfigId: 'inventory-replenishment-dashboard',
            helpLink:
              '/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Inventory/Replenishment-Details',
          },
        ],
      },
      {
        id: 'picking-buffer-area-details',
        label: 'menu.pickingBufferAreaDetails',
        link: '/ict-picking-buffer-area-details',
        viewType: 'dashboard',
        viewConfigId: 'picking-buffer-area-details-dashboard',
        helpLink:
          '/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Picking-Buffer-Area-Details',
      },
      {
        id: 'Daily Performance Report',
        label: 'menu.dailyPerformanceReport',
        link: '/ict-reports',
        viewType: 'daily-performance-report',
        helpLink:
          '/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Daily-Performance-report',
      },
      {
        "id": "dms-movements-overview",
        "link": "/ict-dms-movements-overview",
        "label": "Movements Overview",
        "visible": true,
        "helpLink": "/r/Dematic-Control-Tower/User-Guide/en-US/Operations-Visibility/Picking-Buffer-Area-Details",
        "viewType": "dashboard",
        "viewConfigId": "dms-movements-overview-dashboard"
      }
    ],
  },
  {
    id: 'automation-visualization',
    label: 'menu.automationVisualization',
    icon: 'BatteryCharging',
    link: '/ict-page-not-implemented',
    helpLink:
      '/r/Dematic-Control-Tower/User-Guide/en-US/Automation-Visualization',
    children: [
      {
        id: 'functional-areas',
        label: 'menu.functionalAreas',
        link: '/ict-functional-areas',
        helpLink:
          '/r/Dematic-Control-Tower/User-Guide/en-US/Automation-Visualization/Functional-Areas',
      },
      {
        id: 'alarms',
        label: 'menu.alarms',
        link: '/ict-alarms',
        helpLink:
          '/r/Dematic-Control-Tower/User-Guide/en-US/Automation-Visualization/Alarms',
      },
      {
        id: 'events',
        label: 'menu.events',
        link: 'ict-events',
        helpLink:
          '/r/Dematic-Control-Tower/User-Guide/en-US/Automation-Visualization/Events',
      },
      {
        id: 'controls',
        label: 'menu.controls',
        link: '/ict-controls',
        helpLink:
          '/r/Dematic-Control-Tower/User-Guide/en-US/Automation-Visualization/Controls',
      },
      {
        id: 'system-health',
        label: 'menu.systemHealth',
        link: '/ict-system-health',
        helpLink:
          '/r/Dematic-Control-Tower/User-Guide/en-US/Automation-Visualization/System-Health',
      },
    ],
  },
  {
    id: 'process-flow-visualization',
    label: 'menu.processFlowVisualization',
    icon: 'Flow',
    link: '/ict-page-not-implemented',
    helpLink:
      '/r/Dematic-Control-Tower/User-Guide/en-US/Process-Flow-Visualization',
    children: [
      {
        id: 'facility-process-flow',
        label: 'menu.facilityProcessFlow',
        link: '/ict-facility-process-flow',
        viewType: 'facility-process-flow',
        helpLink:
          '/r/Dematic-Control-Tower/User-Guide/en-US/Process-Flow-Visualization/Picking-Process-Flow',
      },
    ],
  },
  {
    id: 'advanced-orchestration',
    label: 'menu.advancedOrchestration',
    icon: 'ColumnDependency',
    link: '/ict-page-not-implemented',
    helpLink:
      '/r/Dematic-Control-Tower/User-Guide/en-US/Advanced-Orchestration',
    children: [
      {
        id: 'inventory-forecast',
        label: 'menu.inventoryForecast',
        link: '/ict-inventory-forecast',
        viewType: 'inventory-forecast',
        helpLink:
          '/r/Dematic-Control-Tower/User-Guide/en-US/Advanced-Orchestration/Inventory-Forecast',
      },
      {
        id: 'file-upload',
        label: 'menu.fileUpload',
        link: '/ict-file-upload',
        viewType: 'file-upload',
        viewConfigId: 'file-upload-dashboard',
        helpLink:
          '/r/Dematic-Control-Tower/User-Guide/en-US/Advanced-Orchestration/File-Upload',
      },
    ],
  },
  {
    id: 'operational-alerting',
    label: 'menu.operationalAlerting',
    icon: 'Notification',
    link: '/ict-page-not-implemented',
    roles: ['ct_engineers', 'ct_configurator'],
    helpLink: '/r/Dematic-Control-Tower/User-Guide/en-US/Operational-Alerting',
    children: [
      {
        id: 'configured-alerts',
        label: 'menu.configuredAlerts',
        link: '/ict-tableau-alerts',
        helpLink:
          '/r/Dematic-Control-Tower/User-Guide/en-US/Operational-Alerting/Configured-Alerts',
        viewType: 'configured-alerts',
      },
    ],
  },
  {
    id: 'application-health',
    label: 'menu.applicationHealth',
    icon: 'CloudMonitoring',
    link: '/ict-page-not-implemented',
    roles: ['ct_engineers'],
    helpLink: '/r/Dematic-Control-Tower/User-Guide/en-US/Application-Health',
    children: [
      {
        id: 'curated-data',
        label: 'menu.curatedData',
        icon: 'IconAnalytics',
        link: '/ict-curated-data',
        viewType: 'curated-data',
        //  helpLink: '/r/Dematic-Control-Tower/User-Guide/en-US/application-health/curated-data',
      },
    ],
  },
  {
    id: 'scenario-modeling',
    label: 'menu.scenarioModeling',
    icon: 'Analytics',
    link: '/ict-page-not-implemented',
    helpLink: '/r/Dematic-Control-Tower/User-Guide/en-US/Scenario-Modeling',
    children: [
      {
        id: 'simulation',
        label: 'menu.simulation',
        link: '/ict-simulation',
        viewType: 'simulation',
        //  helpLink: '/r/Dematic-Control-Tower/User-Guide/en-US/scenario-modeling/simulation',
      },
    ],
  },
  {
    id: 'performance-analysis',
    label: 'menu.performanceAnalysis',
    link: '/ict-page-not-implemented',
    icon: 'Template',
    helpLink: '/r/Dematic-Control-Tower/User-Guide/en-US/Performance-Analysis',
  },
  {
    id: 'ict-data-explorer',
    label: 'menu.dataExplorer',
    icon: 'Keyboard',
    link: '/ict-data-explorer',
    viewType: 'data-explorer',
    helpLink: '/r/Dematic-Control-Tower/User-Guide/en-US/Data-Explorer',
  },
  {
    id: 'ict-dematic-chat',
    label: 'menu.dematicChat',
    icon: 'IbmWatsonDiscovery',
    link: '/ict-dematic-chat',
    viewType: 'dematic-chat',
    // helpLink: '/r/Dematic-Control-Tower/User-Guide/en-US/general/dematic-chat',
  },
  {
    id: 'administration',
    label: 'menu.administration',
    icon: 'SecurityServices',
    link: '/ict-page-not-implemented',
    roles: ['ct_engineers', 'ct_configurator'],
    helpLink: '/r/Dematic-Control-Tower/User-Guide/en-US/Administration',
    children: [
      {
        id: 'user-management',
        label: 'menu.userManagement',
        link: '/ict-user-management',
        viewType: 'user-management',
        // helpLink: '/r/Dematic-Control-Tower/User-Guide/en-US/Administration/User-Management',
      },
    ],
  },
  {
    id: 'config-administration',
    label: 'menu.dematicInternal',
    visible: true,
    icon: 'Password',
    link: '/ict-page-not-implemented',
    roles: ['ct_engineers', 'ct_configurator'],
    allowedParentIds: [],
    // helpLink:
    //   '/r/Dematic-Control-Tower/User-Guide/en-US/general/dematic-internal',
    children: [
      {
        id: 'debug-info',
        label: 'menu.debugInfo',
        link: '/debug-info',
        viewType: 'debug-info',
        allowedParentIds: ['config-administration'],
        visible: true,
        // helpLink:
        //   '/r/Dematic-Control-Tower/User-Guide/en-US/config-administration/debug-info',
      },
      {
        id: 'feature-flag-config',
        label: 'menu.featureFlags',
        link: '/feature-flags',
        viewType: 'feature-flags',
        allowedParentIds: ['config-administration'],
        visible: true,
        // helpLink:
        //   '/r/Dematic-Control-Tower/User-Guide/en-US/config-administration/feature-flags',
      },
      {
        id: 'app-config-settings',
        label: 'menu.applicationConfig',
        link: '/app-config-settings',
        viewType: 'app-config-settings',
        allowedParentIds: ['config-administration'],
        visible: true,
      },
      {
        id: 'menu-manager',
        label: 'menu.menuManager',
        link: '/menu-manager',
        viewType: 'menu-manager',
        allowedParentIds: ['config-administration'],
        visible: true,
      },
      {
        id: 'mfe-manager',
        label: 'menu.mfeManager',
        link: '/mfe-manager',
        viewType: 'mfe-manager',
        allowedParentIds: ['config-administration'],
        visible: true,
      },
      {
        id: 'playground',
        label: 'menu.playground',
        link: '/playground',
        viewType: 'playground',
        allowedParentIds: ['config-administration'],
        localOnly: true,
        visible: true,
      },
      {
        id: 'examples',
        label: 'menu.examples',
        localOnly: true,
        allowedParentIds: ['config-administration'],
        children: [
          {
            id: 'routable-view',
            label: 'menu.routableView',
            link: '/routable-view',
            viewType: 'routable-view',
            allowedParentIds: ['config-administration', 'examples'],
          },
          {
            id: 'options-view',
            label: 'menu.optionsView',
            link: '/options-view',
            viewType: 'options-view',
            allowedParentIds: ['config-administration', 'examples'],
          },
          {
            id: 'static-view',
            label: 'menu.staticView',
            link: '/static-view',
            viewType: 'static-view',
            allowedParentIds: ['config-administration', 'examples'],
          },
        ],
      },
    ],
  },
];
