// https://cloud.google.com/bigquery/docs/reference/standard-sql/datetime_functions#datetime_trunc_granularity_date
// This is the type, but please use the constant array of the same type for ease of use
export type DateTimeGranularity =
  | 'MICROSECOND'
  | 'MILLISECOND'
  | 'SECOND'
  | 'MINUTE'
  | 'HOUR'
  | 'DAY'
  | 'WEEK'
  | 'MONTH'
  | 'QUARTER'
  | 'YEAR';

// This constant and the above type should be kept in sync
export const DateTimeGranularityValues: DateTimeGranularity[] = [
  'MICROSECOND',
  'MILLISECOND',
  'SECOND',
  'MINUTE',
  'HOUR',
  'DAY',
  'WEEK',
  'MONTH',
  'QUARTER',
  'YEAR',
];
