"""
This module handles loading and managing metric configurations from Postgres and memory.
It provides functionality to load and manage configs per tenant/facility/fact_type.
It is implemented as a singleton to ensure config caching is shared across all requests
within a CloudRun instance.
"""

from typing import Dict

import psycopg2
import time
import structlog
from pydantic import ValidationError

from src.schemas import config_schema
from src.services.postgres_factory import PostgresFactory
from src.utils import EXCLUDED_NODES
from .services.redis_service import RedisService

logger = structlog.get_logger(__name__)


class ConfigLoader:
    """
    Handles loading and managing metric configurations.

    This class is responsible for:
    1. Loading configs from Postgres for each tenant/facility/fact_type
    2. Managing configs in memory
    3. Handling config validation

    This is implemented as a singleton to ensure config caching is shared across all requests
    within a CloudRun instance.
    """

    # =============== Singleton Implementation ===============
    _instance = None
    _initialized = False

    def __new__(cls):
        """
        Returns the singleton instance of the ConfigLoader class,
        creating it if it doesn't exist. This is really a defensive method since our
        singleton is instantiated in main.py at the top of the module.
        """
        if cls._instance is None:
            logger.debug('Creating NEW ConfigLoader singleton instance')
            cls._instance = super().__new__(cls)
        else:
            logger.debug('Returning EXISTING ConfigLoader singleton instance')
        return cls._instance

    def __init__(self):
        """
        Initialize the ConfigLoader class.
        Sets up the configs dictionary on first initialization only.
        """
        if not self._initialized:
            # Structure: configs[tenant_id][facility_id][fact_type][metric_config_name] = config
            self.configs = {}
            self.excluded_nodes_cache = None
            # Structure: configs[tenant_id][facility_id][fact_type][metric_config_name] = time the config was cached
            self.configs_cached_time = {}
            self.redis_service = RedisService.get_instance()
            ConfigLoader._initialized = True

    @classmethod
    def get_instance(cls) -> 'ConfigLoader':
        """
        Get the singleton instance of ConfigLoader.
        This is the preferred way to access the ConfigLoader instance.

        Returns:
            ConfigLoader: The singleton instance
        """
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    @classmethod
    def reset(cls) -> None:
        """
        Reset the singleton instance.
        This should only be used in testing or when absolutely necessary.
        """
        cls._instance = None
        cls._initialized = False

    # =============== Public Interface ===============

    def get_config_by_fact_type(
        self,
        tenant: str,
        facility: str,
        fact_type: str,
        clear_fact_type_metric_config_cache: bool = False,
    ) -> Dict[str, config_schema.MetricConfig]:
        """
        Get metric configurations for a specific fact type, using in-memory cache if available.

        This method implements a three-level caching strategy:
        1. **Cache Check**: First checks if configs are already cached for the specific
            tenant/facility/fact_type combination.
        2. **Cache Hit**: Returns cached configs immediately (no database query).
        3. **Cache Miss**: Queries Postgres and stores results in cache for future requests.

        **Cache Structure**: Configs are cached per tenant/facility/fact_type combination.
        This means that even if the same tenant and fact_type are requested for different
        facilities, each facility will have its own cache entry and may trigger
        separate Postgres queries.

        **Cache Key**: configs[tenant][facility][fact_type] = {metric_config_name: MetricConfig}

        Args:
            tenant: The tenant identifier
            facility: The facility identifier
            fact_type: The type of fact to process
            clear_fact_type_metric_config_cache: If True, clears the cache for this specific
            combination before loading.
            This is useful when the config for a fact type has changed and we want to reload
            the configs.

        Returns:
            Dictionary mapping metric_config_name to MetricConfig objects

        Raises:
            ConnectionError: If database connection fails
            ValueError/KeyError: If configuration data is invalid
            ValidationError: If config validation fails
        """
        # If clear_fact_type_metric_config_cache is True, clear the fact type metric config cache
        if clear_fact_type_metric_config_cache:
            self.clear_cache_by_fact_type(tenant, facility, fact_type)

        # Check in-memory cache first
        cached_config = self._get_cached_config(tenant, facility, fact_type)
        cached_time = self._get_cached_time(tenant, facility, fact_type)

        if cached_config is not None:
            logger.debug(
                f'Cache HIT for fact type {fact_type} in {tenant}/{facility}',
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
                config_count=len(cached_config),
                cached_time=cached_time,
            )

            # check the metric config memory cache for the time the metric config was cached
            # if cached time was less than 30 seconds ago, use the cached config
            if cached_time is None or cached_time > time.time() - 30:
                return cached_config
            else:
                # if cached time is older than 30 seconds, compared to the time postgres was last updated
                last_updated_time = self.redis_service.get_fact_last_updated_time(tenant, facility, fact_type)
                # if postgres update was more recent, query postgres. Otherwise, use the cached config.
                if last_updated_time is not None:
                    last_updated_time = float(last_updated_time)
                    if last_updated_time > cached_time:
                        logger.debug(
                            f'In memory cache is stale for fact type {fact_type} in {tenant}/{facility}',
                            tenant=tenant,
                            facility=facility,
                            fact_type=fact_type,
                            last_updated_time=last_updated_time,
                            cached_time=cached_time,
                        )
                        return self._load_config_from_postgres(tenant, facility, fact_type)
                    else:
                        return cached_config
                else:
                    return self._load_config_from_postgres(tenant, facility, fact_type)
        else:
            logger.debug(
                f'Cache MISS for fact type {fact_type} in {tenant}/{facility}',
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
            )

        # Else load from Postgres if there is no cached config
        return self._load_config_from_postgres(tenant, facility, fact_type)

    def clear_cache(self) -> None:
        """
        Clear the entire config cache in runtime.
        This should be called when configs need to be reloaded.
        """
        self.configs = {}
        self.configs_cached_time = {}
        logger.info('Config cache cleared')

    def clear_cache_by_facility(self, tenant: str, facility: str) -> None:
        """
        Clear the config cache for all fact types in a specific facility.
        """
        if tenant in self.configs and facility in self.configs[tenant]:
            del self.configs[tenant][facility]
            del self.configs_cached_time[tenant][facility]
            logger.debug(
                'Config cache cleared for facility',
                tenant=tenant,
                facility=facility,
            )
        else:
            logger.warning(
                'Config cache not found for facility',
                tenant=tenant,
                facility=facility,
            )

    def clear_cache_by_fact_type(self, tenant: str, facility: str, fact_type: str) -> None:
        """
        Clear the config cache for a specific fact type in a specific facility.
        """
        if tenant in self.configs and facility in self.configs[tenant] and fact_type in self.configs[tenant][facility]:
            del self.configs[tenant][facility][fact_type]
            del self.configs_cached_time[tenant][facility][fact_type]
            logger.info(
                'Config cache cleared for fact type',
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
            )
        else:
            logger.warning(
                'Config cache not found for fact type',
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
            )

    def is_config_active_for_facility(self, metric_config: config_schema.BaseMetricConfig, facility: str) -> bool:
        """
        Check if the given metric configuration is active for the specified facility.

        Handles different active field structures:
        - Custom configs: active is a boolean (true/false)
        - Default configs: active is hstore ({"facility_id" => "true"})

        Args:
            metric_config: The metric configuration object to check
            facility: The facility identifier

        Returns:
            bool: True if the configuration is active for the facility, False otherwise
        """
        if not metric_config:
            return False

        logger.debug(
            'Checking if config is active in cache',
            metric_config_name=getattr(metric_config, 'metric_config_name', 'unknown'),
            facility=facility,
            fact_type=getattr(metric_config, 'fact_type', 'unknown'),
        )

        # Check if it's a custom config (has is_custom attribute)
        is_custom = getattr(metric_config, 'is_custom', None)
        if is_custom:
            # Custom config: active is a boolean, but could be NULL
            active_value = getattr(metric_config, 'active', None)
            # Only return True if explicitly True, everything else (NULL, False, None) is False
            return active_value is True

        # Default config: active is hstore, check facility-specific value
        # Get facility value from hstore, default to False if hstore is invalid or facility not found
        active_hstore = getattr(metric_config, 'active', {})
        facility_value = getattr(active_hstore, facility, False) if isinstance(active_hstore, dict) else False
        return facility_value is True

    def mark_config_active_in_cache(self, tenant: str, facility: str, fact_type: str, metric_config_name: str) -> None:
        """
        Mark a specific metric config as active in cache.

        Handles different active field structures:
        - Custom configs: active is a boolean (set to True)
        - Default configs: active is hstore (set facility_id to "true")

        Args:
            tenant: The tenant identifier
            facility: The facility identifier
            fact_type: The fact type
            metric_config_name: The metric config name to mark as active
        """
        cached_config = self._get_cached_config(tenant, facility, fact_type)
        if cached_config and metric_config_name in cached_config:
            cached_metric = cached_config[metric_config_name]

            # Check if it's a custom config (has is_custom attribute)
            is_custom = getattr(cached_metric, 'is_custom', None)

            if is_custom:
                # Custom config: active is a boolean
                setattr(cached_metric, 'active', True)
            else:
                # Default config: active is hstore, update facility-specific value
                # Store as string "true" to match database hstore format
                active_hstore = getattr(cached_metric, 'active', {})
                # Handle NULL hstore values that might come through as None
                if active_hstore is None:
                    active_hstore = {}
                    logger.debug(
                        'Active hstore was NULL, initializing as empty dict',
                        facility=facility,
                        metric_config_name=metric_config_name,
                    )
                if not isinstance(active_hstore, dict):
                    active_hstore = {}
                    logger.debug(
                        'Active field was not a dict, initializing as empty dict',
                        original_active_hstore=active_hstore,
                        facility=facility,
                        metric_config_name=metric_config_name,
                    )
                active_hstore[facility] = 'true'
                setattr(cached_metric, 'active', active_hstore)
                logger.debug(
                    'Updated active hstore in cache',
                    active_hstore=active_hstore,
                    facility=facility,
                    metric_config_name=metric_config_name,
                )

            logger.debug(
                'Marked config as active in cache',
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
                metric_config_name=metric_config_name,
                is_custom=is_custom,
            )

    # =============== Private Implementation ===============

    def _load_config_from_postgres(
        self, tenant: str, facility: str, fact_type: str
    ) -> Dict[str, config_schema.MetricConfig]:
        """Load configuration from Postgres and cache it."""
        logger.debug(
            f'Loading metric configs for fact type {fact_type} from Postgres for {tenant}/{facility}',
            tenant=tenant,
            facility=facility,
            fact_type=fact_type,
        )

        # Get the Postgres service instance for the tenant
        postgres_service = PostgresFactory.get_instance(tenant)
        if not postgres_service:
            error_msg = f'Failed to get Postgres service for {tenant}/{facility}/{fact_type}'
            logger.error(
                error_msg,
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
            )
            raise ConnectionError(error_msg)

        try:
            metric_config_rows = postgres_service.get_metric_configs(
                tenant=tenant,
                facility_id=facility,
                fact_type=fact_type,
            )

            # If no metric configs are found, log a warning and cache empty result
            if not metric_config_rows:
                logger.debug(
                    f'No metric configs found for fact type {fact_type} in Postgres {tenant}/{facility}',
                    tenant=tenant,
                    facility=facility,
                    fact_type=fact_type,
                )
                # Cache the empty result to prevent repeated database queries
                empty_result = {}
                self.configs.setdefault(tenant, {}).setdefault(facility, {})[fact_type] = empty_result
                # Cache the time we cached the empty result
                self.configs_cached_time.setdefault(tenant, {}).setdefault(facility, {})[fact_type] = time.time()

                logger.debug(
                    f'Cached EMPTY result for fact type {fact_type} in {tenant}/{facility}',
                    tenant=tenant,
                    facility=facility,
                    fact_type=fact_type,
                )
                return empty_result

            # Create the fact_type metrics dict from the metric config rows
            fact_type_metrics_dict = self._create_fact_type_metrics_dict_from_rows(
                metric_config_rows, tenant, facility, fact_type
            )

            # Store in facilitycache
            self.configs.setdefault(tenant, {}).setdefault(facility, {})[fact_type] = fact_type_metrics_dict
            # Cache the time we cached the result
            self.configs_cached_time.setdefault(tenant, {}).setdefault(facility, {})[fact_type] = time.time()
            return fact_type_metrics_dict

        except (psycopg2.Error, psycopg2.OperationalError) as e:
            logger.error(
                f'Database error getting metric configs for fact type {fact_type} '
                f'from Postgres for {tenant}/{facility}',
                exc_info=True,
                facility=facility,
                fact_type=fact_type,
                tenant=tenant,
                error_type=type(e).__name__,
            )
            raise
        except (ValueError, KeyError) as e:
            logger.error(
                f'Configuration error getting metric configs for fact type {fact_type} '
                f'from Postgres for {tenant}/{facility}',
                exc_info=True,
                facility=facility,
                fact_type=fact_type,
                tenant=tenant,
                error_type=type(e).__name__,
            )
            raise

    def _get_cached_config(self, tenant: str, facility: str, fact_type: str) -> Dict[str, config_schema.MetricConfig]:
        """Get configuration from cache if available."""
        if tenant in self.configs and facility in self.configs[tenant] and fact_type in self.configs[tenant][facility]:
            logger.debug(
                f'Returning cached config for fact type {fact_type} in Postgres for {tenant}/{facility}',
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
            )
            return self.configs[tenant][facility][fact_type]
        return None

    def load_excluded_nodes(self) -> list:
        """
        Loads excluded nodes with caching logic.
        First checks the cache, then loads from utils if not cached.

        Returns:
            list: List of excluded nodes, or empty list if none found
        """
        # Check cache first
        if self.excluded_nodes_cache is not None:
            logger.debug('Returning excluded nodes from cache')
            return self.excluded_nodes_cache

        # Load from utils if not in cache
        # This is mostly placeholdder for when we eventually
        # move this into the DB and are not using the utils module
        try:
            self.excluded_nodes_cache = EXCLUDED_NODES
            logger.debug('Loaded excluded nodes from utils and cached')
            return self.excluded_nodes_cache
        except ImportError:
            logger.warning('Could not import EXCLUDED_NODES from utils, using empty list')
            self.excluded_nodes_cache = []
            return self.excluded_nodes_cache
        except AttributeError:
            logger.warning('EXCLUDED_NODES not found in utils module, using empty list')
            self.excluded_nodes_cache = []
            return self.excluded_nodes_cache

    def clear_excluded_nodes_cache(self) -> None:
        """
        Clears the excluded nodes cache, forcing a reload from utils on next access.
        Useful for testing or when the utils EXCLUDED_NODES variable changes.
        """
        self.excluded_nodes_cache = None
        logger.debug('Excluded nodes cache cleared')

    def _get_cached_time(self, tenant: str, facility: str, fact_type: str) -> Dict[str, float]:
        """Get the time that a config was cached if available."""
        if (
            tenant in self.configs_cached_time
            and facility in self.configs_cached_time[tenant]
            and fact_type in self.configs_cached_time[tenant][facility]
        ):
            logger.debug(
                f'Returning cache time for fact type {fact_type} in Postgres for {tenant}/{facility}',
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
            )
            return self.configs_cached_time[tenant][facility][fact_type]
        return None

    def _create_fact_type_metrics_dict_from_rows(
        self, metric_config_rows: list, tenant: str, facility: str, fact_type: str
    ) -> Dict[str, config_schema.MetricConfig]:
        """Process metric config rows and build the fact_type metrics dict."""
        fact_type_metrics_dict = {}

        for row in metric_config_rows:
            try:
                config_data = row.get('config_data', row)
                metric_config = config_schema.MetricConfig.model_validate(config_data)
                # Store the metric config in the fact_type_metrics_dict
                fact_type_metrics_dict[metric_config.metric_config_name] = metric_config
            except (ValueError, KeyError, ValidationError) as e:
                logger.error(
                    'Configuration validation error processing metric config row '
                    'for fact type {fact_type} in Postgres {tenant}/{facility}',
                    exc_info=True,
                    row=row,
                    tenant=tenant,
                    facility=facility,
                    fact_type=fact_type,
                    error_type=type(e).__name__,
                )
                continue
            except (TypeError, AttributeError) as e:
                logger.error(
                    'Unexpected error processing metric config row '
                    'for fact type {fact_type} in Postgres {tenant}/{facility}',
                    exc_info=True,
                    row=row,
                    tenant=tenant,
                    facility=facility,
                    fact_type=fact_type,
                    error_type=type(e).__name__,
                )
                continue

        return fact_type_metrics_dict
