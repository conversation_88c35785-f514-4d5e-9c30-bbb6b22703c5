# datetime.datetime(2024, 10, 31, 21, 32, 26, 937929)
from generate_timestamps import update_timestamp


recent_timestamp = update_timestamp()

messages = [
    {
        'messageId': '123',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'connection_movement',
            'description': 'Creates edges for multiple areas and modules',
            'row_hash': '1',
        },
        'data': {
            # This will create edges:
            #   - Receiving - Multishuttle (Area)
            #   - Receiving - AI01 (Module)
            'movement_end_timestamp_utc': update_timestamp(700),
            'source_location_code': 'RECV-INDUCT',
            'destination_location_code': 'MSAI01CR01PS12',
            'instance_id': '0',
            'handling_unit_code': 'a',
        },
    },
    {
        'messageId': '123',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'connection_movement',
            'description': 'Creates edges for multiple areas and modules',
            'row_hash': '2',
        },
        'data': {
            # This will create edges:
            #   - Receiving - Multishuttle (Area)
            #   - Receiving - AI02 (Module)
            'movement_end_timestamp_utc': update_timestamp(650),
            'source_location_code': 'RECV-INDUCT',
            'destination_location_code': 'MSAI02CR01PS12',
            'instance_id': '0',
            'handling_unit_code': 'b',
        },
    },
    {
        'messageId': '1234',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'connection_movement',
            'description': 'Creates edges for multiple areas and modules',
            'row_hash': '20',
        },
        'data': {
            # This will create edges:
            #   - Receiving - Miniload (Area)
            #   - Receiving - MLAI01 (Module)
            'movement_end_timestamp_utc': update_timestamp(600),
            'source_location_code': 'RECV-INDUCT',
            'destination_location_code': 'MLAI02I01',
            'instance_id': '0',
            'handling_unit_code': 'ABCDEFG',
        },
    },
    {
        'messageId': '123',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'connection_movement',
            'description': 'Creates edges for multiple areas and modules',
            'row_hash': '3',
        },
        'data': {
            # This will create edges:
            #   - Multishuttle (Area) > Workstations (Area)
            #   - AI02 (Module) > Workstations (Area)
            'movement_end_timestamp_utc': update_timestamp(550),
            'source_location_code': 'MSAI01CR01DS12',
            'destination_location_code': 'M0GTPxD1',
            'instance_id': '0',
            'handling_unit_code': 'a',
        },
    },
    {
        'messageId': '123',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'connection_movement',
            'description': 'Creates edges for multiple areas and modules',
            'row_hash': '4',
        },
        'data': {
            'movement_end_timestamp_utc': update_timestamp(500),
            'source_location_code': 'MSAI01CR01DS12',
            'destination_location_code': 'MSAI02CR01PS12',
            'instance_id': '0',
            'handling_unit_code': 'c',
        },
    },
    {
        'messageId': '123',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'connection_movement',
            'description': 'Creates edges for multiple areas and modules',
            'row_hash': '5',
        },
        'data': {
            # This will create edges:
            #   - M11-GTP-03 (Workstation) > Multishuttle (Area)
            #   - Multishuttle (Area) > M11-GTP-03 (Workstation)
            #   - M11-GTP-01 (Workstation) > Multishuttle (Area)
            'movement_end_timestamp_utc': update_timestamp(450),
            'source_location_code': 'M11-GTP-03D1',
            'instance_id': '0',
            'handling_unit_code': 'test1',
        },
    },
    {
        'messageId': '123',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'connection_movement',
            'description': 'Creates edges for multiple areas and modules',
            'row_hash': '6',
        },
        'data': {
            'movement_end_timestamp_utc': update_timestamp(400),
            'destination_location_code': 'MSAI01CR01PS12',
            'instance_id': '0',
            'handling_unit_code': 'test1',
        },
    },
    {
        'messageId': '123',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'connection_movement',
            'description': 'Creates edges for multiple areas and modules',
            'row_hash': '7',
        },
        'data': {
            'movement_end_timestamp_utc': update_timestamp(350),
            'source_location_code': 'MSAI01CR01DS12',
            'instance_id': '0',
            'handling_unit_code': 'test2',
        },
    },
    {
        'messageId': '123',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'connection_movement',
            'description': 'Creates edges for multiple areas and modules',
            'row_hash': '8',
        },
        'data': {
            'movement_end_timestamp_utc': update_timestamp(300),
            'destination_location_code': 'M11-GTP-03F1',
            'instance_id': '0',
            'handling_unit_code': 'test2',
        },
    },
    {
        'messageId': '123',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'connection_movement',
            'description': 'Creates edges for multiple areas and modules',
            'row_hash': '9',
        },
        'data': {
            'movement_end_timestamp_utc': update_timestamp(250),
            'source_location_code': 'M11-GTP-01D1',
            'instance_id': '0',
            'handling_unit_code': 'test3',
        },
    },
    {
        'messageId': '123',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'connection_movement',
            'description': 'Creates edges for multiple areas and modules',
            'row_hash': '10',
        },
        'data': {
            'movement_end_timestamp_utc': update_timestamp(200),
            'destination_location_code': 'MSAI01CR01PS12',
            'instance_id': '0',
            'handling_unit_code': 'test3',
        },
    },
    {
        'messageId': '123',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'connection_movement',
            'description': 'Creates outbound edge from workstation to packing area',
            'row_hash': '11',
        },
        'data': {
            # This will trigger outbound configs (only need source):
            #   - workstations_workstation_outbound_totes_edge_to_packing [workstations view]
            #   - workstations_outbound_totes_edge_to_packing [facility view]
            'movement_end_timestamp_utc': update_timestamp(200),
            'source_location_code': 'M11-GTP-05D1',  # Matches M.*GTP.*D1 pattern
            'instance_id': '0',
            'handling_unit_code': 'pack_test1',
        },
    },
    {
        'messageId': '123',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'connection_movement',
            'description': 'Creates inbound edge to packing area and packing node',
            'row_hash': '12',
        },
        'data': {
            # This will trigger inbound config (only need destination):
            #   - packing_inbound_totes_edge [facility, workstations views]
            #   - Creates the packing area node in Neo4j
            'movement_end_timestamp_utc': update_timestamp(150),
            'destination_location_code': 'PACK-STN-01',  # Matches PACK-STN-\\d+ pattern
            'instance_id': '0',
            'handling_unit_code': 'pack_test1',
        },
    },
    {
        'messageId': 'excluded_test_1',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'connection_movement',
            'description': 'OUTBOUND: Triggers workstations_workstation_outbound_totes_edge config',
            'row_hash': 'excluded_1',
        },
        'data': {
            # This will trigger the OUTBOUND config:
            # - Creates outbound edge with label 'Station'
            'movement_end_timestamp_utc': update_timestamp(150),
            'source_location_code': 'M11-GTP-05D1',
            'destination_location_code': 'GTP78',
            'instance_id': '0',
            'handling_unit_code': 'excluded_test_hu',  # Same HU for both messages
        },
    },
    {
        'messageId': 'excluded_test_2',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'connection_movement',
            'description': 'INBOUND: Triggers workstations_workstation_inbound_totes_edge config - should be EXCLUDED',
            'row_hash': 'excluded_2',
        },
        'data': {
            # This will trigger the INBOUND config:
            # - Should find outbound edge data in Redis with label 'Station'
            # - Should be EXCLUDED because (outbound_node_label='Station', metric_config.label='Station') is in EXCLUDED_EDGES
            'movement_end_timestamp_utc': update_timestamp(100),
            'source_location_code': 'GTP78',
            'destination_location_code': 'M11-GTP-06D1',
            'instance_id': '0',
            'handling_unit_code': 'excluded_test_hu',  # Same HU as previous message
        },
    },
]
