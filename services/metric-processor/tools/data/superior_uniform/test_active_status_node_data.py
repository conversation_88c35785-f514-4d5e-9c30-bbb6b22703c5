from generate_timestamps import update_timestamp

messages = [
    # Test 1: Multishuttle movement (Shuffle) - 3 identical messages
    {
        'messageId': 'test1-active-1',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'multishuttle_movement',
            'description': 'Test 1 - First message - should mark configs as active',
            'row_hash': '100',
        },
        'data': {
            'aisle_code': '01',
            'destination_location_code': 'MSAI01CL01DS13',
            'level_code': 'MSAI01EL01LO01',
            'handling_unit_code': '522ZBA',
            'movement_end_timestamp_utc': update_timestamp(800),
            'movement_type_code': 'Shuffle',
            'source_location_code': 'MSAI01LL01RO11',
            'sku_code': 'SK54321XYZ',
            'shuttle_code': 'MSAI01LV01SH03',
        },
    },
    {
        'messageId': 'test1-active-2',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'multishuttle_movement',
            'description': 'Test 1 - Second message - should NOT mark configs as active (already active)',
            'row_hash': '101',
        },
        'data': {
            'aisle_code': '01',
            'destination_location_code': 'MSAI01CL01DS13',
            'level_code': 'MSAI01EL01LO01',
            'handling_unit_code': '522ZBA',
            'movement_end_timestamp_utc': update_timestamp(800),
            'movement_type_code': 'Shuffle',
            'source_location_code': 'MSAI01LL01RO11',
            'sku_code': 'SK54321XYZ',
            'shuttle_code': 'MSAI01LV01SH03',
        },
    },
    {
        'messageId': 'test1-active-3',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'multishuttle_movement',
            'description': 'Test 1 - Third message - should NOT mark configs as active (already active)',
            'row_hash': '102',
        },
        'data': {
            'aisle_code': '01',
            'destination_location_code': 'MSAI01CL01DS13',
            'level_code': 'MSAI01EL01LO01',
            'handling_unit_code': '522ZBA',
            'movement_end_timestamp_utc': update_timestamp(800),
            'movement_type_code': 'Shuffle',
            'source_location_code': 'MSAI01LL01RO11',
            'sku_code': 'SK54321XYZ',
            'shuttle_code': 'MSAI01LV01SH03',
        },
    },
    # Test 2: Multishuttle movement (Retrieval) - 3 identical messages
    {
        'messageId': 'test2-active-1',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'multishuttle_movement',
            'description': 'Test 2 - First message - should mark retrieval configs as active',
            'row_hash': '103',
        },
        'data': {
            'aisle_code': '01',
            'destination_location_code': 'MSAI01LL01RO11',
            'movement_end_timestamp_utc': update_timestamp(200),
            'level_code': '',
            'handling_unit_code': 'AAY33167',
            'movement_type_code': 'Retrieval',
            'source_location_code': 'MS011019030111',
            'sku_code': 'SK55555XYZ',
            'shuttle_code': 'MSAI01LV01SH03',
        },
    },
    {
        'messageId': 'test2-active-2',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'multishuttle_movement',
            'description': 'Test 2 - Second message - should NOT mark configs as active (already active)',
            'row_hash': '104',
        },
        'data': {
            'aisle_code': '01',
            'destination_location_code': 'MSAI01LL01RO11',
            'movement_end_timestamp_utc': update_timestamp(200),
            'level_code': '',
            'handling_unit_code': 'AAY33167',
            'movement_type_code': 'Retrieval',
            'source_location_code': 'MS011019030111',
            'sku_code': 'SK55555XYZ',
            'shuttle_code': 'MSAI01LV01SH03',
        },
    },
    {
        'messageId': 'test2-active-3',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'multishuttle_movement',
            'description': 'Test 2 - Third message - should NOT mark configs as active (already active)',
            'row_hash': '105',
        },
        'data': {
            'aisle_code': '01',
            'destination_location_code': 'MSAI01LL01RO11',
            'movement_end_timestamp_utc': update_timestamp(200),
            'level_code': '',
            'handling_unit_code': 'AAY33167',
            'movement_type_code': 'Retrieval',
            'source_location_code': 'MS011019030111',
            'sku_code': 'SK55555XYZ',
            'shuttle_code': 'MSAI01LV01SH03',
        },
    },
    # Test 3: Bin utilization - 3 identical messages
    {
        'messageId': 'test3-active-1',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'bin_utilization',
            'description': 'Test 3 - First message - should mark bin utilization configs as active',
            'row_hash': '106',
        },
        'data': {
            'aisle_code': '01',
            'area_code': 'MS - Storage',
            'empty_location_position_count': 2036,
            'event_timestamp_utc': update_timestamp(510),
            'total_location_position_count': 8556,
        },
    },
    {
        'messageId': 'test3-active-2',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'bin_utilization',
            'description': 'Test 3 - Second message - should NOT mark configs as active (already active)',
            'row_hash': '107',
        },
        'data': {
            'aisle_code': '01',
            'area_code': 'MS - Storage',
            'empty_location_position_count': 2036,
            'event_timestamp_utc': update_timestamp(510),
            'total_location_position_count': 8556,
        },
    },
    {
        'messageId': 'test3-active-3',
        'attributes': {
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'event_type': 'bin_utilization',
            'description': 'Test 3 - Third message - should NOT mark configs as active (already active)',
            'row_hash': '108',
        },
        'data': {
            'aisle_code': '01',
            'area_code': 'MS - Storage',
            'empty_location_position_count': 2036,
            'event_timestamp_utc': update_timestamp(510),
            'total_location_position_count': 8556,
        },
    },
    # Test 4: Viz event log - 3 identical messages
    {
        'messageId': 'test4-active-1',
        'attributes': {
            'event_type': 'viz_event_log',
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'description': 'Test 4 - First message - should mark viz event log configs as active',
            'row_hash': '109',
        },
        'data': {
            'classification': 'manual',
            'area': 'DMS1',
            'event_timestamp_utc': update_timestamp(810),
            'fault_duration_milliseconds': '2000',
            'fault_description': 'U117 JAM DETECTED',
        },
    },
    {
        'messageId': 'test4-active-2',
        'attributes': {
            'event_type': 'viz_event_log',
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'description': 'Test 4 - Second message - should NOT mark configs as active (already active)',
            'row_hash': '110',
        },
        'data': {
            'classification': 'manual',
            'area': 'DMS1',
            'event_timestamp_utc': update_timestamp(810),
            'fault_duration_milliseconds': '2000',
            'fault_description': 'U117 JAM DETECTED',
        },
    },
    {
        'messageId': 'test4-active-3',
        'attributes': {
            'event_type': 'viz_event_log',
            'tenant_id': 'superior_uniform',
            'facility_id': 'superioruniform_eudoraar',
            'description': 'Test 4 - Third message - should NOT mark configs as active (already active)',
            'row_hash': '111',
        },
        'data': {
            'classification': 'manual',
            'area': 'DMS1',
            'event_timestamp_utc': update_timestamp(810),
            'fault_duration_milliseconds': '2000',
            'fault_description': 'U117 JAM DETECTED',
        },
    },
    # Test 5: NOK interval - 3 identical messages
    {
        'messageId': 'test5-active-1',
        'attributes': {
            'description': 'Test 5 - First message - should mark NOK interval configs as active',
            'event_type': 'nok_interval',
            'facility_id': 'superioruniform_eudoraar',
            'row_hash': '112',
            'tenant_id': 'superior_uniform',
        },
        'data': {
            'fault_start_timestamp_utc': update_timestamp(800),
            'fault_end_timestamp_utc': update_timestamp(810),
            'fault_duration_milliseconds': '10000',
            'equipment_code': 'MSAI01LV12SH01',
        },
    },
    {
        'messageId': 'test5-active-2',
        'attributes': {
            'description': 'Test 5 - Second message - should NOT mark configs as active (already active)',
            'event_type': 'nok_interval',
            'facility_id': 'superioruniform_eudoraar',
            'row_hash': '113',
            'tenant_id': 'superior_uniform',
        },
        'data': {
            'fault_start_timestamp_utc': update_timestamp(800),
            'fault_end_timestamp_utc': update_timestamp(810),
            'fault_duration_milliseconds': '10000',
            'equipment_code': 'MSAI01LV12SH01',
        },
    },
    {
        'messageId': 'test5-active-3',
        'attributes': {
            'description': 'Test 5 - Third message - should NOT mark configs as active (already active)',
            'event_type': 'nok_interval',
            'facility_id': 'superioruniform_eudoraar',
            'row_hash': '114',
            'tenant_id': 'superior_uniform',
        },
        'data': {
            'fault_start_timestamp_utc': update_timestamp(800),
            'fault_end_timestamp_utc': update_timestamp(810),
            'fault_duration_milliseconds': '10000',
            'equipment_code': 'MSAI01LV12SH01',
        },
    },
]
