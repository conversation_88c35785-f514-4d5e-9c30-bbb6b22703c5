#!/bin/bash

# Set error handling
set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to run a test and handle errors
run_all_local_data() {
    local local_data=$1
    local command=$2
    
    echo -e "${G<PERSON><PERSON>}Running $local_data...${NC}"
    if $command; then
        echo -e "${GREEN}✓ $local_data completed successfully${NC}"
    else
        echo -e "${RED}✗ $local_data failed${NC}"
        exit 1
    fi
    echo "----------------------------------------"
}

# Main execution
echo "Starting metric processing local data..."
echo "----------------------------------------"

# Run each test
run_all_local_data "Test Empty Config Cache" "python3 -m process_metrics_test superior_uniform test"
run_all_local_data "Miniload Test" "python3 -m process_metrics_test superior_uniform receiving"
run_all_local_data "Miniload Test" "python3 -m process_metrics_test superior_uniform miniload"
run_all_local_data "Multishuttle Test" "python3 -m process_metrics_test superior_uniform multishuttle"
run_all_local_data "Shuttle Test" "python3 -m process_metrics_test superior_uniform shuttle"
run_all_local_data "Workstations Test" "python3 -m process_metrics_test superior_uniform workstations"
run_all_local_data "Connection Test" "python3 -m process_metrics_test superior_uniform connection"
#run_all_local_data "Active Status Test" "python3 -m process_metrics_test superior_uniform test_active_status"

echo -e "${GREEN}All local data processed successfully!${NC}"