import { type FullConfig } from '@playwright/test';
import * as dotenv from 'dotenv';
import path from 'path';
import axios from 'axios';
import fs from 'fs';

/**
 *  NOTICE:
 *
 * To run test the CI locally
 * 1) set the EMULATE_CI to true.
 * 2) set the PREVIEW_ENV to the preview env URL you want to test against.
 * 3) set the PROJECT_ACCESS_TOKEN, you get the the token from the following link and doing a rotate token for UI_TEST_AUTOMATION token.
 *    https://gitlab.com/dematic/controltower/control-tower/-/settings/access_tokens?page=1 and
 *
 *  run yarn test:smoke:ci to run the smoke suite emulated in CI locally.
 *
 *  run yarn test:regression:ci to run the regression suite emulated in CI locally.
 *
 *  IMPORTANT:
 *    When updating the e2e.env secure file. run the following command to get the new file ID, and update the value on line 28.
 *    You can use the PROJECT_ACCESS_TOKEN as the token value.
 *
 *    curl --header "PRIVATE-TOKEN:<token value>" "https://gitlab.com/api/v4/projects/65672853/secure_files"
 *
 */
const EMULATE_CI = false;
const LOCAL_ENV = '';

const PROJECT_ID = 65672853;
const SECURE_FILE_ID = 3278649;
const SECURE_FILE_URL = `https://gitlab.com/api/v4/projects/${PROJECT_ID}/secure_files/${SECURE_FILE_ID}/download`;
const PROJECT_ACCESS_TOKEN = EMULATE_CI ? '**************************' : process.env.GITLAB_ACCESS_TOKEN;
const CONFIGURATION_FILE = path.join(process.cwd(), '.env');

/**
 *
 */

async function globalSetup(_config: FullConfig) {
  if (isCIJob()) {
    if (!PROJECT_ACCESS_TOKEN) {
      throw new Error('GITLAB_ACCESS_TOKEN not available!');
    }
    await downloadEnvironmentFile(CONFIGURATION_FILE, PROJECT_ACCESS_TOKEN);

    // Update the values from the pipeline..
    if (process.env.ICT_ENV) updateEnvValue('ICT_ENV', process.env.ICT_ENV);
    if (process.env.ICT_BASE_URL) updateEnvValue('ICT_BASE_URL', process.env.ICT_BASE_URL);
    if (process.env.ICT_API_URL) updateEnvValue('ICT_API_URL', process.env.ICT_API_URL);
    if (process.env.ICT_MOCK_API_URL) updateEnvValue('ICT_MOCK_API_URL', process.env.ICT_MOCK_API_URL);

    //
    if (EMULATE_CI) updateEnvValue('ICT_BASE_URL', LOCAL_ENV);
    if (EMULATE_CI) updateEnvValue('ICT_ENV', 'preview');
  }

  // Print environment variables for test transparency (always run this)
  console.log('='.repeat(60));
  console.log('E2E Test Environment Configuration:');
  console.log('='.repeat(60));

  // Try to read environment configuration from file or use process.env
  let envConfig: Record<string, string> = {};

  if (fs.existsSync(CONFIGURATION_FILE)) {
    try {
      envConfig = dotenv.parse(fs.readFileSync(CONFIGURATION_FILE));
    } catch (_error) {
      console.log(`Warning: Could not read ${CONFIGURATION_FILE}, using process.env values`);
    }
  }

  // Print key environment variables (prefer .env file values, fallback to process.env)
  console.log(`ICT_ENV: ${envConfig.ICT_ENV || process.env.ICT_ENV || 'NOT SET'}`);
  console.log(`ICT_BASE_URL: ${envConfig.ICT_BASE_URL || process.env.ICT_BASE_URL || 'NOT SET'}`);
  console.log(`ICT_API_URL: ${envConfig.ICT_API_URL || process.env.ICT_API_URL || 'NOT SET'}`);
  console.log(`ICT_MOCK_API_URL: ${envConfig.ICT_MOCK_API_URL || process.env.ICT_MOCK_API_URL || 'NOT SET'}`);
  console.log(`EMULATE_CI: ${EMULATE_CI}`);
  console.log(`Running in CI: ${isCIJob()}`);

  if (EMULATE_CI) {
    console.log('⚠️  Running in CI emulation mode');
    console.log(`Local Environment Override: ${LOCAL_ENV || 'NOT SET'}`);
  }

  console.log('='.repeat(60));
}

function isCIJob() {
  if (EMULATE_CI) return true;
  return process.env.CI === 'true' || process.env.CI === '1';
}

async function downloadEnvironmentFile(file: string, token: string) {
  try {
    const response = await axios.get(SECURE_FILE_URL, { headers: { 'PRIVATE-TOKEN': token }, responseType: 'arraybuffer' });
    fs.writeFileSync(file, response.data);
  } catch (error) {
    const er = error as Error;
    console.log(er.message);
    throw new Error('Unable to download project secure files, make sure access token has Developer Access.');
  }
}

function updateEnvValue(key: string, newValue: string): boolean {
  try {
    const envConfig = dotenv.parse(fs.readFileSync(CONFIGURATION_FILE));
    let updatedContent = '';
    let keyExists = false;

    for (const k in envConfig) {
      if (k === key) {
        updatedContent += `${k}=${newValue}\n`;
        keyExists = true;
      } else {
        updatedContent += `${k}=${envConfig[k]}\n`;
      }
    }

    if (!keyExists) {
      updatedContent += `${key}=${newValue}\n`;
    }

    fs.writeFileSync(CONFIGURATION_FILE, updatedContent);
    return true;
  } catch (error) {
    console.error(`Error updating .env file: ${error}`);
    return false;
  }
}

export default globalSetup;
