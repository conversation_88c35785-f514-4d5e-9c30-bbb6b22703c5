import { Locator, <PERSON> } from '@playwright/test';
import { ITestConfigurationService } from '@ui-adk/services/configuration/test-configuration-service';

export class AuthErrorPage {
  private config: ITestConfigurationService;
  public page: Page;
  public title: Locator;
  public emailLink: Locator;
  public emailInput: Locator;
  public logOut: Locator;

  constructor(page: Page, config: ITestConfigurationService) {
    this.config = config;
    this.page = page;
    this.title = this.page.locator('h1');
    this.logOut = this.page.locator('.cds--btn--ghost');
    this.emailLink = this.page.locator('a[href^="mailto:"]');
    this.emailInput = this.page.locator('input[id="email"]');
  }

  public async getTitle(): Promise<string> {
    return await this.title.innerText();
  }

  public async logout() {
    await this.logOut.click();
  }

  public async waitForLoad() {
    await this.title.waitFor({ state: 'visible' });
  }
}
