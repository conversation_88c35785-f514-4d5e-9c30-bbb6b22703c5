import { test } from '@ui-tests/fixtures/regression-fixture';
import { TestGroup } from '@ui-tests/test-groups';
import Container from 'typedi';
import { TestConfigurationService } from '@ui-adk/services/configuration';
import { expect } from '@playwright/test';

test.describe(`Login Screen. User Signup. - ${TestGroup.Login} ${TestGroup.Regression}`, async () => {
  test(`[Test-Case @C3516583] [Test-Case @C3516584] - Test that a user with no tenants/organizations can login`, async ({
    logIn,
    authErrorPage,
  }) => {
    await logIn.goTo();
    await logIn.login(Container.get(TestConfigurationService).unorganizedUser);
    await authErrorPage.waitForLoad();
    const result = await authErrorPage.getTitle();
    expect(result).toBe('No Organizations');
  });
  test(`[Test-Case @C3516585] - Test that a user with no tenants/organizations can logout`, async ({ logIn, authErrorPage }) => {
    await logIn.goTo();
    await logIn.login(Container.get(TestConfigurationService).unorganizedUser);
    await authErrorPage.waitForLoad();
    await authErrorPage.logout();
    const result = await logIn.exists();
    expect(result).toBeTruthy();
  });
  test(`[Test-Case @C3516586] - Test that a user with no tenants/organizations can email to request access`, async ({
    logIn,
    authErrorPage,
  }) => {
    await logIn.goTo();
    await logIn.login(Container.get(TestConfigurationService).unorganizedUser);
    await authErrorPage.waitForLoad();
    await expect(authErrorPage.emailInput).toBeVisible();
  });
  test(`[Test-Case @C3519787] [Test-Case @C3519789] - Test that a user that is blocked can login`, async ({
    logIn,
    authErrorPage,
  }) => {
    await logIn.goTo();
    await logIn.login(Container.get(TestConfigurationService).blockedUser);
    await authErrorPage.waitForLoad();
    const result = await authErrorPage.getTitle();
    expect(result).toBe('Unknown Error');
  });
  test(`[Test-Case @C3519788] - Test that a user that is blocked can logout`, async ({ logIn, authErrorPage }) => {
    await logIn.goTo();
    await logIn.login(Container.get(TestConfigurationService).blockedUser);
    await authErrorPage.waitForLoad();
    await authErrorPage.logout();
    const result = await logIn.exists();
    expect(result).toBeTruthy();
  });
  test(`[Test-Case @C3519791] - Test that a user that is blocked can email to request access`, async ({ logIn, authErrorPage }) => {
    await logIn.goTo();
    await logIn.login(Container.get(TestConfigurationService).blockedUser);
    await authErrorPage.waitForLoad();
    await expect(authErrorPage.emailLink).toBeVisible();
  });
});
