import type {
  CategoryChartMapDefinition,
  ChartInfo,
} from "./category-chart-resolver-types";

export const categoryChartInfo: ChartInfo[] = [
  {
    id: "dms-movements-by-location",
    title: "DMS Movements by Location",
    description: "DMS Movements by Location.",
    groupBy: [
      "source_type",
      "destination_type",
      "source_location",
      "destination_location"
    ],
    dmsId: [
      "inventory",
      "shipping"
    ]
  },
  {
    id: "dms-movements-by-load-unit",
    title: "DMS Movements by Load Unit",
    description: "DMS Movements by Load Unit.",
    groupBy: [
      "loadunit",
      "sku",
    ],
    dmsId: [
      "inventory",
      "shipping"
    ]
  },
  {
    id: "dms-movements-by-day",
    title: "DMS Movements by Day",
    description: "DMS Movements by Day.",
    groupBy: [
      "day",
      "hour",
    ],
    dmsId: [
      "inventory",
      "shipping"
    ]
  }
];

export const categoryChartDefinitions: CategoryChartMapDefinition[] = [
  {
    id: "dms-movements-by-location",
    endpoint: "/movements/location",
    unit: "movements",
    series: [
      {
        id: "locationData",
      },
    ],
  },
  {
    id: "dms-movements-by-load-unit",
    endpoint: "/movements/load-units",
    unit: "movements",
    series: [
      {
        id: "data",
      },
    ],
  },  
  {
    id: "dms-movements-by-day",
    endpoint: "/movements/time-series",
    unit: "movements",
    series: [
      {
        id: "timeSeriesData",
      },
    ],
  }
];