import { vi } from "vitest";
import { ictApi } from "../../ict-api";
import { ChartResolver } from "./category-chart-resolver";
import {
  categoryChartDefinitions,
} from "./category-chart-resolver-data";

const startDate = new Date("2023-01-01");
const endDate = new Date("2023-01-31");
// Mock the ictApi
vi.mock("../../ict-api", () => ({
  ictApi: {
    client: {
      queryOptions: vi.fn().mockReturnValue({
        queryKey: ["test-key"],
      }),
    },
    queryClient: {
      fetchQuery: vi.fn(),
    },
  },
}));

// Mock the Logger
vi.mock("../../../utils", () => ({
  Logger: class {
    info = vi.fn();
    warn = vi.fn();
  },
}));

vi.mock("../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate,
    endDate,
  }),
}));

describe("ChartResolver", () => {
  let chartResolver: ChartResolver;

  beforeEach(() => {
    vi.clearAllMocks();
    chartResolver = new ChartResolver();
  });

  describe("getCategoryChart", () => {
    it("should fetch category chart data successfully", async () => {
      // Setup
      const mockResponse = {
        [categoryChartDefinitions[0].series[0].id]: [
          { name: "Category A", value: 10 },
          { name: "Category B", value: 20 },
        ],
      };

      (ictApi.queryClient.fetchQuery as any).mockResolvedValue(mockResponse);

      const filters = {
        datePeriodRange: "last30days",
      };

      // Execute
      const result = await chartResolver.getCategoryChart(
        categoryChartDefinitions[0].id as any,
        filters as any,
        startDate,
        endDate,
      );

      // Verify
      expect(result.success).toBe(true);

      // Type guard to ensure we have a successful response with data
      if (result.success && result.data !== null) {
        expect(result.data).toBeDefined();
        expect(result.data.id).toBe(categoryChartDefinitions[0].id);
      } else {
        expect.fail("Expected a successful response with category data");
      }

      expect(ictApi.client.queryOptions).toHaveBeenCalled();
      expect(ictApi.queryClient.fetchQuery).toHaveBeenCalled();
    });
  });

  describe("getCategoryChartInfo", () => {
    it("should return category chart info", async () => {
      const result = await chartResolver.getCategoryChartInfo();
      expect(Array.isArray(result)).toBe(true);
    });
  });
});
