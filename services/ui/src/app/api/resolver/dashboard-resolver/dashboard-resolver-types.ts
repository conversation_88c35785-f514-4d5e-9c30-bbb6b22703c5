import type { PathsWithMethod } from "openapi-typescript-helpers";
import type { paths } from "@ict/sdk/openapi-react-query";
 
export type DashboardSummaryType = "summary-movements";
 
export interface DashboardSummaryInfo {
  id: DashboardSummaryType;
  label: string;
  description: string;
  dms_ids: string[];
}

export type DashboardSummaryMapDefinition = {
  id: DashboardSummaryType;
  endpoint: PathsWithMethod<paths, "get"> | string;
  unit: string;
};

/** Future-proof value object for dashboard summary items */
export interface DashboardSummaryValue {
  totalMovements?: number | string;
  percentage?: number | string;
  time?: string;
  /** Any other future fields this API might provide */
  [key: string]: unknown;
}

/** One dashboard summary data point */
export interface DashboardSummaryItem {
  name: string;
  value: DashboardSummaryValue;
}

export type DashboardSummaryResponse =
  | {
    result: "204";
    id: DashboardSummaryType;
    label: string;
  }
  | {
    result: "200";
    id: DashboardSummaryType;
    label: string;
    data: DashboardSummaryItem[];
    /** Optional unit name (may not always be present) */
    unit?: string;
  };
