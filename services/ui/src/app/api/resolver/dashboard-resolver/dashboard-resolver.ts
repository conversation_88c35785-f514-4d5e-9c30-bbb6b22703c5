import type { PathsWithMethod } from "openapi-typescript-helpers";
import { Logger } from "../../../utils";
import { ictApi } from "../../ict-api";
import type { paths } from "@ict/sdk/openapi-react-query";
import { dashboardSummaryDefinitions, dashboardSummaryInfo } from "./dashboard-resolver-data";
import type {
  DashboardSummaryMapDefinition,
  DashboardSummaryResponse,
  DashboardSummaryType,
  DashboardSummaryInfo,
} from "./dashboard-resolver-types";
import type { ApiFilters } from "../../api.types";
import i18n, { i18nInitPromise } from "../../../config/i18n/i18n";
 
export class DashboardSummaryResolver {
  private logger;

  constructor() {
    this.logger = new Logger("DashboardSummaryResolver");
  }

  private buildUrl(
    summaryInfo: DashboardSummaryInfo,
    filters: ApiFilters,
    startDate: Date,
    endDate: Date
  ) {
    this.logger.info("buildQueryParams", filters);

    const definition = dashboardSummaryDefinitions.find(
      (d) => d.id === summaryInfo.id
    );
    if (!definition) {
      throw new Error(`Dashboard summary definition not found for id: ${summaryInfo.id}`);
    }

    const { autoRefresh: _autoRefresh, ...apiFilters } =
      filters as ApiFilters & { autoRefresh?: unknown };

    if (!apiFilters.dms_id) {
      throw new Error(`'dms_id' is required in filters for ${summaryInfo.id}`);
    }

    const queryParams = {
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString(),
      ...apiFilters,
    };

    return ictApi.client.queryOptions(
      "get",
      definition.endpoint as PathsWithMethod<paths, "get">,
      {
        params: { query: queryParams },
      }
    );
  }

  async getSummary(
    summaryId: DashboardSummaryType,
    filters: ApiFilters,
    startDate: Date,
    endDate: Date
  ): Promise<DashboardSummaryResponse> {
    await i18nInitPromise;

    const translatedSummaryInfo = dashboardSummaryInfo(i18n.t);
    const info = translatedSummaryInfo.find((i) => i.id === summaryId);
    if (!info) {
      throw new Error(`Dashboard summary info not found for id: ${summaryId}`);
    }

    const definition = dashboardSummaryDefinitions.find(
      (d) => d.id === summaryId
    ) as DashboardSummaryMapDefinition;
    if (!definition) {
      throw new Error(`Dashboard summary definition not found for id: ${summaryId}`);
    }
 
    const url = this.buildUrl(info, filters, startDate, endDate);
    const response = await ictApi.queryClient.fetchQuery(url);
 
    this.logger.info("DashboardSummaryResolver - response", response);
 
    if (!response || !Array.isArray(response) || response.length === 0) {
      return {
        result: "204",
        id: summaryId,
        label: info.label,
      };
    }

    return {
      result: "200",
      id: summaryId,
      label: info.label,
      data: response,
      unit: definition.unit,
    };
  }
 
  async getSummaryInfo(): Promise<DashboardSummaryInfo[]> {
    await i18nInitPromise;
    return dashboardSummaryInfo(i18n.t);
  }
}
 
export const dashboardSummaryResolver = new DashboardSummaryResolver();