import {
  timeChartDefinitions,
  timeChartInfo,
} from "../time-chart-resolver/time-chart-resolver-data";
import {
  HeatmapChartInfo,
  HeatmapChartMapDefinition,
} from "./heatmap-chart-resolver-types";

export const heatmapChartInfo: HeatmapChartInfo[] = [
  {
    id: "mock",
    title: "Mock",
    description: "Mock heatmap chart",
    sideTitle: "Date",
    bottomTitle: "Status",
  },
  ...timeChartInfo,
];

export const heatmapChartDefinitions: HeatmapChartMapDefinition[] = [
  {
    id: "mock",
    endpoint: "/mock",
    sideTitle: "Date",
    bottomTitle: "Status",
  },
  ...timeChartDefinitions,
];
