import {
  ChartInfo,
  TimeChartMapDefinition,
} from "../time-chart-resolver/time-chart-resolver-types";
import { HeatmapChartEndpoint, HeatmapChartType } from "./heatmap-chart-types";

export type HeatmapChartInfo =
  | {
      id: HeatmapChartType;
      title: string;
      description: string;
      sideTitle: string;
      bottomTitle: string;
    }
  | ChartInfo;

export type HeatmapChartMapDefinition =
  | {
      id: HeatmapChartType;
      endpoint: HeatmapChartEndpoint;
      sideTitle: string;
      bottomTitle: string;
    }
  | TimeChartMapDefinition;
