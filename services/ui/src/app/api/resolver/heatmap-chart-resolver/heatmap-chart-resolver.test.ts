import { Mock, vi } from "vitest";
import { HeatmapChartResolver } from "./heatmap-chart-resolver";
import { chartResolver } from "../time-chart-resolver/time-chart-resolver";
import { ApiFilters } from "../../api.types";

const startDate = new Date("2023-01-01");
const endDate = new Date("2023-01-31");

// Mock the ictApi
vi.mock("../../ict-api", () => ({
  ictApi: {
    client: {
      queryOptions: vi.fn().mockReturnValue({
        queryKey: ["test-key"],
      }),
    },
    queryClient: {
      fetchQuery: vi.fn(),
    },
  },
}));

// Mock the Logger
vi.mock("../../../utils", () => ({
  Logger: class {
    info = vi.fn();
    warn = vi.fn();
    error = vi.fn();
  },
  formatCamelCaseToTitleCase: (str: string) => str,
}));

// Mock useDates if it's used in the resolver directly, though it seems not to be
vi.mock("../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate,
    endDate,
  }),
}));

// Mock the timeChartResolver
vi.mock("../time-chart-resolver/time-chart-resolver", () => ({
  chartResolver: {
    getChart: vi.fn(),
  },
}));

describe("HeatmapChartResolver", () => {
  let heatmapChartResolver: HeatmapChartResolver;
  const defaultFilters: ApiFilters = {
    datePeriodRange: {
      startDate: new Date("2023-01-01"),
      endDate: new Date("2023-01-31"),
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    heatmapChartResolver = new HeatmapChartResolver();
  });

  describe("getHeatmapChart", () => {
    it("should return mock data for 'mock' id", async () => {
      const result = await heatmapChartResolver.getHeatmapChart(
        "mock",
        defaultFilters,
        startDate,
        endDate,
      );
      expect(result.success).toBe(true);

      if (result.success && result.data !== null) {
        expect(result.data).toBeDefined();
        expect(result.data.id).toBe("mock");
      } else {
        expect.fail("Expected a successful response with data");
      }
    });
    it("should return time chart data converted to heatmap format", async () => {
      const mockTimeChartData = {
        success: true,
        data: {
          id: "daily-replenishments",
          series: [
            {
              id: "dailyReplenishments",
              unit: "replenishments",
              data: [
                { unit: "2023-01-01", value: 10 },
                { unit: "2023-01-02", value: 20 },
              ],
            },
          ],
        },
      };

      (chartResolver.getChart as unknown as Mock).mockResolvedValue(
        mockTimeChartData,
      );

      const result = await heatmapChartResolver.getHeatmapChart(
        "daily-replenishments",
        defaultFilters,
        startDate,
        endDate,
      );

      expect(result.success).toBe(true);

      if (result.success && result.data !== null) {
        expect(result.data).toBeDefined();
        expect(result.data.id).toBe("daily-replenishments");
        expect(result.data.series[0].id).toBe("Daily Replenishments");
        expect(result.data.series[0].data[0]).toEqual({
          label: "2023-01-01",
          value: 10,
        });
      } else {
        expect.fail("Expected a successful response with data");
      }
    });

    it("should handle time chart data with multiple series", async () => {
      const mockTimeChartData = {
        success: true,
        data: {
          id: "daily-replenishments-by-shift",
          series: [
            {
              id: "shift1",
              unit: "replenishments",
              data: [
                { unit: "2023-01-01", value: 5 },
                { unit: "2023-01-02", value: 10 },
              ],
            },
            {
              id: "shift2",
              unit: "replenishments",
              data: [
                { unit: "2023-01-01", value: 3 },
                { unit: "2023-01-02", value: 7 },
              ],
            },
          ],
        },
      };

      (chartResolver.getChart as Mock).mockResolvedValue(mockTimeChartData);

      const result = await heatmapChartResolver.getHeatmapChart(
        "daily-replenishments-by-shift",
        defaultFilters,
        startDate,
        endDate,
      );

      expect(result.success).toBe(true);

      if (result.success && result.data !== null) {
        expect(result.data).toBeDefined();
        expect(result.data.id).toBe("daily-replenishments-by-shift");
        expect(result.data.series.length).toBe(2);
        expect(result.data.series[0].id).toBe("Shift1");
        expect(result.data.series[0].data[0]).toEqual({
          label: "2023-01-01",
          value: 5,
        });
        expect(result.data.series[1].id).toBe("Shift2");
        expect(result.data.series[1].data[0]).toEqual({
          label: "2023-01-01",
          value: 3,
        });
      } else {
        expect.fail("Expected a successful response with data");
      }
    });

    it("should return an error if the time chart resolver fails", async () => {
      (chartResolver.getChart as Mock).mockResolvedValue({
        success: false,
        error: new Error("Time chart error"),
      });

      const result = await heatmapChartResolver.getHeatmapChart(
        "daily-replenishments",
        defaultFilters,
        startDate,
        endDate,
      );

      expect(result.success).toBe(false);

      if (!result.success) {
        expect(result.error).toBeInstanceOf(Error);
        expect(result.error?.message).toBe("Time chart error");
      } else {
        expect.fail("Expected a failed response with an error");
      }
    });

    it("should return null data if the time chart resolver returns no data", async () => {
      (chartResolver.getChart as Mock).mockResolvedValue({
        success: true,
        data: null,
      });

      const result = await heatmapChartResolver.getHeatmapChart(
        "daily-replenishments",
        defaultFilters,
        startDate,
        endDate,
      );

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toBeNull();
      }
    });

    it("should throw an error for an unknown heatmap chart id", async () => {
      await expect(
        heatmapChartResolver.getHeatmapChart(
          "unknown-chart" as any,
          defaultFilters,
          startDate,
          endDate,
        ),
      ).rejects.toThrow("Chart info not found for id: unknown-chart");
    });
  });

  describe("getChartInfo", () => {
    it("should return heatmap chart info", async () => {
      const result = await heatmapChartResolver.getChartInfo();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      expect(result[0]).toHaveProperty("id");
      expect(result[0]).toHaveProperty("title");
    });
  });
});
