import { Logger } from "../../../utils";
import type { ApiFilters } from "../../api.types";
import {
  heatmapChartDefinitions,
  heatmapChartInfo,
} from "./heatmap-chart-resolver-data";
import type { HeatmapChartInfo } from "./heatmap-chart-resolver-types";
import type { HeatmapChartType } from "./heatmap-chart-types";
import type {
  ApiResult,
  TimeChartData,
} from "../time-chart-resolver/time-chart-resolver-types";
import { HeatmapChartData } from "../../../components/heatmap-chart";
import { timeChartDefinitions } from "../time-chart-resolver/time-chart-resolver-data";
import { chartResolver } from "../time-chart-resolver/time-chart-resolver";
import { TimeChartType } from "../time-chart-resolver/time-chart-types";
import { formatCamelCaseToTitleCase } from "../../../utils/string-util";

export class HeatmapChartResolver {
  private logger;
  constructor() {
    this.logger = new Logger("HeatmapChartResolver");
  }

  private getMockData(): HeatmapChartData {
    return {
      id: "mock",
      sideTitle: "Date",
      bottomTitle: "Status",
      series: [
        {
          id: "Available",
          data: [
            {
              label: "2025-05-08",
              value: 5,
            },
            {
              label: "2025-05-09",
              value: 6,
            },
            {
              label: "2025-05-10",
              value: 4,
            },
          ],
        },
        {
          id: "Faulted",
          data: [
            {
              label: "2025-05-08",
              value: 6,
            },
            {
              label: "2025-05-09",
              value: 2,
            },
            {
              label: "2025-05-10",
              value: 3,
            },
          ],
        },
        {
          id: "Offline",
          data: [
            {
              label: "2025-05-08",
              value: 8,
            },
            {
              label: "2025-05-09",
              value: 5,
            },
            {
              label: "2025-05-10",
              value: 3,
            },
          ],
        },
      ],
    };
  }

  public async getHeatmapChart(
    id: HeatmapChartType,
    filters: ApiFilters,
    startDate: Date,
    endDate: Date,
  ): Promise<ApiResult<HeatmapChartData>> {
    const info = heatmapChartInfo.find((info) => info.id === id);
    if (!info) {
      throw new Error(`Chart info not found for id: ${id}`);
    }

    const definition = heatmapChartDefinitions.find(
      (definition) => definition.id === id,
    );
    this.logger.info("definition", definition);
    if (!definition) {
      throw new Error(`Metric definition not found for id: ${id}`);
    }

    if (id === "mock") {
      return { success: true, data: this.getMockData() };
    }

    // Handle time chart types
    if (timeChartDefinitions.some((def) => def.id === id)) {
      const timeChartData = await chartResolver.getChart(
        id as TimeChartType,
        filters,
        startDate,
        endDate,
      );
      if (!timeChartData.success) {
        return { success: false, error: timeChartData.error };
      } else if (!timeChartData.data) {
        return { success: true, data: null };
      } else {
        return {
          success: true,
          data: this.timeChartToHeatmap(timeChartData.data as TimeChartData),
        };
      }
    }

    this.logger.error(`HeatmapChartResolver: Invalid chart type: ${id}`);
    return { success: false, error: new Error("Invalid chart type") };
  }

  public async getChartInfo(): Promise<HeatmapChartInfo[]> {
    return heatmapChartInfo;
  }

  private timeChartToHeatmap(timeChartData: TimeChartData) {
    const heatmapData: HeatmapChartData = {
      id: timeChartData.id,
      sideTitle: "Date",
      series: timeChartData.series.map((series) => ({
        id: formatCamelCaseToTitleCase(series.id),
        data: series.data.map((point) => ({
          label: point.unit,
          value: point.value,
        })),
      })),
    };
    return heatmapData;
  }
}

export const heatmapChartResolver = new HeatmapChartResolver();
