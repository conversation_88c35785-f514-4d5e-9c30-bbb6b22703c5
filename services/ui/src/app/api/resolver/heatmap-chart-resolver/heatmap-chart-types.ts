/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  timeChartEndpoints,
  timeChartTypes,
} from "../time-chart-resolver/time-chart-types";

const heatmapChartTypes: string[] = ["mock", ...timeChartTypes] as const;

export type HeatmapChartType = (typeof heatmapChartTypes)[number];

const heatmapChartEndpoints: string[] = [
  "/mock",
  ...timeChartEndpoints,
] as const;

export type HeatmapChartEndpoint = (typeof heatmapChartEndpoints)[number];
