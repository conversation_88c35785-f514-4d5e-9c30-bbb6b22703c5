import { describe, expect, it } from "vitest";
import { searchFilterDefinitions, searchFilterInfo } from "./search-filter-resolver-data";
import i18n from "../../../../test-utils/i18n-testing";

describe("kpi-resolver-data", () => {
    describe("searchFilterInfo", () => {
        it("should contain multiple metric info entries", () => {
            const t = i18n.t;
            const filterInfoArray = searchFilterInfo(t);
            expect(filterInfoArray).toBeInstanceOf(Array);
            expect(filterInfoArray.length).toBeGreaterThan(0);

            const dmsInfo = filterInfoArray.find(
                (info) => info.id === "dms-type",
            );
            expect(dmsInfo).toBeDefined();
        });
    });

    describe("searchFilterDefinitions", () => {
        it("should contain multiple metric definitions", () => {
            expect(searchFilterDefinitions).toBeInstanceOf(Array);
            expect(searchFilterDefinitions.length).toBeGreaterThan(0);

            const filterDefinition = searchFilterDefinitions.find(
                (def) => def.id === "dms-type",
            );
            expect(filterDefinition).toBeDefined();
        });

        it("should have matching definitions for each metric info", () => {
            const t = i18n.t;
            const infoIds = searchFilterInfo(t).map((info) => info.id);
            const definitionIds = searchFilterDefinitions.map((def) => def.id);

            // Check if all info items have a corresponding definition
            for (const id of infoIds) {
                expect(definitionIds).toContain(id);
            }
        });
    });
});
