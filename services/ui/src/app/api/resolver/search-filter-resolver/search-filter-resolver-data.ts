import type { SearchFilterInfo } from "./types";
import type { SearchFilterMapDefinition } from "./search-filter-resolver-types";
import { TFunction } from "i18next";

// Create  typescript type for every "id" field string in the metricInfo array

export const searchFilterInfo: (t: TFunction) => SearchFilterInfo[] = (t: TFunction) => [
  {
    id: "dms-type",
    label: t("DMS Type"),
    description: "Type of DMS (inventory/shipping).",
    apiKey:"dms_id"
  },
  {
    id: "dms-unit-type",
    label: t("Load Unit or SKU"),
    description: "Type of DMS Unit (load-unit/sku).",
    enableSearch: true,
    apiKey:"unit_type",
    apiSearchKey: "unit_id" 
  },
];

export const searchFilterDefinitions: SearchFilterMapDefinition[] = [
  {
    id: "dms-type",
    endpoint: "",
    groupBy: [
      "inventory",
      "shipping"
    ],
  },
  {
    id: "dms-unit-type",
    endpoint: "",
    groupBy: [
      "loadunit",
      "sku"
    ],
  },
];
