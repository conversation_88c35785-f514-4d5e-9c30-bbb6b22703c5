import { Logger } from "../../../utils";
import { searchFilterDefinitions, searchFilterInfo } from "./search-filter-resolver-data";
import type { SearchFilterTypes } from "./search-filter-types";
import type { SearchFilterInfo, SearchFilterResponse } from "./types";
import i18n, { i18nInitPromise } from "../../../config/i18n/i18n";

export class SearchFilterResolver {
  private logger;
  constructor() {
    this.logger = new Logger("SearchFilterResolver");
  }

  async getFilter(
    filterId: SearchFilterTypes,
  ): Promise<SearchFilterResponse> {
    await i18nInitPromise;
    const translatedMetricInfo = searchFilterInfo(i18n.t);
    const info = translatedMetricInfo.find((info) => info.id === filterId);
    if (!info) {
      throw new Error(`Search Filter info not found for id: ${filterId}`);
    }

    const definition = searchFilterDefinitions.find(
      (definition) => definition.id === filterId,
    );

    if (!definition) {
      throw new Error(`Search Filter definition not found for id: ${filterId}`);
    }

    let response;

    if (definition.groupBy) {
      response = definition.groupBy;
    } else if(definition.endpoint) {
      console.log("endpoint")
    }

    if (!response) {
      return {
        result: "204",
        id: filterId,
        label: info.label,
        value: []
      };
    }

    const searchFilterResponse = {
      result: "200",
      id: filterId,
      label: info.label,
      value: response,
    }
    return searchFilterResponse as SearchFilterResponse;
  }

  async getSearchFilterInfo(): Promise<SearchFilterInfo[]> {
    await i18nInitPromise;
    const translatedSearchFilterInfo = searchFilterInfo(i18n.t);
    return translatedSearchFilterInfo;
  }
}

export const searchFilterResolver = new SearchFilterResolver();
