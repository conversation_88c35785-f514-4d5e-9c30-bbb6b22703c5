/* eslint-disable @typescript-eslint/no-unused-vars */
export const timeChartTypes = [
  "orders-customer-line-progress",
  "orders-facility-line-progress-series",
  "orders-customer-line-throughput",
  "orders-facility-line-throughput",
  "inventory-stock-distribution-no-percentage",
  "inventory-stock-distribution-under-percentage",
  "inventory-stock-distribution-over-percentage",
  "inventory-stock-distribution-at-percentage",
  "inventory-wms-stock-distribution-at-percentage",
  "inventory-wms-stock-distribution-no-percentage",
  "inventory-wms-stock-distribution-under-percentage",
  "inventory-wms-stock-distribution-over-percentage",
  "faults",
  "daily-replenishments",
  "daily-pending-orders",
  "daily-cycle-times",
  "daily-replenishments-by-shift",
  "replenishment-task-type-data",
] as const;

export type TimeChartType = (typeof timeChartTypes)[number];

const categoryChartTypes = ["faults"] as const;
export type CategoryChartType = (typeof categoryChartTypes)[number];

export const timeChartEndpoints = [
  "/orders/customer/line/progress/series",
  "/orders/customer/line/throughput/series",
  "/orders/facility/line/throughput/series",
  "/orders/facility/line/progress/series",
  "/inventory/stock/distribution/no/percentage/series",
  "/inventory/stock/distribution/under/percentage/series",
  "/inventory/stock/distribution/over/percentage/series",
  "/inventory/stock/distribution/at/percentage/series",
  "/inventory/wms/stock/distribution/no/percentage/series",
  "/inventory/wms/stock/distribution/under/percentage/series",
  "/inventory/wms/stock/distribution/over/percentage/series",
  "/inventory/wms/stock/distribution/at/percentage/series",
  "/equipment/faults",
  "/inventory/replenishment/details",
  "/inventory/replenishment/task-type-series",
] as const;
export type TimeChartEndpoint = (typeof timeChartEndpoints)[number];

const categoryChartEndpoints = [
  "/equipment/faults/grouped/count/series",
] as const;
export type CategoryChartEndpoint = (typeof categoryChartEndpoints)[number];
