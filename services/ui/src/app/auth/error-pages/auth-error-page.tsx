import {
  Reset,
  Group,
  Email,
  Logout,
  Unknown,
  UserAccessUnlocked,
} from "@carbon/icons-react";
import { useAuth } from "../hooks/use-auth";
import { ErrorView } from "../../components/error-view/error-view";
import { useTranslation } from "react-i18next";
import { useResendEmailVerification } from "../../config/hooks/use-admin";
import { ToastContainer } from "../../components/toast/toast-container";
import { Form, Layer, Stack, TextInput } from "@carbon/react";
import { useState } from "react";
import { useNotification } from "../../components/toast/use-notification";

interface AuthErrorPageProps {
  errorDescription: string | null;
}

export function AuthErrorPage({ errorDescription }: AuthErrorPageProps) {
  const { logout, getAccessTokenSilently, user } = useAuth();
  const { t } = useTranslation();
  const { success, error: showError } = useNotification();
  const { resendVerification, isLoading: isResendingEmail } =
    useResendEmailVerification();

  const [organization, setOrganization] = useState(
    user?.organization?.display_name || "",
  );
  const [organizationError, setOrganizationError] = useState("");
  const [sites, setSites] = useState("");
  const [email, setEmail] = useState(user?.email || "");
  const [emailError, setEmailError] = useState("");
  const [additionalInfo, setAdditionalInfo] = useState("");

  // Function to validate email format
  const isValidEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  // Function to validate form
  const validateForm = () => {
    let isValid = true;

    if (!organization.trim()) {
      setOrganizationError(
        t("accessRequestForm.organizationRequired", "Organization is required"),
      );
      isValid = false;
    } else {
      setOrganizationError("");
    }

    if (!email.trim()) {
      setEmailError(
        t("accessRequestForm.emailRequired", "Email address is required"),
      );
      isValid = false;
    } else if (!isValidEmail(email)) {
      setEmailError(
        t(
          "accessRequestForm.emailInvalid",
          "Please enter a valid email address",
        ),
      );
      isValid = false;
    } else {
      setEmailError("");
    }

    return isValid;
  };

  // Function to clear query parameters and refresh the page
  const handleRefresh = () => {
    // Create a new URL object based on the current URL
    const url = new URL(window.location.href);

    // Replace the current URL without the query parameters
    window.history.replaceState({}, document.title, url.pathname);

    // Reload the page to trigger a fresh authentication attempt
    window.location.reload();
  };

  const handleEmailValidationRefresh = async () => {
    try {
      // Force token refresh
      await getAccessTokenSilently({ cacheMode: "off" });

      // Reload the page to re-init any logic
      window.location.reload();
    } catch (error) {
      console.error("Post email validation token refresh failed :", error);
      // Force logout to clear the bad state
      logout({ logoutParams: { returnTo: window.location.origin } });
    }
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await fetch(
        "https://kion.webhook.office.com/webhookb2/c25450f7-9adb-44f5-b80f-2aef0fc29efe@13c728e0-bb0c-4cf7-8e10-5b327279d6d9/IncomingWebhook/3900a2435abf437291ca01b92ae8caed/3eee62c4-7570-4031-98b7-d70ea24c005c/V2DfP5VI89kL7kVJ52lI5KYbJXoYfmOzRsPTeJcI0YfzQ1",
        {
          method: "POST",
          mode: "no-cors",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            type: "message",
            attachments: [
              {
                contentType: "application/vnd.microsoft.card.adaptive",
                content: {
                  type: "AdaptiveCard",
                  version: "1.0",
                  body: [
                    {
                      type: "TextBlock",
                      size: "large",
                      weight: "bolder",
                      text: "New Access Request",
                      wrap: true,
                    },
                    {
                      type: "FactSet",
                      facts: [
                        {
                          title: "User Name",
                          value: user?.name || "Not provided",
                        },
                        {
                          title: "Email",
                          value: email || user?.email || "Not provided",
                        },
                        {
                          title: "Organization",
                          value: organization,
                        },
                        {
                          title: "Sites",
                          value: sites,
                        },
                        {
                          title: "Additional Information",
                          value: additionalInfo || "None provided",
                        },
                        {
                          title: "Environment",
                          value: import.meta.env.VITE_ENVIRONMENT,
                        },
                      ],
                    },
                  ],
                },
              },
            ],
          }),
        },
      );

      // With mode: "no-cors", we can't read response.ok, but if we get here without an error, it worked
      setIsSubmitted(true);
      success(
        t(
          "accessRequestForm.submitSuccess",
          "Your access request has been submitted successfully",
        ),
      );
    } catch (err) {
      showError(
        t(
          "accessRequestForm.submitError",
          "Failed to submit access request. Please try again.",
        ),
      );
      console.error("Error submitting access request:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // (1) No Orgs/No Facilities
  if (
    errorDescription?.includes("organization membership") ||
    errorDescription?.includes("no facility access")
  ) {
    let title = "";
    let description = "";
    if (errorDescription?.includes("organization membership")) {
      title = t("authErrorPage.noOrganisations", "No Organizations");
      description = t(
        "authErrorPage.noOrganisationsDescription",
        "Your account isn't part of any organization yet. Please provide the organization and sites you need access to.",
      );
    } else {
      title = t("authErrorPage.noFacilityAccess", "No Facility Access");
      description = t(
        "authErrorPage.noFacilityAccessDescription",
        "Your account does not have access to any facilities. Please contact your administrator to get access.",
      );
    }
    return (
      <>
        <ErrorView
          icon={<Group />}
          title={title}
          description={
            <div style={{ maxWidth: "600px", margin: "0 auto" }}>
              <div style={{ marginBottom: "1.5rem" }}>
                {isSubmitted ? (
                  t(
                    "authErrorPage.requestSubmitted",
                    "Thank you for your request. Our team will review it and get back to you shortly. You may close this window or log out.",
                  )
                ) : (
                  <>
                    {description}
                    <Form style={{ marginTop: "1.5rem" }}>
                      <Stack gap={5}>
                        <Layer>
                          <TextInput
                            id="email"
                            labelText={t(
                              "accessRequestForm.emailLabel",
                              "Email Address (Required)",
                            )}
                            value={email}
                            onChange={(e) => {
                              setEmail(e.target.value);
                              if (
                                e.target.value.trim() &&
                                isValidEmail(e.target.value)
                              ) {
                                setEmailError("");
                              }
                            }}
                            placeholder={t(
                              "accessRequestForm.emailPlaceholder",
                              "Enter your email address",
                            )}
                            type="email"
                            invalid={!!emailError}
                            invalidText={emailError}
                            required
                          />
                        </Layer>

                        <Layer>
                          <TextInput
                            id="organization"
                            labelText={t(
                              "accessRequestForm.organizationLabel",
                              "Organization (Required)",
                            )}
                            value={organization}
                            onChange={(e) => {
                              setOrganization(e.target.value);
                              if (e.target.value.trim()) {
                                setOrganizationError("");
                              }
                            }}
                            placeholder={t(
                              "accessRequestForm.organizationPlaceholder",
                              "Enter the organization or company name you would like access to (e.g., Dematic)",
                            )}
                            invalid={!!organizationError}
                            invalidText={organizationError}
                            required
                          />
                        </Layer>

                        <Layer>
                          <TextInput
                            id="sites"
                            labelText={t(
                              "accessRequestForm.sitesLabel",
                              "Sites",
                            )}
                            value={sites}
                            onChange={(e) => setSites(e.target.value)}
                            placeholder={t(
                              "accessRequestForm.sitesPlaceholder",
                              "List the facility locations you would like access to (e.g., New York, NY or Los Angeles, CA)",
                            )}
                          />
                        </Layer>

                        <Layer>
                          <TextInput
                            id="additional-info"
                            labelText={t(
                              "accessRequestForm.additionalInfoLabel",
                              "Additional Information (Optional)",
                            )}
                            value={additionalInfo}
                            onChange={(e) => setAdditionalInfo(e.target.value)}
                            placeholder={t(
                              "accessRequestForm.additionalInfoPlaceholder",
                              "Any additional information that would help with your request",
                            )}
                          />
                        </Layer>
                      </Stack>
                    </Form>
                  </>
                )}
              </div>
            </div>
          }
          primaryAction={
            !isSubmitted
              ? {
                  label: t("authErrorPage.submitRequest", "Submit Request"),
                  icon: UserAccessUnlocked,
                  onClick: handleSubmit,
                  disabled: isSubmitting,
                }
              : undefined
          }
          secondaryAction={
            !isSubmitted
              ? {
                  label: t("authErrorPage.refresh", "Refresh"),
                  onClick: handleRefresh,
                  icon: Reset,
                }
              : undefined
          }
          tertiaryAction={{
            label: t("authErrorPage.logout", "Log Out"),
            icon: Logout,
            onClick: () =>
              logout({ logoutParams: { returnTo: window.location.origin } }),
          }}
        />
        <ToastContainer position="top-right" />
      </>
    );
  }

  // (2) Email Not Verified
  if (errorDescription?.includes("not-verified")) {
    return (
      <>
        <ErrorView
          icon={<Email />}
          title={t("authErrorPage.unverifiedEmail", "Email Not Verified")}
          description={t(
            "authErrorPage.unverifiedEmailDescription",
            "Please verify your email address to continue. Check your inbox for a verification link.",
          )}
          primaryAction={{
            label: t(
              "authErrorPage.unverifiedEmailResend",
              "Resend Verification Email",
            ),
            icon: Reset,
            onClick: () => resendVerification(),
            disabled: isResendingEmail,
          }}
          secondaryAction={{
            label: t("authErrorPage.refresh", "Refresh"),
            onClick: handleEmailValidationRefresh,
            icon: Reset,
          }}
          tertiaryAction={{
            label: t("authErrorPage.logout", "Log Out"),
            icon: Logout,
            onClick: () =>
              logout({ logoutParams: { returnTo: window.location.origin } }),
          }}
        />
        <ToastContainer position="top-right" />
      </>
    );
  }

  // Unknown error fallback
  return (
    <>
      <ErrorView
        icon={<Unknown />}
        title={t("authErrorPage.unknownError", "Unknown Error")}
        description={t(
          "authErrorPage.unknownErrorDescription",
          "An unknown error occurred. Please contact your administrator.",
        )}
        primaryAction={{
          label: t(
            "authErrorPage.contactAdministrator",
            "Contact Administrator",
          ),
          icon: Email,
          href: "mailto:<EMAIL>",
        }}
        secondaryAction={{
          label: t("authErrorPage.refresh", "Refresh"),
          onClick: handleRefresh,
          icon: Reset,
        }}
        tertiaryAction={{
          label: t("authErrorPage.logout", "Log Out"),
          icon: Logout,
          onClick: () =>
            logout({ logoutParams: { returnTo: window.location.origin } }),
        }}
      />
      <ToastContainer position="top-right" />
    </>
  );
}
