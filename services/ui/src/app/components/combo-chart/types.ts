import { CategoryChartData } from "src/app/api/resolver/category-chart-resolver/category-chart-resolver-types";
import { TimeChartData } from "../../api/resolver/time-chart-resolver/time-chart-resolver-types";

export interface ComboChartDataPoint {
  date: string;
  value: number;
  group: string;
}

export type ChartStyle =
  | "line"
  | "area"
  | "column"
  | "stacked-column"
  |  "stacked-row"
  | "stacked-area";

export interface ComboChartComponentProps {
  chartData: TimeChartData | CategoryChartData;
  title?: string;
  chartStyle?: ChartStyle;
  showAverageLine?: boolean;
  showTargetLine?: boolean;
  showLegend?: boolean;
  targetValue?: number;
  timezone?: string;
  dateFormat?: Intl.DateTimeFormatOptions;
  height?: string;
  width?: string;
  color?: string | string[];
}
