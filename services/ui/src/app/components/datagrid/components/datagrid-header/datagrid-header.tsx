import { Export, Renew, Settings } from "@carbon/icons-react"; // Consolidated icons
import {
  Button,
  Checkbox,
  Search,
  DismissibleTag,
  Popover,
  PopoverContent,
  CheckboxGroup,
  RadioButtonGroup,
  RadioButton,
  Tooltip,
  TableBatchActions,
  TableBatchAction,
} from "@carbon/react";
import { useEffect, useRef, useState } from "react";
import type {
  DatagridHeaderProps,
  ExportOption,
  LayoutDensity,
} from "../../types";
import styles from "./datagrid-header.module.scss";
import { useTranslation } from "react-i18next";
import { ActionBar } from "../../../action-bar/action-bar";

/**
 * DatagridHeader component provides a header for data grids with filtering, column visibility,
 * density adjustment, and export functionality.
 *
 * @param globalFilter - Current global filter value
 * @param onGlobalFilterChange - Callback when global filter changes
 * @param columnVisibility - Object tracking visibility state of each column
 * @param onColumnVisibilityChange - Callback when column visibility changes
 * @param columns - Array of column definitions
 * @param density - Current layout density setting
 * @param onDensityChange - Callback when density setting changes
 * @param onExport - Optional callback for export functionality
 * @param onRefreshClick - Optional callback for refresh functionality
 * @param className - Optional additional CSS class
 * @param selectedRowCount - Number of selected rows
 * @param onClearSelection - Callback to clear row selection
 * @param batchActions - list of batch actions
 * @param actionBarItems - Items to render in the action bar
 */
export function DatagridHeader({
  globalFilter,
  onGlobalFilterChange,
  columnVisibility,
  onColumnVisibilityChange,
  columns,
  density,
  onDensityChange,
  onExport,
  exportOptions,
  onExportAction,
  onRefreshClick,
  className,
  selectedRowCount = 0,
  onClearSelection = () => {},
  batchActions,
  actionBarItems,
}: DatagridHeaderProps & {
  exportOptions?: ExportOption[];
  onExportAction?: (actionId: string) => void;
}) {
  const { t } = useTranslation();
  const [isExportMenuOpen, setIsExportMenuOpen] = useState(false);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);

  // Refs for the menu components
  const popoverButtonRef = useRef<HTMLButtonElement>(null);
  const popoverContentRef = useRef<HTMLDivElement>(null);
  const exportButtonRef = useRef<HTMLButtonElement>(null);
  const exportMenuRef = useRef<HTMLDivElement>(null);

  // Close menus when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (!isPopoverOpen && !isExportMenuOpen) return;

      const target = event.target as Node;

      // Handle popover - close if click is outside both the popover and its button
      if (isPopoverOpen) {
        const isClickInPopover = popoverContentRef.current?.contains(target);
        const isClickOnPopoverButton =
          popoverButtonRef.current?.contains(target);
        const isClickInPopoverContent = document
          .querySelector(".cds--popover-content")
          ?.contains(target);

        if (
          !isClickInPopover &&
          !isClickOnPopoverButton &&
          !isClickInPopoverContent
        ) {
          setIsPopoverOpen(false);
        }
      }

      if (isExportMenuOpen) {
        const isClickInExportMenu = exportMenuRef.current?.contains(target);
        const isClickOnExportButton = exportButtonRef.current?.contains(target);

        if (!isClickInExportMenu && !isClickOnExportButton) {
          setIsExportMenuOpen(false);
        }
      }
    }

    // Add event listener
    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("click", handleClickOutside);

    // Add escape key handler to close menus
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setIsPopoverOpen(false);
        setIsExportMenuOpen(false);
      }
    };

    document.addEventListener("keydown", handleEscKey);

    // Clean up
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("click", handleClickOutside);
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [isPopoverOpen, isExportMenuOpen]);

  const handleColumnVisibilityChange = (
    columnId: string,
    isVisible: boolean,
  ) => {
    const newVisibility = {
      ...columnVisibility,
      [columnId]: isVisible,
    };
    onColumnVisibilityChange(newVisibility);
  };

  const handleSelectAllColumns = (
    _event: unknown,
    { checked }: { checked: boolean },
  ) => {
    const newVisibility = columns.reduce(
      (acc, column) => ({
        ...acc,
        [column.id]: checked,
      }),
      {},
    );
    onColumnVisibilityChange(newVisibility);
  };

  const areAllColumnsVisible = columns.every(
    (column) => columnVisibility[column.id] !== false,
  );

  const handleDensityChange = (newDensity: LayoutDensity) => {
    onDensityChange(newDensity);
  };

  const toggleExportMenu = () => {
    setIsExportMenuOpen(!isExportMenuOpen);
  };

  return (
    <div className={`${styles.header} ${className || ""}`}>
      <TableBatchActions
        className={styles.batchActionsHeader}
        data-testid="table-batch-actions"
        onCancel={onClearSelection}
        totalSelected={selectedRowCount}
        shouldShowBatchActions={selectedRowCount > 0}
      >
        {batchActions?.map((action) => {
          if (action.disabled && action.tooltip) {
            // For disabled actions with tooltips, create a wrapper that captures hover events
            return (
              <Tooltip key={action.label} label={action.tooltip} align="top">
                <span
                  style={{
                    display: "inline-block",
                    cursor: "not-allowed",
                  }}
                >
                  <TableBatchAction
                    data-testid={`table-batch-action-${action.label}`}
                    onClick={() => {}} // No-op for disabled actions
                    renderIcon={action.icon ?? (() => null)}
                    disabled={true}
                    style={{
                      pointerEvents: "none", // Prevent the disabled button from blocking tooltip
                    }}
                  >
                    {action.label}
                  </TableBatchAction>
                </span>
              </Tooltip>
            );
          }

          // Regular enabled action
          return (
            <TableBatchAction
              data-testid={`table-batch-action-${action.label}`}
              key={action.label}
              onClick={action.onClick}
              renderIcon={action.icon ?? (() => null)}
              disabled={action.disabled}
            >
              {action.label}
            </TableBatchAction>
          );
        })}
      </TableBatchActions>
      <div className={styles.leftSection}>
        <Search
          labelText={t("datagridHeader.searchTable", "Search Table")}
          placeholder={t("datagridHeader.searchTable", "Search Table")}
          value={globalFilter}
          onChange={(e) => onGlobalFilterChange(e.target.value)}
          size="md"
          style={{ borderBottom: "none" }}
        />

        {/* Selection count tag */}
        {selectedRowCount > 0 && onClearSelection && (
          <div className={styles.selectionTag}>
            <DismissibleTag type="gray" onClose={onClearSelection} text={`${selectedRowCount} row${selectedRowCount !== 1 ? "s" : ""} selected`}/>
          </div>
        )}
      </div>

      <div className={styles.rightSection}>
        {/* Refresh button */}
        {onRefreshClick && (
          <Button
            kind="ghost"
            size="md"
            hasIconOnly
            renderIcon={Renew}
            iconDescription="Refresh"
            onClick={onRefreshClick}
            data-testid="button-refresh"
            className={styles.rightSectionButton}
          />
        )}

        {/* Settings popover */}
        <Popover isTabTip align="bottom-right" open={isPopoverOpen} dropShadow>
          <Button
            ref={popoverButtonRef}
            kind="ghost"
            size="md"
            hasIconOnly
            renderIcon={Settings}
            iconDescription="Settings"
            onClick={() => setIsPopoverOpen(!isPopoverOpen)}
            data-testid="button-settings"
            className={`${styles.rightSectionButton} ${isPopoverOpen ? styles.buttonOpen : ""}`}
            aria-expanded={isPopoverOpen}
          />
          <PopoverContent
            ref={popoverContentRef}
            className={styles.columnVisibilityPopoverContent}
          >
            <RadioButtonGroup
              legendText="Row Density"
              name="row-density"
              valueSelected={density}
              onChange={(value) => handleDensityChange(value as LayoutDensity)}
              orientation="vertical"
              className={styles.radioButtonGroup}
            >
              <RadioButton
                labelText="Compact"
                value="compact"
                id="density-compact"
              />
              <RadioButton
                labelText="Default"
                value="default"
                id="density-default"
              />
              <RadioButton
                labelText="Relaxed"
                value="relaxed"
                id="density-relaxed"
              />
            </RadioButtonGroup>
            <CheckboxGroup legendText="Column Visibility">
              <Checkbox
                id="select-all-columns"
                labelText="Select All"
                checked={areAllColumnsVisible}
                onChange={handleSelectAllColumns}
                className={styles.selectAllCheckbox}
              />
              {columns.map((column) => (
                <Checkbox
                  key={column.id}
                  className={styles.checkbox}
                  id={`popover-checkbox-${column.id}`}
                  labelText={column.label}
                  checked={columnVisibility[column.id] !== false}
                  onChange={(_event, { checked }) => {
                    handleColumnVisibilityChange(column.id, checked);
                  }}
                />
              ))}
            </CheckboxGroup>
          </PopoverContent>
        </Popover>

        {/* Export button */}
        <div className={styles.exportContainer}>
          {exportOptions && exportOptions.length > 0 && onExportAction ? (
            <>
              <Button
                kind="primary"
                size="md"
                renderIcon={Export}
                hasIconOnly
                iconDescription={t(
                  "datagridHeader.exportOptions",
                  "Export Options",
                )}
                onClick={toggleExportMenu}
                ref={exportButtonRef}
                className={styles.exportButton}
              />
              {isExportMenuOpen && (
                <div className={styles.exportMenu} ref={exportMenuRef}>
                  {" "}
                  <ul className="cds--overflow-menu--flip">
                    {" "}
                    {exportOptions.map((option) => (
                      <li
                        key={option.id}
                        className={`cds--overflow-menu-options__option ${
                          option.disabled
                            ? "cds--overflow-menu-options__option--disabled"
                            : ""
                        }`}
                      >
                        <button
                          className="cds--overflow-menu-options__btn"
                          type="button"
                          title={option.text}
                          onClick={() => {
                            if (!option.disabled) {
                              onExportAction(option.id);
                              setIsExportMenuOpen(false);
                            }
                          }}
                          disabled={option.disabled}
                          tabIndex={isExportMenuOpen ? 0 : -1}
                        >
                          <span className="cds--overflow-menu-options__option-content">
                            {" "}
                            {option.text}
                          </span>
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </>
          ) : (
            onExport && (
              <Button
                data-testid="button-export"
                kind="ghost"
                size="md"
                hasIconOnly
                renderIcon={Export}
                iconDescription={t("datagridHeader.export", "Export")}
                onClick={onExport}
                className={styles.exportButton}
                tooltipPosition="top"
                tooltipAlignment="end"
              />
            )
          )}
        </div>
        {/* Action Bar */}
        {actionBarItems && <ActionBar>{actionBarItems}</ActionBar>}
      </div>
    </div>
  );
}
