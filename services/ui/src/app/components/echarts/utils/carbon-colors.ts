/**
 * Carbon Design System color utility for charts
 * Provides consistent colors based on IBM Carbon palette
 */

export interface ColorOption {
  id: string;
  text: string;
  lightHex: string;
  darkHex: string;
}

export const CARBON_COLORS: ColorOption[] = [
  {
    id: "purple",
    text: "Purple",
    lightHex: "#6929c4", // Purple 70
    darkHex: "#8a3ffc", // Purple 60
  },
  {
    id: "cyan",
    text: "<PERSON><PERSON>",
    lightHex: "#1192e8", // Cyan 50
    darkHex: "#33b1ff", // Cyan 40
  },
  {
    id: "teal",
    text: "Teal",
    lightHex: "#005d5d", // Teal 70
    darkHex: "#007d79", // Teal 60
  },
  {
    id: "magenta",
    text: "Magenta",
    lightHex: "#9f1853", // Magenta 70
    darkHex: "#ff7eb6", // Magenta 40
  },
  {
    id: "red",
    text: "Red",
    lightHex: "#fa4d56", // Red 50
    darkHex: "#fa4d56", // Red 50
  },
  {
    id: "red-dark",
    text: "Red (Dark)",
    lightHex: "#570408", // Red 90
    darkHex: "#fff1f1", // Red 10
  },
  {
    id: "green",
    text: "Green",
    lightHex: "#198038", // Green 60
    darkHex: "#6fdc8c", // Green 30
  },
  {
    id: "blue",
    text: "Blue",
    lightHex: "#002d9c", // Blue 80
    darkHex: "#4589ff", // Blue 50
  },
  {
    id: "magenta-light",
    text: "Magenta (Light)",
    lightHex: "#ee538b", // Magenta 50
    darkHex: "#d12771", // Magenta 60
  },
  {
    id: "yellow",
    text: "Yellow",
    lightHex: "#b28600", // Yellow 50
    darkHex: "#d2a106", // Yellow 40
  },
  {
    id: "teal-light",
    text: "Teal (Light)",
    lightHex: "#009d9a", // Teal 50
    darkHex: "#08bdba", // Teal 40
  },
  {
    id: "cyan-dark",
    text: "Cyan (Dark)",
    lightHex: "#012749", // Cyan 90
    darkHex: "#bae6ff", // Cyan 20
  },
  {
    id: "orange",
    text: "Orange",
    lightHex: "#8a3800", // Orange 70
    darkHex: "#ba4e00", // Orange 60
  },
  {
    id: "purple-light",
    text: "Purple (Light)",
    lightHex: "#a56eff", // Purple 50
    darkHex: "#d4bbff", // Purple 30
  },
];

export const DASHBOARD_COLORS: ColorOption[] = [
  {
    id: "storage",
    text: "Storage",
    lightHex: "#ee5396",
    darkHex: "#4589ff",
  },
  {
    id: "shuffle",
    text: "Shuffle",
    lightHex: "#009d9a",
    darkHex: "#bae6ff",
  },
  {
    id: "retrieval",
    text: "Retrieval",
    lightHex: "#012749",
    darkHex: "#08bdba",
  },
  {
    id: "bypass",
    text: "Bypass",
    lightHex: "#6929c4",
    darkHex: "#8a3ffc",
  },
  {
    id: "iat",
    text: "IAT",
    lightHex: "#8a3800",
    darkHex: "#ba4e00",
  },
  {
    id: "positioning",
    text: "Positioning",
    lightHex: "#b28600",
    darkHex: "#d2a106",
  },
  {
    id: "all",
    text: "All",
    lightHex: "#fa4d56",
    darkHex: "#fa4d56",
  },
];

/**
 * Get a Carbon color by index
 * @param index - The index of the color to retrieve
 * @param isDark - Whether to use the dark theme colors
 * @returns The hex color code
 */
export const getColorByIndex = (index: number, isDark = false): string => {
  const colorOption = CARBON_COLORS[index % CARBON_COLORS.length];
  return isDark ? colorOption.darkHex : colorOption.lightHex;
};

/**
 * Get a Carbon color by ID
 * @param id - The ID of the color to retrieve
 * @param isDark - Whether to use the dark theme colors
 * @returns The hex color code or undefined if not found
 */
export const getColorById = (
  id: string,
  isDark = false,
): string | undefined => {
  const colorOption = CARBON_COLORS.find((color) => color.id === id);
  return colorOption
    ? isDark
      ? colorOption.darkHex
      : colorOption.lightHex
    : undefined;
};

/**
 * Get all Carbon colors for the specified theme
 * @param isDark - Whether to use the dark theme colors
 * @returns Array of hex color codes
 */
export const getAllColors = (isDark = false): string[] => {
  return CARBON_COLORS.map((color) =>
    isDark ? color.darkHex : color.lightHex,
  );
};

/**
 * Get color options for use in a combobox
 * @returns Array of color options with id and text properties
 */
export const getColorOptions = (): Pick<ColorOption, "id" | "text">[] => {
  return CARBON_COLORS.map(({ id, text }) => ({ id, text, label: text }));
};

/**
 * ----------------------------
 * Dashboard Colors Utility
 * ----------------------------
 */
export const getDashboardColorById = (
  id: string,
  isDark = false,
): string | undefined => {
  const colorOption = DASHBOARD_COLORS.find(
    (color) => color.id === id,
  );
  return colorOption
    ? isDark
      ? colorOption.darkHex
      : colorOption.lightHex
    : undefined;
};