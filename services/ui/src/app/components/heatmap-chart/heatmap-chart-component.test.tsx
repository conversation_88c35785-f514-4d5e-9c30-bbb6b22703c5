import { vi } from "vitest";
import { render, screen } from "../../../test-utils";
import { HeatmapChartComponent } from "./heatmap-chart-component";
import { ThemeMode } from "../../layout/theme";
import { HeatmapChartData } from "./types";

// Mock the Carbon HeatmapChart component
vi.mock("@carbon/charts-react", () => ({
  HeatmapChart: ({ data, options }: any) => (
    <div data-testid="heatmap-chart">
      <div data-testid="chart-data">{JSON.stringify(data)}</div>
      <div data-testid="chart-options">{JSON.stringify(options)}</div>
    </div>
  ),
  ScaleTypes: {
    LABELS: "labels",
  },
}));

const mockedUseTheme = vi.hoisted(() =>
  vi.fn(() => ({ theme: ThemeMode.LIGHT })),
);

// Mock useMeasure hook
vi.mock("@uidotdev/usehooks", () => ({
  useMeasure: () => [() => {}, { height: 300 }],
}));

vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    useTheme: mockedUseTheme,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

describe("HeatmapChartComponent", () => {
  const mockSeriesData: HeatmapChartData = {
    id: "test-id",
    bottomTitle: "Bottom Axis",
    sideTitle: "Side Axis",
    series: [
      {
        id: "A",
        data: [
          { label: "X", value: 10 },
          { label: "Y", value: 30 },
        ],
      },
      {
        id: "B",
        data: [
          { label: "X", value: 20 },
          { label: "Y", value: 40 },
        ],
      },
    ],
  };

  it("renders with default props", () => {
    render(<HeatmapChartComponent chartData={mockSeriesData} />);

    // Check if the heatmap chart is rendered
    expect(screen.getByTestId("heatmap-chart")).toBeInTheDocument();

    // Check if chart data is formatted correctly
    const chartData = JSON.parse(
      screen.getByTestId("chart-data").textContent || "{}",
    );
    const expectedChartData = [
      { bottomCategory: "A", sideCategory: "X", value: 10 },
      { bottomCategory: "A", sideCategory: "Y", value: 30 },
      { bottomCategory: "B", sideCategory: "X", value: 20 },
      { bottomCategory: "B", sideCategory: "Y", value: 40 },
    ];
    expect(chartData).toEqual(expectedChartData);

    // Check if chart options are set correctly
    const chartOptions = JSON.parse(
      screen.getByTestId("chart-options").textContent || "{}",
    );
    expect(chartOptions.axes.bottom.title).toBe("Bottom Axis");
    expect(chartOptions.axes.left.title).toBe("Side Axis"); // Default sidePosition is 'left'
    expect(chartOptions.heatmap.colorLegend.title).toBeUndefined();
    expect(chartOptions.height).toBe("295px"); // 300px - 5px
    expect(chartOptions.width).toBe("100%");
    expect(chartOptions.theme).toBe("g10"); // Default theme is light
    expect(chartOptions.toolbar.enabled).toBe(false);
    expect(chartOptions.legend.enabled).toBe(true);
  });

  it("renders with no data", () => {
    const emptySeriesData: HeatmapChartData = {
      id: "",
      bottomTitle: "Bottom Axis",
      sideTitle: "Side Axis",
      series: [],
    };
    render(<HeatmapChartComponent chartData={emptySeriesData} />);
    expect(screen.getByText("No Chart Data Available")).toBeInTheDocument();
  });

  it("renders with custom legendTitle", () => {
    render(
      <HeatmapChartComponent
        chartData={mockSeriesData}
        legendTitle="Custom Legend"
      />,
    );
    const chartOptions = JSON.parse(
      screen.getByTestId("chart-options").textContent || "{}",
    );
    expect(chartOptions.heatmap.colorLegend.title).toBe("Custom Legend");
  });

  it("renders with sidePosition 'right'", () => {
    render(
      <HeatmapChartComponent chartData={mockSeriesData} sidePosition="right" />,
    );
    const chartOptions = JSON.parse(
      screen.getByTestId("chart-options").textContent || "{}",
    );
    expect(chartOptions.axes.right.title).toBe("Side Axis");
    expect(chartOptions.axes.left).toBeUndefined();
  });

  it("renders with showLegend set to false", () => {
    render(
      <HeatmapChartComponent chartData={mockSeriesData} showLegend={false} />,
    );
    const chartOptions = JSON.parse(
      screen.getByTestId("chart-options").textContent || "{}",
    );
    expect(chartOptions.legend.enabled).toBe(false);
  });

  it("renders with showToolbar set to true", () => {
    render(
      <HeatmapChartComponent chartData={mockSeriesData} showToolbar={true} />,
    );
    const chartOptions = JSON.parse(
      screen.getByTestId("chart-options").textContent || "{}",
    );
    expect(chartOptions.toolbar.enabled).toBe(true);
  });

  it("uses the g10 theme when light theme is active", () => {
    render(<HeatmapChartComponent chartData={mockSeriesData} />);

    const chartOptions = JSON.parse(
      screen.getByTestId("chart-options").textContent || "{}",
    );
    expect(chartOptions.theme).toBe("g10");
  });

  it("uses the g100 theme when dark theme is active", () => {
    // Override the mock for this specific test
    mockedUseTheme.mockReturnValueOnce({ theme: ThemeMode.DARK });

    render(<HeatmapChartComponent chartData={mockSeriesData} />);

    const chartOptions = JSON.parse(
      screen.getByTestId("chart-options").textContent || "{}",
    );
    expect(chartOptions.theme).toBe("g100");
  });

  it("applies bottomLabelFormatter", () => {
    const customFormatter = (label: string) => `Bottom: ${label}`;
    render(
      <HeatmapChartComponent
        chartData={mockSeriesData}
        bottomLabelFormatter={customFormatter}
      />,
    );

    const chartData = JSON.parse(
      screen.getByTestId("chart-data").textContent || "{}",
    );
    expect(chartData[0].bottomCategory).toBe("Bottom: A");
    expect(chartData[2].bottomCategory).toBe("Bottom: B");
  });

  it("applies sideLabelFormatter", () => {
    const customFormatter = (label: string) => `Side: ${label}`;
    render(
      <HeatmapChartComponent
        chartData={mockSeriesData}
        sideLabelFormatter={customFormatter}
      />,
    );

    const chartData = JSON.parse(
      screen.getByTestId("chart-data").textContent || "{}",
    );
    expect(chartData[0].sideCategory).toBe("Side: X");
    expect(chartData[3].sideCategory).toBe("Side: Y");
  });
});
