import {
  HeatmapChart,
  HeatmapChartOptions,
  ScaleTypes,
} from "@carbon/charts-react";
import "@carbon/charts/styles.css";
import { useTheme } from "@carbon/react";
import { ThemeMode } from "../../layout/theme";
import styles from "./heatmap-chart-component.module.css";
import { useMeasure } from "@uidotdev/usehooks";
import { HeatmapChartData } from "./types";

export interface HeatmapChartComponentProps {
  chartData: HeatmapChartData;
  legendTitle?: string;
  sidePosition?: "left" | "right";
  showLegend?: boolean;
  showToolbar?: boolean;
  /** Swap X and Y axes */
  swapAxes?: boolean;
  bottomLabelFormatter?: (label: string) => string;
  sideLabelFormatter?: (label: string) => string;
}

export const HeatmapChartComponent = ({
  chartData,
  legendTitle,
  sidePosition = "left",
  showLegend = true,
  showToolbar = false,
  swapAxes = false,
  bottomLabelFormatter = (label) => label,
  sideLabelFormatter = (label) => label,
}: HeatmapChartComponentProps) => {
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;
  const [ref, { height }] = useMeasure();

  if (chartData.series.length === 0) {
    return <div>No Chart Data Available</div>;
  }

  const formattedData = chartData.series.flatMap((series) =>
    series.data.map((dataPoint) => ({
      bottomCategory: bottomLabelFormatter(series.id),
      sideCategory: sideLabelFormatter(dataPoint.label),
      value: dataPoint.value,
    })),
  );

  const bottomOptions = {
    mapsTo: "bottomCategory",
    title: chartData.bottomTitle,
    scaleType: ScaleTypes.LABELS,
  };

  const sideOptions = {
    mapsTo: "sideCategory",
    title: chartData.sideTitle,
    scaleType: ScaleTypes.LABELS,
  };

  const options: HeatmapChartOptions = {
    axes: {
      bottom: swapAxes ? sideOptions : bottomOptions,
      [sidePosition]: swapAxes ? bottomOptions : sideOptions,
    },
    heatmap: {
      colorLegend: {
        title: legendTitle,
      },
    },
    height: height ? `${height - 5}px` : undefined,
    width: "100%",
    theme: isDark ? "g100" : "g10",
    toolbar: {
      enabled: showToolbar,
    },
    legend: {
      enabled: showLegend,
    },
  };

  const chartKey = JSON.stringify({
    legendTitle,
    swapAxes,
    showToolbar,
    showLegend,
    sidePosition,
  });

  return (
    <div className={styles.chartContainer} ref={ref}>
      <HeatmapChart key={chartKey} data={formattedData} options={options} />
    </div>
  );
};

export default HeatmapChartComponent;
