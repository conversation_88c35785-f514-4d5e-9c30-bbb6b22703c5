import {
  defaultFormatter,
  localDateFormatter,
} from "./heatmap-chart-formatters";

describe("heatmap-chart-formatters", () => {
  describe("defaultFormatter", () => {
    it("should return the input string as is", () => {
      const testString = "test string";
      expect(defaultFormatter(testString)).toBe(testString);
    });

    it("should handle empty strings", () => {
      expect(defaultFormatter("")).toBe("");
    });

    it("should handle numbers converted to strings", () => {
      expect(defaultFormatter("123")).toBe("123");
    });
  });

  describe("localDateFormatter", () => {
    it("should format a valid date string to a local date string", () => {
      const dateString = "2023-10-27T10:00:00Z";
      const expectedDate = new Date(dateString).toLocaleDateString();
      expect(localDateFormatter(dateString)).toBe(expectedDate);
    });

    it("should handle date strings with different formats if parsable by Date constructor", () => {
      const dateString = "10/27/2023";
      const expectedDate = new Date(dateString).toLocaleDateString();
      expect(localDateFormatter(dateString)).toBe(expectedDate);
    });

    it("should return 'Invalid Date' if the input is not a valid date string", () => {
      const invalidDateString = "not-a-date";
      expect(localDateFormatter(invalidDateString)).toBe(
        new Date(invalidDateString).toLocaleDateString(),
      );
    });

    it("should handle empty strings gracefully (results in Invalid Date)", () => {
      expect(localDateFormatter("")).toBe(new Date("").toLocaleDateString());
    });
  });
});
