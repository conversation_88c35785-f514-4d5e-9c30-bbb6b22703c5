import { AccordionItem, Layer } from "@carbon/react";
import type { ReactNode } from "react";
import styles from "./options-accordion-group.module.scss";
export interface OptionsAccordionGroupProps {
  id: string;
  title: string;
  icon: React.ReactNode;
  children: ReactNode;
  defaultOpen?: boolean;
}

export const OptionsAccordionGroup = ({
  id,
  title,
  icon,
  children,
  defaultOpen = true,
}: OptionsAccordionGroupProps) => {
  return (
    <AccordionItem
      key={id}
      title={
        <div className={styles.optionsAccordionTitle}>
          {icon && <span>{icon}</span>}
          {title}
        </div>
      }
      open={defaultOpen}
      className={styles.optionsAccordionItem}
    >
      <Layer>
        <div className={styles.content}>{children}</div>
      </Layer>
    </AccordionItem>
  );
};
