import { DatePeriod } from "../../types";
import { render, screen, userEvent } from "../../../test-utils";
import { ViewBar } from "./view-bar";
import classes from "./view-bar.module.css";
import { useSafeLocation } from "../../hooks/use-safe-location";

// Mock the config hooks
vi.mock("../../config/hooks/use-config", () => ({
  useConfig: () => ({
    data: [],
    isLoading: false,
    error: null,
  }),
  useConfigSetting: () => ({
    setting: null,
    isLoading: false,
    error: null,
  }),
  useFeatureFlag: () => ({
    enabled: false,
    isLoading: false,
    error: null,
  }),
  useFeatureFlags: () => ({
    featureFlags: [],
    isLoading: false,
    error: null,
  }),
}));

// Mock the auth hooks
vi.mock("../../auth/hooks/use-roles", () => ({
  useRoles: () => ({
    roles: [],
    hasRole: () => false,
    hasAnyRole: () => false,
    isLoading: false,
  }),
}));

// Mock the auth0 client
vi.mock("@auth0/auth0-react", () => ({
  useAuth0: () => ({
    isAuthenticated: true,
    user: { name: "Test User" },
    getIdTokenClaims: vi.fn().mockResolvedValue({
      "https://ict.dematic.cloud/roles": [],
    }),
  }),
}));

// Mock the useSafeLocation hook
vi.mock("../../hooks/use-safe-location", () => ({
  useSafeLocation: vi.fn(() => ({
    pathname: "/dashboard",
    search: "",
    hash: "",
    state: null,
  })),
}));

// Mock the use-favorites hook
vi.mock("../../config/hooks/use-favorites", () => ({
  useFavorites: () => ({
    favorites: [],
    isLoading: false,
    error: null,
    addFavorite: vi.fn(),
    removeFavorite: vi.fn(),
    isFavorite: () => false,
  }),
}));

// Mock the use-menu hook
vi.mock("../../config/menu/use-menu", () => ({
  useMenu: () => ({
    menuItems: [
      {
        id: "dashboard",
        label: "Dashboard",
        path: "/dashboard",
      },
      {
        id: "container-list",
        label: "Container List",
        path: "/ict-container-list",
      },
    ],
    isLoading: false,
  }),
}));

// Mock the findMenuItem function
vi.mock("../../router/router.util", () => ({
  findMenuItem: vi.fn((_menuItems, path) => {
    // Return the dashboard menu item for /dashboard path
    if (path === "/dashboard") {
      return { id: "dashboard", label: "Dashboard", path: "/dashboard" };
    }
    // Return the container-list menu item for /ict-container-list path
    if (path === "/ict-container-list") {
      return {
        id: "container-list",
        label: "Container List",
        path: "/ict-container-list",
      };
    }
    // Return null for unknown paths
    return null;
  }),
}));

// Mock Carbon components
vi.mock("@carbon/react", () => ({
  Layer: ({ children }: { children: React.ReactNode }) => children,
  Button: ({
    renderIcon: Icon,
    iconDescription,
    onClick,
    hasIconOnly,
    kind,
    children,
  }: {
    renderIcon?: React.ComponentType;
    iconDescription?: string;
    onClick?: () => void;
    hasIconOnly?: boolean;
    kind?: string;
    children?: React.ReactNode;
  }) => (
    <button
      type="button"
      data-testid={`button-${iconDescription?.toLowerCase() || "default"}`}
      data-icon-only={hasIconOnly ? "true" : "false"}
      data-kind={kind}
      onClick={onClick}
    >
      {Icon && (
        <Icon
          data-testid={`icon-${iconDescription?.toLowerCase() || "default"}`}
        />
      )}
      {!hasIconOnly && children}
      {iconDescription && <span>{iconDescription}</span>}
    </button>
  ),
  Select: ({
    id,
    value,
    onChange,
    children,
  }: {
    id: string;
    value: string;
    onChange: (e: { target: { value: string } }) => void;
    children: React.ReactNode;
  }) => (
    <select
      data-testid="date-range-select"
      id={id}
      value={value}
      onChange={(e: any) => onChange(e)}
    >
      {children}
    </select>
  ),
  SelectItem: ({
    value,
    text,
    disabled,
    hidden,
  }: {
    value: string;
    text: string;
    disabled?: boolean;
    hidden?: boolean;
  }) => (
    <option
      value={value}
      disabled={disabled}
      hidden={hidden}
      data-testid={`select-item-${value || "empty"}`}
    >
      {text}
    </option>
  ),
  GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock Carbon icons
vi.mock("@carbon/icons-react", () => ({
  Save: () => <div data-testid="save-icon" />,
  SettingsAdjust: () => <div data-testid="settings-icon" />,
  Star: () => <div data-testid="star-icon" />,
}));

// Mock SaveViewModal
vi.mock("../save-view-modal/save-view-modal", () => ({
  default: ({
    open,
    onClose,
    onSave,
  }: {
    open: boolean;
    onClose: () => void;
    onSave: () => void;
  }) =>
    open ? (
      <div data-testid="save-view-modal">
        <button
          type="button"
          onClick={onClose}
          data-testid="modal-cancel-button"
        >
          Cancel
        </button>
        <button type="button" onClick={onSave} data-testid="modal-save-button">
          Save
        </button>
      </div>
    ) : null,
}));

// Mock FavoriteButton
vi.mock("../favorite-button", () => ({
  FavoriteButton: ({
    menuItemId,
    title,
  }: {
    menuItemId?: string;
    title?: string;
  }) => (
    <button
      type="button"
      data-testid="button-favorite"
      data-menu-item-id={menuItemId}
      data-title={title}
    >
      Favorite
    </button>
  ),
}));

describe("ViewBar", () => {
  const onDateRangeChangeMock = vi.fn();
  const onSettingsClickMock = vi.fn();
  const onSaveClickMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render with a string title", () => {
    render(<ViewBar title="Dashboard Title" />);

    expect(screen.getByText("Dashboard Title")).toBeInTheDocument();
    expect(screen.getByText("Dashboard Title").tagName).toBe("H2");
  });

  it("should render with a ReactNode title", () => {
    render(
      <ViewBar title={<span data-testid="custom-title">Custom Title</span>} />,
    );

    expect(screen.getByTestId("custom-title")).toBeInTheDocument();
    expect(screen.queryByRole("heading")).not.toBeInTheDocument();
  });

  it("should render a favorite button when currentMenuItem exists", () => {
    render(<ViewBar title="Dashboard Title" />);

    expect(screen.getByTestId("button-favorite")).toBeInTheDocument();
  });

  it("should not render a favorite button when currentMenuItem does not exist", () => {
    // Mock useSafeLocation to return a path that doesn't match any menu items
    vi.mocked(useSafeLocation).mockReturnValueOnce({
      pathname: "/unknown-path",
      search: "",
      hash: "",
      state: null,
      key: "test-key",
    });

    render(<ViewBar title="Dashboard Title" />);

    expect(screen.queryByTestId("button-favorite")).not.toBeInTheDocument();
  });

  it("should not render a favorite button even when explicit menuItemId is provided if currentMenuItem does not exist", () => {
    // The favorite button only renders when currentMenuItem exists, regardless of menuItemId prop
    vi.mocked(useSafeLocation).mockReturnValueOnce({
      pathname: "/unknown-path",
      search: "",
      hash: "",
      state: null,
      key: "test-key",
    });

    render(<ViewBar title="Dashboard Title" menuItemId="custom-menu-item" />);

    expect(screen.queryByTestId("button-favorite")).not.toBeInTheDocument();
  });

  it("should not render date range select by default", () => {
    render(<ViewBar title="Dashboard Title" />);

    expect(screen.queryByTestId("date-range-select")).not.toBeInTheDocument();
  });

  it("should render date range select when showDatePeriodRange is true", () => {
    render(
      <ViewBar
        title="Dashboard Title"
        showDatePeriodRange={true}
        onDatePeriodRangeChange={onDateRangeChangeMock}
      />,
    );

    expect(screen.getByTestId("date-range-select")).toBeInTheDocument();
  });

  it("should call onDatePeriodRangeChange when date range is changed", async () => {
    render(
      <ViewBar
        title="Dashboard Title"
        showDatePeriodRange={true}
        onDatePeriodRangeChange={onDateRangeChangeMock}
      />,
    );

    const select = screen.getByTestId("date-range-select");
    await userEvent.selectOptions(select, String(DatePeriod.last7days));

    expect(onDateRangeChangeMock).toHaveBeenCalledWith(DatePeriod.last7days);
  });

  it("should select the correct date range option when selectedDatePeriodRange is provided", () => {
    render(
      <ViewBar
        title="Dashboard Title"
        showDatePeriodRange={true}
        selectedDatePeriodRange={DatePeriod.last7days}
        onDatePeriodRangeChange={onDateRangeChangeMock}
      />,
    );

    const select = screen.getByTestId("date-range-select");
    expect(select).toHaveValue(String(DatePeriod.last7days));
  });

  it("should filter date range options when availableDateRanges is provided", () => {
    render(
      <ViewBar
        title="Dashboard Title"
        showDatePeriodRange={true}
        availableDateRanges={[
          DatePeriod.last7days,
          DatePeriod.last14days,
          DatePeriod.last30days,
        ]}
        onDatePeriodRangeChange={onDateRangeChangeMock}
      />,
    );

    expect(
      screen.getByTestId(`select-item-${DatePeriod.last7days}`),
    ).toBeInTheDocument();
    expect(
      screen.getByTestId(`select-item-${DatePeriod.last14days}`),
    ).toBeInTheDocument();
    expect(
      screen.getByTestId(`select-item-${DatePeriod.last30days}`),
    ).toBeInTheDocument();

    expect(
      screen.queryByTestId(`select-item-${DatePeriod.today}`),
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId(`select-item-${DatePeriod.yesterday}`),
    ).not.toBeInTheDocument();
  });

  it("should show all date range options when availableDateRanges is not provided", () => {
    render(
      <ViewBar
        title="Dashboard Title"
        showDatePeriodRange={true}
        onDatePeriodRangeChange={onDateRangeChangeMock}
      />,
    );

    expect(
      screen.getByTestId(`select-item-${DatePeriod.today}`),
    ).toBeInTheDocument();
    expect(
      screen.getByTestId(`select-item-${DatePeriod.yesterday}`),
    ).toBeInTheDocument();
    expect(
      screen.getByTestId(`select-item-${DatePeriod.last7days}`),
    ).toBeInTheDocument();
    expect(
      screen.getByTestId(`select-item-${DatePeriod.last14days}`),
    ).toBeInTheDocument();
    expect(
      screen.getByTestId(`select-item-${DatePeriod.last30days}`),
    ).toBeInTheDocument();
  });

  it("should not render date range select when availableDateRanges is empty", () => {
    render(
      <ViewBar
        title="Dashboard Title"
        showDatePeriodRange={true}
        availableDateRanges={[]}
        onDatePeriodRangeChange={onDateRangeChangeMock}
      />,
    );

    expect(screen.queryByTestId("date-range-select")).not.toBeInTheDocument();
  });

  it("should not render settings button by default", () => {
    render(<ViewBar title="Dashboard Title" />);

    expect(screen.queryByTestId("button-settings")).not.toBeInTheDocument();
  });

  it("should render settings button when showSettings and hasConfiguratorAccess are true", () => {
    render(
      <ViewBar
        title="Dashboard Title"
        showSettings={true}
        hasConfiguratorAccess={true}
        onSettingsClick={onSettingsClickMock}
      />,
    );

    expect(screen.getByTestId("button-settings")).toBeInTheDocument();
  });

  it("should not render settings button when hasConfiguratorAccess is false", () => {
    render(
      <ViewBar
        title="Dashboard Title"
        showSettings={true}
        hasConfiguratorAccess={false}
        onSettingsClick={onSettingsClickMock}
      />,
    );

    expect(screen.queryByTestId("button-settings")).not.toBeInTheDocument();
  });

  it("should call onSettingsClick when settings button is clicked", async () => {
    render(
      <ViewBar
        title="Dashboard Title"
        showSettings={true}
        hasConfiguratorAccess={true}
        onSettingsClick={onSettingsClickMock}
      />,
    );

    const settingsButton = screen.getByTestId("button-settings");
    await userEvent.click(settingsButton);

    expect(onSettingsClickMock).toHaveBeenCalledTimes(1);
  });

  it("should not render save button by default", () => {
    render(<ViewBar title="Dashboard Title" />);

    expect(screen.queryByTestId("button-save")).not.toBeInTheDocument();
  });

  it("should render save button when showSave and saveEnabled are true", () => {
    render(
      <ViewBar
        title="Dashboard Title"
        showSave={true}
        saveEnabled={true}
        onSaveClick={onSaveClickMock}
      />,
    );

    expect(screen.getByTestId("button-save")).toBeInTheDocument();
  });

  it("should render save button when saveEnabled is false and showSave is true", () => {
    render(
      <ViewBar
        title="Dashboard Title"
        showSave={true}
        saveEnabled={false}
        onSaveClick={onSaveClickMock}
      />,
    );

    expect(screen.queryByTestId("button-save")).toBeInTheDocument();
  });

  it("should open save modal when save button is clicked", async () => {
    render(
      <ViewBar
        title="Dashboard Title"
        showSave={true}
        saveEnabled={true}
        onSaveClick={onSaveClickMock}
      />,
    );

    const saveButton = screen.getByTestId("button-save");
    await userEvent.click(saveButton);

    expect(screen.getByTestId("save-view-modal")).toBeInTheDocument();
  });

  it("should close save modal when cancel is clicked", async () => {
    render(
      <ViewBar
        title="Dashboard Title"
        showSave={true}
        saveEnabled={true}
        onSaveClick={onSaveClickMock}
      />,
    );

    // Open the modal
    const saveButton = screen.getByTestId("button-save");
    await userEvent.click(saveButton);

    // Click cancel
    const cancelButton = screen.getByTestId("modal-cancel-button");
    await userEvent.click(cancelButton);

    // Modal should be closed
    expect(screen.queryByTestId("save-view-modal")).not.toBeInTheDocument();
    // onSaveClick should not be called
    expect(onSaveClickMock).not.toHaveBeenCalled();
  });

  it("should call onSaveClick when save is confirmed in modal", async () => {
    render(
      <ViewBar
        title="Dashboard Title"
        showSave={true}
        saveEnabled={true}
        onSaveClick={onSaveClickMock}
      />,
    );

    // Open the modal
    const saveButton = screen.getByTestId("button-save");
    await userEvent.click(saveButton);

    // Click save in modal
    const modalSaveButton = screen.getByTestId("modal-save-button");
    await userEvent.click(modalSaveButton);

    // Modal should be closed
    expect(screen.queryByTestId("save-view-modal")).not.toBeInTheDocument();
    // onSaveClick should be called
    expect(onSaveClickMock).toHaveBeenCalledTimes(1);
  });

  it("should render children in the actions group", () => {
    render(
      <ViewBar title="Dashboard Title">
        <div data-testid="custom-child">Custom Child</div>
      </ViewBar>,
    );

    expect(screen.getByTestId("custom-child")).toBeInTheDocument();
  });

  it("should apply the correct CSS classes", () => {
    const { container } = render(<ViewBar title="Dashboard Title" />);

    const viewBar = container.firstChild;
    expect(viewBar).toHaveClass(classes.viewBar);

    const viewBarInner = viewBar?.firstChild;
    expect(viewBarInner).toHaveClass(classes.viewBarInner);

    const titleGroup = viewBarInner?.firstChild;
    expect(titleGroup).toHaveClass(classes.titleGroup);

    const actionsGroup = viewBarInner?.childNodes[1];
    expect(actionsGroup).toHaveClass(classes.actionsGroup);
  });
});
