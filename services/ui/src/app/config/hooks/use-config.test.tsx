import { renderHook } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { FeatureFlagName } from "@ict/sdk/types";
import { ictApi } from "../../api/ict-api";
import {
  getSettingSchema,
  toggleFeatureFlag,
  updateConfigSetting,
  useConfig,
  useConfigSetting,
  useFeatureFlag,
  useFeatureFlags,
} from "./use-config";

// Mock the ictApi
vi.mock("../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: vi.fn(),
      queryOptions: vi.fn(),
    },
    queryClient: {
      fetchQuery: vi.fn(),
      invalidateQueries: vi.fn(),
    },
  },
}));

describe("Configuration Hooks", () => {
  const mockSettings = [
    {
      id: "1",
      name: "setting1",
      value: "value1",
      dataType: "string" as const,
      source: "tenant" as const,
      group: "general",
      description: "Test setting 1",
    },
    {
      id: "2",
      name: "feature1",
      value: true,
      dataType: "boolean" as const,
      source: "tenant" as const,
      group: "feature-flags",
      description: "Test feature 1",
    },
    {
      id: "3",
      name: "feature2",
      value: false,
      dataType: "boolean" as const,
      source: "default" as const,
      group: "feature-flags",
      description: "Test feature 2",
    },
  ];

  const mockFeatureFlagSettings = [
    {
      id: "2",
      name: "ict-nav-help-content",
      value: true,
      dataType: "boolean" as const,
      source: "tenant" as const,
      group: "feature-flags",
      description: "Test feature 1",
    },
    {
      id: "3",
      name: "data-explorer-debug-data",
      value: false,
      dataType: "boolean" as const,
      source: "default" as const,
      group: "feature-flags",
      description: "Test feature 2",
    },
  ];

  beforeEach(() => {
    vi.resetAllMocks();
  });

  describe("useConfig", () => {
    it("should return configuration data when successful", () => {
      ictApi.client.useQuery = vi.fn().mockReturnValue({
        data: mockSettings,
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useConfig());

      expect(result.current.data).toEqual(mockSettings);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(ictApi.client.useQuery).toHaveBeenCalledWith(
        "get",
        "/config/facility-config",
        {},
        expect.objectContaining({
          staleTime: 1000 * 60 * 5,
          retry: 1,
        }),
      );
    });

    it("should throw an error when API call fails", () => {
      const mockError = new Error("API error");
      ictApi.client.useQuery = vi.fn().mockReturnValue({
        data: null,
        isLoading: false,
        error: mockError,
      });

      expect(() => {
        renderHook(() => useConfig());
      }).toThrow("Failed to load application configuration");
    });
  });

  describe("useConfigSetting", () => {
    it("should return a specific setting by name", () => {
      ictApi.client.useQuery = vi.fn().mockReturnValue({
        data: mockSettings,
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useConfigSetting("setting1"));

      expect(result.current.setting).toEqual(mockSettings[0]);
      expect(result.current.isLoading).toBe(false);
    });

    it("should return undefined when setting not found", () => {
      ictApi.client.useQuery = vi.fn().mockReturnValue({
        data: mockSettings,
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useConfigSetting("nonexistent"));

      expect(result.current.setting).toBeUndefined();
    });
  });

  describe("useFeatureFlag", () => {
    it("should return enabled status for a feature flag", () => {
      ictApi.client.useQuery = vi.fn().mockReturnValue({
        data: mockSettings,
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() =>
        useFeatureFlag("feature1" as FeatureFlagName),
      );

      expect(result.current.enabled).toBe(true);
      expect(result.current.isLoading).toBe(false);
    });

    it("should return false when feature flag is disabled", () => {
      ictApi.client.useQuery = vi.fn().mockReturnValue({
        data: mockSettings,
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() =>
        useFeatureFlag("feature2" as FeatureFlagName),
      );

      expect(result.current.enabled).toBe(false);
    });

    it("should return false when feature flag doesn't exist", () => {
      ictApi.client.useQuery = vi.fn().mockReturnValue({
        data: mockSettings,
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() =>
        useFeatureFlag("nonexistent" as FeatureFlagName),
      );

      expect(result.current.enabled).toBe(false);
    });
  });

  describe("useFeatureFlags", () => {
    it("should return all feature flags", () => {
      ictApi.client.useQuery = vi.fn().mockReturnValue({
        data: mockFeatureFlagSettings,
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useFeatureFlags());

      expect(result.current.featureFlags).toHaveLength(2);
      expect(result.current.featureFlags).toEqual([
        mockFeatureFlagSettings[0],
        mockFeatureFlagSettings[1],
      ]);
    });

    it("should return empty array when no feature flags exist", () => {
      ictApi.client.useQuery = vi.fn().mockReturnValue({
        data: [mockSettings[0]],
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useFeatureFlags());

      expect(result.current.featureFlags).toHaveLength(0);
    });
  });

  describe("updateConfigSetting", () => {
    it("should update a setting and invalidate queries", async () => {
      const setting = { ...mockSettings[0] };
      const updatedSetting = {
        id: setting.id,
        name: setting.name,
        group: setting.group,
        dataType: setting.dataType,
        value: "new value",
        levelToUpdate: "tenant",
        description: setting.description,
      };

      ictApi.client.queryOptions = vi.fn().mockReturnValue({
        queryKey: ["put", "/config/settings"],
      });
      ictApi.queryClient.fetchQuery = vi.fn().mockResolvedValue({
        success: true,
      });

      await updateConfigSetting({ ...setting, value: "new value" });

      expect(ictApi.client.queryOptions).toHaveBeenCalledWith(
        "put",
        "/config/settings",
        {
          body: expect.objectContaining(updatedSetting),
        },
      );
      expect(ictApi.queryClient.invalidateQueries).toHaveBeenCalled();
    });
  });

  describe("getSettingSchema", () => {
    it("should call queryClient", async () => {
      const mockSettingId = "my-id";
      const mockSettingName = "my-setting";

      ictApi.client.queryOptions = vi.fn().mockReturnValue({
        queryKey: ["get", "/config/settings/schema"],
      });
      ictApi.queryClient.fetchQuery = vi.fn().mockResolvedValue({
        success: true,
      });

      await getSettingSchema(mockSettingId, mockSettingName);

      expect(ictApi.client.queryOptions).toHaveBeenCalledWith(
        "get",
        "/config/settings/schema",
        {
          params: {
            query: {
              settingId: mockSettingId,
              settingName: mockSettingName,
            },
          },
        },
      );
    });
    it("should return undefined if 404 error", async () => {
      const mockSettingId = "my-id";
      const mockSettingName = "my-setting";

      const error404 = new Error("Not found");
      (error404 as any).status = 404;
      ictApi.client.queryOptions = vi.fn().mockReturnValue({
        queryKey: ["get", "/config/settings/schema"],
      });
      ictApi.queryClient.fetchQuery = vi.fn().mockRejectedValue(error404);

      const schema = await getSettingSchema(mockSettingId, mockSettingName);

      expect(schema).toBeUndefined();
    });
  });

  describe("toggleFeatureFlag", () => {
    it("should toggle a feature flag", async () => {
      ictApi.queryClient.fetchQuery = vi.fn().mockResolvedValue(mockSettings);
      ictApi.client.queryOptions = vi.fn().mockReturnValue({});

      await toggleFeatureFlag("feature2", true);

      expect(ictApi.queryClient.fetchQuery).toHaveBeenCalledTimes(2);
      expect(ictApi.client.queryOptions).toHaveBeenCalledWith(
        "get",
        "/config/settings",
      );
    });

    it("should throw an error when feature flag not found", async () => {
      ictApi.queryClient.fetchQuery = vi.fn().mockResolvedValue(mockSettings);

      await expect(toggleFeatureFlag("nonexistent", true)).rejects.toThrow(
        "Feature flag with ID nonexistent not found",
      );
    });
  });
});
