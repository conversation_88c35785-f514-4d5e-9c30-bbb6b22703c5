import { ictApi } from "../../api/ict-api";
import type { components } from "@ict/sdk/openapi-react-query";
import {
  FeatureFlagNames,
  FeatureFlagName,
  AppConfigSettingName,
  IctApiError,
} from "@ict/sdk/types";
import { NO_FACILITY_ACCESS_ERROR } from "../../components/error-boundary/config-error-boundary";

/**
 * Type definitions for configuration settings
 */
export type AppConfigSetting = components["schemas"]["AppConfigSetting"];
export type UpdateSettingData = components["schemas"]["UpdateSettingData"];
export type UpdateSettingSource =
  components["schemas"]["AppConfigSettingSource"];

/**
 * Hook to fetch all application configuration settings
 * @returns Object containing configuration data, loading state, and error
 */
export const useConfig = () => {
  const { data, isLoading, error } = ictApi.client.useQuery(
    "get",
    "/config/facility-config",
    {},
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
      onError: (error: Error) => {
        console.error("Critical config loading error:", error);
      },
    },
  );

  // Throw error after retries exhausted - this will trigger the error boundary
  if (error) {
    // Safely check if it's an IctApiError
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const isIctApiError = (err: any): err is IctApiError => {
      return (
        err &&
        typeof err === "object" &&
        typeof err.status === "number" &&
        typeof err.detail === "string"
      );
    };

    const errorObj = error as unknown;
    if (
      isIctApiError(errorObj) &&
      errorObj.status === 403 &&
      errorObj.detail.includes("User does not have access to any facilities")
    ) {
      throw new Error(NO_FACILITY_ACCESS_ERROR);
    }

    throw new Error("Failed to load application configuration", error);
  }

  return {
    data: data as AppConfigSetting[],
    isLoading,
    error,
  };
};

/**
 * Utility hook to get a specific setting by name
 * @param settingName The name of the setting to retrieve
 * @returns Object containing the specific setting, loading state, and error
 */
export const useConfigSetting = (settingName?: AppConfigSettingName) => {
  const { data, isLoading, error } = useConfig();
  const setting = settingName
    ? data?.find((s) => s.name === settingName)
    : undefined;
  return { setting, isLoading, error };
};

export const useDefaultConfigSetting = (settingName: string) => {
  const { data, isLoading, error } = ictApi.client.useQuery(
    "get",
    "/config/settings",
    {
      params: { query: { settingId: settingName, allStoredValues: true } },
    },
    { queryHash: `default-config-setting-${settingName}` },
  );

  // Throw error after retries exhausted - this will trigger the error boundary
  if (error) {
    throw new Error("Failed to load application configuration");
  }

  if (!data) {
    return {
      data: null,
      isLoading,
      error,
    };
  }

  if (Array.isArray(data)) {
    // Filter for the default "source" setting
    const defaultSetting = (data as AppConfigSetting[])?.find(
      (s) => s.source === "default",
    );
    return {
      data: defaultSetting,
      isLoading,
      error,
    };
  }

  return {
    data,
    isLoading,
    error,
  };
};

export const useConfigSettingLogs = (settingId: string) => {
  const { data, isLoading, error } = ictApi.client.useQuery(
    "get",
    `/config/setting-logs`,
    {
      params: {
        query: {
          setting_id: settingId,
          limit: 30,
        },
      },
    },
    {},
  );
  return { data, isLoading, error };
};

// Utility hook for feature flags
export const useFeatureFlag = (flagName: FeatureFlagName) => {
  const { setting, isLoading, error } = useConfigSetting(flagName);
  return {
    enabled: Boolean(setting?.value),
    isLoading,
    error,
  };
};

// Utility hook to get all feature flags
export const useFeatureFlags = () => {
  const { data, isLoading, error } = useConfig();

  const featureFlags =
    data?.filter(
      (setting) =>
        setting.group === "feature-flags" &&
        FeatureFlagNames.includes(setting.name as FeatureFlagName),
    ) || [];

  return {
    featureFlags,
    isLoading,
    error,
  };
};

export const getSettingSchema = async (
  settingId?: string,
  settingName?: string,
) => {
  try {
    const response = await ictApi.queryClient.fetchQuery(
      ictApi.client.queryOptions("get", "/config/settings/schema", {
        params: {
          query: {
            settingId: settingId,
            settingName: settingName,
          },
        },
      }),
    );
    return response;
  } catch (e) {
    console.error(`Error obtaining schema. Error: ${e}`);
    return undefined;
  }
};

export const updateConfigSetting = async (
  setting: AppConfigSetting,
  source?: UpdateSettingSource,
) => {
  let levelToUpdate = !setting.source ? "facility" : setting.source;
  if (source) {
    levelToUpdate = source;
  }

  const updatedSetting: UpdateSettingData = {
    id: setting.id,
    name: setting.name,
    group: setting.group ?? undefined,
    dataType: setting.dataType,
    value: setting.value,
    levelToUpdate: levelToUpdate,
    description: setting.description,
  };

  // update the setting
  const response = await ictApi.queryClient.fetchQuery(
    ictApi.client.queryOptions("put", "/config/settings", {
      body: updatedSetting,
    }),
  );

  // invalidate
  await ictApi.queryClient.invalidateQueries({
    queryKey: ictApi.client.queryOptions("get", "/config/settings").queryKey,
  });

  await ictApi.queryClient.invalidateQueries({
    queryKey: ictApi.client.queryOptions("get", "/config/facility-config")
      .queryKey,
  });

  return response;
};

export const deleteConfigSetting = async (
  setting: AppConfigSetting,
  force = false,
) => {
  try {
    const response = await ictApi.queryClient.fetchQuery(
      ictApi.client.queryOptions("delete", "/config/settings/{settingId}", {
        params: {
          path: {
            settingId: setting.id,
          },
          query: {
            levelToDelete: setting.source,
            force,
          },
        },
      }),
    );

    // invalidate
    await ictApi.queryClient.invalidateQueries({
      queryKey: ictApi.client.queryOptions("get", "/config/settings").queryKey,
    });

    await ictApi.queryClient.invalidateQueries({
      queryKey: ictApi.client.queryOptions("get", "/config/facility-config")
        .queryKey,
    });
    return { response, error: null };
  } catch (error) {
    console.error("Error deleting config setting:", error);
    return { response: null, error: error as Error };
  }
};

// Utility function to toggle a feature flag
export const toggleFeatureFlag = async (
  featureId: string,
  enabled: boolean,
) => {
  const response = await ictApi.queryClient.fetchQuery(
    ictApi.client.queryOptions("get", "/config/settings"),
  );
  // Get all feature flags
  const settings = response as AppConfigSetting[];

  // Find the feature flag by ID
  const featureToUpdate = settings.find(
    (feature) =>
      feature.name === featureId && feature.group === "feature-flags",
  );

  if (!featureToUpdate) {
    throw new Error(`Feature flag with ID ${featureId} not found`);
  }

  // Update the feature flag
  const updatedFeature: AppConfigSetting = {
    ...featureToUpdate,
    value: enabled,
    // Force the update to be tenant level only
    source: "tenant",
  };

  await updateConfigSetting(updatedFeature);
};

// Utility hook to get all user writable settings
export const useUserPreferences = () => {
  const { data, isLoading, error } = useConfig();

  const userPreferences =
    data?.filter((setting) => setting.group === "user-writable") || [];

  return {
    userPreferences,
    isLoading,
    error,
  };
};

// Utility hook to get a specific user writable setting
export const useUserPreference = (preferenceName: string) => {
  const { data, isLoading, error } = useConfig();
  const preference = data?.find(
    (s) => s.name === preferenceName && s.group === "user-writable",
  );
  return { preference, isLoading, error };
};

// Utility hook to get user favorites
export const useUserFavorites = () => {
  const { preference, isLoading, error } = useUserPreference("user-favorites");
  const favorites =
    preference?.value &&
    typeof preference.value === "object" &&
    "favorites" in preference.value
      ? (preference.value as { favorites: string[] }).favorites
      : [];
  return {
    favorites,
    isLoading,
    error,
  };
};

// Utility function to update a user writable setting
export const updateUserWritableSetting = async (setting: AppConfigSetting) => {
  const updatedSetting: UpdateSettingData = {
    id: setting.id,
    name: setting.name,
    group: "user-writable",
    dataType: setting.dataType,
    value: setting.value,
    levelToUpdate: "user",
    description: setting.description,
  };

  // update the setting using the user writable setting endpoint
  const response = await ictApi.queryClient.fetchQuery(
    ictApi.client.queryOptions("put", "/config/user-writable", {
      body: updatedSetting,
    }),
  );

  // invalidate
  await ictApi.queryClient.invalidateQueries({
    queryKey: ictApi.client.queryOptions("get", "/config/settings").queryKey,
  });

  await ictApi.queryClient.invalidateQueries({
    queryKey: ictApi.client.queryOptions("get", "/config/facility-config")
      .queryKey,
  });

  return response;
};

// Utility hook to get all user writable settings
export const useCustomDashboards = () => {
  const { data, isLoading, error } = useConfig();

  const dashboards =
    data?.filter((setting) => setting.group === "custom-dashboards") || [];

  return {
    dashboards,
    isLoading,
    error,
  };
};
