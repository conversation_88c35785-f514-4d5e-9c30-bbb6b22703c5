import { create } from "zustand";

interface FilterStore {
  selectedFilterValues: { [filterId: string]: string };
  inputValues: { [filterId: string]: string };
  filtersVersion: number;
  setSelectedFilterValue: (filterId: string, value: string) => void;
  setInputValue: (filterId: string, value: string) => void;
  bumpFiltersVersion: () => void;
}

export const useFilterStore = create<FilterStore>((set) => ({
  selectedFilterValues: {},
  inputValues: {},
  filtersVersion: 0,
  setSelectedFilterValue: (filterId, value) =>
    set((state) => ({
      selectedFilterValues: {
        ...state.selectedFilterValues,
        [filterId]: value ?? undefined, // never set to null
      },
    })),
  setInputValue: (filterId, value) =>
    set((state) => ({
      inputValues: {
        ...state.inputValues,
        [filterId]: value,
      },
    })),
  bumpFiltersVersion: () =>
    set((state) => ({ filtersVersion: state.filtersVersion + 1 })),
}));
