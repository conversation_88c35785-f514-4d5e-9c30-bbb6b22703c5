import isEqual from "fast-deep-equal";
import {
  autoFormatWithMilliseconds,
  createLabel,
  type FormattedValueItem,
} from "../structured-value-display";

const EXCLUDED_FIELDS = [
  "processIdentifier",
  "modifiedTime",
  "modifiedUser",
  "alarmIdentifier",
  "operationType",
  "alarm_ext_pk",
  "alarm_pk",
  "etl_insert_datetime",
  "etl_update_datetime",
  "upd_start_utc",
  "upd_end_utc",
];

export const useAuditLogComparison = (
  sourceData: Record<string, unknown> | null | undefined,
  compareData: Record<string, unknown> | null | undefined,
  timezone?: string,
): FormattedValueItem[] => {
  if (!sourceData || !compareData) return [];

  const filteredEntries = Object.entries(sourceData).filter(([key, value]) => {
    if (EXCLUDED_FIELDS.includes(key)) {
      return false;
    }

    const compareValue = compareData[key];
    return !isEqual(value, compareValue);
  });

  return filteredEntries.map(
    ([key, value]): FormattedValueItem => ({
      key,
      label: createLabel(key),
      value: autoFormatWithMilliseconds(value, timezone, key),
    }),
  );
};
