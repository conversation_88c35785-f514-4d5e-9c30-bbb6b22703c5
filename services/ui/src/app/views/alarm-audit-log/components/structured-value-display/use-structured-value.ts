import isEqual from "fast-deep-equal";
import {
  autoFormat,
  createLabel,
  type FormattedValueItem,
} from "./value-formatters";

/**
 * Configuration options for structured value processing
 */
export interface StructuredValueOptions {
  /** Fields to exclude from display (defaults to system/metadata fields) */
  excludeFields?: string[];
  /** Maximum number of items to display before truncation */
  maxItems?: number;
  /** Preferred order for displaying fields (important fields first) */
  fieldOrder?: string[];
  /** Whether to show fields with null/undefined/empty values */
  showEmptyValues?: boolean;
  /** Data to compare against - only shows fields that differ from this */
  compareWith?: Record<string, unknown> | null | undefined;
  /** Timezone for timestamp formatting */
  timezone?: string;
}

/**
 * System and metadata fields that are typically excluded from user-facing displays
 * These fields provide technical context but aren't useful for understanding alarm changes
 */
const DEFAULT_EXCLUDED_FIELDS = [
  "processIdentifier",
  "modifiedTime",
  "modifiedUser",
  "alarmIdentifier",
  "operationType",
  "alarm_ext_pk",
  "alarm_pk",
  "etl_insert_datetime",
  "etl_update_datetime",
  "alarm_start_datetime_utc",
  "alarm_end_datetime_utc",
];

/**
 * Preferred display order for alarm fields, prioritizing business-critical information
 * Fields appear in this order when displayed, with unlisted fields sorted alphabetically at the end
 */
const DEFAULT_FIELD_ORDER = [
  "alarm_included",
  "upd_excluded_reason",
  "alarm_area",
  "alarm_duration_ms",
  "upd_start_local",
  "upd_end_local",
  "upd_duration_ms",
  "upd_comment",
  "upd_text",
  "alarm_end_datetime_local",
  "alarm_end_datetime_utc",
  "alarm_equipment",
];
/**
 * Deep comparison utility to determine if two values are different
 * Uses fast-deep-equal for accurate comparison of complex objects
 *
 * @param value1 - First value to compare
 * @param value2 - Second value to compare
 * @returns true if values are different, false if they are equal
 */
const valuesAreDifferent = (value1: unknown, value2: unknown): boolean => {
  return !isEqual(value1, value2);
};

/**
 * Result object returned by structured value processing functions
 */
export interface StructuredValueResult {
  /** Array of formatted items ready for display */
  items: FormattedValueItem[];
  /** Whether the original data was truncated due to maxItems limit */
  wasTruncated: boolean;
}

/**
 * Core function that processes raw alarm data into a structured, formatted display
 *
 * This function handles the complete pipeline of:
 * 1. Filtering out excluded/system fields
 * 2. Comparing with baseline data (if provided) to show only changes
 * 3. Removing empty values (unless specifically requested)
 * 4. Sorting fields by business importance
 * 5. Formatting values appropriately (dates, booleans, etc.)
 * 6. Truncating to specified limits
 *
 * @param rawData - Raw alarm data object (e.g., originalValues, newValues)
 * @param options - Configuration options for processing
 * @returns Processed items and truncation status
 *
 * @example
 * ```typescript
 * // Show only changes from originalValues compared to newValues
 * const result = useStructuredValue(originalValues, {
 *   compareWith: newValues,
 *   maxItems: 2,
 *   timezone: 'UTC'
 * });
 * ```
 */
export const useStructuredValue = (
  rawData: Record<string, unknown> | null | undefined,
  options: StructuredValueOptions = {},
): StructuredValueResult => {
  const {
    excludeFields = DEFAULT_EXCLUDED_FIELDS,
    maxItems,
    fieldOrder = DEFAULT_FIELD_ORDER,
    showEmptyValues = false,
    compareWith,
    timezone,
  } = options;

  if (!rawData || typeof rawData !== "object") {
    return { items: [], wasTruncated: false };
  }

  let filteredEntries = Object.entries(rawData).filter(
    ([key]) => !excludeFields.includes(key),
  );

  if (compareWith && typeof compareWith === "object") {
    filteredEntries = filteredEntries.filter(([key, value]) => {
      const compareValue = compareWith[key];
      return valuesAreDifferent(value, compareValue);
    });
  }

  const validEntries = showEmptyValues
    ? filteredEntries
    : filteredEntries.filter(([, value]) => {
        if (value === null || value === undefined) return false;
        if (typeof value === "string" && value.trim() === "") return false;
        return true;
      });

  const sortedEntries = validEntries.sort(([keyA], [keyB]) => {
    const indexA = fieldOrder.indexOf(keyA);
    const indexB = fieldOrder.indexOf(keyB);

    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }

    if (indexA !== -1) return -1;
    if (indexB !== -1) return 1;

    return keyA.localeCompare(keyB);
  });

  let formattedItems = sortedEntries.map(
    ([key, value]): FormattedValueItem => ({
      key,
      label: createLabel(key),
      value: autoFormat(value, timezone, key),
    }),
  );

  const wasTruncated = maxItems && formattedItems.length > maxItems;
  if (wasTruncated) {
    formattedItems = formattedItems.slice(0, maxItems);
  }

  return { items: formattedItems, wasTruncated: !!wasTruncated };
};

/**
 * Convenience function for displaying structured values in regular table cells
 *
 * Configured for general cell display with moderate truncation limits.
 * Used when you want to show alarm data without comparison logic.
 *
 * @param rawData - Raw alarm data to display
 * @returns Formatted items (max 5) with truncation status
 *
 * @example
 * ```typescript
 * // Display alarm data in a regular cell
 * const { items, wasTruncated } = useStructuredValueForCell(alarmData);
 * ```
 */
export const useStructuredValueForCell = (
  rawData: Record<string, unknown> | null | undefined,
): StructuredValueResult => {
  return useStructuredValue(rawData, {
    maxItems: 5,
    showEmptyValues: false,
  });
};

/**
 * Specialized function for Previous/New Value cells in the alarm audit log table
 *
 * This is the key function that makes the audit log intelligent by:
 * - Showing only the fields that CHANGED between two states
 * - Limiting to 2 items to keep table rows compact and scannable
 * - Properly formatting timestamps with timezone support
 * - Using context-aware semantic truncation
 *
 * **Smart Truncation Behavior:**
 * - When multiple fields changed: Shows semantic meaning "2 of 5 fields changed"
 *   via `wasTruncated` flag (not simple character truncation)
 * - When only 1 field changed: Falls back to simple character truncation for long values
 * - This provides meaningful context about the scope of changes vs raw text cutting
 *
 * Used by PreviousValueCell and NewValueCell components to create
 * the smart comparison-based display that highlights what actually changed.
 *
 * @param rawData - Source alarm data (e.g., originalValues or newValues)
 * @param compareWith - Comparison data to diff against
 * @param timezone - Timezone for formatting timestamps
 * @returns Formatted changed fields (max 2) with truncation status
 *
 * @example
 * ```typescript
 * // Show what changed FROM the original state
 * const previousCell = useStructuredValueForCellWithComparison(
 *   entry.originalValues,  // Source: what it was
 *   entry.newValues,       // Compare: what it became
 *   timezone
 * );
 *
 * // Show what changed TO the new state
 * const newCell = useStructuredValueForCellWithComparison(
 *   entry.newValues,       // Source: what it became
 *   entry.originalValues,  // Compare: what it was
 *   timezone
 * );
 * ```
 */
export const useStructuredValueForCellWithComparison = (
  rawData: Record<string, unknown> | null | undefined,
  compareWith: Record<string, unknown> | null | undefined,
  timezone?: string,
): StructuredValueResult => {
  return useStructuredValue(rawData, {
    maxItems: 2,
    showEmptyValues: false,
    compareWith,
    timezone,
  });
};
