import { describe, it, expect, vi } from "vitest";
import { formatters, autoFormat, createLabel } from "./value-formatters";

// Mock the date utility
vi.mock("../../../../utils/date-util", () => ({
  formatISOTime: vi.fn((isoString, _format, timezone) => {
    // Throw error for invalid dates to test error handling
    if (isoString === "invalid-date") {
      throw new Error("Invalid date");
    }
    // Mock proper timezone conversion for test date
    // The timestamp formatter adds 'Z' to timestamps without timezone info
    // So "2025-08-16T16:43:05" becomes "2025-08-16T16:43:05Z"
    // Tests expect EST (UTC-5) = 11:43:05 AM
    if (
      (isoString === "2025-08-16T16:43:05Z" ||
        isoString === "2025-08-16T16:43:05") &&
      timezone === "America/New_York"
    ) {
      return "8/16/2025 11:43:05 AM";
    }
    // Fallback for other test cases
    return "8/16/2025 4:43:05 PM";
  }),
}));

describe("value-formatters", () => {
  describe("createLabel", () => {
    it("should use label mappings for known fields", () => {
      expect(createLabel("alarm_included")).toBe("Status");
      expect(createLabel("alarm_area")).toBe("Area");
      expect(createLabel("facility_bid")).toBe("Facility");
    });

    it("should convert snake_case to readable labels for unknown fields", () => {
      expect(createLabel("unknown_field_name")).toBe("Unknown Field Name");
      expect(createLabel("custom_data")).toBe("Custom Data");
    });

    it("should handle single words", () => {
      expect(createLabel("severity")).toBe("Severity");
    });
  });

  describe("formatters.boolean", () => {
    it("should format alarm_included field with tags", () => {
      const includedResult = formatters.boolean(true, "alarm_included");
      expect(includedResult).toEqual({
        text: "Included",
        isIncluded: true,
        useTag: true,
      });

      const excludedResult = formatters.boolean(false, "alarm_included");
      expect(excludedResult).toEqual({
        text: "Excluded",
        isIncluded: false,
        useTag: true,
      });
    });

    it("should format unmapped boolean fields without tags", () => {
      const result = formatters.boolean(true, "some_other_field");
      expect(result).toEqual({
        text: "true",
        isIncluded: true,
        useTag: false,
      });
    });
  });

  describe("formatters.number", () => {
    it("should format numbers with locale formatting", () => {
      const result = formatters.number(178000);
      expect(result.text).toBe("178,000"); // Locale formatted
    });

    it("should handle decimals", () => {
      const result = formatters.number(123.45);
      expect(result.text).toBe("123.45");
    });
  });

  describe("formatters.timestamp", () => {
    it("should format timestamps using date utility", () => {
      const result = formatters.timestamp(
        "2025-08-16T16:43:05",
        "America/New_York",
      );
      expect(result).toEqual({
        text: "8/16/2025 11:43:05 AM", // 16:43 UTC = 11:43 AM Eastern (UTC-5)
      });
    });

    it("should handle error cases gracefully", () => {
      const result = formatters.timestamp("invalid-date");
      // The implementation either returns formatted value or handles errors gracefully
      expect(result.text).toBeDefined();
      expect(typeof result.text).toBe("string");
    });
  });

  describe("formatters.default", () => {
    it("should return 'N/A' for null/undefined/empty", () => {
      expect(formatters.default(null).text).toBe("N/A");
      expect(formatters.default(undefined).text).toBe("N/A");
      expect(formatters.default("").text).toBe("N/A");
      expect(formatters.default("   ").text).toBe("N/A");
    });

    it("should return string value for other types", () => {
      expect(formatters.default("test").text).toBe("test");
      expect(formatters.default(123).text).toBe("123");
    });
  });

  describe("formatters.truncate", () => {
    it("should truncate long text", () => {
      const longText = "A".repeat(100);
      const result = formatters.truncate(longText, 20);
      expect(result.text).toBe("A".repeat(17) + "...");
      expect(result.truncated).toBe(true);
    });

    it("should not truncate short text", () => {
      const result = formatters.truncate("short text");
      expect(result.text).toBe("short text");
      expect(result.truncated).toBeUndefined();
    });
  });

  describe("autoFormat", () => {
    it("should auto-detect null/undefined", () => {
      expect(autoFormat(null).text).toBe("N/A");
      expect(autoFormat(undefined).text).toBe("N/A");
    });

    it("should auto-detect booleans", () => {
      const result = autoFormat(true, undefined, "alarm_included");
      expect(result).toEqual({
        text: "Included",
        isIncluded: true,
        useTag: true,
      });
    });

    it("should auto-detect numbers", () => {
      const result = autoFormat(178000);
      expect(result.text).toBe("178,000");
    });

    it("should auto-detect timestamp strings", () => {
      const result = autoFormat("2025-08-16T16:43:05", "America/New_York");
      expect(result.text).toBe("8/16/2025 11:43:05 AM"); // 16:43 UTC = 11:43 AM Eastern (UTC-5)
    });

    it("should auto-detect long strings and truncate", () => {
      const longString = "A".repeat(100);
      const result = autoFormat(longString);
      expect(result.text).toContain("...");
      expect(result.truncated).toBe(true);
    });

    it("should handle regular strings", () => {
      const result = autoFormat("Shipping");
      expect(result.text).toBe("Shipping");
    });
  });

  describe("real alarm data", () => {
    it("should handle actual field values from sample data", () => {
      expect(autoFormat("Shipping").text).toBe("Shipping");
      expect(autoFormat("600").text).toBe("600");
      expect(autoFormat(178000).text).toBe("178,000");
      expect(autoFormat("2025-08-16T16:43:05", "America/New_York").text).toBe(
        "8/16/2025 11:43:05 AM", // 16:43 UTC = 11:43 AM Eastern (UTC-5)
      );
      expect(autoFormat(null).text).toBe("N/A");
    });
  });
});
