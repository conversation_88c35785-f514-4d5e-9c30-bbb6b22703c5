import { formatISOTime } from "../../../../utils/date-util";
import { DateTime } from "luxon";

export interface FormattedValue {
  text: string;
  truncated?: boolean;
  useTag?: boolean;
  isIncluded?: boolean;
}

export interface FormattedValueItem {
  key: string;
  label: string;
  value: FormattedValue;
  type?: "time" | "string" | "number";
}
// added in case we want to map more boolean fields later on
const BOOLEAN_FIELD_MAPPINGS: Record<string, { true: string; false: string }> =
  {
    alarm_included: { true: "Included", false: "Excluded" },
  };

export const formatters = {
  boolean: (booleanValue: boolean, fieldKey?: string): FormattedValue => {
    const mapping = fieldKey ? BOOLEAN_FIELD_MAPPINGS[fieldKey] : null;
    const text = mapping
      ? mapping[booleanValue.toString() as "true" | "false"]
      : booleanValue.toString();

    return {
      text,
      isIncluded: booleanValue,
      useTag: !!mapping,
    };
  },

  timestamp: (value: string, timezone?: string): FormattedValue => {
    try {
      // EDP stores all non-localtimestamps as UTC but returns them without 'Z' suffix
      let isoString = value;
      if (
        !value.endsWith("Z") &&
        !value.includes("+") &&
        !value.includes("-", 10)
      ) {
        isoString = value + "Z";
      }

      const formatted = formatISOTime(
        isoString,
        "M/d/yyyy h:mm:ss a",
        timezone,
      );
      return { text: formatted };
    } catch {
      return { text: value };
    }
  },

  timestampWithMilliseconds: (
    value: string,
    timezone?: string,
    fieldKey?: string,
  ): FormattedValue => {
    try {
      let isoString = value;

      // Local time fields (upd_start_local, upd_end_local) actually contain UTC time
      // but represent the original times sent to the API before EDP conversion
      // Treat them as UTC and convert to local time for display
      if (fieldKey && fieldKey.includes("_local")) {
        // Add 'Z' to treat as UTC, then convert to local timezone
        if (
          !value.endsWith("Z") &&
          !value.includes("+") &&
          !value.includes("-", 10)
        ) {
          isoString = value + "Z";
        }
        const formatted = formatISOTime(
          isoString,
          "M/d/yyyy h:mm:ss.SSS a",
          timezone,
        );
        return { text: formatted };
      }

      // All other timestamp fields are UTC but returned without 'Z' suffix
      // Add 'Z' to ensure they're parsed as UTC, then convert to local time
      if (
        !value.endsWith("Z") &&
        !value.includes("+") &&
        !value.includes("-", 10)
      ) {
        isoString = value + "Z";
      }

      const formatted = formatISOTime(
        isoString,
        "M/d/yyyy h:mm:ss.SSS a",
        timezone,
      );
      return { text: formatted };
    } catch {
      return { text: value };
    }
  },

  truncate: (text: string, maxLength: number = 50): FormattedValue => {
    if (text.length <= maxLength) {
      return { text };
    }
    return {
      text: text.substring(0, maxLength - 3) + "...",
      truncated: true,
    };
  },

  number: (value: number): FormattedValue => ({
    text: typeof value === "number" ? value.toLocaleString() : String(value),
  }),

  duration: (milliseconds: number): FormattedValue => {
    if (typeof milliseconds !== "number" || milliseconds < 0) {
      return { text: "N/A" };
    }

    const totalMs = Math.floor(milliseconds);
    const hours = Math.floor(totalMs / 3600000);
    const minutes = Math.floor((totalMs % 3600000) / 60000);
    const seconds = Math.floor((totalMs % 60000) / 1000);
    const ms = totalMs % 1000;

    return {
      text: `${hours}:${minutes}:${seconds}:${ms}`,
    };
  },

  default: (value: unknown): FormattedValue => {
    if (value === null || value === undefined) {
      return { text: "N/A" };
    }
    if (typeof value === "string" && value.trim() === "") {
      return { text: "N/A" };
    }
    return { text: String(value) };
  },
};

export const autoFormat = (
  value: unknown,
  timezone?: string,
  fieldKey?: string,
): FormattedValue => {
  if (value === null || value === undefined) {
    return formatters.default(value);
  }

  if (typeof value === "boolean") {
    return formatters.boolean(value, fieldKey);
  }

  if (typeof value === "number") {
    return formatters.number(value);
  }

  if (typeof value === "string") {
    const dateTime = DateTime.fromISO(value);
    if (dateTime.isValid) {
      return formatters.timestamp(value, timezone);
    }
    if (value.length > 80) {
      return formatters.truncate(value, 80);
    }
  }

  return formatters.default(value);
};

export const autoFormatWithMilliseconds = (
  value: unknown,
  timezone?: string,
  fieldKey?: string,
): FormattedValue => {
  if (value === null || value === undefined) {
    return formatters.default(value);
  }

  if (typeof value === "boolean") {
    return formatters.boolean(value, fieldKey);
  }

  if (typeof value === "number") {
    // Format duration fields specially
    if (
      fieldKey &&
      (fieldKey.includes("duration") || fieldKey.endsWith("_ms"))
    ) {
      return formatters.duration(value);
    }
    return formatters.number(value);
  }

  if (typeof value === "string") {
    const dateTime = DateTime.fromISO(value);
    if (dateTime.isValid) {
      return formatters.timestampWithMilliseconds(value, timezone, fieldKey);
    }
    if (value.length > 80) {
      return formatters.truncate(value, 80);
    }
  }

  return formatters.default(value);
};

export const createLabel = (key: string): string => {
  const labelMappings: Record<string, string> = {
    alarm_included: "Status",
    upd_excluded_reason: "Reason",
    upd_start_local: "Start Time",
    upd_end_local: "End Time",
    upd_duration_ms: "Duration",
    upd_text: "Text",
    upd_comment: "Comment",
    equipment_pk: "Equipment ID",
    section_pk: "Section ID",
    status_pk: "Status ID",
    facility_bid: "Facility",
    etl_insert_datetime: "Insert Time",
    etl_update_datetime: "Update Time",
    alarm_area: "Area",
    alarm_duration_ms: "Duration",
    alarm_end_datetime_local: "End Time",
    alarm_end_datetime_utc: "End Time (UTC)",
    alarm_equipment: "Equipment",
  };

  if (labelMappings[key]) {
    return labelMappings[key];
  }

  return key
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};
