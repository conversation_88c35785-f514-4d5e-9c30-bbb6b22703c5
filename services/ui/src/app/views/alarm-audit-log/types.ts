import type { components } from "@ict/sdk/openapi-react-query";

export type AuditLogEntry = components["schemas"]["AlarmAuditLogEntry"];
export type SortField = components["schemas"]["SortField"];
export type AuditLogListResponse =
  components["schemas"]["PostAuditLogsListResponse"];

export interface DateRange {
  start_date: Date;
  end_date: Date;
}

export type OperationType = "insert" | "update";
