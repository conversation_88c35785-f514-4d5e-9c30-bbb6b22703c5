import { vi } from "vitest";
import { fireEvent, render, screen, waitFor } from "../../../../test-utils";
import { AppConfigSettingDetail } from "../app-config-setting-detail";
import { AppConfigSettingSource } from "@ict/sdk/types";
import { toastManager } from "../../../components/toast/toast-container";
import { getSettingSchema } from "../../../config/hooks/use-config";

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

vi.mock("../../../components/toast/toast-container", () => ({
  toastManager: {
    addToast: vi.fn(),
  },
}));

const mockSetting = {
  id: "test-setting",
  name: "Test Setting",
  group: "Test Group",
  description: "Test Description",
  dataType: "string",
  source: AppConfigSettingSource.default,
  value: "test-value",
};

// Mock the ICT API
const mockUseQuery = vi.fn();
vi.mock("../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: (...args: any[]) => {
        mockUseQuery(...args);
        return {
          data: mockSetting,
          isLoading: false,
          error: null,
        };
      },
    },
  },
}));

// mock the use-roles hook
vi.mock("../../../auth/hooks/use-roles", () => ({
  useRoles: vi.fn().mockReturnValue({ hasConfiguratorAccess: true }),
}));

// mock the updateConfigSetting hook
vi.mock("../../../config/hooks/use-config", () => ({
  updateConfigSetting: vi.fn().mockReturnValue({}),
  getSettingSchema: vi.fn().mockReturnValue({}),
}));

describe("AppConfigSettingDetail", () => {
  const onCloseMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders correctly when creating a new setting", () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    // Check that the dialog title is displayed
    expect(screen.getByText("Add New App Setting")).toBeInTheDocument();

    // Check that the name input is displayed
    expect(screen.getByLabelText("Name")).toBeInTheDocument();

    // Check that the data type dropdown is displayed
    expect(screen.getByText("Data Type")).toBeInTheDocument();

    // Check that the default value accordion is displayed
    expect(screen.getByText("Default value")).toBeInTheDocument();
  });

  it("renders correctly when editing an existing setting", async () => {
    render(
      <AppConfigSettingDetail settingId="test-setting" onClose={onCloseMock} />,
    );

    // Wait for the setting to load
    await waitFor(() => {
      expect(screen.getByText("Edit App Setting")).toBeInTheDocument();
    });

    // Check that the setting details are displayed
    expect(screen.getByLabelText("ID")).toHaveValue("test-setting");
    expect(screen.getByLabelText("Name")).toHaveValue("Test Setting");
    expect(screen.getByLabelText("Group")).toHaveValue("Test Group");
    expect(screen.getByLabelText("Description")).toHaveValue(
      "Test Description",
    );
    expect(screen.getByLabelText("Data Type")).toHaveValue("string");
    expect(screen.getByText("Default value")).toBeInTheDocument();
  });

  it("updates setting name, group, and description", async () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    const nameInput = screen.getByLabelText("Name");
    const groupInput = screen.getByLabelText("Group");
    const descriptionInput = screen.getByLabelText("Description");

    fireEvent.change(nameInput, { target: { value: "New Name" } });
    fireEvent.change(groupInput, { target: { value: "New Group" } });
    fireEvent.change(descriptionInput, {
      target: { value: "New Description" },
    });

    expect(nameInput).toHaveValue("New Name");
    expect(groupInput).toHaveValue("New Group");
    expect(descriptionInput).toHaveValue("New Description");
  });

  it("updates setting value for string type", async () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    const valueInput = screen.getByTestId("string-input");
    fireEvent.change(valueInput, { target: { value: "New Value" } });

    expect(valueInput).toHaveValue("New Value");
  });

  it("updates setting value for number type", async () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    // Change data type to number
    // need to get by string to get the button element for the dropdown
    fireEvent.click(screen.getByTitle("String"));
    console.log(screen.getByTitle("String"));
    const numberOption = await screen.findByText("Number");
    fireEvent.click(numberOption);

    // Wait for the number input to render
    await waitFor(() => {
      expect(screen.getByRole("spinbutton")).toBeInTheDocument();
    });

    const valueInput = screen.getByRole("spinbutton");
    fireEvent.change(valueInput, { target: { value: "123" } });

    expect(valueInput).toHaveValue(123);
  });

  it("updates setting value for boolean type", async () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    // change data type to boolean
    // need to get by string to get the button element for the dropdown
    fireEvent.click(screen.getByTitle("String"));
    console.log(screen.getByTitle("String"));
    const booleanOption = await screen.findByText("Boolean");
    fireEvent.click(booleanOption);

    // Wait for the toggle to render
    await waitFor(() => {
      expect(screen.getByTestId("boolean-input")).toBeInTheDocument();
    });

    const valueInput = screen.getByTestId("boolean-input");
    fireEvent.click(valueInput);

    expect(valueInput).toBeChecked();
  });

  it("updates setting value for json type", async () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    // Change data type to json
    // need to get by string to get the button element for the dropdown
    fireEvent.click(screen.getByTitle("String"));
    console.log(screen.getByTitle("String"));
    const jsonOption = await screen.findByText("JSON");
    fireEvent.click(jsonOption);

    // Wait for the textarea to render
    await waitFor(() => {
      expect(screen.getByTestId("json-input")).toBeInTheDocument();
    });

    const valueInput = screen.getByTestId("json-input");
    fireEvent.change(valueInput, { target: { value: '{"key": "value"}' } });

    expect(valueInput).toHaveValue('{"key": "value"}');
  });

  it("beautifies json value", async () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    // Change data type to json
    // need to get by string to get the button element for the dropdown
    fireEvent.click(screen.getByTitle("String"));
    console.log(screen.getByTitle("String"));
    const jsonOption = await screen.findByText("JSON");
    fireEvent.click(jsonOption);

    // Wait for the textarea to render
    await waitFor(() => {
      expect(screen.getByTestId("json-input")).toBeInTheDocument();
    });

    const valueInput = screen.getByTestId("json-input");
    fireEvent.change(valueInput, { target: { value: '{"key":"value"}' } });

    const beautifyButton = screen.getByText("Beautify");
    fireEvent.click(beautifyButton);

    expect(valueInput).toHaveValue('{\n    "key": "value"\n}');
  });

  it("adds a new tenant value", async () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    screen.debug();

    const addTenantValueButton = screen.getByTestId("add-tenant-value-button");
    fireEvent.click(addTenantValueButton);

    expect(screen.getByText("Tenant value*")).toBeInTheDocument();
  });

  it("adds a new facility value", async () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    screen.debug();

    const addFacilityValueButton = screen.getByTestId(
      "add-facility-value-button",
    );
    fireEvent.click(addFacilityValueButton);

    expect(screen.getByText("Facility value*")).toBeInTheDocument();
  });

  it("adds a new user value", async () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    const addUserValueButton = screen.getByText("Add User Value");
    fireEvent.click(addUserValueButton);

    expect(screen.getByText("User value*")).toBeInTheDocument();
  });

  it("saves a new setting", async () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    const nameInput = screen.getByLabelText("Name");
    fireEvent.change(nameInput, { target: { value: "New Setting" } });

    const saveButton = screen.getByText("Save");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(toastManager.addToast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Success",
        }),
      );
    });
  });

  it("shows error when saving a setting with empty name", async () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    // give the setting a value
    const valueInput = screen.getByTestId("string-input");
    fireEvent.change(valueInput, { target: { value: "New Value" } });

    const saveButton = screen.getByText("Save");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(toastManager.addToast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Error",
          message: "Setting name is required!",
        }),
      );
    });
  });

  it("shows error when saving a setting with empty json value", async () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    const nameInput = screen.getByLabelText("Name");
    fireEvent.change(nameInput, { target: { value: "New Setting" } });

    // Change data type to json
    // need to get by string to get the button element for the dropdown
    fireEvent.click(screen.getByTitle("String"));
    console.log(screen.getByTitle("String"));
    const jsonOption = await screen.findByText("JSON");
    fireEvent.click(jsonOption);

    const jsonInput = screen.getByTestId("json-input");
    fireEvent.change(jsonInput, { target: { value: "" } });

    const saveButton = screen.getByText("Save");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(toastManager.addToast).not.toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Success",
        }),
      );
    });

    expect(
      screen.getByText("You must enter a valid JSON value!"),
    ).toBeInTheDocument();
  });

  it("shows error when saving a setting with invalid json value", async () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    const testSchema = {
      type: "object",
      properties: {
        name: { type: "string" },
        age: { type: "number" },
      },
      required: ["name", "age"],
    };
    const mockGetSettingSchema = vi.mocked(getSettingSchema);
    mockGetSettingSchema.mockResolvedValue(testSchema);

    const nameInput = screen.getByLabelText("Name");
    fireEvent.change(nameInput, { target: { value: "New Setting" } });

    // Change data type to json
    // need to get by string to get the button element for the dropdown
    fireEvent.click(screen.getByTitle("String"));
    console.log(screen.getByTitle("String"));
    const jsonOption = await screen.findByText("JSON");
    fireEvent.click(jsonOption);

    const jsonInput = screen.getByTestId("json-input");
    fireEvent.change(jsonInput, {
      target: {
        value: `
      {
        "name": "Test User",
        "age": "twenty"
      }
      `,
      },
    });

    const saveButton = screen.getByText("Save");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(toastManager.addToast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Error",
          message: "Setting value does not match JSON schema",
        }),
      );
    });

    // Setting value does not match JSON schema
    // Is an error message below the json text field
    expect(screen.getByText("should be number")).toBeInTheDocument();
  });

  it("should save setting when schema is undefined", async () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    const testSchema = undefined;
    const mockGetSettingSchema = vi.mocked(getSettingSchema);
    mockGetSettingSchema.mockResolvedValue(testSchema);

    const nameInput = screen.getByLabelText("Name");
    fireEvent.change(nameInput, { target: { value: "New Setting" } });

    // Change data type to json
    // need to get by string to get the button element for the dropdown
    fireEvent.click(screen.getByTitle("String"));
    console.log(screen.getByTitle("String"));
    const jsonOption = await screen.findByText("JSON");
    fireEvent.click(jsonOption);

    const jsonInput = screen.getByTestId("json-input");
    fireEvent.change(jsonInput, {
      target: {
        value: `
      {
        "name": "Test User",
        "age": "twenty"
      }
      `,
      },
    });

    const saveButton = screen.getByText("Save");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(toastManager.addToast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Success",
        }),
      );
    });
  });

  it("should save setting when schema is not object", async () => {
    render(<AppConfigSettingDetail onClose={onCloseMock} />);

    const testSchema = 2;
    const mockGetSettingSchema = vi.mocked(getSettingSchema);
    mockGetSettingSchema.mockResolvedValue(testSchema);

    const nameInput = screen.getByLabelText("Name");
    fireEvent.change(nameInput, { target: { value: "New Setting" } });

    // Change data type to json
    // need to get by string to get the button element for the dropdown
    fireEvent.click(screen.getByTitle("String"));
    console.log(screen.getByTitle("String"));
    const jsonOption = await screen.findByText("JSON");
    fireEvent.click(jsonOption);

    const jsonInput = screen.getByTestId("json-input");
    fireEvent.change(jsonInput, {
      target: {
        value: `
      {
        "name": "Test User",
        "age": "twenty"
      }
      `,
      },
    });

    const saveButton = screen.getByText("Save");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(toastManager.addToast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Success",
        }),
      );
    });
  });
});
