import { vi } from "vitest";
import { fireEvent, render, screen } from "../../../test-utils";
import { ictApi } from "../../api/ict-api";
import { ContainerList } from "./container-list";
import { ChangeEvent } from "react";
import { AppConfigSetting } from "../../config/menu/types";

// Mock the useLocation hook from react-router
let mockLocation = {
  search: "",
  pathname: "/container-list",
};

// Mock navigate function
const mockNavigate = vi.fn();

vi.mock("react-router", () => ({
  useLocation: () => mockLocation,
  useNavigate: () => mockNavigate,
  useOutletContext: () => vi.fn(), // Mock function for showing view aside content
}));

// Mock the ictApi
vi.mock("../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: vi.fn(),
    },
  },
}));

// Mock the transformFilters utility
vi.mock("../../api/util/filter-transform-util", () => ({
  transformFilters: () => [],
}));

// Mock the useConfig hooks
vi.mock("../../config/hooks/use-config", () => ({
  useConfig: vi.fn(() => ({
    data: [],
    isLoading: false,
  })),
  useConfigSetting: vi.fn(() => ({ setting: { value: "America/New_York" } })),
}));

// Mock the auth hooks
vi.mock("../../auth/hooks/use-roles", () => ({
  useRoles: () => ({
    roles: [],
    actualRoles: [],
    isInternalUser: false,
    hasTableauAccess: false,
    hasConfiguratorAccess: false,
    isActualInternalUser: false,
    isFacilityAdmin: false,
    isLoading: false,
  }),
}));

// Mock the auth0 client
vi.mock("@auth0/auth0-react", () => ({
  useAuth0: () => ({
    isAuthenticated: true,
    user: { name: "Test User" },
    getIdTokenClaims: vi.fn().mockResolvedValue({
      "https://ict.dematic.cloud/roles": [],
    }),
    loginWithRedirect: vi.fn(),
    logout: vi.fn(),
    getAccessTokenSilently: vi.fn(),
  }),
}));

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    Checkbox: ({
      labelText,
      id,
      checked,
      onChange,
    }: {
      labelText: string;
      id: string;
      checked: boolean;
      onChange: (evt: ChangeEvent<HTMLInputElement>) => void;
    }) => (
      <div data-testid="checkbox">
        <label htmlFor={id}>{labelText}</label>
        <input
          type="checkbox"
          id={id}
          checked={checked}
          onChange={(e) => onChange(e)}
          data-testid={id}
        />
      </div>
    ),
    Tag: ({
      children,
      type,
      filter: _filter,
      onClose,
    }: {
      children: React.ReactNode;
      type: string;
      filter?: boolean;
      onClose?: () => void;
    }) => (
      <div data-testid="filter-tag" data-type={type}>
        {children}
        {onClose && (
          <button data-testid="dismiss-tag" onClick={onClose}>
            ×
          </button>
        )}
      </div>
    ),
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock the ContainerListTable component
vi.mock("./components/container-list-table/container-list-table", () => ({
  ContainerListTable: vi
    .fn()
    .mockReturnValue(
      <div data-testid="container-list-table">Container List Table</div>,
    ),
}));

describe("ContainerList", () => {
  const mockUseQuery = ictApi.client.useQuery as any;
  const mockSettingProp: AppConfigSetting = {
    id: "test",
    name: "container-list-dashboard",
    dataType: "json",
    group: "dashboard",
    value: {
      dataSource: "standard",
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockLocation = {
      search: "",
      pathname: "/container-list",
    };

    // Set up the default mock implementation for useQuery
    mockUseQuery.mockReturnValue({
      data: {
        data: [],
        metadata: { totalResults: 0 },
      },
      isLoading: false,
      isFetching: false,
      error: null,
      dataUpdatedAt: 1620000000000,
      refetch: vi.fn(),
    });
  });

  test("renders the component with ContainerListTable", () => {
    render(
      <ContainerList
        id="inventory-list"
        options={{}}
        setting={mockSettingProp}
      />,
    );

    expect(screen.getByText("Container List")).toBeInTheDocument();
    expect(screen.getByTestId("container-list-table")).toBeInTheDocument();
  });

  test("renders the bypass filtering checkbox with initial state", () => {
    render(
      <ContainerList
        id="inventory-list"
        options={{}}
        setting={mockSettingProp}
      />,
    );

    const checkbox = screen.getByTestId("bypass-filtering-checkbox");
    expect(checkbox).toBeInTheDocument();
    expect(checkbox).not.toBeChecked();
    expect(
      screen.getByText("Check this to reset automation zone filtering"),
    ).toBeInTheDocument();
  });

  test("toggles checkbox state when clicked", () => {
    render(
      <ContainerList
        id="inventory-list"
        options={{}}
        setting={mockSettingProp}
      />,
    );

    const checkbox = screen.getByTestId("bypass-filtering-checkbox");
    expect(checkbox).not.toBeChecked();
    fireEvent.click(checkbox);
    expect(checkbox).toBeChecked();
    expect(
      screen.getByText("Uncheck this for automation zone filtering"),
    ).toBeInTheDocument();

    // Click again to uncheck
    fireEvent.click(checkbox);
    expect(checkbox).not.toBeChecked();
    expect(
      screen.getByText("Check this to reset automation zone filtering"),
    ).toBeInTheDocument();
  });

  test("API call includes byPassConfigSetting: false when checkbox is unchecked", () => {
    render(
      <ContainerList
        id="inventory-list"
        options={{}}
        setting={mockSettingProp}
      />,
    );

    // Verify API call with byPassConfigSetting: false
    expect(mockUseQuery).toHaveBeenCalledWith(
      "post",
      "/inventory/containers/list",
      expect.objectContaining({
        body: expect.objectContaining({
          byPassConfigSetting: false,
        }),
      }),
      expect.anything(),
    );
  });

  test("API call includes byPassConfigSetting: true when checkbox is checked", () => {
    render(
      <ContainerList
        id="inventory-list"
        options={{}}
        setting={mockSettingProp}
      />,
    );

    const checkbox = screen.getByTestId("bypass-filtering-checkbox");

    // Click the checkbox to check it
    fireEvent.click(checkbox);

    // Verify API call with byPassConfigSetting: true
    expect(mockUseQuery).toHaveBeenCalledWith(
      "post",
      "/inventory/containers/list",
      expect.objectContaining({
        body: expect.objectContaining({
          byPassConfigSetting: true,
        }),
      }),
      expect.anything(),
    );
  });

  test("displays SKU filter tag when SKU parameter is in URL", () => {
    // Update the mock to include a SKU parameter
    mockLocation = {
      search: "?sku=TEST-SKU",
      pathname: "/container-list",
    };

    render(
      <ContainerList
        id="inventory-list"
        options={{}}
        setting={mockSettingProp}
      />,
    );

    // Check that the filter tag is displayed with the correct SKU
    const filterTags = screen.getAllByTestId("filter-tag");
    expect(filterTags).toHaveLength(1);
    expect(screen.getByText("SKU: TEST-SKU")).toBeInTheDocument();
  });

  test("dismissing SKU tag removes it from URL and filters", () => {
    // Update the mock to include a SKU parameter
    mockLocation = {
      search: "?sku=TEST-SKU",
      pathname: "/container-list",
    };

    render(
      <ContainerList
        id="inventory-list"
        options={{}}
        setting={mockSettingProp}
      />,
    );

    // Verify the tag is displayed
    const filterTags = screen.getAllByTestId("filter-tag");
    expect(filterTags).toHaveLength(1);

    // Dismiss the tag
    fireEvent.click(screen.getByTestId("dismiss-tag"));

    // Verify navigate was called to update the URL
    expect(mockNavigate).toHaveBeenCalledWith({ search: "" });
  });

  test("does not display SKU filter tag when no SKU parameter", () => {
    render(
      <ContainerList
        id="inventory-list"
        options={{}}
        setting={mockSettingProp}
      />,
    );

    // Check that the filter tag is not displayed
    expect(screen.queryByTestId("filter-tag")).not.toBeInTheDocument();
  });
});
