import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { ChangeEvent, useEffect, useMemo, useState } from "react";
import { useLocation, useNavigate } from "react-router";
import { ictApi } from "../../api/ict-api";
import { transformFilters } from "../../api/util/filter-transform-util";
import { ContainerListTable } from "./components/container-list-table/container-list-table";
import type { ContainerItem, SortField } from "./types";
import { useTranslation } from "react-i18next";
import { Checkbox, DismissibleTag } from "@carbon/react";
import styles from "./container-list.module.css";
import { ExportModal } from "../../components/export-modal/export-modal";
import { useApiErrorState } from "../../hooks/use-api-error-state";
import { useConfigSetting } from "../../config/hooks/use-config";
import { formatISOTimeToReadableDateTime } from "../../utils";
import { ViewBar } from "../../components/view-bar/view-bar";
import { BaseViewProps } from "../view-registry.types";
import FullPageContainer from "../../components/full-page-container/full-page-container";
import { useViewOptions } from "../../hooks/use-view-options";
import ContainerListOptionsForm, {
  ContainerListOptions,
} from "./container-list-options";
import { useRoles } from "../../auth/hooks/use-roles";

type ContainerListProps = BaseViewProps;

export const ContainerList = ({ id, setting }: ContainerListProps) => {
  // Default options for container list
  const defaultOptions: ContainerListOptions = {
    dataSource: "facility",
  };

  // Create initial setting if none provided
  const initialSetting = setting || {
    id: id,
    name: id || "container-list-dashboard",
    dataType: "json" as const,
    value: defaultOptions,
  };

  const { draftOptions, handleShowSettings, handleSaveOptions, isDirty } =
    useViewOptions<ContainerListOptions>({
      setting: initialSetting,
      defaultOptions,
      optionsComponent: ContainerListOptionsForm,
    });

  const { t } = useTranslation();
  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");
  const timezone = timezoneConfig?.value as string;
  const { hasConfiguratorAccess } = useRoles();
  const location = useLocation();
  const navigate = useNavigate();
  const [bypassFiltering, setBypassFiltering] = useState(false);
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 50,
  });
  const [sorting, setSorting] = useState<SortingState>([
    { id: "container_id", desc: false },
  ]);
  // Add state for the export dialog
  const [exportDialogOpen, setExportDialogOpen] = useState(false);

  // Get the SKU from the URL query parameters
  const queryParams = useMemo(
    () => new URLSearchParams(location.search),
    [location.search],
  );
  const skuParam = queryParams.get("sku");

  // Initialize column filters with the SKU filter if provided in URL
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(() => {
    const initialFilters: ColumnFiltersState = [];

    // If SKU parameter is provided in the URL, add it as a filter
    if (skuParam) {
      initialFilters.push({
        id: "sku",
        value: skuParam,
      });
    }

    return initialFilters;
  });

  // Update column filters when SKU parameter changes
  useEffect(() => {
    setColumnFilters((prev) => {
      // Check if we already have a SKU filter
      const skuFilterIndex = prev.findIndex((filter) => filter.id === "sku");

      if (skuParam) {
        if (skuFilterIndex >= 0) {
          // Update existing SKU filter
          const newFilters = [...prev];
          newFilters[skuFilterIndex] = {
            id: "sku",
            value: skuParam,
          };
          return newFilters;
        }

        // Add new SKU filter
        return [
          ...prev,
          {
            id: "sku",
            value: skuParam,
          },
        ];
      } else {
        // Remove SKU filter if skuParam is null/undefined
        if (skuFilterIndex >= 0) {
          const newFilters = [...prev];
          newFilters.splice(skuFilterIndex, 1);
          return newFilters;
        }
        return prev;
      }
    });
  }, [skuParam]);

  // Handle dismissing the SKU tag
  const handleDismissSku = () => {
    // Remove the SKU filter from columnFilters
    setColumnFilters((prev) => prev.filter((filter) => filter.id !== "sku"));

    // Remove the SKU from URL query params
    const newParams = new URLSearchParams(location.search);
    newParams.delete("sku");
    navigate({ search: newParams.toString() });
  };

  const sortFields: SortField[] = useMemo(
    () =>
      sorting.map((sort) => ({
        columnName: sort.id,
        isDescending: sort.desc,
      })),
    [sorting],
  );

  // Function to generate column definitions for the export dialog
  const getColumnDefinitions = () => {
    return [
      { id: "container_id", header: "Container ID" },
      { id: "location_id", header: "Location ID" },
      { id: "zone", header: "Zone" },
      { id: "sku", header: "SKU" },
      { id: "quantity", header: "Quantity" },
      {
        id: "last_activity_date",
        header: "Last Activity",
      },
      {
        id: "last_cycle_count",
        header: "Last Cycle Count",
      },
      { id: "data_updated", header: "Data Updated" },
      {
        id: "free_cycle_count",
        header: "Free Cycle Count",
      },
    ];
  };

  const apiFilters = useMemo(
    () => transformFilters(columnFilters),
    [columnFilters],
  );

  // Determine which endpoint to use based on data source
  const endpoint =
    draftOptions.dataSource === "wms"
      ? "/inventory/wms/containers/list"
      : "/inventory/containers/list";

  // Use the API to fetch inventory data based on selected data source
  const { data, dataUpdatedAt, error, isLoading, isFetching, refetch } =
    ictApi.client.useQuery(
      "post",
      endpoint,
      {
        body: {
          page: pagination.pageIndex,
          limit: pagination.pageSize,
          filters: apiFilters,
          sortFields,
          byPassConfigSetting: bypassFiltering,
          ...(globalFilter !== "" && { searchString: globalFilter }),
        },
      },
      {
        enabled: true,
        keepPreviousData: true,
        placeholderData: (prev) => prev,
        retry: false,
        refetchOnWindowFocus: false,
      },
    );

  const isNoDataAvailable = useApiErrorState(error);

  const containerData = useMemo(
    () => (data?.data ?? []) as unknown as ContainerItem[],
    [data],
  );

  const formattedLastUpdated = dataUpdatedAt
    ? formatISOTimeToReadableDateTime(
        new Date(dataUpdatedAt).toISOString(),
        timezone,
      )
    : t("containerList.loading", "Loading...");

  const handleRefresh = () => {
    refetch();
  };

  const handleContainerSelect = (containerId: string) => {
    navigate(`container/${containerId}`, {
      state: { datasource: draftOptions.dataSource },
    });
  };

  // Export handler to use selected columns
  const handleExport = async (
    fileName: string,
    selectedColumns: Record<string, boolean>,
    signal: AbortSignal,
  ) => {
    console.log("Starting export process...");

    const { error, data: blob } = await ictApi.fetchClient.POST(
      `${endpoint}/export`,
      {
        body: {
          page: pagination.pageIndex,
          limit: pagination.pageSize,
          filters: apiFilters,
          sortFields,
          columns: selectedColumns,
          searchString: globalFilter || undefined,
          byPassConfigSetting: bypassFiltering,
        },
        parseAs: "blob",
        signal,
      },
    );

    if (error) {
      throw new Error("Export failed");
    }
    if (!blob) {
      throw new Error("No data to export");
    }

    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(a);
    console.log("Export completed successfully!");
  };

  // Function to handle modal close
  const handleExportModalClose = () => {
    setExportDialogOpen(false);
  };

  return (
    <div
      data-testid="container-list"
      className={styles.container}
      style={{ flex: 1, width: "100%", overflowX: "hidden" }}
    >
      <ViewBar
        title={"Container List"}
        showDatePeriodRange={false}
        showSettings={true}
        hasConfiguratorAccess={hasConfiguratorAccess}
        onSettingsClick={handleShowSettings}
        saveEnabled={isDirty}
        showSave={true}
        onSaveClick={handleSaveOptions}
      >
        <div className={styles.controls}>
          {skuParam && (
            <div className={styles.filterTag}>
              <span>Filter:</span>
              <DismissibleTag
                type="high-contrast"
                onClose={handleDismissSku}
                text={`SKU: ${skuParam}`}
              />
            </div>
          )}
          <Checkbox
            className={styles.checkbox}
            labelText={
              bypassFiltering
                ? "Uncheck this for automation zone filtering"
                : "Check this to reset automation zone filtering"
            }
            id="bypass-filtering-checkbox"
            checked={bypassFiltering}
            onChange={(evt: ChangeEvent<HTMLInputElement>) =>
              setBypassFiltering(evt.target.checked)
            }
          />
          <p
            className={styles.lastUpdated}
            data-testid="container-list-last-update"
          >
            {t(
              "containerList.lastUpdated",
              "Inventory Data Updated: {{formattedLastUpdated}}",
              { formattedLastUpdated: formattedLastUpdated },
            )}
          </p>
        </div>
      </ViewBar>
      <FullPageContainer>
        <ContainerListTable
          data={containerData}
          pagination={pagination}
          setPagination={setPagination}
          sorting={sorting}
          setSorting={setSorting}
          columnFilters={columnFilters}
          setColumnFilters={setColumnFilters}
          isLoading={isLoading}
          isFetching={isFetching}
          error={isNoDataAvailable ? null : error}
          rowCount={data?.metadata.totalResults ?? 0}
          timezone={timezone}
          onRefresh={handleRefresh}
          onExport={() => setExportDialogOpen(true)}
          setGlobalFilter={setGlobalFilter}
          onContainerSelect={handleContainerSelect}
        />
      </FullPageContainer>
      <ExportModal
        open={exportDialogOpen}
        onClose={handleExportModalClose}
        onExport={handleExport}
        filters={columnFilters}
        columnDefs={getColumnDefinitions()}
        globalFilter={globalFilter}
        baseFileName="Containers_Forward-Pick_"
      />
    </div>
  );
};

export default ContainerList;
