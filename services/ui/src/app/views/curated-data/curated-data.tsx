import { useState } from "react";
import { ViewBar } from "../../components/view-bar/view-bar";
import { CuratedDataTablePicker } from "./components/curated-data-table-picker/curated-data-table-picker";
import { CuratedDataTable } from "./components/curated-data-table/curated-data-table";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";
import { BaseViewProps } from "../view-registry.types";

export function CuratedData({
  title = "Curated Data",
}: Pick<BaseViewProps, "title">) {
  const [tableId, setTableId] = useState<string>("bronze_bin_utilization");

  return (
    <div style={{ flex: 1, width: "100%" }} data-testid="curated-data-table">
      <ViewBar title={title} showDatePeriodRange={false}>
        <CuratedDataTablePicker onTableChange={setTableId} />
      </ViewBar>
      <FullPageContainer>
        <CuratedDataTable tableId={tableId} />
      </FullPageContainer>
    </div>
  );
}

export default CuratedData;
