import { Select, SelectItem } from "@carbon/react";
import { useState } from "react";
import { ViewBar } from "../../components/view-bar/view-bar";
import { DatePeriod } from "../../types";
import { DailyPerformanceReportTable } from "./components/daily-performance-report-table/daily-performance-report-table";
import { WorkstationFilter } from "./components/workstation-filter/workstation-filter";
import { useTranslation } from "react-i18next";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";
import { BaseViewProps } from "../view-registry.types";

export function DailyPerformanceReport({
  title = "Daily Performance Report",
}: Pick<BaseViewProps, "title">) {
  const [workstations, setWorkstations] = useState<string[]>([]);
  const [datePeriod, setDatePeriod] = useState<DatePeriod>(
    DatePeriod.last7days,
  );

  const { t } = useTranslation();

  return (
    <div style={{ flex: 1, width: "100%" }}>
      <ViewBar title={title} showDatePeriodRange={false}>
        <Select
          id="date-period-select"
          labelText="Date Period"
          hideLabel
          value={datePeriod}
          onChange={(e) => setDatePeriod(e.target.value as DatePeriod)}
          size="md"
        >
          <SelectItem
            value={DatePeriod.last7days}
            text={t("last7Days", "Last 7 days")}
          />
          <SelectItem
            value={DatePeriod.last30days}
            text={t("last30Days", "Last 30 days")}
          />
          <SelectItem
            value={DatePeriod.last60days}
            text={t("last60Days", "Last 60 days")}
          />
        </Select>

        <WorkstationFilter onChange={setWorkstations} />
      </ViewBar>
      <FullPageContainer>
        <DailyPerformanceReportTable
          workstations={workstations}
          datePeriod={datePeriod}
        />
      </FullPageContainer>
    </div>
  );
}

export default DailyPerformanceReport;
