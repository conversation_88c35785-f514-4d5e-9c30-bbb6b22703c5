import { Add } from "@carbon/icons-react";
import { But<PERSON> } from "@carbon/react";
import type { components } from "@ict/sdk/openapi-react-query";
import { useEffect, useState } from "react";
import { useOutletContext } from "react-router";
import { useRoles } from "../../auth/hooks/use-roles";
import { ViewBar } from "../../components/view-bar/view-bar";
import { useViewOptions } from "../../hooks/use-view-options";
import { DatePeriod, type DatePeriodRange } from "../../types";
import type { BaseViewProps } from "../view-registry.types";
import { AddWidgetDialog } from "./components/add-widget-dialog";
import { DashboardSettings } from "./components/dashboard-settings";
import classes from "./dashboard.module.css";
import { useDashboardWidgets } from "./hooks/use-dashboard-widgets";
import {
  DashboardGridLayout,
  type DashboardWidget,
} from "./react-grid-layout/react-grid-layout";
import type { WidgetFilters } from "../../widgets/widget.types";
import { useTranslation } from "react-i18next";
type AppConfigSetting = components["schemas"]["AppConfigSetting"];

interface AutoRefreshDefaults {
  autoRefreshEnabled: boolean;
  autoRefreshInterval: number;
}

const getAutoRefreshDefaultsFromConfig = (
  configValue: unknown,
): AutoRefreshDefaults => {
  if (configValue && typeof configValue === "object" && configValue !== null) {
    const config = configValue as Record<string, unknown>;
    return {
      autoRefreshEnabled: (config.autoRefreshEnabled as boolean) ?? false,
      autoRefreshInterval: (config.autoRefreshInterval as number) ?? 60,
    };
  }

  return {
    autoRefreshEnabled: false,
    autoRefreshInterval: 60,
  };
};

type DashboardProps = BaseViewProps & {
  id: string;
  setting: AppConfigSetting;
};

export type DashboardOptions = {
  title: string;
  content: DashboardWidget[];
  defaultDatePeriodRange: DatePeriodRange;
  showDateRange: boolean;
  availableDateRanges?: DatePeriod[];
  autoRefreshEnabled?: boolean;
  autoRefreshInterval?: number;
};

export function Dashboard({ id, setting, title }: DashboardProps) {
  const { t } = useTranslation();
  const { hasConfiguratorAccess } = useRoles();
  const handleShowViewAside =
    useOutletContext<(content: React.ReactNode) => void>();

  const autoRefreshDefaults = getAutoRefreshDefaultsFromConfig(setting?.value);

  // Extract initial values from setting or use defaults
  const settingValue = setting?.value as DashboardOptions;
  const initialOptions: DashboardOptions = settingValue
    ? {
        ...settingValue,
        autoRefreshEnabled:
          settingValue.autoRefreshEnabled ??
          autoRefreshDefaults.autoRefreshEnabled,
        autoRefreshInterval:
          settingValue.autoRefreshInterval ??
          autoRefreshDefaults.autoRefreshInterval,
      }
    : {
        title: "Dashboard",
        content: [],
        defaultDatePeriodRange: DatePeriod.today,
        showDateRange: true,
        availableDateRanges: undefined,
        autoRefreshEnabled: autoRefreshDefaults.autoRefreshEnabled,
        autoRefreshInterval: autoRefreshDefaults.autoRefreshInterval,
      };

  const initialSettings = setting || {
    name: id,
    dataType: "json",
    value: initialOptions,
  };

  const {
    draftOptions,
    handleShowSettings,
    handleShowHistory,
    handleOptionsChange,
    handleSaveOptions,
    isDirty,
  } = useViewOptions<DashboardOptions>({
    setting: initialSettings,
    defaultOptions: initialOptions,
    optionsComponent: DashboardSettings,
    successMessage: t(
      "dashboard.settingSavedSuccessMessage",
      "Your dashboard has been saved",
    ),
    errorMessage: t(
      "dashboard.settingSavedErrorMessage",
      "Your dashboard was not saved successfully",
    ),
  });

  // Initialize with the default date period range from draftOptions
  const [selectedDatePeriodRange, setDatePeriodRange] =
    useState<DatePeriodRange>(
      draftOptions?.defaultDatePeriodRange || DatePeriod.today,
    );

  // Update selectedDatePeriodRange when defaultDatePeriodRange changes in draftOptions
  useEffect(() => {
    if (draftOptions?.defaultDatePeriodRange) {
      setDatePeriodRange(draftOptions.defaultDatePeriodRange);
    }
  }, [draftOptions?.defaultDatePeriodRange]);

  useEffect(() => {
    // Close the settings aside when the dashboard is unmounted
    return () => {
      if (handleShowViewAside) {
        handleShowViewAside(null);
      }
    };
  }, [handleShowViewAside]);

  const { handleUpdateDashboard, handleAddWidget, handleDeleteWidget } =
    useDashboardWidgets({
      draftOptions,
      handleOptionsChange,
      selectedDatePeriodRange,
    });

  // Validation: Check if date range picker is enabled but no available ranges are selected
  const isInvalidDateRangeConfig =
    draftOptions?.showDateRange &&
    (!draftOptions.availableDateRanges ||
      draftOptions.availableDateRanges.length === 0);

  // Only enable save when there are changes AND the configuration is valid
  const canSave = isDirty && !isInvalidDateRangeConfig;

  const handleDatePeriodRangeChange = (range: DatePeriodRange) => {
    setDatePeriodRange(range);
  };

  const handleAddWidgetDialog = () => {
    handleShowViewAside(
      <AddWidgetDialog
        onAddWidget={handleAddWidget}
        onClose={() => handleShowViewAside(null)}
      />,
    );
  };

  // Early return if no content
  if (!draftOptions) {
    return null;
  }

  return (
    <div
      data-testid={id}
      style={{ flex: 1, overflowX: "hidden" }}
      className={classes.container}
    >
      <div className={classes.viewBarContainer}>
        {" "}
        <ViewBar
          title={title ?? draftOptions.title}
          hasConfiguratorAccess={hasConfiguratorAccess}
          showDatePeriodRange={draftOptions.showDateRange}
          selectedDatePeriodRange={selectedDatePeriodRange}
          onDatePeriodRangeChange={handleDatePeriodRangeChange}
          availableDateRanges={draftOptions.availableDateRanges}
          showSettings={true}
          saveEnabled={canSave}
          showSave={hasConfiguratorAccess}
          showViewHistory={true}
          onSettingsClick={handleShowSettings}
          onSaveClick={handleSaveOptions}
          onViewHistoryClick={handleShowHistory}
        >
          {hasConfiguratorAccess && (
            <Button
              renderIcon={Add}
              size="sm"
              kind="secondary"
              onClick={handleAddWidgetDialog}
            >
              {t("dashboard.addWidget", "Add Widget")}
            </Button>
          )}
        </ViewBar>
      </div>
      <div className={classes.dashboardContainer}>
        <DashboardGridLayout
          key={`react-grid-${id}`}
          items={draftOptions.content}
          filters={
            {
              datePeriodRange: selectedDatePeriodRange,
              autoRefresh: {
                enabled: draftOptions.autoRefreshEnabled || false,
                interval: draftOptions.autoRefreshInterval || 60,
              },
            } as WidgetFilters
          }
          onShowAside={handleShowViewAside}
          onUpdateDashboard={handleUpdateDashboard}
          onDeleteWidget={handleDeleteWidget}
          canEdit={hasConfiguratorAccess}
        />
      </div>
    </div>
  );
}

export default Dashboard;
