import { <PERSON><PERSON>, ProgressBar, Search } from "@carbon/react";
import { ViewBar } from "../../components/view-bar/view-bar";
import styles from "./data-explorer.module.css";
import { Bookmark, Time } from "@carbon/icons-react";
import { useState } from "react";
import { ictApi } from "../../api/ict-api";
import { components } from "@ict/sdk/openapi-react-query";
import SearchResultsView from "./components/search-results-view";
import RecentSearchesView from "./components/recent-searches-view";
import RecommendedQuestions from "./components/recommended-questions";
import BookmarksView from "./components/bookmarks-view";
import AcknowledgementDialog from "./components/acknowledgement-dialog";
import { BaseViewProps } from "../view-registry.types";

enum DataExplorerViewState {
  BOOKMARKS_VIEW = "BookmarksView",
  SEARCH_RESULT_VIEW = "SearchResultView",
  RECENT_SEARCHES_VIEW = "RecentSearchesView",
}

export default function DataExplorer({
  title = "Data Explorer",
}: Pick<BaseViewProps, "title">) {
  const [query, setQuery] = useState("");
  const [viewState, setViewState] = useState<DataExplorerViewState>(
    DataExplorerViewState.RECENT_SEARCHES_VIEW,
  );
  const [savedSearchId, setSavedSearchId] = useState("");
  const [searchIsFetching, setSearchIsFetching] = useState(false);

  const cancelSearchQuery = () => {
    ictApi.queryClient.cancelQueries({
      queryKey: ["get", "/data-explorer/search"],
      fetchStatus: "fetching",
    });
    setSearchIsFetching(false);
  };

  const search = async (searchText: string) => {
    setSearchIsFetching(true);
    try {
      const data = await ictApi.queryClient.fetchQuery(
        ictApi.client.queryOptions("get", "/data-explorer/search", {
          params: {
            query: {
              searchText,
            },
          },
        }),
      );
      setSavedSearchId(data.id);
      handleViewSavedSearch(data);
    } catch (error) {
      console.error(error);
    }
    setSearchIsFetching(false);
  };

  const handleSearch = async (searchText: string) => {
    cancelSearchQuery();
    setSavedSearchId("");
    setQuery(searchText);
    search(searchText);
  };

  const handleSearchClear = () => {
    cancelSearchQuery();
    setQuery("");
    setViewState(DataExplorerViewState.RECENT_SEARCHES_VIEW);
  };

  const handleGoToRecentSearchesClick = () => {
    cancelSearchQuery();
    setViewState(DataExplorerViewState.RECENT_SEARCHES_VIEW);
  };

  const handleGoToBookmarkedSearchesView = () => {
    cancelSearchQuery();
    setViewState(DataExplorerViewState.BOOKMARKS_VIEW);
  };

  const handleViewSavedSearch = async (
    savedSearch: components["schemas"]["DataExplorerResult"],
  ) => {
    cancelSearchQuery();
    const detailQueryKey = [
      "get",
      "/data-explorer/results/{resultId}",
      {
        params: {
          path: { resultId: savedSearch.id },
        },
      },
    ];
    ictApi.queryClient.setQueryData(detailQueryKey, savedSearch);
    setQuery(savedSearch.prompt);
    setSavedSearchId(savedSearch.id);
    setViewState(DataExplorerViewState.SEARCH_RESULT_VIEW);
  };

  const renderResults = () => {
    switch (viewState) {
      case DataExplorerViewState.RECENT_SEARCHES_VIEW:
        return (
          <RecentSearchesView onViewRecentSearch={handleViewSavedSearch} />
        );
      case DataExplorerViewState.SEARCH_RESULT_VIEW:
        return <SearchResultsView resultId={savedSearchId} />;
      case DataExplorerViewState.BOOKMARKS_VIEW:
        return <BookmarksView onViewBookmarkedSearch={handleViewSavedSearch} />;
      default:
        return (
          <RecentSearchesView onViewRecentSearch={handleViewSavedSearch} />
        );
    }
  };

  return (
    <div data-testid="data-explorer">
      <AcknowledgementDialog />
      <ViewBar title={title} />
      <div className={styles.progressBarContainer}>
        {searchIsFetching && <ProgressBar label="Loading" hideLabel />}
      </div>
      <div data-testid="search-container" className={styles.searchContainer}>
        <div className={styles.searchInput}>
          <Search
            data-testid="search-input"
            placeholder="Ask me questions about warehouse operations"
            labelText="Search"
            value={query}
            onChange={(e) => {
              setQuery(e.target.value);
              if (e.target.value === "") {
                handleSearchClear();
              }
            }}
            onKeyDown={(e) => e.key === "Enter" && handleSearch(query)}
            onClear={handleSearchClear}
          />
          <div className={styles.searchControls}>
            <Button
              data-testid="button-search"
              onClick={() => handleSearch(query)}
            >
              Search
            </Button>
            <Button
              data-testid="button-bookmark"
              hasIconOnly
              renderIcon={Bookmark}
              iconDescription="Bookmarks"
              kind="ghost"
              onClick={handleGoToBookmarkedSearchesView}
            />
            <Button
              data-testid="button-recent-search"
              hasIconOnly
              renderIcon={Time}
              iconDescription="Recent Searches"
              kind="ghost"
              onClick={handleGoToRecentSearchesClick}
            />
          </div>
        </div>
        <RecommendedQuestions onQuestionClicked={handleSearch} />
      </div>
      <div className={styles.resultsContainer}>{renderResults()}</div>
    </div>
  );
}
