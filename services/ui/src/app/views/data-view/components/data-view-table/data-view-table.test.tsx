import { vi } from "vitest";
import { render, screen } from "../../../../../test-utils";
import { DataViewTable } from "./data-view-table";

// Keep the test simple: mock only what we must control
vi.mock("../../../../config/hooks/use-config", () => {
  return {
    useConfig: vi.fn(() => ({ data: [], isLoading: false, error: null })),
    updateConfigSetting: vi.fn(),
  };
});

vi.mock("../../../../auth/hooks/use-roles", () => {
  return {
    useRoles: vi.fn(() => ({ hasConfiguratorAccess: true })),
  };
});

vi.mock("react-router", () => ({
  useNavigate: vi.fn(() => vi.fn()),
}));

// Keep complex components lightweight for this unit test
vi.mock("../../../../components/view-bar/view-bar", () => ({
  ViewBar: ({
    title,
    children,
  }: {
    title: string;
    children: React.ReactNode;
  }) => (
    <div data-testid="view-bar">
      <h1>{title}</h1>
      <div>{children}</div>
    </div>
  ),
}));

vi.mock(
  "../../../../components/full-page-container/full-page-container",
  () => ({
    FullPageContainer: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="full-page-container">{children}</div>
    ),
  }),
);

vi.mock("../../../../components/datagrid", () => ({
  Datagrid: ({
    columns,
    data,
    isLoading,
  }: {
    columns: unknown;
    data: unknown;
    isLoading: boolean;
  }) => (
    <div data-testid="datagrid">
      <div data-testid="columns">{JSON.stringify(columns)}</div>
      <div data-testid="data">{JSON.stringify(data)}</div>
      <div data-testid="loading">{String(isLoading)}</div>
    </div>
  ),
}));

// Avoid rendering the Carbon Modal in unit tests here
vi.mock("../new-data-query-modal/new-data-query-modal", () => ({
  NewDataQueryModal: () => null,
}));

// Ensure Carbon GlobalTheme doesn't interfere
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

import { useConfig } from "../../../../config/hooks/use-config";
import { useRoles } from "../../../../auth/hooks/use-roles";

describe("DataViewTable", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders the title", () => {
    render(<DataViewTable />);
    expect(screen.getByTestId("view-bar")).toBeInTheDocument();
    expect(screen.getByText("Data Configuration")).toBeInTheDocument();
  });

  it("shows add button when user has configurator access", () => {
    // Default mocked useRoles returns hasConfiguratorAccess: true
    render(<DataViewTable />);
    expect(screen.getByText("New Data Query")).toBeInTheDocument();
  });

  it("hides add button without configurator access", () => {
    (useRoles as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      hasConfiguratorAccess: false,
    });
    render(<DataViewTable />);
    expect(screen.queryByText("New Data Query")).not.toBeInTheDocument();
  });

  it("passes loading state to datagrid", () => {
    (useConfig as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      data: [],
      isLoading: true,
      error: null,
    });
    render(<DataViewTable />);
    expect(screen.getByTestId("datagrid")).toBeInTheDocument();
    expect(screen.getByTestId("loading")).toHaveTextContent("true");
  });

  it("renders error notification when there is an error", () => {
    (useConfig as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      data: [],
      isLoading: false,
      error: new Error("Test error"),
    });
    render(<DataViewTable />);
    expect(
      screen.getByText(/Error loading data config settings:/),
    ).toBeInTheDocument();
  });
});
