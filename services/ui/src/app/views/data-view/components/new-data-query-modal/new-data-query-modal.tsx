import { Modal, ModalBody, Select, SelectItem, TextInput } from "@carbon/react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { DATA_QUERY_TYPES } from "../../types/data-query-types";
import classes from "./new-data-query-modal.module.css";

export interface NewDataQueryModalProps {
  open: boolean;
  onClose: () => void;
  onCreate: (name: string, type: string) => void;
  isLoading?: boolean;
}

/**
 * Modal component for creating new data queries
 */
export const NewDataQueryModal = ({
  open,
  onClose,
  onCreate,
  isLoading = false,
}: NewDataQueryModalProps) => {
  const { t } = useTranslation();
  const [name, setName] = useState("");
  const [type, setType] = useState("data-point");

  const handleCreate = () => {
    if (name.trim()) {
      onCreate(name.trim(), type);
    }
  };

  const handleClose = () => {
    setName("");
    setType("data-point");
    onClose();
  };

  const isValid = name.trim().length > 0;

  return (
    <Modal
      open={open}
      onRequestClose={handleClose}
      modalHeading="New Data Query"
      primaryButtonText={isLoading ? "Creating..." : "Create"}
      secondaryButtonText="Cancel"
      onRequestSubmit={handleCreate}
      primaryButtonDisabled={!isValid || isLoading}
      size="sm"
    >
      <ModalBody>
        <div className={classes.formContainer}>
          <TextInput
            id="data-query-name"
            labelText="Name"
            placeholder="Enter data query name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            invalid={name.length > 0 && name.trim().length === 0}
          />

          <Select
            id="data-query-type"
            labelText="Type"
            value={type}
            onChange={(e) => setType(e.target.value)}
          >
            {DATA_QUERY_TYPES.map((queryType) => (
              <SelectItem
                key={queryType.value}
                value={queryType.value}
                text={t(queryType.label)}
              />
            ))}
          </Select>

          {type !== "data-point" && type !== "filter" && (
            <div>
              <p>
                Note: {DATA_QUERY_TYPES.find((qt) => qt.value === type)?.label}{" "}
                queries are not yet implemented. Only Data Point and Filter
                queries are currently supported.
              </p>
            </div>
          )}
        </div>
      </ModalBody>
    </Modal>
  );
};

export default NewDataQueryModal;
