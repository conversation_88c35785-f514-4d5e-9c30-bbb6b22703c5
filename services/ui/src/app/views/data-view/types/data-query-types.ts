import { components } from "../../../../../../../libs/shared/ict-sdk/generated/openapi-react-query";

export type DataQuery = components["schemas"]["DataQuery"];
export type DataQueryResult = components["schemas"]["DataQueryResult"];
export type DataQueryType = components["schemas"]["DataQueryType"];

export const DATA_QUERY_TYPES: Array<{ value: DataQueryType; label: string }> =
  [
    { value: "singleValue", label: "Data Point" },
    { value: "categorySeries", label: "Trend" },
    { value: "filter", label: "Filter" },
    { value: "tabular", label: "Grid" },
  ] as const;
