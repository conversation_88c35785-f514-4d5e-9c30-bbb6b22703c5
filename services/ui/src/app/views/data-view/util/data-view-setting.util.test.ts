import { AppConfigSettingSource } from "@ict/sdk/types";
import {
  createNewDataSetting,
  getNavigationPath,
  isDataQueryTypeSupported,
} from "./data-view-setting.util";
import { AppConfigSetting } from "../../../config/menu/types";

describe("data-view-setting.util", () => {
  describe("createNewDataSetting", () => {
    it("should create a data-point setting", () => {
      const result = createNewDataSetting("Test Query", "data-point") as any;

      expect(result.name).toBe("Test Query");
      expect(result.description).toBe("Test Query data point query");
      expect(result.group).toBe("data-point");
      expect(result.dataType).toBe("json");
      expect(result.source).toBe(AppConfigSettingSource.default);
      expect(result.value).toBeDefined();
      expect(result.value?.type).toBe("data-point");
      expect(result.value?.isDraft).toBe(true);
      expect(result.value?.config).toEqual([]);
      expect(result.value?.metadata?.unit).toBe("");
    });

    it("should create a filter setting", () => {
      const result = createNewDataSetting("Test Filter", "filter") as any;

      expect(result.name).toBe("Test Filter");
      expect(result.description).toBe("Test Filter filter query");
      expect(result.group).toBe("data-filter");
      expect(result.value).toBeDefined();
      expect(result.value?.type).toBe("filter");
      expect(result.value?.isDraft).toBe(true);
      expect(result.value?.config).toBeUndefined();
      expect(result.value?.metadata?.unit).toBeUndefined();
    });

    it("should include correct query template for data-point", () => {
      const result = createNewDataSetting(
        "Test",
        "data-point",
      ) as AppConfigSetting;

      expect(result.value).toBeDefined();
    });

    it("should include correct query template for filter", () => {
      const result = createNewDataSetting("Test", "filter");

      expect(result.value).toBeDefined();
    });
  });

  describe("getNavigationPath", () => {
    it("should return correct path for data-point", () => {
      const result = getNavigationPath("data-point", "test-id");

      expect(result).toBe("data-point/test-id");
    });

    it("should return correct path for filter", () => {
      const result = getNavigationPath("filter", "test-id");

      expect(result).toBe("filter/test-id");
    });
  });

  describe("isDataQueryTypeSupported", () => {
    it("should return true for data-point", () => {
      expect(isDataQueryTypeSupported("data-point")).toBe(true);
    });

    it("should return true for filter", () => {
      expect(isDataQueryTypeSupported("filter")).toBe(true);
    });

    it("should return false for unsupported types", () => {
      expect(isDataQueryTypeSupported("trend")).toBe(false);
      expect(isDataQueryTypeSupported("grid")).toBe(false);
      expect(isDataQueryTypeSupported("invalid")).toBe(false);
      expect(isDataQueryTypeSupported("")).toBe(false);
    });
  });
});
