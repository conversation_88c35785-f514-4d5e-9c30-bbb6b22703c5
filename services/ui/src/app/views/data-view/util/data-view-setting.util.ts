import type { AppConfigSetting } from "@ict/sdk/types";
import { AppConfigSettingSource } from "@ict/sdk/types";

export type DataQueryType = "data-point" | "filter";

/**
 * Creates a new data setting configuration for data-point or filter queries
 */
export const createNewDataSetting = (
  name: string,
  type: DataQueryType,
): Partial<AppConfigSetting> => {
  const isFilter = type === "filter";

  return {
    name: name,
    description: `${name} ${isFilter ? "filter" : "data point"} query`,
    group: isFilter ? "data-filter" : "data-point",
    dataType: "json",
    source: AppConfigSettingSource.default,
    value: {
      id: name,
      label: name,
      type: isFilter ? "filter" : "data-point",
      description: `${name} ${isFilter ? "filter" : "data point"} query`,
      filters: [],
      isDraft: true,
      metadata: {
        ...(isFilter ? {} : { unit: "" }), // No unit for filters
        category: "",
      },
      dataSources: [
        {
          id: "edp-bigquery",
          type: "edp-bigquery",
          table: "",
        },
      ],
      // No config for filters
      ...(isFilter ? {} : { config: [] }),
      parameters: {
        required: ["start_date", "end_date"],
      },
      query: isFilter
        ? "-- Enter your SQL query here to return multiple values for a filter dropdown\n-- Example: SELECT DISTINCT category FROM your_table WHERE condition\n"
        : "-- Enter your SQL query here\n",
    },
  };
};

/**
 * Gets the navigation path for a data query type
 */
export const getNavigationPath = (type: DataQueryType, id: string): string => {
  const isFilter = type === "filter";
  return `${isFilter ? "filter" : "data-point"}/${id}`;
};

/**
 * Validates if a data query type is supported
 */
export const isDataQueryTypeSupported = (
  type: string,
): type is DataQueryType => {
  return type === "data-point" || type === "filter";
};
