import { createColumnHelper } from "@tanstack/react-table";
import { Button } from "@carbon/react";
import { MetricConfigFacts } from "./types";
import { CheckmarkOutline, CloseOutline } from "@carbon/icons-react";

type FactColumnHelper = ReturnType<
  typeof createColumnHelper<MetricConfigFacts>
>;

export const getFactDatagridColumns = (
  columnHelper: FactColumnHelper | undefined,
  handleDelete: (itemName: string) => void,
  handleView: (itemName: string) => void,
  // TODO: Replace this any
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): any[] => {
  if (!columnHelper) return [];

  return [
    columnHelper.accessor("factType", {
      header: "Fact Name",
      id: "factType",
      size: 200,
    }),
    columnHelper.accessor("totalConfigs", {
      header: "Total Configs",
      id: "totalConfigs",
      size: 120,
    }),
    columnHelper.accessor("enabledConfigs", {
      header: "Enabled Configs",
      id: "enabledConfigs",
      size: 140,
    }),
    columnHelper.accessor("active", {
      header: "Active",
      id: "active",
      size: 100,
      cell: ({ getValue }) => {
        const active = getValue();
        return active ? (
          <CheckmarkOutline color="green" />
        ) : (
          <CloseOutline color="red" />
        );
      },
    }),
    columnHelper.accessor("factType", {
      header: "",
      id: "deleteButton",
      size: 50,
      cell: ({ getValue }) => {
        const factType = getValue();
        if (!factType) return <div>--</div>;
        return (
          <div>
            <Button
              className="gridButton"
              kind="tertiary"
              size="sm"
              onClick={() => handleDelete(factType)}
            >
              Delete
            </Button>
          </div>
        );
      },
    }),
    columnHelper.accessor("factType", {
      header: "",
      id: "viewButton",
      size: 50,
      cell: ({ getValue }) => {
        const factType = getValue();
        if (!factType) return <div>--</div>;
        return (
          <Button
            className="gridButton"
            kind="tertiary"
            size="sm"
            onClick={() => handleView(factType)}
          >
            View
          </Button>
        );
      },
    }),
  ];
};
