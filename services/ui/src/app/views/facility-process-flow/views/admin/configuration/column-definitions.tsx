import { createColumnHelper } from "@tanstack/react-table";
import { Button } from "@carbon/react";
import { FactConfigurationData } from "./types";

type FactColumnHelper = ReturnType<
  typeof createColumnHelper<FactConfigurationData>
>;

export const getFactDatagridColumns = (
  columnHelper: FactColumnHelper | undefined,
  handleDelete: (itemName: string) => void,
  handleView: (itemName: string) => void,
  // TODO: Replace this any
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): any[] => {
  if (!columnHelper) return [];

  return [
    columnHelper.accessor("fact_name", {
      header: "Fact Name",
      id: "fact_name",
      size: 200,
    }),
    columnHelper.accessor("enabled", {
      header: "Enabled",
      size: 100,
    }),
    columnHelper.accessor("active", {
      header: "Active",
      size: 100,
    }),
    columnHelper.accessor("num_configurations", {
      header: "Configurations",
      size: 100,
    }),
    columnHelper.accessor("fact_name", {
      header: "",
      id: "deleteButton",
      size: 50,
      cell: ({ getValue }) => {
        const itemName = getValue();
        if (!itemName) return <div>--</div>;
        return (
          <div>
            <Button
              className="gridButton"
              kind="tertiary"
              size="sm"
              onClick={() => handleDelete(itemName)}
            >
              Delete
            </Button>
          </div>
        );
      },
    }),
    columnHelper.accessor("fact_name", {
      header: "",
      id: "viewButton",
      size: 50,
      cell: ({ getValue }) => {
        const itemName = getValue();
        if (!itemName) return <div>--</div>;
        return (
          <Button
            className="gridButton"
            kind="tertiary"
            size="sm"
            onClick={() => handleView(itemName)}
          >
            View
          </Button>
        );
      },
    }),
  ];
};
