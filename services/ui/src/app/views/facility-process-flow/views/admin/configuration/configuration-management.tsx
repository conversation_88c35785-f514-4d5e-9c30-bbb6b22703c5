import { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Column,
  ComposedModal,
  Grid,
  ModalBody,
  ModalHeader,
  <PERSON><PERSON>,
  <PERSON>b,
  TabList,
  TabPanels,
  TabPanel,
} from "@carbon/react";
import { AdminModals } from "../admin-controls";
import { Datagrid } from "../../../../../components/datagrid";
import { mockFactData } from "./mock-configuration-management-data";
import { FactDetail } from "./fact-detail";
import {
  ConfigBreadcrumb,
  ConfigBreadcrumbs,
} from "./configuration-breadcrumbs";
import { ConfigurationData } from "./types";
import { MetricConfigsList } from "./metric-configs-list";
import { MetricConfigFactsList } from "./metric-config-facts-list";
import { createPortal } from "react-dom";
import styles from "./configuration-management.module.scss";

interface ConfigurationManagementProps {
  isModalOpen: boolean;
  setActiveModal: Dispatch<SetStateAction<AdminModals>>;
}

export enum ConfigItemTypes {
  Fact = "fact",
  Metric = "metric",
  Node = "node",
}

export interface SelectedItem {
  type: ConfigItemTypes;
  name: string;
}

function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

const getItemTypeFromIndex = (index: number): ConfigItemTypes | undefined => {
  const orderedConfigItemTypes: ConfigItemTypes[] = [
    ConfigItemTypes.Fact,
    ConfigItemTypes.Metric,
    ConfigItemTypes.Node,
  ];
  return orderedConfigItemTypes[index];
};

export function ConfigurationManagement({
  isModalOpen,
  setActiveModal,
}: ConfigurationManagementProps) {
  const [itemType, setItemType] = useState<ConfigItemTypes>(
    ConfigItemTypes.Fact,
  );
  const [selectedItem, setSelectedItem] = useState<SelectedItem | null>(null);
  const [datagridData, setDatagridData] = useState<ConfigurationData[]>(
    mockFactData.data,
  );
  const [breadcrumbs, setBreadcrumbs] = useState<ConfigBreadcrumb[]>([]);
  // TODO: Remove this any and work out the typescript errors.
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [columns, setColumns] = useState<any[]>([]); // ← new state for columns

  // Fact handlers moved to MetricConfigFactsList component
  const handleViewFactButton = (factType: string): void => {
    setSelectedItem({
      type: ConfigItemTypes.Fact,
      name: factType,
    });
  };

  useEffect(() => {
    // Initialize with mock data for legacy tabs (Nodes)
    setDatagridData(mockFactData.data);
  }, []);

  useEffect(() => {
    if (selectedItem) {
      setBreadcrumbs([
        {
          label: `${capitalize(selectedItem.type)} Configuration`,
          isCurrentPage: false,
          onClick: () => {
            setSelectedItem(null);
          },
        },
        {
          label: selectedItem.name,
          isCurrentPage: true,
          onClick: () => {},
        },
      ]);
    } else {
      setBreadcrumbs([
        {
          label: `${capitalize(itemType)} Configuration`,
          isCurrentPage: true,
          onClick: () => {},
        },
      ]);
    }
  }, [selectedItem, itemType]);

  // TODO: should this useEffect should set columns and datagridData?

  const handleTabChange = (selectedTabIndex: number) => {
    const selectedItemType = getItemTypeFromIndex(selectedTabIndex);
    if (!selectedItemType) return;

    setItemType(selectedItemType);
    setSelectedItem(null); // Clear selection when tab changes

    // Facts and Metrics tabs use their own components now
    // Only Nodes tab still uses the legacy Datagrid with mock data
    if (selectedItemType === ConfigItemTypes.Node) {
      setDatagridData(mockFactData.data);
    } else {
      setColumns([]);
      setDatagridData([]);
    }
  };

  const isError = false;
  const isLoading = false;

  const renderDetailView = () => {
    if (!selectedItem) return null;

    switch (selectedItem.type) {
      case ConfigItemTypes.Fact:
        return (
          <FactDetail
            name={selectedItem.name}
            setBreadcrumbs={setBreadcrumbs}
            setSelectedItem={setSelectedItem}
          />
        );
      case ConfigItemTypes.Node:
        return <div>Node Detail View - Not implemented.</div>;
      default:
        return null;
    }
  };

  // Get the tab index from the item type
  const getTabIndexFromItemType = (type: ConfigItemTypes): number => {
    const orderedConfigItemTypes: ConfigItemTypes[] = [
      ConfigItemTypes.Fact,
      ConfigItemTypes.Metric,
      ConfigItemTypes.Node,
    ];
    return orderedConfigItemTypes.indexOf(type);
  };

  const modalContent = (
    <ComposedModal
      open={isModalOpen}
      onClose={() => setActiveModal(AdminModals.None)}
      preventCloseOnClickOutside={true}
      size="lg"
    >
      <ModalHeader
        title="Configuration Management"
        closeModal={() => setActiveModal(AdminModals.None)}
      />
      <ModalBody>
        <div className={styles.configManagementModal}>
          <Grid>
            <Column lg={16} md={8} sm={4}>
              <ConfigBreadcrumbs breadcrumbs={breadcrumbs} />
              <div className={styles.configManagementMainPanel}>
                {selectedItem ? (
                  renderDetailView()
                ) : (
                  <>
                    <Tabs
                      onChange={({ selectedIndex }) =>
                        handleTabChange(selectedIndex)
                      }
                      selectedIndex={getTabIndexFromItemType(itemType)}
                    >
                      <TabList contained>
                        <Tab id="fact">Facts</Tab>
                        <Tab id="metric">Metrics</Tab>
                        <Tab id="node">Nodes</Tab>
                      </TabList>

                      <TabPanels>
                        <TabPanel>
                          <MetricConfigFactsList onViewFact={handleViewFactButton} />
                        </TabPanel>
                        <TabPanel>
                          <MetricConfigsList />
                        </TabPanel>
                        <TabPanel>
                          <Datagrid
                            columns={columns}
                            data={datagridData ?? []}
                            mode="client"
                            enableSelection={false}
                            initialPagination={{ pageIndex: 0, pageSize: 100 }}
                            initialDensity="compact"
                            isLoading={isLoading}
                          />
                        </TabPanel>
                      </TabPanels>
                    </Tabs>
                    {isError && <div>Failed to load data</div>}
                  </>
                )}
              </div>
            </Column>
          </Grid>
        </div>
      </ModalBody>
    </ComposedModal>
  );

  return isModalOpen ? createPortal(modalContent, document.body) : <></>;
}
