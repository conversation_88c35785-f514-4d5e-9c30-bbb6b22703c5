import { Datagrid } from "../../../../../components/datagrid";
import { createColumnHelper } from "@tanstack/react-table";
import { Button } from "@carbon/react";
import { CheckmarkOutline, CloseOutline } from "@carbon/icons-react";
import { useQuery } from "@tanstack/react-query";
import { authService } from "../../../../../auth/auth-service";
import { getApiOverrideUrl } from "../../../../../api/util/api-override-util";
import { MetricConfigFacts } from "./types";

export function MetricConfigFactsList() {
  // Use direct fetch since the endpoint isn't in the OpenAPI schema yet
  // Apply API override mechanism to respect localhost/mock settings
  const {
    data: metricConfigFacts,
    isLoading: isLoadingFacts,
    error: factsError,
  } = useQuery({
    queryKey: ["metric-config-facts"],
    queryFn: async (): Promise<MetricConfigFacts[]> => {
      const baseUrl = import.meta.env.VITE_API_BASE_URL;
      const accessToken = await authService.getAccessToken();

      // Apply API override to respect debug settings (localhost/mock)
      const originalUrl = `${baseUrl}/config/process-flow/metric-configs/facts`;
      const finalUrl = getApiOverrideUrl(originalUrl);

      const response = await fetch(finalUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
          "ict-facility-id": "superior_uniform#eudoraar", // TODO: Get this from context
        },
      });

      if (!response.ok) {
        throw new Error(
          `Failed to fetch metric config facts: ${response.statusText}`,
        );
      }

      const data = await response.json();

      // API should already return the correct camelCase format
      return data as MetricConfigFacts[];
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1,
    retryDelay: (attemptIndex: number) =>
      Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });

  const handleDeleteFactButton = (factType: string): void => {
    if (!confirm(`Are you sure you want to delete ${factType}?`)) return;
    console.log("Delete fact config not implemented...", factType);
    // TODO: Perform a soft-delete on metric config. Use the API to update the isDeleted column for metric config.
  };

  const handleViewFactButton = (factType: string): void => {
    console.log("View fact config not implemented...", factType);
    // TODO: Navigate to fact detail view
  };

  const columnHelper = createColumnHelper<MetricConfigFacts>();
  const factDatagridColumns = [
    columnHelper.accessor("factType", {
      header: "Fact Name",
      id: "factType",
      size: 200,
    }),
    columnHelper.accessor("totalConfigs", {
      header: "Total Configs",
      id: "totalConfigs",
      size: 120,
    }),
    columnHelper.accessor("enabledConfigs", {
      header: "Enabled Configs",
      id: "enabledConfigs",
      size: 140,
    }),
    columnHelper.accessor("active", {
      header: "Active",
      id: "active",
      size: 100,
      cell: ({ getValue }) => {
        const active = getValue();
        return active ? (
          <CheckmarkOutline color="green" />
        ) : (
          <CloseOutline color="red" />
        );
      },
    }),
    columnHelper.accessor("factType", {
      header: "",
      id: "deleteButton",
      size: 50,
      cell: ({ row }) => {
        const factType = row.original.factType;
        if (!factType) return <div>--</div>;
        return (
          <div>
            <Button
              className="gridButton"
              kind="tertiary"
              size="sm"
              onClick={() => handleDeleteFactButton(factType)}
            >
              Delete
            </Button>
          </div>
        );
      },
    }),
    columnHelper.accessor("factType", {
      header: "",
      id: "viewButton",
      size: 50,
      cell: ({ row }) => {
        const factType = row.original.factType;
        if (!factType) return <div>--</div>;
        return (
          <Button
            className="gridButton"
            kind="tertiary"
            size="sm"
            onClick={() => handleViewFactButton(factType)}
          >
            View
          </Button>
        );
      },
    }),
  ];

  if (factsError) {
    return <div>Error loading metric config facts: {factsError.message}</div>;
  }

  return (
    <Datagrid
      columns={factDatagridColumns}
      data={metricConfigFacts || []}
      mode="client"
      initialPagination={{ pageIndex: 0, pageSize: 15 }}
      enableSelection={false}
      initialDensity="compact"
      isLoading={isLoadingFacts}
      error={factsError ?? undefined}
    />
  );
}
