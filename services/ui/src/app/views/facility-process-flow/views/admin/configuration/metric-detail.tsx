import React, { useEffect, useState } from "react";
import {
  Form,
  TextInput,
  Dropdown,
  Button,
  InlineNotification,
  TextArea,
  MultiSelect,
  ComboBox,
  IconButton,
} from "@carbon/react";
import { mockMetricConfigDetailData } from "./mock-configuration-management-data";
import {
  configTypeOptions,
  factNameOptions,
  graphOperationOptions,
  labelOptions,
  MetricConfiguration,
  redisOperationOptions,
  viewOptions,
} from "./types";
import { ChevronLeft } from "@carbon/react/icons";

interface MetricDetailProps {
  metricName: string;
  onClose: () => void;
}

export const MetricDetail = (props: MetricDetailProps) => {
  const { metricName, onClose } = props;
  const [error, setError] = useState<string>("");
  const [success, setSuccess] = useState<string>("");
  const [metricConfig, setMetricConfig] = useState<MetricConfiguration | null>(
    null,
  );
  const [matchConditionsJson, setMatchConditionsJson] = useState<string>("");
  const [configType, setConfigType] = useState<string>("node");

  useEffect(() => {
    // TODO: Fetch metric data from API and populate form fields
    const data = mockMetricConfigDetailData;
    setMetricConfig(data);

    // Initialize match conditions as a string for the text area
    if (data.match_conditions) {
      setMatchConditionsJson(JSON.stringify(data.match_conditions, null, 2));
    }
  }, [metricName]); // Only run when metricName changes

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError("");
    setSuccess("");

    if (!metricConfig) return;

    if (!validateMetricConfig(metricConfig)) return;

    // TODO: Update this with correct api info when endpoint is created.
    // try {
    //   // TODO: Replace with actual API call
    //   const response = await fetch(`/api/metrics/${metricName}`, {
    //     method: "PUT",
    //     headers: { "Content-Type": "application/json" },
    //     body: JSON.stringify(metricConfig),
    //   });
    //   if (!response.ok)
    //     throw new Error("Failed to update metric configuration");
    //   setSuccess("Metric configuration updated successfully!");
    // } catch (err: unknown) {
    //   if (err instanceof Error) {
    //     setError(err.message);
    //   } else {
    //     setError("An unexpected error occurred.");
    //   }
    // }
    console.log(">>> Update metric config not implemented");
    console.log("POST BODY:", metricConfig);
    setSuccess("Metric configuration updated successfully!");
  };

  const validateMetricConfig = (metricConfig: MetricConfiguration): boolean => {
    // Validate required fields
    if (!metricConfig.fact_name) {
      setError("Fact name is required.");
      return false;
    }
    if (!metricConfig.config_type) {
      setError("Config type is required.");
      return false;
    }
    if (!metricConfig.views || metricConfig.views.length === 0) {
      setError("At least one view is required.");
      return false;
    }
    if (!metricConfig.graph_operation) {
      setError("Graph operation is required.");
      return false;
    }
    if (!metricConfig.redis_operation) {
      setError("Redis operation is required.");
      return false;
    }

    // Validate required fields for each config type
    if (configType === "node") {
      if (!metricConfig.node_name && !metricConfig.name_formula) {
        setError("Node name or name forumula is required.");
        return false;
      }
    } else if (configType === "inbound_edge") {
      if (!metricConfig.inbound_area) {
        setError("Inbound area is required.");
        return false;
      }
    } else if (configType === "outbound_edge") {
      if (!metricConfig.outbound_area) {
        setError("Outbound area is required.");
        return false;
      }
    } else if (configType === "complete_edge") {
      if (!metricConfig.inbound_area) {
        setError("Inbound area is required.");
        return false;
      }
      if (!metricConfig.outbound_area) {
        setError("Outbound area is required.");
        return false;
      }
    }

    // Parse match conditions from JSON
    try {
      const matchConditions = JSON.parse(matchConditionsJson);
      if (typeof matchConditions !== "object") {
        setError("Match conditions must be a valid JSON object.");
        return false;
      }
      metricConfig.match_conditions = matchConditions;
    } catch (err: unknown) {
      setError(
        `Invalid JSON in match conditions: ${err instanceof Error ? err.message : "Unknown error"}`,
      );
      return false;
    }
    return true;
  };

  const updateField = (
    field: keyof MetricConfiguration,
    value: string | string[] | { [key: string]: string } | null,
  ) => {
    if (!metricConfig) return;
    setMetricConfig({
      ...metricConfig,
      [field]: value,
    });
    if (field === "config_type") {
      setConfigType(value as string);
    }
  };

  if (!metricConfig) {
    return <div>Loading...</div>;
  }

  return (
    <>
      <div style={{ display: "flex", marginBottom: "2rem" }}>
        <IconButton
          label="Close"
          onClick={onClose}
          data-testid="close-button"
          size="sm"
        >
          <ChevronLeft />
        </IconButton>
        <h3 style={{ margin: "auto" }}>{metricName}</h3>
      </div>
      <Form onSubmit={handleSubmit}>
        <TextInput
          id="metric_name"
          labelText="Metric Name"
          placeholder="multishuttle_total_storage_movements"
          value={metricConfig.metric_name || ""}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            updateField("metric_name", e.target.value)
          }
        />

        <ComboBox
          allowCustomValue
          id="fact_name"
          titleText="Fact"
          items={factNameOptions}
          itemToString={(item) => (item ? item.text : "")}
          initialSelectedItem={factNameOptions.find(
            (item) => item.id === metricConfig.fact_name,
          )}
          onChange={(val) => {
            if (val?.inputValue) {
              updateField("fact_name", val.inputValue);
            } else {
              updateField("fact_name", val?.selectedItem?.id ?? "");
            }
          }}
        />

        <Dropdown
          id="config_type"
          titleText="Config Type"
          label="Select a config type"
          items={configTypeOptions}
          selectedItem={configTypeOptions.find(
            (item) => item.id === metricConfig.config_type,
          )}
          itemToString={(item) => (item ? item.text : "")}
          onChange={({ selectedItem }) =>
            updateField("config_type", selectedItem?.id ?? "")
          }
        />

        <TextInput
          id="node_name"
          labelText="Node Name"
          placeholder="multishuttle"
          value={metricConfig.node_name || ""}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            updateField("node_name", e.target.value)
          }
        />
        <TextInput
          id="name_formula"
          labelText="Node Name Formula"
          placeholder="e.g., Lift {lift_id}"
          value={metricConfig.name_formula || ""}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            updateField("name_formula", e.target.value)
          }
        />

        <TextArea
          id="match_conditions"
          labelText="Match Conditions (JSON)"
          placeholder='{"event_type": "^lift_move$"}'
          value={matchConditionsJson}
          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
            setMatchConditionsJson(e.target.value)
          }
        />

        {/* Edge Fields */}
        {(configType === "inbound_edge" || configType === "complete_edge") && (
          <>
            <TextInput
              id="inbound_node_label"
              labelText="Inbound Node Label"
              placeholder="Defaults to Area"
              value={metricConfig.inbound_node_label || ""}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                updateField("inbound_node_label", e.target.value)
              }
            />
            <TextInput
              id="inbound_area"
              labelText="Inbound Area"
              value={metricConfig.inbound_area || ""}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                updateField("inbound_area", e.target.value)
              }
            />
          </>
        )}
        {(configType === "outbound_edge" || configType === "complete_edge") && (
          <>
            <TextInput
              id="outbound_area"
              labelText="Outbound Area"
              value={metricConfig.outbound_area || ""}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                updateField("outbound_area", e.target.value)
              }
            />

            <TextInput
              id="outbound_node_label"
              labelText="Outbound Node Label"
              placeholder="Defaults to Area"
              value={metricConfig.outbound_node_label || ""}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                updateField("outbound_node_label", e.target.value)
              }
            />
          </>
        )}
        {configType.includes("edge") && (
          <TextInput
            id="hu_id"
            labelText="Handling Unit ID"
            placeholder="e.g., lift_id"
            value={metricConfig.hu_id || ""}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              updateField("hu_id", e.target.value)
            }
          />
        )}
        <MultiSelect
          id="views"
          titleText="Views"
          label="Select views"
          items={viewOptions}
          itemToString={(item) => (item ? item.text : "")}
          onChange={({ selectedItems }) =>
            updateField("views", selectedItems?.map((item) => item.id) ?? [])
          }
        />

        <TextInput
          id="metric_units"
          labelText="Metric Units"
          placeholder="e.g., /hr"
          value={metricConfig.metric_units || ""}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            updateField("metric_units", e.target.value)
          }
        />

        <Dropdown
          id="label"
          titleText="Label"
          label="Select a label"
          items={labelOptions}
          itemToString={(item) => (item ? item.text : "")}
          selectedItem={labelOptions.find(
            (item) => item.id === metricConfig.label,
          )}
          onChange={({ selectedItem }) =>
            updateField("label", selectedItem?.id ?? "")
          }
        />

        <Dropdown
          id="graph_operation"
          titleText="Graph Operation"
          label="Select a graph operation"
          items={graphOperationOptions}
          itemToString={(item) => (item ? item.text : "")}
          selectedItem={graphOperationOptions.find(
            (item) => item.id === metricConfig.graph_operation,
          )}
          onChange={({ selectedItem }) =>
            updateField("graph_operation", selectedItem?.id ?? "")
          }
        />

        <Dropdown
          id="redis_operation"
          titleText="Redis Operation"
          label="Select a redis operation"
          items={redisOperationOptions}
          itemToString={(item) => (item ? item.text : "")}
          selectedItem={redisOperationOptions.find(
            (item) => item.id === metricConfig.redis_operation,
          )}
          onChange={({ selectedItem }) =>
            updateField("redis_operation", selectedItem?.id ?? "")
          }
        />

        <Button type="submit" kind="primary">
          Save Changes
        </Button>

        {error && (
          <InlineNotification
            kind="error"
            title="Error"
            subtitle={error}
            onCloseButtonClick={() => setError("")}
          />
        )}
        {success && (
          <InlineNotification
            kind="success"
            title="Success"
            subtitle={success}
            onCloseButtonClick={() => setSuccess("")}
          />
        )}
      </Form>
    </>
  );
};
