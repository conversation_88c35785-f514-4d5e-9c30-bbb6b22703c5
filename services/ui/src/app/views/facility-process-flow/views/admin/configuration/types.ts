import type { components } from "@ict/sdk/openapi-react-query";

export type MetricConfigSummary = components["schemas"]["MetricConfigSummary"];

// These are all temporarily here for mockup
// Will eventually be replaced by the types defined in API

export type MetricConfigFacts = {
  factType: string;
  totalConfigs: number;
  enabledConfigs: number;
  active: boolean;
};

export type FactConfigurationData = {
  active: boolean;
  enabled: boolean;
  fact_name: string;
  num_configurations: number;
};

export interface MetricConfiguration {
  active: boolean;
  config_type: string;
  enabled: boolean;
  fact_name: string;
  graph_operation: string;
  hu_id: string;
  inbound_area?: string;
  inbound_node_label?: string;
  inbound_parent_nodes?: string[];
  label: string;
  match_conditions: { [key: string]: string };
  metric_name: string;
  metric_units: string;
  name_formula: string;
  node_name: string;
  outbound_area?: string;
  outbound_node_label?: string;
  outbound_parent_nodes?: string[];
  parent_nodes: string[];
  redis_operation: string;
  views: string[];
}

export type ConfigurationData = FactConfigurationData | MetricConfigSummary;

// Config type options
export const configTypeOptions = [
  { id: "node", text: "Node" },
  { id: "inbound_edge", text: "Inbound Edge" },
  { id: "outbound_edge", text: "Outbound Edge" },
  { id: "complete_edge", text: "Complete Edge" },
];

// Label options
export const labelOptions = [
  { id: "Aisle", text: "Aisle" },
  { id: "AisleLevel", text: "Aisle Level" },
  { id: "Area", text: "Area" },
  { id: "Lift", text: "Lift" },
  { id: "Shuttle", text: "Shuttle" },
  { id: "Station", text: "Station" },
];

export const factNameOptions = [
  { id: "bin_utilization", text: "Bin Utilization" },
  { id: "connection_movement", text: "Connection Movement" },
  { id: "fault_event", text: "Fault Event" },
  { id: "multishuttle_movement", text: "Multishuttle Movement" },
  { id: "pick", text: "Pick" },
  { id: "pick_activity", text: "Pick Activity" },
  { id: "vehicle_movement", text: "Vehicle Movement" },
];

// Graph operation options
export const graphOperationOptions = [
  { id: "area_node", text: "Area Node" },
  { id: "area_edge", text: "Edge" },
  { id: "shuttle_node", text: "Shuttle Node" },
];

// Views options
export const viewOptions = [
  { id: "facility", text: "Facility" },
  { id: "receiving", text: "Receiving" },
  { id: "multishuttle", text: "Multishuttle" },
  { id: "workstations", text: "Workstations" },
  { id: "miniload", text: "Miniload" },
  { id: "packing", text: "Packing" },
  { id: "shipping", text: "Shipping" },
];

// Redis operation options
export const redisOperationOptions = [
  { id: "cycle_time_start", text: "Cycle Time Start" },
  { id: "cycle_time_stop", text: "Cycle Time Stop" },
  { id: "incr", text: "Increment" },
  { id: "decr", text: "Decrement" },
  { id: "distinct_item_count", text: "Distinct Item Count" },
  { id: "distinct_item_subtract", text: "Distinct Item Subtract" },
  { id: "event_set", text: "Event Set" },
  { id: "complex_event_set", text: "Complex Event Set" },
  { id: "fault_start", text: "Fault Start" },
  { id: "fault_end", text: "Fault End" },
  { id: "set_last_processed_time", text: "Set Last Processed Time" },
  {
    id: "storage_location_distribution_available",
    text: "Storage Location Distribution Available",
  },
  {
    id: "storage_location_distribution_occupied",
    text: "Storage Location Distribution Occupied",
  },
  { id: "storage_utilization", text: "Storage Utilization" },
  { id: "store_handling_unit_for_edge", text: "Store Handling Unit For Edge" },
  {
    id: "total_storage_locations_occupied",
    text: "Total Storage Locations Occupied",
  },
];
