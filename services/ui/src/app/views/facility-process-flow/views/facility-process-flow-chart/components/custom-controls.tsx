import React from "react";
import { useReactFlow } from "@reactflow/core";
import { ControlButton, Controls, useViewport } from "reactflow";
import {
  FaLock,
  FaLock<PERSON>pen,
  FaSearchPlus,
  FaSearchMinus,
} from "react-icons/fa";

interface CustomControlsProps {
  handleInteractiveChange: () => void;
  isAdmin: boolean;
  isInteractive: boolean;
  onFitView: () => void;
  handleZoomIn?: () => void;
  handleZoomOut?: () => void;
}

const CustomControls = (props: CustomControlsProps) => {
  const { handleInteractiveChange, isAdmin, isInteractive, onFitView } = props;

  const { zoomIn, zoomOut, getViewport } = useReactFlow();

  const handleZoomIn = () => {
    zoomIn();
    setTimeout(storeZoomLevel, 500); // Adding a delay to ensure the zoom level is stored after the zoom action
  };
  const handleZoomOut = () => {
    zoomOut();
    setTimeout(storeZoomLevel, 500); // Adding a delay to ensure the zoom level is stored after the zoom action
  };

  const storeZoomLevel = () => {
    const zoom_level = getViewport();
    sessionStorage.setItem(
      "PROCESS_FLOW_ZOOM_LEVEL",
      JSON.stringify(zoom_level),
    );
  };

  // React flow hook
  const displayZoom = (useViewport().zoom * 100).toFixed(0);

  return (
    <>
      <Controls
        onInteractiveChange={handleInteractiveChange}
        showInteractive={false}
        showZoom={false}
        showFitView={true}
        onFitView={onFitView}
      >
        <div className="customControlsContainer">
          {isAdmin && (
            <ControlButton
              className="interactiveModeToggle"
              onClick={handleInteractiveChange}
              title={"Interactive Mode"}
            >
              {isInteractive ? <FaLockOpen /> : <FaLock />}
            </ControlButton>
          )}
          <ControlButton onClick={handleZoomIn} title={"Zoom In"}>
            <FaSearchPlus />
          </ControlButton>
          <ControlButton onClick={handleZoomOut} title={"Zoom Out"}>
            <FaSearchMinus />
          </ControlButton>
          <div className="zoomDisplay">Zoom: {displayZoom}%</div>
        </div>
      </Controls>
    </>
  );
};

export default CustomControls;
