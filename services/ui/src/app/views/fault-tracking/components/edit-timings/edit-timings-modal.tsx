import { Modal } from "@carbon/react";
import { useTranslation } from "react-i18next";
import { useConfigSetting } from "../../../../config/hooks/use-config";
import {
  getMinStartDate,
  useEditTimingsForm,
} from "../../hooks/edit-timings/use-edit-timings-form";
import { useUpdateAlarmTimings } from "../../hooks/edit-timings/use-update-alarm-timings";
import { EditTimingsForm } from "./edit-timings-form";
import type { FaultAlarm } from "../../types/types";

export function EditTimingsModal({
  isEditTimingsModalOpen,
  toggleEditTimingsModal,
  selectedAlarm,
  onSuccess,
}: {
  isEditTimingsModalOpen: boolean;
  toggleEditTimingsModal: () => void;
  selectedAlarm?: FaultAlarm | null;
  onSuccess?: () => void;
}) {
  const { t } = useTranslation();
  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");
  const facilityTimezone =
    (timezoneConfig?.value as string) ?? "America/New_York";

  const { updateAlarmTimings, isSubmitting } = useUpdateAlarmTimings(() => {
    // Reset form and close modal
    reset();
    toggleEditTimingsModal();

    // Trigger data refresh to update the table
    if (onSuccess) {
      onSuccess();
    }
  });

  const {
    formData,
    originalData,
    reset,
    resetToOriginal,
    validate,
    setErrors,
    isValid,
    setField,
    setFieldTouched,
    calculateDuration,
    touched,
    resetTrigger,
    errors,
    hasChanges,
  } = useEditTimingsForm(
    selectedAlarm,
    isEditTimingsModalOpen,
    facilityTimezone,
  );
  const minStartDate = getMinStartDate();

  const handleSubmit = async () => {
    const validationErrors = validate();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    if (!selectedAlarm) {
      return;
    }

    setErrors({});
    await updateAlarmTimings(formData, selectedAlarm);
  };

  const handleCancel = () => {
    setErrors({});
    toggleEditTimingsModal();
  };

  return (
    <Modal
      modalHeading={t("faultTracking.editAlarmTimings", "Edit Alarm Timings")}
      open={isEditTimingsModalOpen}
      onRequestClose={handleCancel}
      primaryButtonText={t("faultTracking.save", "Save")}
      secondaryButtonText={t("faultTracking.cancel", "Cancel")}
      onRequestSubmit={handleSubmit}
      onSecondarySubmit={handleCancel}
      passiveModal={false}
      primaryButtonDisabled={isSubmitting || !isValid || !hasChanges}
    >
      <p style={{ marginBottom: "1.5rem" }}>
        {t(
          "faultTracking.editTimingsSubtitle",
          "Adjust the date range, start and end time, the duration will update automatically.",
        )}
      </p>
      {selectedAlarm ? (
        <EditTimingsForm
          formData={formData}
          originalData={originalData}
          setField={setField}
          resetToOriginal={resetToOriginal}
          errors={errors}
          minStartDate={minStartDate}
          validate={validate}
          setErrors={setErrors}
          touched={touched}
          setFieldTouched={setFieldTouched}
          calculateDuration={calculateDuration}
          resetTrigger={resetTrigger}
        />
      ) : (
        <p>{t("faultTracking.noAlarmSelected", "No alarm selected")}</p>
      )}
    </Modal>
  );
}
