import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import { OriginalValuesDisplay } from "./original-values-display";
import type {
  OriginalTimingData,
  EditTimingsForm,
} from "../../hooks/edit-timings/use-edit-timings-form";

// Mock react-i18next
const mockT = vi.fn((_key: string, defaultValue: string) => defaultValue);
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}));

// Mock Carbon components
vi.mock("@carbon/react", () => ({
  Button: ({ children, onClick, renderIcon: Icon, ...props }: any) => (
    <button onClick={onClick} data-testid="reset-button" {...props}>
      {Icon && <Icon data-testid="reset-icon" />}
      {children}
    </button>
  ),
  Stack: ({ children, orientation, ...props }: any) => (
    <div data-testid={`stack-${orientation}`} {...props}>
      {children}
    </div>
  ),
}));

// Mock Carbon icons
vi.mock("@carbon/icons-react", () => ({
  Reset: (props: any) => <svg data-testid="reset-icon" {...props} />,
}));

describe("OriginalValuesDisplay", () => {
  const mockOnResetToOriginal = vi.fn();

  const baseOriginalData: OriginalTimingData = {
    startDate: new Date("2024-02-15T00:00:00.000Z"),
    endDate: new Date("2024-02-15T00:00:00.000Z"),
    startTime: "10:30",
    startPeriod: "AM" as const,
    endTime: "11:45",
    endPeriod: "AM" as const,
    startSeconds: "15:250",
    endSeconds: "30:500",
  };

  const baseFormData: EditTimingsForm = {
    startDate: new Date("2024-02-15T00:00:00.000Z"),
    endDate: new Date("2024-02-15T00:00:00.000Z"),
    startTime: "10:30",
    startPeriod: "AM" as const,
    endTime: "11:45",
    endPeriod: "AM" as const,
    startSeconds: "15:250",
    endSeconds: "30:500",
    comments: "Test comments",
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("rendering", () => {
    it("should render nothing when originalData is null", () => {
      const { container } = render(
        <OriginalValuesDisplay
          originalData={null}
          currentFormData={baseFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      expect(container.firstChild).toBeNull();
    });

    it("should render original values display when originalData exists and there are differences", () => {
      const differentFormData = {
        ...baseFormData,
        startTime: "09:00", // Different from original 10:30
      };

      render(
        <OriginalValuesDisplay
          originalData={baseOriginalData}
          currentFormData={differentFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      expect(screen.getByText("Initial Value")).toBeInTheDocument();
      expect(screen.getByText(/Start Time:/)).toBeInTheDocument();
      expect(screen.getByText(/End Time:/)).toBeInTheDocument();
      expect(screen.getByTestId("reset-button")).toBeInTheDocument();
    });

    it("should not render when there are no differences between original and current data", () => {
      const { container } = render(
        <OriginalValuesDisplay
          originalData={baseOriginalData}
          currentFormData={baseFormData} // Identical data
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      expect(container.firstChild).toBeNull();
    });

    it("should format date and time correctly", () => {
      const differentFormData = {
        ...baseFormData,
        startTime: "09:00", // Different from original 10:30
      };

      render(
        <OriginalValuesDisplay
          originalData={baseOriginalData}
          currentFormData={differentFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      // Check that the formatted date/time appears in the content
      const startTimeText = screen.getByText(/Start Time:/).textContent;
      const endTimeText = screen.getByText(/End Time:/).textContent;

      expect(startTimeText).toContain("10:30:15 AM");
      expect(endTimeText).toContain("11:45:30 AM");
    });

    it("should handle missing time data gracefully", () => {
      const incompleteOriginalData: OriginalTimingData = {
        startDate: null,
        endDate: null,
        startTime: "",
        startPeriod: "AM" as const,
        endTime: "",
        endPeriod: "AM" as const,
        startSeconds: "",
        endSeconds: "",
      };

      render(
        <OriginalValuesDisplay
          originalData={incompleteOriginalData}
          currentFormData={baseFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      const startTimeText = screen.getByText(/Start Time:/).textContent;
      const endTimeText = screen.getByText(/End Time:/).textContent;

      expect(startTimeText).toContain("N/A");
      expect(endTimeText).toContain("N/A");
    });

    it("should format time without seconds when seconds are not provided", () => {
      const originalDataWithoutSeconds: OriginalTimingData = {
        ...baseOriginalData,
        startSeconds: "",
        endSeconds: "",
      };

      render(
        <OriginalValuesDisplay
          originalData={originalDataWithoutSeconds}
          currentFormData={baseFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      const startTimeText = screen.getByText(/Start Time:/).textContent;
      const endTimeText = screen.getByText(/End Time:/).textContent;

      expect(startTimeText).toContain("10:30 AM");
      expect(endTimeText).toContain("11:45 AM");
      // Should not have seconds appended (no additional colon for seconds)
      expect(startTimeText).not.toContain("10:30:");
      expect(endTimeText).not.toContain("11:45:");
    });
  });

  describe("difference detection", () => {
    it("should show display when there are differences in dates", () => {
      const differentFormData: EditTimingsForm = {
        ...baseFormData,
        startDate: new Date("2024-02-16T00:00:00.000Z"), // Different date
      };

      render(
        <OriginalValuesDisplay
          originalData={baseOriginalData}
          currentFormData={differentFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      expect(screen.getByText("Initial Value")).toBeInTheDocument();
    });

    it("should show display when there are differences in times", () => {
      const differentFormData: EditTimingsForm = {
        ...baseFormData,
        startTime: "09:00", // Different time
      };

      render(
        <OriginalValuesDisplay
          originalData={baseOriginalData}
          currentFormData={differentFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      expect(screen.getByText("Initial Value")).toBeInTheDocument();
    });

    it("should show display when there are differences in periods", () => {
      const differentFormData: EditTimingsForm = {
        ...baseFormData,
        startPeriod: "PM", // Different period
      };

      render(
        <OriginalValuesDisplay
          originalData={baseOriginalData}
          currentFormData={differentFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      expect(screen.getByText("Initial Value")).toBeInTheDocument();
    });

    it("should show display when there are differences in seconds", () => {
      const differentFormData: EditTimingsForm = {
        ...baseFormData,
        startSeconds: "20:300", // Different seconds
      };

      render(
        <OriginalValuesDisplay
          originalData={baseOriginalData}
          currentFormData={differentFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      expect(screen.getByText("Initial Value")).toBeInTheDocument();
    });

    it("should handle null dates in difference detection", () => {
      const originalDataWithNullDates: OriginalTimingData = {
        ...baseOriginalData,
        startDate: null,
        endDate: null,
      };

      const formDataWithNullDates: EditTimingsForm = {
        ...baseFormData,
        startDate: null,
        endDate: null,
        startTime: "09:00", // Add a difference to make it render
      };

      render(
        <OriginalValuesDisplay
          originalData={originalDataWithNullDates}
          currentFormData={formDataWithNullDates}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      // Should render since there is a time difference
      expect(screen.getByText("Initial Value")).toBeInTheDocument();
    });
  });

  describe("reset functionality", () => {
    it("should call onResetToOriginal when reset button is clicked", async () => {
      const user = userEvent.setup();
      const differentFormData = {
        ...baseFormData,
        startTime: "09:00", // Different from original 10:30
      };

      render(
        <OriginalValuesDisplay
          originalData={baseOriginalData}
          currentFormData={differentFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      const resetButton = screen.getByTestId("reset-button");
      await user.click(resetButton);

      expect(mockOnResetToOriginal).toHaveBeenCalledTimes(1);
    });

    it("should render reset button with correct text and icon", () => {
      const differentFormData = {
        ...baseFormData,
        startTime: "09:00", // Different from original 10:30
      };

      render(
        <OriginalValuesDisplay
          originalData={baseOriginalData}
          currentFormData={differentFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      const resetButton = screen.getByTestId("reset-button");
      const resetIcon = screen.getByTestId("reset-icon");

      expect(resetButton).toHaveTextContent("Restore to Initial Value");
      expect(resetIcon).toBeInTheDocument();
    });
  });

  describe("accessibility", () => {
    it("should have proper button role for reset functionality", () => {
      const differentFormData = {
        ...baseFormData,
        startTime: "09:00", // Different from original 10:30
      };

      render(
        <OriginalValuesDisplay
          originalData={baseOriginalData}
          currentFormData={differentFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      const resetButton = screen.getByRole("button");
      expect(resetButton).toBeInTheDocument();
      expect(resetButton).toHaveTextContent("Restore to Initial Value");
    });

    it("should have descriptive text for timing information", () => {
      const differentFormData = {
        ...baseFormData,
        startTime: "09:00", // Different from original 10:30
      };

      render(
        <OriginalValuesDisplay
          originalData={baseOriginalData}
          currentFormData={differentFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      expect(screen.getByText("Initial Value")).toBeInTheDocument();
      expect(screen.getByText(/Start Time:/)).toBeInTheDocument();
      expect(screen.getByText(/End Time:/)).toBeInTheDocument();
    });
  });

  describe("translations", () => {
    it("should call translation function with correct keys", () => {
      const differentFormData = {
        ...baseFormData,
        startTime: "09:00", // Different from original 10:30
      };

      render(
        <OriginalValuesDisplay
          originalData={baseOriginalData}
          currentFormData={differentFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      expect(mockT).toHaveBeenCalledWith(
        "faultTracking.initialValue",
        "Initial Value",
      );
      expect(mockT).toHaveBeenCalledWith(
        "faultTracking.startTime",
        "Start Time",
      );
      expect(mockT).toHaveBeenCalledWith("faultTracking.endTime", "End Time");
      expect(mockT).toHaveBeenCalledWith(
        "faultTracking.restoreToInitialValue",
        "Restore to Initial Value",
      );
    });

    it("should call translation for N/A when data is missing", () => {
      const incompleteOriginalData: OriginalTimingData = {
        startDate: null,
        endDate: null,
        startTime: "",
        startPeriod: "AM" as const,
        endTime: "",
        endPeriod: "AM" as const,
        startSeconds: "",
        endSeconds: "",
      };

      render(
        <OriginalValuesDisplay
          originalData={incompleteOriginalData}
          currentFormData={baseFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      expect(mockT).toHaveBeenCalledWith("faultTracking.notAvailable", "N/A");
    });
  });

  describe("edge cases", () => {
    it("should handle PM times correctly", () => {
      const pmOriginalData: OriginalTimingData = {
        ...baseOriginalData,
        startTime: "02:30",
        startPeriod: "PM",
        endTime: "03:45",
        endPeriod: "PM",
      };

      render(
        <OriginalValuesDisplay
          originalData={pmOriginalData}
          currentFormData={baseFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      const startTimeText = screen.getByText(/Start Time:/).textContent;
      const endTimeText = screen.getByText(/End Time:/).textContent;

      expect(startTimeText).toContain("2:30:15 PM");
      expect(endTimeText).toContain("3:45:30 PM");
    });

    it("should handle complex seconds format correctly", () => {
      const complexSecondsData: OriginalTimingData = {
        ...baseOriginalData,
        startSeconds: "05:123",
        endSeconds: "59:999",
      };

      render(
        <OriginalValuesDisplay
          originalData={complexSecondsData}
          currentFormData={baseFormData}
          onResetToOriginal={mockOnResetToOriginal}
        />,
      );

      const startTimeText = screen.getByText(/Start Time:/).textContent;
      const endTimeText = screen.getByText(/End Time:/).textContent;

      expect(startTimeText).toContain("10:30:05 AM");
      expect(endTimeText).toContain("11:45:59 AM");
    });
  });
});
