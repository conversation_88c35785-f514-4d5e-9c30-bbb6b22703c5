import { <PERSON><PERSON>, <PERSON><PERSON> } from "@carbon/react";
import { useTranslation } from "react-i18next";
import { Reset } from "@carbon/icons-react";
import type {
  OriginalTimingData,
  EditTimingsForm,
} from "../../hooks/edit-timings/use-edit-timings-form";
import { datesAreDifferent } from "../../utils/time-validation";
import styles from "./original-values-display.module.scss";

interface OriginalValuesDisplayProps {
  originalData: OriginalTimingData | null;
  currentFormData: EditTimingsForm;
  onResetToOriginal: () => void;
}

export function OriginalValuesDisplay({
  originalData,
  currentFormData,
  onResetToOriginal,
}: OriginalValuesDisplayProps) {
  const { t } = useTranslation();

  if (!originalData) return null;

  const hasDifferences =
    datesAreDifferent(originalData.startDate, currentFormData.startDate) ||
    datesAreDifferent(originalData.endDate, currentFormData.endDate) ||
    originalData.startTime !== currentFormData.startTime ||
    originalData.startPeriod !== currentFormData.startPeriod ||
    originalData.endTime !== currentFormData.endTime ||
    originalData.endPeriod !== currentFormData.endPeriod ||
    originalData.startSeconds !== currentFormData.startSeconds ||
    originalData.endSeconds !== currentFormData.endSeconds;

  if (!hasDifferences) return null;
  const formatOriginalDateTime = (
    date: Date | null,
    time: string,
    period: "AM" | "PM",
    seconds: string,
  ) => {
    if (!date || !time) return t("faultTracking.notAvailable", "N/A");

    const dateStr = date.toLocaleDateString();
    const timeWithSeconds = seconds ? `${time}:${seconds.split(":")[0]}` : time;
    return `${dateStr} ${timeWithSeconds} ${period}`;
  };

  const originalStartDateTime = formatOriginalDateTime(
    originalData.startDate,
    originalData.startTime,
    originalData.startPeriod,
    originalData.startSeconds,
  );

  const originalEndDateTime = formatOriginalDateTime(
    originalData.endDate,
    originalData.endTime,
    originalData.endPeriod,
    originalData.endSeconds,
  );

  return (
    <Stack
      orientation="horizontal"
      as="div"
      className={styles.originalValuesDisplayContainer}
    >
      <Stack orientation="vertical" gap="0.1rem">
        <div>{t("faultTracking.initialValue", "Initial Value")}</div>

        <div>
          {t("faultTracking.startTime", "Start Time")}: {originalStartDateTime}
        </div>
        <div>
          {t("faultTracking.endTime", "End Time")}: {originalEndDateTime}
        </div>
      </Stack>
      <Button
        kind="tertiary"
        renderIcon={Reset}
        onClick={onResetToOriginal}
        className={styles.resetButton}
      >
        {t("faultTracking.restoreToInitialValue", "Restore to Initial Value")}
      </Button>
    </Stack>
  );
}
