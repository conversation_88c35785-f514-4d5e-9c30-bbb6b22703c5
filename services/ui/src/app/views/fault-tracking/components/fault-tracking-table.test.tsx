import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import "@testing-library/jest-dom";
import { FaultTrackingTable } from "./fault-tracking-table";
import type { FaultAlarm } from "../types/types";
import type {
  ColumnDef,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";

// Mock the Datagrid component
vi.mock("../../../components/datagrid/datagrid", () => ({
  Datagrid: vi.fn(
    ({
      columns,
      data,
      mode,
      totalRows,
      isLoading,
      error,
      onPageChange,
      onSort,
      onFilter,
      enableSelection,
      showExportButton,
      onRowSelectionChange,
      showRefreshButton,
      onRefreshClick,
      batchActions,
    }) => (
      <div data-testid="fault-tracking-datagrid">
        <div data-testid="columns-count">{columns.length}</div>
        <div data-testid="data-count">{data.length}</div>
        <div data-testid="mode">{mode}</div>
        <div data-testid="total-rows">{totalRows}</div>
        <div data-testid="loading-state">
          {isLoading ? "loading" : "not-loading"}
        </div>
        <div data-testid="error-message">{error || "no-error"}</div>
        <div data-testid="enable-selection">
          {enableSelection ? "enabled" : "disabled"}
        </div>
        <div data-testid="show-export">
          {showExportButton ? "show" : "hide"}
        </div>
        <div data-testid="show-refresh">
          {showRefreshButton ? "show" : "hide"}
        </div>
        <div data-testid="batch-actions-count">{batchActions?.length || 0}</div>

        {/* Mock interactive elements for testing handlers */}
        <button
          data-testid="mock-page-change"
          onClick={() =>
            onPageChange && onPageChange({ pageIndex: 1, pageSize: 20 })
          }
        >
          Change Page
        </button>
        <button
          data-testid="mock-sort"
          onClick={() => onSort && onSort([{ id: "faultId", desc: false }])}
        >
          Sort
        </button>
        <button
          data-testid="mock-filter"
          onClick={() =>
            onFilter &&
            onFilter({ filters: { status: "Included" }, globalFilter: "test" })
          }
        >
          Filter
        </button>
        <button
          data-testid="mock-refresh"
          onClick={() => onRefreshClick && onRefreshClick()}
        >
          Refresh
        </button>
        <button
          data-testid="mock-row-selection"
          onClick={() =>
            onRowSelectionChange && onRowSelectionChange({ "0": true })
          }
        >
          Select Row
        </button>
      </div>
    ),
  ),
}));

describe("FaultTrackingTable", () => {
  const mockFaultData: FaultAlarm[] = [
    {
      faultId: "FAULT-001",
      title: "Test fault 1",
      description: "Test fault 1",
      tag: "TAG001",
      status: "Included",
      reason: "Equipment malfunction",
      id: "user123",
      comments: "Test comment",
      location: {
        area: "Area A",
        section: "Section 1",
        equipment: "Equipment 1",
      },
      timing: {
        startTime: "01/01/2024 10:00 AM",
        duration: "01:00:00",
        origStartTime: "01/01/2024 10:00 AM",
        origEndTime: "01/01/2024 11:00 AM",
        origDuration: "01:00:00",
        updatedStartTime: "01/01/2024 10:05 AM",
        updatedEndTime: "01/01/2024 11:05 AM",
        updatedDuration: "01:00:00",
      },
    },
    {
      faultId: "FAULT-002",
      title: "Test fault 2",
      description: "Test fault 2",
      tag: "TAG002",
      status: "Excluded",
      reason: "Manual override",
      id: "user456",
      comments: "Another test comment",
      location: {
        area: "Area B",
        section: "Section 2",
        equipment: "Equipment 2",
      },
      timing: {
        startTime: "01/01/2024 12:00 PM",
        duration: "01:00:00",
        origStartTime: "01/01/2024 12:00 PM",
        origEndTime: "01/01/2024 01:00 PM",
        origDuration: "01:00:00",
      },
    },
  ];

  const mockColumns: ColumnDef<FaultAlarm, string>[] = [
    {
      id: "faultId",
      header: "Alarm ID",
      accessorKey: "faultId",
    },
    {
      id: "description",
      header: "Description",
      accessorKey: "description",
    },
    {
      id: "status",
      header: "Status",
      accessorKey: "status",
    },
  ];

  const defaultProps = {
    tableKey: 1,
    columns: mockColumns,
    data: mockFaultData,
    rowCount: 25,
    isLoading: false,
    isFetching: false,
    error: undefined,
    pagination: { pageIndex: 0, pageSize: 10 } as PaginationState,
    setPagination: vi.fn(),
    sorting: [] as SortingState,
    setSorting: vi.fn(),
    setColumnFilters: vi.fn(),
    setGlobalFilter: vi.fn(),
    rowSelection: {},
    onRowSelectionChange: vi.fn(),
    onRefreshClick: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render the datagrid with correct props", () => {
    render(<FaultTrackingTable {...defaultProps} />);

    expect(screen.getByTestId("fault-tracking-datagrid")).toBeInTheDocument();
    expect(screen.getByTestId("columns-count")).toHaveTextContent("3");
    expect(screen.getByTestId("data-count")).toHaveTextContent("2");
    expect(screen.getByTestId("mode")).toHaveTextContent("server");
    expect(screen.getByTestId("total-rows")).toHaveTextContent("25");
  });

  it("should display loading state when isLoading is true", () => {
    render(<FaultTrackingTable {...defaultProps} isLoading={true} />);
    expect(screen.getByTestId("loading-state")).toHaveTextContent("loading");
  });

  it("should display loading state when isFetching is true", () => {
    render(<FaultTrackingTable {...defaultProps} isFetching={true} />);
    expect(screen.getByTestId("loading-state")).toHaveTextContent("loading");
  });

  it("should display not loading when both flags are false", () => {
    render(
      <FaultTrackingTable
        {...defaultProps}
        isLoading={false}
        isFetching={false}
      />,
    );
    expect(screen.getByTestId("loading-state")).toHaveTextContent(
      "not-loading",
    );
  });

  it("should display error message when error is provided", () => {
    const errorMessage = "Failed to load data";
    render(<FaultTrackingTable {...defaultProps} error={errorMessage} />);

    expect(screen.getByTestId("error-message")).toHaveTextContent(errorMessage);
  });

  it("should handle no error gracefully", () => {
    render(<FaultTrackingTable {...defaultProps} error={undefined} />);

    expect(screen.getByTestId("error-message")).toHaveTextContent("no-error");
  });

  it("should configure datagrid with correct boolean props", () => {
    render(<FaultTrackingTable {...defaultProps} />);

    expect(screen.getByTestId("enable-selection")).toHaveTextContent("enabled");
    expect(screen.getByTestId("show-export")).toHaveTextContent("hide");
    expect(screen.getByTestId("show-refresh")).toHaveTextContent("show");
  });

  it("should handle pagination changes", () => {
    const setPaginationMock = vi.fn();
    render(
      <FaultTrackingTable
        {...defaultProps}
        setPagination={setPaginationMock}
      />,
    );

    fireEvent.click(screen.getByTestId("mock-page-change"));

    expect(setPaginationMock).toHaveBeenCalledWith({
      pageIndex: 1,
      pageSize: 20,
    });
  });

  it("should handle sorting changes", () => {
    const setSortingMock = vi.fn();
    render(
      <FaultTrackingTable {...defaultProps} setSorting={setSortingMock} />,
    );

    fireEvent.click(screen.getByTestId("mock-sort"));

    expect(setSortingMock).toHaveBeenCalledWith([
      { id: "faultId", desc: false },
    ]);
  });

  it("should handle filtering changes", () => {
    const setColumnFiltersMock = vi.fn();
    const setGlobalFilterMock = vi.fn();
    render(
      <FaultTrackingTable
        {...defaultProps}
        setColumnFilters={setColumnFiltersMock}
        setGlobalFilter={setGlobalFilterMock}
      />,
    );

    fireEvent.click(screen.getByTestId("mock-filter"));

    expect(setColumnFiltersMock).toHaveBeenCalledWith([
      { id: "status", value: "Included" },
    ]);
    expect(setGlobalFilterMock).toHaveBeenCalledWith("test");
  });

  it("should handle refresh clicks", () => {
    const onRefreshClickMock = vi.fn();
    render(
      <FaultTrackingTable
        {...defaultProps}
        onRefreshClick={onRefreshClickMock}
      />,
    );

    fireEvent.click(screen.getByTestId("mock-refresh"));

    expect(onRefreshClickMock).toHaveBeenCalled();
  });

  it("should handle row selection changes", () => {
    const onRowSelectionChangeMock = vi.fn();
    render(
      <FaultTrackingTable
        {...defaultProps}
        onRowSelectionChange={onRowSelectionChangeMock}
      />,
    );

    fireEvent.click(screen.getByTestId("mock-row-selection"));

    expect(onRowSelectionChangeMock).toHaveBeenCalledWith({ "0": true });
  });

  it("should handle batch actions when provided", () => {
    const batchActions = [
      {
        label: "Update Status",
        onClick: vi.fn(),
        disabled: false,
        tooltip: "Update selected items",
      },
      {
        label: "Delete",
        onClick: vi.fn(),
        disabled: true,
      },
    ];

    render(
      <FaultTrackingTable {...defaultProps} batchActions={batchActions} />,
    );

    expect(screen.getByTestId("batch-actions-count")).toHaveTextContent("2");
  });

  it("should handle no batch actions", () => {
    render(<FaultTrackingTable {...defaultProps} batchActions={undefined} />);

    expect(screen.getByTestId("batch-actions-count")).toHaveTextContent("0");
  });

  it("should pass through tableKey for re-rendering", () => {
    const { rerender } = render(
      <FaultTrackingTable {...defaultProps} tableKey={1} />,
    );

    // The key should be passed to the Datagrid component
    expect(screen.getByTestId("fault-tracking-datagrid")).toBeInTheDocument();

    // Re-render with different key
    rerender(<FaultTrackingTable {...defaultProps} tableKey={2} />);

    expect(screen.getByTestId("fault-tracking-datagrid")).toBeInTheDocument();
  });

  it("should handle complex filter transformation", () => {
    const setColumnFiltersMock = vi.fn();
    const setGlobalFilterMock = vi.fn();

    render(
      <FaultTrackingTable
        {...defaultProps}
        setColumnFilters={setColumnFiltersMock}
        setGlobalFilter={setGlobalFilterMock}
      />,
    );

    // Use the mock filter button to test the transformation
    fireEvent.click(screen.getByTestId("mock-filter"));

    expect(setColumnFiltersMock).toHaveBeenCalledWith([
      { id: "status", value: "Included" },
    ]);
    expect(setGlobalFilterMock).toHaveBeenCalledWith("test");
  });

  it("should handle empty data gracefully", () => {
    render(<FaultTrackingTable {...defaultProps} data={[]} rowCount={0} />);

    expect(screen.getByTestId("data-count")).toHaveTextContent("0");
    expect(screen.getByTestId("total-rows")).toHaveTextContent("0");
  });

  it("should handle Error object types", () => {
    const errorObj = new Error("Network error");
    render(<FaultTrackingTable {...defaultProps} error={errorObj} />);
    expect(screen.getByTestId("error-message")).toHaveTextContent(
      "Error: Network error",
    );
  });

  it("should handle string error types", () => {
    render(
      <FaultTrackingTable {...defaultProps} error="Simple error message" />,
    );
    expect(screen.getByTestId("error-message")).toHaveTextContent(
      "Simple error message",
    );
  });

  it("should handle number error types", () => {
    render(<FaultTrackingTable {...defaultProps} error={404} />);
    expect(screen.getByTestId("error-message")).toHaveTextContent("404");
  });

  it("should maintain correct prop types and interfaces", () => {
    // This test verifies that our component accepts all required props correctly
    const completeProps = {
      ...defaultProps,
      batchActions: [
        {
          label: "Test Action",
          onClick: vi.fn(),
          disabled: false,
          tooltip: "Test tooltip",
        },
      ],
    };

    expect(() =>
      render(<FaultTrackingTable {...completeProps} />),
    ).not.toThrow();
  });
});
