import { render, screen, userEvent } from "../../../../../test-utils";
import { CreateManualEntryButton } from "./create-manual-entry-button";

// Mock the useCreateManualEntry hook
const mockIsDisabled = vi.fn().mockReturnValue(false);
const mockDisabledReason = vi.fn().mockReturnValue(undefined);

vi.mock("../../hooks/manual-entry/use-create-manual-entry", () => ({
  useCreateManualEntry: () => ({
    isDisabled: mockIsDisabled(),
    disabledReason: mockDisabledReason(),
    createEntry: vi.fn(),
    isSubmitting: false,
  }),
}));

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    Button: ({ children, onClick, renderIcon, kind, ...props }: any) => (
      <button onClick={onClick} data-kind={kind} {...props}>
        {renderIcon && <span data-testid="button-icon">Icon</span>}
        {children}
      </button>
    ),
    Tooltip: ({ children, label }: any) => (
      <div data-testid="tooltip" title={label}>
        {children}
      </div>
    ),
  };
});

describe("CreateManualEntryButton", () => {
  const toggleManualEntryModalMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    // Reset mocks to default values
    mockIsDisabled.mockReturnValue(false);
    mockDisabledReason.mockReturnValue(undefined);
  });

  it("should render with correct text", () => {
    render(
      <CreateManualEntryButton
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    expect(screen.getByText("Add Manual Entry")).toBeInTheDocument();
  });

  it("should render as primary button", () => {
    render(
      <CreateManualEntryButton
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    const button = screen.getByRole("button");
    expect(button).toHaveAttribute("data-kind", "primary");
  });

  it("should render with Add icon", () => {
    render(
      <CreateManualEntryButton
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    expect(screen.getByTestId("button-icon")).toBeInTheDocument();
  });

  it("should have correct test id", () => {
    render(
      <CreateManualEntryButton
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    expect(screen.getByTestId("button-add-manual-entry")).toBeInTheDocument();
  });

  it("should call toggleManualEntryModal when clicked", async () => {
    render(
      <CreateManualEntryButton
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    const button = screen.getByRole("button");
    await userEvent.click(button);

    expect(toggleManualEntryModalMock).toHaveBeenCalledTimes(1);
  });

  it("should render translated text", () => {
    render(
      <CreateManualEntryButton
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    // Should use the translation key "faultTracking.addManualEntry"
    expect(screen.getByText("Add Manual Entry")).toBeInTheDocument();
  });

  describe("disabled state behavior", () => {
    it("should disable button when manual entry is disabled", () => {
      mockIsDisabled.mockReturnValue(true);
      mockDisabledReason.mockReturnValue("Timezone configuration is required");

      render(
        <CreateManualEntryButton
          toggleManualEntryModal={toggleManualEntryModalMock}
        />,
      );

      const button = screen.getByRole("button");
      expect(button).toBeDisabled();
    });

    it("should enable button when manual entry is not disabled", () => {
      mockIsDisabled.mockReturnValue(false);
      mockDisabledReason.mockReturnValue(undefined);

      render(
        <CreateManualEntryButton
          toggleManualEntryModal={toggleManualEntryModalMock}
        />,
      );

      const button = screen.getByRole("button");
      expect(button).not.toBeDisabled();
    });

    it("should show tooltip when disabled", () => {
      mockIsDisabled.mockReturnValue(true);
      mockDisabledReason.mockReturnValue(
        "Timezone configuration is required for manual entries",
      );

      render(
        <CreateManualEntryButton
          toggleManualEntryModal={toggleManualEntryModalMock}
        />,
      );

      const tooltip = screen.getByTestId("tooltip");
      expect(tooltip).toBeInTheDocument();
      expect(tooltip).toHaveAttribute(
        "title",
        "Timezone configuration is required for manual entries",
      );
    });

    it("should not show tooltip when enabled", () => {
      mockIsDisabled.mockReturnValue(false);
      mockDisabledReason.mockReturnValue(undefined);

      render(
        <CreateManualEntryButton
          toggleManualEntryModal={toggleManualEntryModalMock}
        />,
      );

      expect(screen.queryByTestId("tooltip")).not.toBeInTheDocument();
    });

    it("should not call toggleManualEntryModal when disabled and clicked", async () => {
      mockIsDisabled.mockReturnValue(true);
      mockDisabledReason.mockReturnValue("Timezone configuration is required");

      render(
        <CreateManualEntryButton
          toggleManualEntryModal={toggleManualEntryModalMock}
        />,
      );

      const button = screen.getByRole("button");
      await userEvent.click(button);

      // Disabled buttons should not trigger click events
      expect(toggleManualEntryModalMock).not.toHaveBeenCalled();
    });

    it("should show different tooltip messages based on disabled reason", () => {
      mockIsDisabled.mockReturnValue(true);
      mockDisabledReason.mockReturnValue("Loading timezone configuration...");

      render(
        <CreateManualEntryButton
          toggleManualEntryModal={toggleManualEntryModalMock}
        />,
      );

      const tooltip = screen.getByTestId("tooltip");
      expect(tooltip).toHaveAttribute(
        "title",
        "Loading timezone configuration...",
      );
    });
  });
});
