import { render, screen } from "../../../../../test-utils";
import { IncludedDefaultToggle } from "./included-default-toggle";

// Mock Carbon Toggle component
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    Toggle: ({
      id,
      labelText,
      labelB,
      defaultToggled,
      disabled,
      ...props
    }: any) => (
      <div data-testid="toggle-container">
        <label htmlFor={id}>{labelText}</label>
        <input
          type="checkbox"
          id={id}
          defaultChecked={defaultToggled}
          disabled={disabled}
          data-testid="toggle-input"
          {...props}
        />
        <span data-testid="toggle-label-b">{labelB}</span>
      </div>
    ),
  };
});

describe("IncludedDefaultToggle", () => {
  it("should render with correct main label", () => {
    render(<IncludedDefaultToggle />);

    expect(
      screen.getByText("System Availability Calculation"),
    ).toBeInTheDocument();
  });

  it("should render with correct toggle label", () => {
    render(<IncludedDefaultToggle />);

    expect(screen.getByTestId("toggle-label-b")).toHaveTextContent("Included");
  });

  it("should render with correct id", () => {
    render(<IncludedDefaultToggle />);

    const toggle = screen.getByTestId("toggle-input");
    expect(toggle).toHaveAttribute("id", "included-default-toggle");
  });

  it("should be defaultToggled (checked by default)", () => {
    render(<IncludedDefaultToggle />);

    const toggle = screen.getByTestId("toggle-input");
    expect(toggle).toBeChecked();
  });

  it("should be read only", () => {
    render(<IncludedDefaultToggle />);

    const toggle = screen.getByTestId("toggle-input");
    expect(toggle).toHaveAttribute("readonly");
  });

  it("should use correct translation keys", () => {
    render(<IncludedDefaultToggle />);

    // Should use "faultTracking.systemAvailabilityCalculation"
    expect(
      screen.getByText("System Availability Calculation"),
    ).toBeInTheDocument();

    // Should use "faultTracking.included"
    expect(screen.getByText("Included")).toBeInTheDocument();
  });

  it("should render toggle container", () => {
    render(<IncludedDefaultToggle />);

    expect(screen.getByTestId("toggle-container")).toBeInTheDocument();
  });
});
