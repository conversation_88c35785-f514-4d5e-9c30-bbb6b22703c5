import { Toggle } from "@carbon/react";
import { useTranslation } from "react-i18next";

export function IncludedDefaultToggle() {
  const { t } = useTranslation();
  return (
    <Toggle
      defaultToggled
      readOnly
      id="included-default-toggle"
      labelB={t("faultTracking.included", "Included")}
      labelText={t(
        "faultTracking.systemAvailabilityCalculation",
        "System Availability Calculation",
      )}
    />
  );
}
