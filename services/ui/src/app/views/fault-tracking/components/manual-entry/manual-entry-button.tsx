import { useFeatureFlag } from "../../../../config/hooks/use-config";
import { CreateManualEntryButton } from "./create-manual-entry-button";

const MANUAL_ENTRY_BUTTON_FLAG_NAME = "fault-tracking-manual-entry-button";

export function ManualEntryButton({
  toggleManualEntryModal,
}: {
  toggleManualEntryModal: () => void;
}) {
  const { enabled: manualEntryButtonEnabled } = useFeatureFlag(
    MANUAL_ENTRY_BUTTON_FLAG_NAME,
  );
  return manualEntryButtonEnabled ? (
    <CreateManualEntryButton toggleManualEntryModal={toggleManualEntryModal} />
  ) : null;
}
