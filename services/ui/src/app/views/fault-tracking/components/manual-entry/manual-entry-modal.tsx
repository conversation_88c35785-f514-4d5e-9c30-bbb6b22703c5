import { Modal } from "@carbon/react";
import { useTranslation } from "react-i18next";
import { ManualEntryForm } from "./manual-entry-form";
import {
  getMinStartDate,
  useManualEntryForm,
} from "../../hooks/manual-entry/use-manual-entry-form";
import { useCreateManualEntry } from "../../hooks/manual-entry/use-create-manual-entry";
import { useEffect } from "react";
import styles from "./manual-entry-modal.module.scss";

export function ManualEntryModal({
  isManualEntryModalOpen,
  toggleManualEntryModal,
  onSuccess,
}: {
  isManualEntryModalOpen: boolean;
  toggleManualEntryModal: () => void;
  onSuccess?: () => void;
}) {
  const { t } = useTranslation();
  const {
    formData,
    setField,
    reset,
    validate,
    setErrors,
    errors,
    touched,
    setFieldTouched,
    isValid,
    calculateDuration,
    resetTrigger,
  } = useManualEntryForm();
  const { createEntry, isSubmitting, isDisabled, disabledReason } =
    useCreateManualEntry(onSuccess);
  const minStartDate = getMinStartDate();

  useEffect(() => {
    if (isManualEntryModalOpen) {
      reset();
      setErrors({});
    }
  }, [isManualEntryModalOpen, reset, setErrors]);

  const handleSubmit = async () => {
    const validationErrors = validate();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setErrors({});
    const success = await createEntry(formData);

    if (success) {
      toggleManualEntryModal();
    }
  };

  const handleCancel = () => {
    setErrors({});
    toggleManualEntryModal();
  };

  return (
    <Modal
      modalHeading={t("faultTracking.manualEntry", "Create a Manual Alarm")}
      open={isManualEntryModalOpen}
      onRequestClose={handleCancel}
      primaryButtonText={t("faultTracking.save", "Save")}
      secondaryButtonText={t("faultTracking.cancel", "Cancel")}
      onRequestSubmit={handleSubmit}
      onSecondarySubmit={handleCancel}
      passiveModal={false}
      primaryButtonDisabled={isSubmitting || !isValid || isDisabled}
    >
      {isDisabled && disabledReason ? (
        <p className={styles.errorMessage}>{disabledReason}</p>
      ) : (
        <p style={{ marginBottom: "1.5rem" }}>
          {t(
            "faultTracking.manualEntrySubtitle",
            "Adjust the date range, start and end time, the duration will update automatically.",
          )}
        </p>
      )}
      <ManualEntryForm
        formData={formData}
        setField={setField}
        errors={errors}
        minStartDate={minStartDate}
        validate={validate}
        setErrors={setErrors}
        touched={touched}
        setFieldTouched={setFieldTouched}
        calculateDuration={calculateDuration}
        resetTrigger={resetTrigger}
      />
    </Modal>
  );
}
