import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { DateRangePicker } from "./date-range-picker";

// Mock Carbon components
vi.mock("@carbon/react", () => ({
  DatePicker: ({
    value,
    onChange,
    datePickerType,
    minDate,
    maxDate,
    closeOnSelect,
    className,
    children,
  }: any) => (
    <div
      data-testid="date-picker"
      data-type={datePickerType}
      data-close-on-select={closeOnSelect}
      className={className}
    >
      <input
        data-testid="date-picker-value"
        value={
          value?.length > 0
            ? value.map((d: Date) => d.toISOString()).join(",")
            : ""
        }
        onChange={(e) => {
          const dates = e.target.value
            .split(",")
            .map((d) => (d ? new Date(d) : null));
          onChange(dates);
        }}
      />
      <div data-testid="date-picker-min">{minDate?.toISOString()}</div>
      <div data-testid="date-picker-max">{maxDate?.toISOString()}</div>
      {children}
    </div>
  ),
  DatePickerInput: ({
    id,
    labelText,
    placeholder,
    invalid,
    invalidText,
    style,
  }: any) => (
    <div data-testid={id} style={style}>
      <label htmlFor={`${id}-input`}>{labelText}</label>
      <input
        id={`${id}-input`}
        data-testid={`${id}-input`}
        placeholder={placeholder}
        aria-invalid={invalid}
        aria-describedby={invalid ? `${id}-error` : undefined}
      />
      {invalid && invalidText && (
        <div id={`${id}-error`} data-testid={`${id}-error`}>
          {invalidText}
        </div>
      )}
    </div>
  ),
  GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock react-i18next
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (_key: string, defaultValue: string) => defaultValue,
  }),
}));

describe("DateRangePicker", () => {
  const mockOnStartDateChange = vi.fn();
  const mockOnEndDateChange = vi.fn();
  const minDate = new Date("2025-01-01");

  const defaultProps = {
    startDate: null,
    endDate: null,
    onStartDateChange: mockOnStartDateChange,
    onEndDateChange: mockOnEndDateChange,
    minStartDate: minDate,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("rendering", () => {
    it("should render with required props", () => {
      render(<DateRangePicker {...defaultProps} />);

      expect(screen.getByTestId("date-picker")).toBeDefined();
      expect(screen.getByTestId("start-date")).toBeDefined();
      expect(screen.getByTestId("end-date")).toBeDefined();
    });

    it("should render with correct labels", () => {
      render(<DateRangePicker {...defaultProps} />);

      expect(screen.getByLabelText("Start Date")).toBeDefined();
      expect(screen.getByLabelText("End Date")).toBeDefined();
    });

    it("should render with correct placeholders", () => {
      render(<DateRangePicker {...defaultProps} />);

      const startInput = screen.getByTestId("start-date-input");
      const endInput = screen.getByTestId("end-date-input");

      expect(startInput.getAttribute("placeholder")).toBe("yyyy-mm-dd");
      expect(endInput.getAttribute("placeholder")).toBe("yyyy-mm-dd");
    });

    it("should configure DatePicker with correct props", () => {
      render(<DateRangePicker {...defaultProps} />);

      const datePicker = screen.getByTestId("date-picker");
      expect(datePicker.getAttribute("data-type")).toBe("range");
      expect(datePicker.getAttribute("data-close-on-select")).toBe("true");
    });

    it("should set min and max dates correctly", () => {
      render(<DateRangePicker {...defaultProps} />);

      const minElement = screen.getByTestId("date-picker-min");
      const maxElement = screen.getByTestId("date-picker-max");

      expect(minElement.textContent).toBe(minDate.toISOString());
      expect(maxElement.textContent).toBeTruthy(); // Should be today's date
    });

    it("should apply full width styling", () => {
      render(<DateRangePicker {...defaultProps} />);

      const wrapper = screen.getByTestId("date-picker").parentElement;
      expect(wrapper?.style.width).toBe("100%");

      const startInput = screen.getByTestId("start-date");
      const endInput = screen.getByTestId("end-date");
      expect(startInput.style.width).toBe("100%");
      expect(endInput.style.width).toBe("100%");
    });
  });

  describe("date values", () => {
    it("should handle null dates", () => {
      render(
        <DateRangePicker {...defaultProps} startDate={null} endDate={null} />,
      );

      const valueInput = screen.getByTestId(
        "date-picker-value",
      ) as HTMLInputElement;
      expect(valueInput.value).toBe(""); // Empty array when both dates are null
    });

    it("should display provided start and end dates", () => {
      const startDate = new Date("2025-01-15");
      const endDate = new Date("2025-01-20");

      render(
        <DateRangePicker
          {...defaultProps}
          startDate={startDate}
          endDate={endDate}
        />,
      );

      const valueInput = screen.getByTestId(
        "date-picker-value",
      ) as HTMLInputElement;
      const expectedValue = `${startDate.toISOString()},${endDate.toISOString()}`;
      expect(valueInput.value).toBe(expectedValue);
    });

    it("should filter out null values from DatePicker value prop", () => {
      const startDate = new Date("2025-01-15");

      render(
        <DateRangePicker
          {...defaultProps}
          startDate={startDate}
          endDate={null}
        />,
      );

      const valueInput = screen.getByTestId(
        "date-picker-value",
      ) as HTMLInputElement;
      expect(valueInput.value).toBe(startDate.toISOString()); // Only valid date, no comma
    });
  });

  describe("date change handling", () => {
    it("should call onStartDateChange and onEndDateChange when dates change", async () => {
      const user = userEvent.setup();
      render(<DateRangePicker {...defaultProps} />);

      const valueInput = screen.getByTestId("date-picker-value");
      const testStartDate = new Date("2025-01-15");
      const testEndDate = new Date("2025-01-20");

      await user.type(
        valueInput,
        `${testStartDate.toISOString()},${testEndDate.toISOString()}`,
      );

      expect(mockOnStartDateChange).toHaveBeenCalled();
      expect(mockOnEndDateChange).toHaveBeenCalled();
    });

    it("should handle null dates in onChange", async () => {
      const user = userEvent.setup();
      render(<DateRangePicker {...defaultProps} />);

      const valueInput = screen.getByTestId("date-picker-value");
      await user.type(valueInput, ","); // Empty dates

      expect(mockOnStartDateChange).toHaveBeenCalledWith(null);
      expect(mockOnEndDateChange).toHaveBeenCalledWith(null);
    });
  });

  describe("resetTrigger", () => {
    it("should accept resetTrigger prop without errors", () => {
      // Since key prop can't be tested directly, we just verify the component renders
      render(<DateRangePicker {...defaultProps} resetTrigger={5} />);

      expect(screen.getByTestId("date-picker")).toBeDefined();
      expect(screen.getByTestId("start-date")).toBeDefined();
      expect(screen.getByTestId("end-date")).toBeDefined();
    });

    it("should handle resetTrigger changes without breaking", () => {
      const { rerender } = render(
        <DateRangePicker {...defaultProps} resetTrigger={1} />,
      );

      expect(screen.getByTestId("date-picker")).toBeDefined();

      rerender(<DateRangePicker {...defaultProps} resetTrigger={2} />);

      expect(screen.getByTestId("date-picker")).toBeDefined();
    });
  });

  describe("validation states", () => {
    it("should not show errors when not touched", () => {
      render(
        <DateRangePicker
          {...defaultProps}
          errors={{ startDate: "Start date error", endDate: "End date error" }}
          touched={{ startDate: false, endDate: false }}
        />,
      );

      const startInput = screen.getByTestId("start-date-input");
      const endInput = screen.getByTestId("end-date-input");

      expect(startInput.getAttribute("aria-invalid")).toBe("false");
      expect(endInput.getAttribute("aria-invalid")).toBe("false");
      expect(screen.queryByTestId("start-date-error")).toBe(null);
      expect(screen.queryByTestId("end-date-error")).toBe(null);
    });

    it("should show start date error when touched", () => {
      render(
        <DateRangePicker
          {...defaultProps}
          errors={{ startDate: "Start date is required" }}
          touched={{ startDate: true }}
        />,
      );

      const startInput = screen.getByTestId("start-date-input");
      expect(startInput.getAttribute("aria-invalid")).toBe("true");
      expect(screen.getByTestId("start-date-error").textContent).toBe(
        "Start date is required",
      );
    });

    it("should show end date error when touched", () => {
      render(
        <DateRangePicker
          {...defaultProps}
          errors={{ endDate: "End date is required" }}
          touched={{ endDate: true }}
        />,
      );

      const endInput = screen.getByTestId("end-date-input");
      expect(endInput.getAttribute("aria-invalid")).toBe("true");
      expect(screen.getByTestId("end-date-error").textContent).toBe(
        "End date is required",
      );
    });

    it("should show both errors when both are touched", () => {
      render(
        <DateRangePicker
          {...defaultProps}
          errors={{ startDate: "Start error", endDate: "End error" }}
          touched={{ startDate: true, endDate: true }}
        />,
      );

      expect(screen.getByText("Start error")).toBeDefined();
      expect(screen.getByText("End error")).toBeDefined();
    });
  });

  describe("accessibility", () => {
    it("should have proper label associations", () => {
      render(<DateRangePicker {...defaultProps} />);

      const startInput = screen.getByTestId("start-date-input");
      const endInput = screen.getByTestId("end-date-input");
      const startLabel = screen.getByLabelText("Start Date");
      const endLabel = screen.getByLabelText("End Date");

      expect(startInput).toBe(startLabel);
      expect(endInput).toBe(endLabel);
    });

    it("should have aria-describedby when showing errors", () => {
      render(
        <DateRangePicker
          {...defaultProps}
          errors={{ startDate: "Start error", endDate: "End error" }}
          touched={{ startDate: true, endDate: true }}
        />,
      );

      const startInput = screen.getByTestId("start-date-input");
      const endInput = screen.getByTestId("end-date-input");

      expect(startInput.getAttribute("aria-describedby")).toBe(
        "start-date-error",
      );
      expect(endInput.getAttribute("aria-describedby")).toBe("end-date-error");
    });

    it("should not have aria-describedby when no errors", () => {
      render(<DateRangePicker {...defaultProps} />);

      const startInput = screen.getByTestId("start-date-input");
      const endInput = screen.getByTestId("end-date-input");

      expect(startInput.getAttribute("aria-describedby")).toBe(null);
      expect(endInput.getAttribute("aria-describedby")).toBe(null);
    });
  });

  describe("component integration", () => {
    it("should handle all props together", () => {
      const startDate = new Date("2025-01-15");
      const endDate = new Date("2025-01-20");

      render(
        <DateRangePicker
          {...defaultProps}
          startDate={startDate}
          endDate={endDate}
          resetTrigger={3}
          errors={{ startDate: "Start error" }}
          touched={{ startDate: true }}
        />,
      );

      const datePicker = screen.getByTestId("date-picker");
      const startInput = screen.getByTestId("start-date-input");

      expect(datePicker).toBeDefined();
      expect(startInput.getAttribute("aria-invalid")).toBe("true");
      expect(screen.getByText("Start error")).toBeDefined();
    });

    it("should handle Carbon DatePicker auto-flip behavior note", () => {
      render(<DateRangePicker {...defaultProps} />);

      // The component should render without issues even with the auto-flip note
      expect(screen.getByTestId("date-picker")).toBeDefined();
      expect(screen.getByTestId("start-date")).toBeDefined();
      expect(screen.getByTestId("end-date")).toBeDefined();
    });
  });
});
