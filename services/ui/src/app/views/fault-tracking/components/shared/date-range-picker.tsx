import { DatePicker, DatePickerInput } from "@carbon/react";
import { useTranslation } from "react-i18next";
import styles from "./date-range-picker.module.scss";

interface DateRangePickerProps {
  startDate: Date | null;
  endDate: Date | null;
  onStartDateChange: (date: Date | null) => void;
  onEndDateChange: (date: Date | null) => void;
  minStartDate: Date;
  resetTrigger?: number;
  errors?: {
    startDate?: string;
    endDate?: string;
  };
  touched?: {
    startDate?: boolean;
    endDate?: boolean;
  };
}

export function DateRangePicker({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  minStartDate,
  resetTrigger = 0,
  errors,
  touched,
}: DateRangePickerProps) {
  const { t } = useTranslation();
  const today = new Date();

  return (
    <div style={{ width: "100%" }}>
      <DatePicker
        key={`datepicker-${resetTrigger}`}
        // NOTE: Carbon auto-flips start/end dates in range mode if end < start,
        // but the visual range highlight may not appear when dates are entered manually.
        // This is a known Carbon UI bug
        // ensures the DatePicker only ever receives an array of valid Date objects
        value={[startDate, endDate].filter(
          (date): date is Date => date instanceof Date,
        )}
        onChange={(dates) => {
          onStartDateChange(dates[0] ?? null);
          onEndDateChange(dates[1] ?? null);
        }}
        datePickerType="range"
        minDate={minStartDate}
        closeOnSelect={true}
        maxDate={today}
        className={styles.fullWidthDatePicker}
      >
        <DatePickerInput
          id="start-date"
          labelText={t("faultTracking.startDate", "Start Date")}
          placeholder="yyyy-mm-dd"
          invalid={touched?.startDate && !!errors?.startDate}
          invalidText={touched?.startDate ? errors?.startDate : undefined}
          style={{ width: "100%" }}
        />
        <DatePickerInput
          id="end-date"
          labelText={t("faultTracking.endDate", "End Date")}
          placeholder="yyyy-mm-dd"
          invalid={touched?.endDate && !!errors?.endDate}
          invalidText={touched?.endDate ? errors?.endDate : undefined}
          style={{ width: "100%" }}
        />
      </DatePicker>
    </div>
  );
}
