import { render, screen, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { expect } from "vitest";
import { DateTimePicker, type DateTimeData } from "./date-time-picker";

// Mock the sub-components
vi.mock("./time-picker-field", () => ({
  TimePickerField: ({
    id,
    label,
    value,
    period,
    onTimeChange,
    onPeriodChange,
    error,
    touched,
    onTimeBlur,
    onPeriodBlur,
  }: any) => (
    <div data-testid={id}>
      <label>{label}</label>
      <input
        data-testid={`${id}-time-input`}
        value={value}
        onChange={(e) => onTimeChange(e.target.value)}
        onBlur={onTimeBlur}
      />
      <select
        data-testid={`${id}-period-select`}
        value={period}
        onChange={(e) => onPeriodChange(e.target.value)}
        onBlur={onPeriodBlur}
      >
        <option value="AM">AM</option>
        <option value="PM">PM</option>
      </select>
      {touched && error && <span data-testid={`${id}-error`}>{error}</span>}
    </div>
  ),
}));

vi.mock("./seconds-input-field", () => ({
  SecondsInputField: ({
    id,
    label,
    value,
    onChange,
    error,
    touched,
    onBlur,
  }: any) => (
    <div data-testid={id}>
      <label>{label}</label>
      <input
        data-testid={`${id}-input`}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onBlur={onBlur}
        placeholder="00:000"
      />
      {touched && error && <span data-testid={`${id}-error`}>{error}</span>}
    </div>
  ),
}));

describe("DateTimePicker", () => {
  const mockDateTimeData: DateTimeData = {
    date: new Date("2024-01-15"),
    time: "10:30",
    period: "AM",
    seconds: "15:500",
  };

  const defaultProps = {
    id: "test-datetime",
    dateLabel: "Test Date",
    timeLabel: "Test Time",
    value: mockDateTimeData,
    onChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("rendering", () => {
    it("should render all components with default props", () => {
      render(<DateTimePicker {...defaultProps} />);

      // Check that all sub-components are rendered
      expect(screen.getByLabelText("Test Date")).toBeDefined();
      expect(screen.getByTestId("test-datetime-time")).toBeDefined();
      expect(screen.getByTestId("test-datetime-seconds")).toBeDefined();
    });

    it("should render with custom labels", () => {
      render(
        <DateTimePicker
          {...defaultProps}
          dateLabel="Custom Date"
          timeLabel="Custom Time"
          secondsLabel="Custom Seconds"
        />,
      );

      expect(screen.getByLabelText("Custom Date")).toBeDefined();
      expect(screen.getByText("Custom Time")).toBeDefined();
      expect(screen.getByText("Custom Seconds")).toBeDefined();
    });

    it("should hide seconds input when showSeconds is false", () => {
      render(<DateTimePicker {...defaultProps} showSeconds={false} />);

      expect(screen.getByTestId("test-datetime-time")).toBeDefined();
      expect(screen.queryByTestId("test-datetime-seconds")).toBeNull();
    });

    it("should use vertical orientation when specified", () => {
      const { container } = render(
        <DateTimePicker {...defaultProps} orientation="vertical" />,
      );

      // Check that the main stack has vertical orientation
      const mainStack = container.querySelector(".cds--stack-vertical");
      expect(mainStack).toBeDefined();
    });
  });

  describe("date handling", () => {
    it("should call onChange when date changes", async () => {
      const onChange = vi.fn();

      render(<DateTimePicker {...defaultProps} onChange={onChange} />);

      // Since we're using the real DatePicker component, we test the component renders correctly
      // The actual date change testing would require more complex mocking of the DatePicker
      const dateInput = screen.getByLabelText("Test Date");
      expect(dateInput).toBeDefined();

      // Verify the component renders without errors
      expect(screen.getByTestId("test-datetime-time")).toBeDefined();
    });

    it("should handle null date values", () => {
      const emptyData: DateTimeData = {
        date: null,
        time: "",
        period: "AM",
      };

      render(<DateTimePicker {...defaultProps} value={emptyData} />);

      const dateInput = screen.getByLabelText("Test Date");
      expect(dateInput).toBeDefined();
      expect((dateInput as HTMLInputElement).value).toBe("");
    });
  });

  describe("time handling", () => {
    it("should call onChange when time changes", async () => {
      const onChange = vi.fn();

      render(<DateTimePicker {...defaultProps} onChange={onChange} />);

      const timeInput = screen.getByTestId("test-datetime-time-time-input");

      // Use fireEvent for more predictable testing instead of user typing
      fireEvent.change(timeInput, { target: { value: "11:45" } });

      // Check that onChange was called with the expected time
      expect(onChange).toHaveBeenCalledWith({ time: "11:45" });
    });

    it("should call onChange when period changes", async () => {
      const user = userEvent.setup();
      const onChange = vi.fn();

      render(<DateTimePicker {...defaultProps} onChange={onChange} />);

      const periodSelect = screen.getByTestId(
        "test-datetime-time-period-select",
      );
      await user.selectOptions(periodSelect, "PM");

      expect(onChange).toHaveBeenCalledWith({ period: "PM" });
    });
  });

  describe("seconds handling", () => {
    it("should call onChange when seconds changes", async () => {
      const onChange = vi.fn();

      render(<DateTimePicker {...defaultProps} onChange={onChange} />);

      const secondsInput = screen.getByTestId("test-datetime-seconds-input");

      // Use fireEvent for more predictable testing instead of user typing
      fireEvent.change(secondsInput, { target: { value: "30:250" } });

      // Check that onChange was called with the expected seconds
      expect(onChange).toHaveBeenCalledWith({ seconds: "30:250" });
    });

    it("should handle empty seconds value", () => {
      const dataWithoutSeconds: DateTimeData = {
        date: new Date("2024-01-15"),
        time: "10:30",
        period: "AM",
      };

      render(<DateTimePicker {...defaultProps} value={dataWithoutSeconds} />);

      const secondsInput = screen.getByTestId("test-datetime-seconds-input");
      expect((secondsInput as HTMLInputElement).value).toBe("");
    });
  });

  describe("validation and errors", () => {
    it("should display date error when touched and has error", () => {
      render(
        <DateTimePicker
          {...defaultProps}
          errors={{ date: "Date is required" }}
          touched={{ date: true }}
        />,
      );

      const dateInput = screen.getByLabelText("Test Date");
      // The DatePicker component sets invalid class instead of aria-invalid
      expect(
        dateInput.classList.contains("cds--date-picker__input--invalid") ||
          dateInput.getAttribute("aria-invalid") === "true",
      ).toBe(true);
    });

    it("should display time error when touched and has error", () => {
      render(
        <DateTimePicker
          {...defaultProps}
          errors={{ time: "Time is required" }}
          touched={{ time: true }}
        />,
      );

      expect(screen.getByTestId("test-datetime-time-error").textContent).toBe(
        "Time is required",
      );
    });

    it("should display seconds error when touched and has error", () => {
      render(
        <DateTimePicker
          {...defaultProps}
          errors={{ seconds: "Invalid format" }}
          touched={{ seconds: true }}
        />,
      );

      expect(
        screen.getByTestId("test-datetime-seconds-error").textContent,
      ).toBe("Invalid format");
    });

    it("should not display errors when not touched", () => {
      render(
        <DateTimePicker
          {...defaultProps}
          errors={{ time: "Time error", seconds: "Seconds error" }}
          touched={{}}
        />,
      );

      expect(screen.queryByTestId("test-datetime-time-error")).toBeNull();
      expect(screen.queryByTestId("test-datetime-seconds-error")).toBeNull();
    });
  });

  describe("blur handling", () => {
    it("should call onBlur callbacks when provided", async () => {
      const user = userEvent.setup();
      const onBlur = {
        date: vi.fn(),
        time: vi.fn(),
        period: vi.fn(),
        seconds: vi.fn(),
      };

      render(<DateTimePicker {...defaultProps} onBlur={onBlur} />);

      // Test time blur
      const timeInput = screen.getByTestId("test-datetime-time-time-input");
      await user.click(timeInput);
      await user.tab();
      expect(onBlur.time).toHaveBeenCalled();

      // Test period blur
      const periodSelect = screen.getByTestId(
        "test-datetime-time-period-select",
      );
      await user.click(periodSelect);
      await user.tab();
      expect(onBlur.period).toHaveBeenCalled();

      // Test seconds blur
      const secondsInput = screen.getByTestId("test-datetime-seconds-input");
      await user.click(secondsInput);
      await user.tab();
      expect(onBlur.seconds).toHaveBeenCalled();
    });

    it("should handle missing onBlur callbacks gracefully", () => {
      render(<DateTimePicker {...defaultProps} />);

      // Should not throw errors when onBlur callbacks are not provided
      const timeInput = screen.getByTestId("test-datetime-time-time-input");
      fireEvent.blur(timeInput);

      expect(screen.getByTestId("test-datetime-time")).toBeDefined();
    });
  });

  describe("date constraints", () => {
    it("should pass minDate and maxDate to DatePicker", () => {
      const minDate = new Date("2024-01-01");
      const maxDate = new Date("2024-12-31");

      render(
        <DateTimePicker
          {...defaultProps}
          minDate={minDate}
          maxDate={maxDate}
        />,
      );

      // The DatePicker should receive these props
      // Since we're using the real DatePicker component, we can't easily test the props
      // but we can verify the component renders without errors
      expect(screen.getByLabelText("Test Date")).toBeDefined();
    });
  });

  describe("accessibility", () => {
    it("should have proper labels for all inputs", () => {
      render(<DateTimePicker {...defaultProps} />);

      expect(screen.getByLabelText("Test Date")).toBeDefined();
      expect(screen.getByText("Test Time")).toBeDefined();
      expect(screen.getByText("Seconds & Milliseconds")).toBeDefined();
    });

    it("should use custom labels when provided", () => {
      render(
        <DateTimePicker
          {...defaultProps}
          dateLabel="Custom Date Label"
          timeLabel="Custom Time Label"
          secondsLabel="Custom Seconds Label"
        />,
      );

      expect(screen.getByLabelText("Custom Date Label")).toBeDefined();
      expect(screen.getByText("Custom Time Label")).toBeDefined();
      expect(screen.getByText("Custom Seconds Label")).toBeDefined();
    });
  });

  describe("component integration", () => {
    it("should work with all components together", () => {
      render(<DateTimePicker {...defaultProps} />);

      // Verify all components are rendered and working together
      expect(screen.getByLabelText("Test Date")).toBeDefined();
      expect(screen.getByTestId("test-datetime-time")).toBeDefined();
      expect(screen.getByTestId("test-datetime-seconds")).toBeDefined();

      // Check that data is properly displayed using correct value access
      const timeInput = screen.getByTestId(
        "test-datetime-time-time-input",
      ) as HTMLInputElement;
      const periodSelect = screen.getByTestId(
        "test-datetime-time-period-select",
      ) as HTMLSelectElement;
      const secondsInput = screen.getByTestId(
        "test-datetime-seconds-input",
      ) as HTMLInputElement;

      expect(timeInput.value).toBe("10:30");
      expect(periodSelect.value).toBe("AM");
      expect(secondsInput.value).toBe("15:500");
    });
  });
});
