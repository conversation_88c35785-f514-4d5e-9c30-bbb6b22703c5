import { DatePicker, DatePickerInput, Stack } from "@carbon/react";
import { TimePickerField } from "./time-picker-field";
import { SecondsInputField } from "./seconds-input-field";

export interface DateTimeData {
  date: Date | null;
  time: string;
  period: "AM" | "PM";
  seconds?: string;
}

interface DateTimePickerProps {
  id: string;
  dateLabel: string;
  timeLabel: string;
  secondsLabel?: string;
  value: DateTimeData;
  onChange: (data: Partial<DateTimeData>) => void;
  errors?: {
    date?: string;
    time?: string;
    seconds?: string;
  };
  touched?: {
    date?: boolean;
    time?: boolean;
    seconds?: boolean;
  };
  onBlur?: {
    date?: () => void;
    time?: () => void;
    period?: () => void;
    seconds?: () => void;
  };
  minDate?: Date;
  maxDate?: Date;
  showSeconds?: boolean;
  orientation?: "horizontal" | "vertical";
}

export function DateTimePicker({
  id,
  dateLabel,
  timeLabel,
  secondsLabel = "Seconds & Milliseconds",
  value,
  onChange,
  errors = {},
  touched = {},
  onBlur = {},
  minDate,
  maxDate,
  showSeconds = true,
  orientation = "horizontal",
}: DateTimePickerProps) {
  const handleDateChange = (dates: (Date | null)[]) => {
    onChange({ date: dates[0] ?? null });
  };

  const handleTimeChange = (time: string) => {
    onChange({ time });
  };

  const handlePeriodChange = (period: "AM" | "PM") => {
    onChange({ period });
  };

  const handleSecondsChange = (seconds: string) => {
    onChange({ seconds });
  };

  return (
    <Stack orientation={orientation} gap="1rem">
      <DatePicker
        value={value.date ? [value.date] : []}
        onChange={handleDateChange}
        datePickerType="single"
        minDate={minDate}
        maxDate={maxDate}
        className="date-time-picker__date"
      >
        <DatePickerInput
          id={`${id}-date`}
          labelText={dateLabel}
          placeholder="yyyy-mm-dd"
          invalid={touched.date && !!errors.date}
          invalidText={touched.date ? errors.date : undefined}
          onBlur={onBlur.date}
        />
      </DatePicker>

      <Stack orientation="horizontal" gap="0.5rem">
        <TimePickerField
          id={`${id}-time`}
          label={timeLabel}
          value={value.time}
          period={value.period}
          onTimeChange={handleTimeChange}
          onPeriodChange={handlePeriodChange}
          error={errors.time}
          touched={touched.time}
          onTimeBlur={onBlur.time || (() => {})}
          onPeriodBlur={onBlur.period || (() => {})}
        />

        {showSeconds && (
          <SecondsInputField
            id={`${id}-seconds`}
            label={secondsLabel}
            value={value.seconds || ""}
            onChange={handleSecondsChange}
            error={errors.seconds}
            touched={touched.seconds}
            onBlur={onBlur.seconds || (() => {})}
          />
        )}
      </Stack>
    </Stack>
  );
}
