import { useTranslation } from "react-i18next";
import styles from "./duration-display-field.module.scss";

export function DurationDisplayField({
  calculateDuration,
}: {
  calculateDuration: () => string;
}) {
  const { t } = useTranslation();
  return (
    <div data-testid="duration-display-field">
      <label className="cds--label">
        {t("faultTracking.duration", "Duration")}
      </label>
      <div className="cds--form-item">
        <div className={styles.durationDisplay}>
          {calculateDuration() ||
            t(
              "faultTracking.enterTimesToCalculate",
              "Enter times to calculate",
            )}
        </div>
      </div>
    </div>
  );
}
