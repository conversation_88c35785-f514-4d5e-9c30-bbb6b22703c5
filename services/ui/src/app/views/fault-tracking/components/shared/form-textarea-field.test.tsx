import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { FormTextAreaField } from "./form-textarea-field";

// Mock Carbon components
vi.mock("@carbon/react", () => ({
  TextArea: ({
    id,
    "data-testid": dataTestId,
    labelText,
    placeholder,
    value,
    onChange,
    invalid,
    invalidText,
    onBlur,
    required,
    maxCount,
    rows,
    enableCounter,
  }: any) => (
    <div data-testid={dataTestId}>
      <label htmlFor={`${id}-input`}>{labelText}</label>
      <textarea
        id={`${id}-input`}
        data-testid={`${id}-input`}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        aria-invalid={invalid}
        aria-describedby={invalid ? `${id}-error` : undefined}
        aria-required={required}
        maxLength={maxCount}
        rows={rows}
      />
      {invalid && invalidText && (
        <div id={`${id}-error`} data-testid={`${id}-error`}>
          {invalidText}
        </div>
      )}
      {enableCounter && (
        <div data-testid={`${id}-counter`}>
          {value.length}/{maxCount}
        </div>
      )}
    </div>
  ),
  GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock react-i18next
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (_key: string, defaultValue: string) => defaultValue,
  }),
}));

describe("FormTextAreaField", () => {
  const mockOnChange = vi.fn();
  const mockOnBlur = vi.fn();

  const defaultProps = {
    id: "test-textarea",
    fieldName: "testField",
    labelKey: "test.label",
    labelDefault: "Test Label",
    placeholderKey: "test.placeholder",
    placeholderDefault: "Test placeholder",
    value: "",
    onChange: mockOnChange,
    onBlur: mockOnBlur,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("rendering", () => {
    it("should render with required props", () => {
      render(<FormTextAreaField {...defaultProps} />);

      expect(screen.getByTestId("alarm-testField")).toBeDefined();
      expect(screen.getByLabelText("Test Label")).toBeDefined();
      expect(screen.getByTestId("test-textarea-input")).toBeDefined();
    });

    it("should render with correct placeholder", () => {
      render(<FormTextAreaField {...defaultProps} />);

      const textarea = screen.getByTestId("test-textarea-input");
      expect(textarea.getAttribute("placeholder")).toBe("Test placeholder");
    });

    it("should display the provided value", () => {
      render(<FormTextAreaField {...defaultProps} value="Test content" />);

      const textarea = screen.getByTestId(
        "test-textarea-input",
      ) as HTMLTextAreaElement;
      expect(textarea.value).toBe("Test content");
    });

    it("should render with default props", () => {
      render(<FormTextAreaField {...defaultProps} />);

      const textarea = screen.getByTestId("test-textarea-input");
      expect(textarea.getAttribute("maxlength")).toBe("350");
      expect(textarea.getAttribute("rows")).toBe("4");
      expect(textarea.getAttribute("aria-required")).toBe("true");
      expect(screen.getByTestId("test-textarea-counter")).toBeDefined();
    });

    it("should render with custom props", () => {
      render(
        <FormTextAreaField
          {...defaultProps}
          maxCount={200}
          rows={6}
          required={false}
          enableCounter={false}
        />,
      );

      const textarea = screen.getByTestId("test-textarea-input");
      expect(textarea.getAttribute("maxlength")).toBe("200");
      expect(textarea.getAttribute("rows")).toBe("6");
      expect(textarea.getAttribute("aria-required")).toBe("false");
      expect(screen.queryByTestId("test-textarea-counter")).toBe(null);
    });

    it("should generate correct data-testid from fieldName", () => {
      render(<FormTextAreaField {...defaultProps} fieldName="comments" />);

      expect(screen.getByTestId("alarm-comments")).toBeDefined();
    });
  });

  describe("user interactions", () => {
    it("should call onChange when user types", async () => {
      const user = userEvent.setup();
      render(<FormTextAreaField {...defaultProps} />);

      const textarea = screen.getByTestId("test-textarea-input");
      await user.type(textarea, "Hello");

      expect(mockOnChange).toHaveBeenCalledTimes(5); // Once for each character
      expect(mockOnChange).toHaveBeenLastCalledWith("o"); // Last character typed
    });

    it("should call onChange with complete value when pasting", async () => {
      const user = userEvent.setup();
      render(<FormTextAreaField {...defaultProps} />);

      const textarea = screen.getByTestId("test-textarea-input");
      await user.click(textarea);
      await user.paste("Pasted content");

      expect(mockOnChange).toHaveBeenCalled();
    });

    it("should call onBlur when textarea loses focus", async () => {
      const user = userEvent.setup();
      render(<FormTextAreaField {...defaultProps} />);

      const textarea = screen.getByTestId("test-textarea-input");
      await user.click(textarea);
      await user.tab(); // Move focus away

      expect(mockOnBlur).toHaveBeenCalledTimes(1);
    });

    it("should clear textarea value", async () => {
      const user = userEvent.setup();
      render(<FormTextAreaField {...defaultProps} value="Initial content" />);

      const textarea = screen.getByTestId("test-textarea-input");
      await user.clear(textarea);

      expect(mockOnChange).toHaveBeenCalledWith("");
    });
  });

  describe("validation states", () => {
    it("should not show error when not touched", () => {
      render(
        <FormTextAreaField
          {...defaultProps}
          error="This field is required"
          touched={false}
        />,
      );

      const textarea = screen.getByTestId("test-textarea-input");
      expect(textarea.getAttribute("aria-invalid")).toBe("false");
      expect(screen.queryByTestId("test-textarea-error")).toBe(null);
    });

    it("should not show error when touched but no error", () => {
      render(<FormTextAreaField {...defaultProps} touched={true} />);

      const textarea = screen.getByTestId("test-textarea-input");
      expect(textarea.getAttribute("aria-invalid")).toBe("false");
      expect(screen.queryByTestId("test-textarea-error")).toBe(null);
    });

    it("should show error when touched and has error", () => {
      render(
        <FormTextAreaField
          {...defaultProps}
          error="This field is required"
          touched={true}
        />,
      );

      const textarea = screen.getByTestId("test-textarea-input");
      expect(textarea.getAttribute("aria-invalid")).toBe("true");
      expect(screen.getByTestId("test-textarea-error").textContent).toBe(
        "This field is required",
      );
    });

    it("should not show error when has error but not touched", () => {
      render(
        <FormTextAreaField
          {...defaultProps}
          error="This field is required"
          touched={false}
        />,
      );

      const textarea = screen.getByTestId("test-textarea-input");
      expect(textarea.getAttribute("aria-invalid")).toBe("false");
      expect(screen.queryByTestId("test-textarea-error")).toBe(null);
    });
  });

  describe("counter functionality", () => {
    it("should show character counter when enabled", () => {
      render(
        <FormTextAreaField
          {...defaultProps}
          value="Test content"
          maxCount={100}
          enableCounter={true}
        />,
      );

      const counter = screen.getByTestId("test-textarea-counter");
      expect(counter.textContent).toBe("12/100");
    });

    it("should not show character counter when disabled", () => {
      render(
        <FormTextAreaField
          {...defaultProps}
          value="Test content"
          enableCounter={false}
        />,
      );

      expect(screen.queryByTestId("test-textarea-counter")).toBe(null);
    });

    it("should update counter as user types", () => {
      render(
        <FormTextAreaField
          {...defaultProps}
          value="Hello world"
          maxCount={50}
        />,
      );

      const counter = screen.getByTestId("test-textarea-counter");
      expect(counter.textContent).toBe("11/50");
    });
  });

  describe("accessibility", () => {
    it("should have proper label association", () => {
      render(<FormTextAreaField {...defaultProps} />);

      const textarea = screen.getByTestId("test-textarea-input");
      const label = screen.getByLabelText("Test Label");

      expect(textarea).toBe(label);
    });

    it("should have aria-describedby when showing error", () => {
      render(
        <FormTextAreaField
          {...defaultProps}
          error="This field is required"
          touched={true}
        />,
      );

      const textarea = screen.getByTestId("test-textarea-input");
      expect(textarea.getAttribute("aria-describedby")).toBe(
        "test-textarea-error",
      );
    });

    it("should not have aria-describedby when no error", () => {
      render(<FormTextAreaField {...defaultProps} />);

      const textarea = screen.getByTestId("test-textarea-input");
      expect(textarea.getAttribute("aria-describedby")).toBe(null);
    });

    it("should have correct required attribute", () => {
      render(<FormTextAreaField {...defaultProps} required={true} />);

      const textarea = screen.getByTestId("test-textarea-input");
      expect(textarea.getAttribute("aria-required")).toBe("true");
    });

    it("should handle optional fields", () => {
      render(<FormTextAreaField {...defaultProps} required={false} />);

      const textarea = screen.getByTestId("test-textarea-input");
      expect(textarea.getAttribute("aria-required")).toBe("false");
    });
  });

  describe("translation integration", () => {
    it("should use translation keys for label and placeholder", () => {
      render(
        <FormTextAreaField
          {...defaultProps}
          labelKey="custom.label"
          labelDefault="Custom Label"
          placeholderKey="custom.placeholder"
          placeholderDefault="Custom placeholder"
        />,
      );

      expect(screen.getByLabelText("Custom Label")).toBeDefined();
      const textarea = screen.getByTestId("test-textarea-input");
      expect(textarea.getAttribute("placeholder")).toBe("Custom placeholder");
    });
  });

  describe("component integration", () => {
    it("should handle all props together", () => {
      render(
        <FormTextAreaField
          {...defaultProps}
          value="Sample text"
          error="Validation error"
          touched={true}
          maxCount={200}
          rows={6}
          required={false}
          enableCounter={true}
        />,
      );

      const textarea = screen.getByTestId(
        "test-textarea-input",
      ) as HTMLTextAreaElement;
      expect(textarea.value).toBe("Sample text");
      expect(textarea.getAttribute("aria-invalid")).toBe("true");
      expect(textarea.getAttribute("maxlength")).toBe("200");
      expect(textarea.getAttribute("rows")).toBe("6");
      expect(textarea.getAttribute("aria-required")).toBe("false");
      expect(screen.getByText("Validation error")).toBeDefined();
      expect(screen.getByTestId("test-textarea-counter").textContent).toBe(
        "11/200",
      );
    });

    it("should handle empty values gracefully", () => {
      render(<FormTextAreaField {...defaultProps} value="" />);

      const textarea = screen.getByTestId(
        "test-textarea-input",
      ) as HTMLTextAreaElement;
      expect(textarea.value).toBe("");
      expect(screen.getByTestId("test-textarea-counter").textContent).toBe(
        "0/350",
      );
    });
  });
});
