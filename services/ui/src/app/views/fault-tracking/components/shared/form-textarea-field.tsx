import { TextArea } from "@carbon/react";
import { useTranslation } from "react-i18next";

interface FormTextAreaFieldProps {
  id: string;
  fieldName: string;
  labelKey: string;
  labelDefault: string;
  placeholderKey: string;
  placeholderDefault: string;
  value: string;
  onChange: (value: string) => void;
  onBlur: () => void;
  error?: string;
  touched?: boolean;
  maxCount?: number;
  rows?: number;
  required?: boolean;
  enableCounter?: boolean;
}

export function FormTextAreaField({
  id,
  fieldName,
  labelKey,
  labelDefault,
  placeholderKey,
  placeholderDefault,
  value,
  onChange,
  onBlur,
  error,
  touched,
  maxCount = 350,
  rows = 4,
  required = true,
  enableCounter = true,
}: FormTextAreaFieldProps) {
  const { t } = useTranslation();

  return (
    <TextArea
      id={id}
      data-testid={`alarm-${fieldName}`}
      labelText={t(labelKey, labelDefault)}
      placeholder={t(placeholderKey, placeholderDefault)}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      invalid={touched && !!error}
      invalidText={touched ? error : undefined}
      onBlur={onBlur}
      required={required}
      maxCount={maxCount}
      rows={rows}
      enableCounter={enableCounter}
    />
  );
}
