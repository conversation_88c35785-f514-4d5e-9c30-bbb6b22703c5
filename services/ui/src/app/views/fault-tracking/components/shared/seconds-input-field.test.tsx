import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { SecondsInputField } from "./seconds-input-field";

// Mock Carbon components
vi.mock("@carbon/react", () => ({
  TextInput: ({
    id,
    labelText,
    placeholder,
    value,
    onChange,
    invalid,
    invalidText,
    onBlur,
  }: any) => (
    <div data-testid={id}>
      <label htmlFor={`${id}-input`}>{labelText}</label>
      <input
        id={`${id}-input`}
        data-testid={`${id}-input`}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        aria-invalid={invalid}
        aria-describedby={invalid ? `${id}-error` : undefined}
      />
      {invalid && invalidText && (
        <div id={`${id}-error`} data-testid={`${id}-error`}>
          {invalidText}
        </div>
      )}
    </div>
  ),
}));

describe("SecondsInputField", () => {
  const mockOnChange = vi.fn();
  const mockOnBlur = vi.fn();

  const defaultProps = {
    id: "test-seconds",
    label: "Test Seconds",
    value: "",
    onChange: mockOnChange,
    onBlur: mockOnBlur,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("rendering", () => {
    it("should render with required props", () => {
      render(<SecondsInputField {...defaultProps} />);

      expect(screen.getByTestId("test-seconds")).toBeDefined();
      expect(screen.getByLabelText("Test Seconds")).toBeDefined();
      expect(screen.getByTestId("test-seconds-input")).toBeDefined();
    });

    it("should render with default placeholder", () => {
      render(<SecondsInputField {...defaultProps} />);

      const input = screen.getByTestId("test-seconds-input");
      expect(input.getAttribute("placeholder")).toBe("00:000");
    });

    it("should render with custom placeholder", () => {
      render(<SecondsInputField {...defaultProps} placeholder="ss:mmm" />);

      const input = screen.getByTestId("test-seconds-input");
      expect(input.getAttribute("placeholder")).toBe("ss:mmm");
    });

    it("should display the provided value", () => {
      render(<SecondsInputField {...defaultProps} value="30:500" />);

      const input = screen.getByTestId(
        "test-seconds-input",
      ) as HTMLInputElement;
      expect(input.value).toBe("30:500");
    });
  });

  describe("user interactions", () => {
    it("should call onChange when user types", async () => {
      const user = userEvent.setup();
      render(<SecondsInputField {...defaultProps} />);

      const input = screen.getByTestId("test-seconds-input");
      await user.type(input, "45");

      expect(mockOnChange).toHaveBeenCalled();
      expect(mockOnChange).toHaveBeenCalledWith("5"); // Last character typed
    });

    it("should call onBlur when input loses focus", async () => {
      const user = userEvent.setup();
      render(<SecondsInputField {...defaultProps} />);

      const input = screen.getByTestId("test-seconds-input");
      await user.click(input);
      await user.tab(); // Move focus away

      expect(mockOnBlur).toHaveBeenCalledTimes(1);
    });

    it("should clear input value", async () => {
      const user = userEvent.setup();
      render(<SecondsInputField {...defaultProps} value="30:500" />);

      const input = screen.getByTestId("test-seconds-input");
      await user.clear(input);

      expect(mockOnChange).toHaveBeenCalledWith("");
    });
  });

  describe("validation states", () => {
    it("should not show error when not touched", () => {
      render(
        <SecondsInputField
          {...defaultProps}
          error="Invalid format"
          touched={false}
        />,
      );

      const input = screen.getByTestId("test-seconds-input");
      expect(input.getAttribute("aria-invalid")).toBe("false");
      expect(screen.queryByTestId("test-seconds-error")).toBe(null);
    });

    it("should not show error when touched but no error", () => {
      render(<SecondsInputField {...defaultProps} touched={true} />);

      const input = screen.getByTestId("test-seconds-input");
      expect(input.getAttribute("aria-invalid")).toBe("false");
      expect(screen.queryByTestId("test-seconds-error")).toBe(null);
    });

    it("should show error when touched and has error", () => {
      render(
        <SecondsInputField
          {...defaultProps}
          error="Invalid format"
          touched={true}
        />,
      );

      const input = screen.getByTestId("test-seconds-input");
      expect(input.getAttribute("aria-invalid")).toBe("true");
      expect(screen.getByTestId("test-seconds-error").textContent).toBe(
        "Invalid format",
      );
    });

    it("should not show error when has error but not touched", () => {
      render(
        <SecondsInputField
          {...defaultProps}
          error="Invalid format"
          touched={false}
        />,
      );

      const input = screen.getByTestId("test-seconds-input");
      expect(input.getAttribute("aria-invalid")).toBe("false");
      expect(screen.queryByTestId("test-seconds-error")).toBe(null);
    });
  });

  describe("accessibility", () => {
    it("should have proper label association", () => {
      render(<SecondsInputField {...defaultProps} />);

      const input = screen.getByTestId("test-seconds-input");
      const label = screen.getByLabelText("Test Seconds");

      expect(input).toBe(label);
    });

    it("should have aria-describedby when showing error", () => {
      render(
        <SecondsInputField
          {...defaultProps}
          error="Invalid format"
          touched={true}
        />,
      );

      const input = screen.getByTestId("test-seconds-input");
      expect(input.getAttribute("aria-describedby")).toBe("test-seconds-error");
    });

    it("should not have aria-describedby when no error", () => {
      render(<SecondsInputField {...defaultProps} />);

      const input = screen.getByTestId("test-seconds-input");
      expect(input.getAttribute("aria-describedby")).toBe(null);
    });
  });
});
