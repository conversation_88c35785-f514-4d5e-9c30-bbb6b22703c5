import { TextInput } from "@carbon/react";

interface SecondsInputFieldProps {
  id: string;
  label: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
  touched?: boolean;
  onBlur: () => void;
}

export function SecondsInputField({
  id,
  label,
  placeholder = "00:000",
  value,
  onChange,
  error,
  touched,
  onBlur,
}: SecondsInputFieldProps) {
  return (
    <TextInput
      id={id}
      labelText={label}
      placeholder={placeholder}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      invalid={touched && !!error}
      invalidText={touched ? error : undefined}
      onBlur={onBlur}
    />
  );
}
