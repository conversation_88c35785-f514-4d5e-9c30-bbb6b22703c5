import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { TimePickerField } from "./time-picker-field";

// Mock Carbon components
vi.mock("@carbon/react", () => ({
  TimePicker: ({
    id,
    labelText,
    placeholder,
    value,
    onChange,
    invalid,
    invalidText,
    onBlur,
    children,
  }: any) => (
    <div data-testid={id}>
      <label htmlFor={`${id}-input`}>{labelText}</label>
      <input
        id={`${id}-input`}
        data-testid={`${id}-input`}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        aria-invalid={invalid}
        aria-describedby={invalid ? `${id}-error` : undefined}
      />
      {invalid && invalidText && (
        <div id={`${id}-error`} data-testid={`${id}-error`}>
          {invalidText}
        </div>
      )}
      {children}
    </div>
  ),
  TimePickerSelect: ({ id, value, onChange, onBlur, children }: any) => (
    <select
      id={id}
      data-testid={id}
      value={value}
      onChange={onChange}
      onBlur={onBlur}
    >
      {children}
    </select>
  ),
  SelectItem: ({ text, value }: any) => <option value={value}>{text}</option>,
}));

describe("TimePickerField", () => {
  const mockOnTimeChange = vi.fn();
  const mockOnPeriodChange = vi.fn();
  const mockOnTimeBlur = vi.fn();
  const mockOnPeriodBlur = vi.fn();

  const defaultProps = {
    id: "test-time-picker",
    label: "Test Time",
    value: "",
    period: "AM" as const,
    onTimeChange: mockOnTimeChange,
    onPeriodChange: mockOnPeriodChange,
    onTimeBlur: mockOnTimeBlur,
    onPeriodBlur: mockOnPeriodBlur,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("rendering", () => {
    it("should render with required props", () => {
      render(<TimePickerField {...defaultProps} />);

      expect(screen.getByTestId("test-time-picker")).toBeDefined();
      expect(screen.getByLabelText("Test Time")).toBeDefined();
      expect(screen.getByTestId("test-time-picker-input")).toBeDefined();
      expect(screen.getByTestId("test-time-picker-am-pm")).toBeDefined();
    });

    it("should render with default placeholder", () => {
      render(<TimePickerField {...defaultProps} />);

      const timeInput = screen.getByTestId("test-time-picker-input");
      expect(timeInput.getAttribute("placeholder")).toBe("hh:mm");
    });

    it("should render with custom placeholder", () => {
      render(<TimePickerField {...defaultProps} placeholder="HH:MM" />);

      const timeInput = screen.getByTestId("test-time-picker-input");
      expect(timeInput.getAttribute("placeholder")).toBe("HH:MM");
    });

    it("should display the provided time value", () => {
      render(<TimePickerField {...defaultProps} value="10:30" />);

      const timeInput = screen.getByTestId(
        "test-time-picker-input",
      ) as HTMLInputElement;
      expect(timeInput.value).toBe("10:30");
    });

    it("should display the provided period value", () => {
      render(<TimePickerField {...defaultProps} period="PM" />);

      const periodSelect = screen.getByTestId(
        "test-time-picker-am-pm",
      ) as HTMLSelectElement;
      expect(periodSelect.value).toBe("PM");
    });

    it("should render AM and PM options", () => {
      render(<TimePickerField {...defaultProps} />);

      const amOption = screen.getByText("AM");
      const pmOption = screen.getByText("PM");

      expect(amOption).toBeDefined();
      expect(pmOption).toBeDefined();
    });
  });

  describe("time input interactions", () => {
    it("should call onTimeChange when time input changes", async () => {
      const user = userEvent.setup();
      render(<TimePickerField {...defaultProps} />);

      const timeInput = screen.getByTestId("test-time-picker-input");
      await user.type(timeInput, "15");

      expect(mockOnTimeChange).toHaveBeenCalled();
      expect(mockOnTimeChange).toHaveBeenCalledWith("5"); // Last character typed
    });

    it("should call onTimeBlur when time input loses focus", async () => {
      const user = userEvent.setup();
      render(<TimePickerField {...defaultProps} />);

      const timeInput = screen.getByTestId("test-time-picker-input");
      await user.click(timeInput);
      await user.tab(); // Move focus away

      expect(mockOnTimeBlur).toHaveBeenCalledTimes(1);
    });

    it("should clear time input value", async () => {
      const user = userEvent.setup();
      render(<TimePickerField {...defaultProps} value="10:30" />);

      const timeInput = screen.getByTestId("test-time-picker-input");
      await user.clear(timeInput);

      expect(mockOnTimeChange).toHaveBeenCalledWith("");
    });
  });

  describe("period select interactions", () => {
    it("should call onPeriodChange when period changes to PM", async () => {
      const user = userEvent.setup();
      render(<TimePickerField {...defaultProps} />);

      const periodSelect = screen.getByTestId("test-time-picker-am-pm");
      await user.selectOptions(periodSelect, "PM");

      expect(mockOnPeriodChange).toHaveBeenCalledWith("PM");
    });

    it("should call onPeriodChange when period changes to AM", async () => {
      const user = userEvent.setup();
      render(<TimePickerField {...defaultProps} period="PM" />);

      const periodSelect = screen.getByTestId("test-time-picker-am-pm");
      await user.selectOptions(periodSelect, "AM");

      expect(mockOnPeriodChange).toHaveBeenCalledWith("AM");
    });

    it("should call onPeriodBlur when period select loses focus", async () => {
      const user = userEvent.setup();
      render(<TimePickerField {...defaultProps} />);

      const periodSelect = screen.getByTestId("test-time-picker-am-pm");
      await user.click(periodSelect);
      await user.tab(); // Move focus away

      expect(mockOnPeriodBlur).toHaveBeenCalledTimes(1);
    });
  });

  describe("validation states", () => {
    it("should not show error when not touched", () => {
      render(
        <TimePickerField
          {...defaultProps}
          error="Invalid time format"
          touched={false}
        />,
      );

      const timeInput = screen.getByTestId("test-time-picker-input");
      expect(timeInput.getAttribute("aria-invalid")).toBe("false");
      expect(screen.queryByTestId("test-time-picker-error")).toBe(null);
    });

    it("should not show error when touched but no error", () => {
      render(<TimePickerField {...defaultProps} touched={true} />);

      const timeInput = screen.getByTestId("test-time-picker-input");
      expect(timeInput.getAttribute("aria-invalid")).toBe("false");
      expect(screen.queryByTestId("test-time-picker-error")).toBe(null);
    });

    it("should show error when touched and has error", () => {
      render(
        <TimePickerField
          {...defaultProps}
          error="Invalid time format"
          touched={true}
        />,
      );

      const timeInput = screen.getByTestId("test-time-picker-input");
      expect(timeInput.getAttribute("aria-invalid")).toBe("true");
      expect(screen.getByTestId("test-time-picker-error").textContent).toBe(
        "Invalid time format",
      );
    });

    it("should not show error when has error but not touched", () => {
      render(
        <TimePickerField
          {...defaultProps}
          error="Invalid time format"
          touched={false}
        />,
      );

      const timeInput = screen.getByTestId("test-time-picker-input");
      expect(timeInput.getAttribute("aria-invalid")).toBe("false");
      expect(screen.queryByTestId("test-time-picker-error")).toBe(null);
    });
  });

  describe("accessibility", () => {
    it("should have proper label association for time input", () => {
      render(<TimePickerField {...defaultProps} />);

      const timeInput = screen.getByTestId("test-time-picker-input");
      const label = screen.getByLabelText("Test Time");

      expect(timeInput).toBe(label);
    });

    it("should have aria-describedby when showing error", () => {
      render(
        <TimePickerField
          {...defaultProps}
          error="Invalid time format"
          touched={true}
        />,
      );

      const timeInput = screen.getByTestId("test-time-picker-input");
      expect(timeInput.getAttribute("aria-describedby")).toBe(
        "test-time-picker-error",
      );
    });

    it("should not have aria-describedby when no error", () => {
      render(<TimePickerField {...defaultProps} />);

      const timeInput = screen.getByTestId("test-time-picker-input");
      expect(timeInput.getAttribute("aria-describedby")).toBe(null);
    });

    it("should have proper id for period select", () => {
      render(<TimePickerField {...defaultProps} />);

      const periodSelect = screen.getByTestId("test-time-picker-am-pm");
      expect(periodSelect.getAttribute("id")).toBe("test-time-picker-am-pm");
    });
  });

  describe("component integration", () => {
    it("should render both time input and period select together", () => {
      render(<TimePickerField {...defaultProps} value="02:45" period="PM" />);

      const timeInput = screen.getByTestId(
        "test-time-picker-input",
      ) as HTMLInputElement;
      const periodSelect = screen.getByTestId(
        "test-time-picker-am-pm",
      ) as HTMLSelectElement;

      expect(timeInput.value).toBe("02:45");
      expect(periodSelect.value).toBe("PM");
    });

    it("should handle empty values gracefully", () => {
      render(<TimePickerField {...defaultProps} value="" period="AM" />);

      const timeInput = screen.getByTestId(
        "test-time-picker-input",
      ) as HTMLInputElement;
      const periodSelect = screen.getByTestId(
        "test-time-picker-am-pm",
      ) as HTMLSelectElement;

      expect(timeInput.value).toBe("");
      expect(periodSelect.value).toBe("AM");
    });
  });

  describe("selectedDate prop", () => {
    it("should accept selectedDate prop without affecting rendering", () => {
      const today = new Date();
      render(<TimePickerField {...defaultProps} selectedDate={today} />);

      expect(screen.getByTestId("test-time-picker")).toBeDefined();
      expect(screen.getByLabelText("Test Time")).toBeDefined();
    });

    it("should accept null selectedDate", () => {
      render(<TimePickerField {...defaultProps} selectedDate={null} />);

      expect(screen.getByTestId("test-time-picker")).toBeDefined();
      expect(screen.getByLabelText("Test Time")).toBeDefined();
    });

    it("should accept undefined selectedDate", () => {
      render(<TimePickerField {...defaultProps} selectedDate={undefined} />);

      expect(screen.getByTestId("test-time-picker")).toBeDefined();
      expect(screen.getByLabelText("Test Time")).toBeDefined();
    });

    it("should work with past dates", () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      render(<TimePickerField {...defaultProps} selectedDate={yesterday} />);

      expect(screen.getByTestId("test-time-picker")).toBeDefined();
      expect(screen.getByLabelText("Test Time")).toBeDefined();
    });

    it("should not break functionality when selectedDate changes", async () => {
      const user = userEvent.setup();
      const today = new Date();
      const { rerender } = render(
        <TimePickerField {...defaultProps} selectedDate={today} />,
      );

      const timeInput = screen.getByTestId("test-time-picker-input");
      await user.type(timeInput, "10:30");

      expect(mockOnTimeChange).toHaveBeenCalled();

      // Change selectedDate
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      rerender(<TimePickerField {...defaultProps} selectedDate={yesterday} />);

      // Should still be functional
      await user.type(timeInput, "11:45");
      expect(mockOnTimeChange).toHaveBeenCalled();
    });

    it("should handle date prop alongside other props", () => {
      const today = new Date();
      render(
        <TimePickerField
          {...defaultProps}
          selectedDate={today}
          value="14:30"
          period="PM"
          error="Test error"
          touched={true}
        />,
      );

      expect(screen.getByTestId("test-time-picker")).toBeDefined();
      expect(screen.getByDisplayValue("14:30")).toBeDefined();
      expect(screen.getByDisplayValue("PM")).toBeDefined();
      expect(screen.getByText("Test error")).toBeDefined();
    });
  });
});
