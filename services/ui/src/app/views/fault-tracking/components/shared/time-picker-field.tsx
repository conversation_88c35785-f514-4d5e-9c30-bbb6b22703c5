import { TimePicker, TimePickerSelect, SelectItem } from "@carbon/react";

interface TimePickerFieldProps {
  id: string;
  label: string;
  placeholder?: string;
  value: string;
  period: "AM" | "PM";
  onTimeChange: (time: string) => void;
  onPeriodChange: (period: "AM" | "PM") => void;
  error?: string;
  touched?: boolean;
  onTimeBlur: () => void;
  onPeriodBlur: () => void;
  selectedDate?: Date | null;
}

export function TimePickerField({
  id,
  label,
  placeholder = "hh:mm",
  value,
  period,
  onTimeChange,
  onPeriodChange,
  error,
  touched,
  onTimeBlur,
  onPeriodBlur,
}: TimePickerFieldProps) {
  return (
    <TimePicker
      id={id}
      labelText={label}
      placeholder={placeholder}
      value={value}
      onChange={(e) => onTimeChange(e.target.value)}
      invalid={touched && !!error}
      invalidText={touched ? error : undefined}
      onBlur={onTimeBlur}
    >
      <TimePickerSelect
        id={`${id}-am-pm`}
        value={period}
        onChange={(e) => onPeriodChange(e.target.value as "AM" | "PM")}
        onBlur={onPeriodBlur}
      >
        <SelectItem text="AM" value="AM" />
        <SelectItem text="PM" value="PM" />
      </TimePickerSelect>
    </TimePicker>
  );
}
