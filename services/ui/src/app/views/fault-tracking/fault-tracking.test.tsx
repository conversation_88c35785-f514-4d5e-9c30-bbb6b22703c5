import { vi } from "vitest";
import { fireEvent, render, screen } from "../../../test-utils";
import FaultTrackingView from "./fault-tracking";

// Mock the ictApi
vi.mock("../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: vi.fn(() => ({
        data: { facilityTimeZone: "America/New_York" },
        isLoading: false,
        error: null,
      })),
      useMutation: vi.fn(() => ({
        mutate: vi.fn(),
        isLoading: false,
        isError: false,
        error: null,
      })),
    },
  },
}));

// Mock the config hooks
vi.mock("../../config/hooks/use-config", () => ({
  useFeatureFlag: vi.fn(() => ({
    enabled: false,
    isLoading: false,
    error: null,
  })),
  useConfigSetting: vi.fn(() => ({
    setting: { value: "America/New_York" },
    isLoading: false,
    error: null,
  })),
}));

// Mock the ViewBar component
vi.mock("../../components/view-bar/view-bar", () => ({
  ViewBar: ({ title }: { title: string }) => (
    <div data-testid="view-bar">
      <h1>{title}</h1>
    </div>
  ),
}));

// Mock the FaultTrackingTable component
vi.mock("./components/fault-tracking-table", () => ({
  FaultTrackingTable: ({
    data,
    pagination,
    setPagination,
    sorting,
    setSorting,
    setColumnFilters,
    setGlobalFilter,
    isLoading,
    isFetching,
    error,
    rowCount,
    onRefreshClick,
    rowSelection,
    onRowSelectionChange,
    batchActions,
    actionBarItems,
  }: any) => (
    <div data-testid="fault-tracking-table">
      <div data-testid="table-data">{JSON.stringify(data)}</div>
      <div data-testid="table-pagination">{JSON.stringify(pagination)}</div>
      <div data-testid="table-sorting">{JSON.stringify(sorting)}</div>
      <div data-testid="table-loading">{String(isLoading)}</div>
      <div data-testid="table-fetching">{String(isFetching)}</div>
      <div data-testid="table-error">{error ? String(error) : "null"}</div>
      <div data-testid="table-row-count">{rowCount}</div>
      <div data-testid="table-row-selection">
        {JSON.stringify(rowSelection)}
      </div>
      <div data-testid="table-batch-actions">
        {JSON.stringify(batchActions)}
      </div>
      <div data-testid="table-action-bar-items">{actionBarItems}</div>
      <button
        data-testid="refresh-button"
        type="button"
        onClick={onRefreshClick}
      >
        Refresh
      </button>
      <button
        data-testid="change-pagination"
        type="button"
        onClick={() => setPagination({ pageIndex: 1, pageSize: 25 })}
      >
        Change Page
      </button>
      <button
        data-testid="change-sorting"
        type="button"
        onClick={() => setSorting([{ id: "faultStartTime", desc: false }])}
      >
        Change Sorting
      </button>
      <button
        data-testid="change-filters"
        type="button"
        onClick={() => setColumnFilters([{ id: "status", value: "Included" }])}
      >
        Change Filters
      </button>
      <button
        data-testid="change-global-filter"
        type="button"
        onClick={() => setGlobalFilter("test search")}
      >
        Change Global Filter
      </button>
      <button
        data-testid="select-row"
        type="button"
        onClick={() => onRowSelectionChange({ "0": true })}
      >
        Select Row
      </button>
    </div>
  ),
}));

// Mock custom hooks
vi.mock("./hooks/use-fault-tracking-data", () => ({
  useFaultTrackingData: () => ({
    data: {
      data: [
        {
          faultId: "FAULT-001",
          description: "Test fault",
          tag: "TAG001",
          status: "Included",
          location: { area: "A", section: "S1", equipment: "EQ1" },
          timing: {
            origStartTime: "2024-01-01 10:00:00 AM",
            origEndTime: "2024-01-01 11:00:00 AM",
            origDuration: "01:00:00",
          },
        },
      ],
      metadata: { totalResults: 1 },
    },
    error: null,
    isLoading: false,
    isFetching: false,
    refetch: vi.fn(),
    columnFilters: [],
    setColumnFilters: vi.fn(),
    globalFilter: "",
    setGlobalFilter: vi.fn(),
    sorting: [{ id: "faultStartTime", desc: true }],
    setSorting: vi.fn(),
    pagination: { pageIndex: 0, pageSize: 50 },
    setPagination: vi.fn(),
  }),
}));

vi.mock("./hooks/use-fault-tracking-table", () => ({
  useFaultTrackingTable: () => ({
    tableData: [
      {
        faultId: "FAULT-001",
        description: "Test fault",
        tag: "TAG001",
        status: "Included",
        location: { area: "A", section: "S1", equipment: "EQ1" },
        timing: {
          origStartTime: "2024-01-01 10:00:00 AM",
          origEndTime: "2024-01-01 11:00:00 AM",
          origDuration: "01:00:00",
        },
      },
    ],
    setTableData: vi.fn(),
    tableKey: 0,
    rowSelection: {},
    setRowSelection: vi.fn(),
    selectedFaults: [],
    columns: [],
    handleRefresh: vi.fn(),
  }),
}));

// Mock the manual entry modal hook
vi.mock("./hooks/manual-entry/use-manual-entry-modal", () => ({
  useManualEntryModal: vi.fn(() => ({
    isManualEntryModalOpen: false,
    toggleManualEntryModal: vi.fn(),
  })),
}));

// Mock the manual entry components
vi.mock("./components/manual-entry", () => ({
  ManualEntryModal: ({
    isManualEntryModalOpen,
    toggleManualEntryModal,
  }: any) => (
    <div data-testid="manual-entry-modal" data-open={isManualEntryModalOpen}>
      Manual Entry Modal
      <button onClick={toggleManualEntryModal}>Close</button>
    </div>
  ),
  ManualEntryButton: ({ toggleManualEntryModal }: any) => (
    <button data-testid="manual-entry-button" onClick={toggleManualEntryModal}>
      Add Manual Entry
    </button>
  ),
}));

// Mock the FullPageContainer component
vi.mock("../../components/full-page-container/full-page-container", () => ({
  FullPageContainer: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="full-page-container">{children}</div>
  ),
}));

// Mock react-i18next
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (_key: string, defaultValue: string) => defaultValue,
  }),
  initReactI18next: {
    type: "3rdParty",
    init: vi.fn(),
  },
  Trans: ({ children }: { children: React.ReactNode }) => children,
  I18nextProvider: ({ children }: { children: React.ReactNode }) => children,
  withTranslation: () => (Component: React.ComponentType) => Component,
}));

describe("FaultTrackingView", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render the view bar with correct title", () => {
    render(<FaultTrackingView />);

    const viewBar = screen.getByTestId("view-bar");
    expect(viewBar).toBeDefined();
    expect(screen.getByText("Fault Tracking")).toBeDefined();
  });

  it("should render the fault tracking table with correct props", () => {
    render(<FaultTrackingView />);

    const table = screen.getByTestId("fault-tracking-table");
    expect(table).toBeDefined();

    // Check that data is passed correctly
    const tableData = screen.getByTestId("table-data");
    const parsedData = JSON.parse(tableData.textContent || "[]");
    expect(parsedData).toHaveLength(1);
    expect(parsedData[0].faultId).toBe("FAULT-001");

    // Check pagination
    const tablePagination = screen.getByTestId("table-pagination");
    const parsedPagination = JSON.parse(tablePagination.textContent || "{}");
    expect(parsedPagination).toEqual({ pageIndex: 0, pageSize: 50 });

    // Check loading states
    expect(screen.getByTestId("table-loading").textContent).toBe("false");
    expect(screen.getByTestId("table-fetching").textContent).toBe("false");

    // Check row count
    expect(screen.getByTestId("table-row-count").textContent).toBe("1");

    // Check that the manual entry button is rendered as part of action bar
    expect(screen.getByTestId("manual-entry-button")).toBeDefined();
  });

  it("should handle refresh action", () => {
    render(<FaultTrackingView />);

    const refreshButton = screen.getByTestId("refresh-button");
    fireEvent.click(refreshButton);

    // Note: The actual refresh logic is handled by the hooks
    // This test verifies the UI interaction works
    expect(refreshButton).toBeDefined();
  });

  it("should handle table interactions", () => {
    render(<FaultTrackingView />);

    // Test pagination change
    const changePaginationButton = screen.getByTestId("change-pagination");
    fireEvent.click(changePaginationButton);

    // Test sorting change
    const changeSortingButton = screen.getByTestId("change-sorting");
    fireEvent.click(changeSortingButton);

    // Test filters change
    const changeFiltersButton = screen.getByTestId("change-filters");
    fireEvent.click(changeFiltersButton);

    // Test global filter change
    const changeGlobalFilterButton = screen.getByTestId("change-global-filter");
    fireEvent.click(changeGlobalFilterButton);

    // Test row selection
    const selectRowButton = screen.getByTestId("select-row");
    fireEvent.click(selectRowButton);

    // All these interactions should work without errors
    // The actual logic is handled by the custom hooks
    expect(changePaginationButton).toBeDefined();
  });

  it("should properly integrate all hooks and pass correct props", () => {
    render(<FaultTrackingView />);

    // Verify that all components are rendered and properly connected
    expect(screen.getByTestId("view-bar")).toBeDefined();
    expect(screen.getByTestId("fault-tracking-table")).toBeDefined();
    expect(screen.getByTestId("full-page-container")).toBeDefined();
    expect(screen.getByTestId("manual-entry-modal")).toBeDefined();

    // Verify that the component acts as a proper orchestrator
    // without containing business logic itself
    const tableData = screen.getByTestId("table-data");
    expect(tableData).toBeDefined();

    const batchActions = screen.getByTestId("table-batch-actions");
    expect(batchActions).toBeDefined();

    // Verify manual entry modal is rendered (but closed by default)
    const modal = screen.getByTestId("manual-entry-modal");
    expect(modal.getAttribute("data-open")).toBe("false");
  });

  it("should handle manual entry modal interactions", () => {
    render(<FaultTrackingView />);

    // Verify modal is rendered (closed by default)
    const modal = screen.getByTestId("manual-entry-modal");
    expect(modal.getAttribute("data-open")).toBe("false");

    // Verify button is rendered and can be clicked
    const manualEntryButton = screen.getByTestId("manual-entry-button");
    expect(manualEntryButton).toBeDefined();

    // Click should not throw error (actual behavior is mocked)
    fireEvent.click(manualEntryButton);
    expect(manualEntryButton).toBeDefined();
  });
});
