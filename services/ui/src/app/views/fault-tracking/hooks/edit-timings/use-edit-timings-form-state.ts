import type {
  EditTimingsForm,
  EditTimingsFormField,
} from "./use-edit-timings-form";
import { useFaultTrackingFormState } from "../shared/use-fault-tracking-form-state";
import type { FaultAlarm } from "../../types/types";
import { useEffect } from "react";
import { parseAlarmToEditTimingsFormState } from "../../utils/alarm-data-parser";

const initialFormState: EditTimingsForm = {
  startDate: null,
  endDate: null,
  startTime: "",
  startPeriod: "AM",
  endTime: "",
  endPeriod: "AM",
  startSeconds: "",
  endSeconds: "",
  comments: "",
};

export function useEditTimingsFormState(
  selectedAlarm?: FaultAlarm | null,
  modalOpen?: boolean,
  facilityTimezone?: string,
) {
  const formState = useFaultTrackingFormState<
    EditTimingsForm,
    EditTimingsFormField
  >(initialFormState);

  useEffect(() => {
    if (selectedAlarm?.id && modalOpen) {
      const alarmFormState = parseAlarmToEditTimingsFormState(
        selectedAlarm,
        facilityTimezone,
      );
      Object.entries(alarmFormState).forEach(([field, value]) => {
        formState.setField(
          field as EditTimingsFormField,
          value as EditTimingsForm[EditTimingsFormField],
        );
      });
    }
  }, [
    selectedAlarm?.id,
    selectedAlarm?.timing?.updatedStartTime,
    selectedAlarm?.timing?.updatedEndTime,
    selectedAlarm?.timing?.startTime,
    selectedAlarm?.timing?.endTime,
    selectedAlarm?.comments,
    modalOpen,
    facilityTimezone,
    formState.setField,
    formState.resetState,
  ]);

  return formState;
}
