import { useEditTimingsFormState } from "./use-edit-timings-form-state";
import { useEditTimingsValidation } from "./use-edit-timings-validation";
import {
  useFaultTrackingForm,
  getMinStartDate,
  type BaseFaultTrackingForm,
} from "../shared/use-fault-tracking-form";
import type { FaultAlarm } from "../../types/types";
import { useMemo, useCallback } from "react";
import {
  parseOriginalTimingData,
  parseAlarmToEditTimingsFormState,
} from "../../utils/alarm-data-parser";
import { hasFormDataChanged } from "../../utils/time-validation";

// Re-export for convenience
export { getMinStartDate };

export type EditTimingsForm = BaseFaultTrackingForm;

export type EditTimingsFormField = keyof EditTimingsForm;

export interface OriginalTimingData {
  startDate: Date | null;
  endDate: Date | null;
  startTime: string;
  startPeriod: "AM" | "PM";
  endTime: string;
  endPeriod: "AM" | "PM";
  startSeconds: string;
  endSeconds: string;
}

export function useEditTimingsForm(
  selectedAlarm?: FaultAlarm | null,
  modalOpen?: boolean,
  facilityTimezone?: string,
) {
  const baseForm = useFaultTrackingForm(
    () => useEditTimingsFormState(selectedAlarm, modalOpen, facilityTimezone),
    useEditTimingsValidation,
  );

  const originalData = useMemo(() => {
    if (!selectedAlarm) return null;
    return parseOriginalTimingData(selectedAlarm, facilityTimezone);
  }, [selectedAlarm, facilityTimezone]);

  const resetToOriginal = useCallback(() => {
    if (originalData) {
      baseForm.setField("startDate", originalData.startDate);
      baseForm.setField("endDate", originalData.endDate);
      baseForm.setField("startTime", originalData.startTime);
      baseForm.setField("startPeriod", originalData.startPeriod);
      baseForm.setField("endTime", originalData.endTime);
      baseForm.setField("endPeriod", originalData.endPeriod);
      baseForm.setField("startSeconds", originalData.startSeconds);
      baseForm.setField("endSeconds", originalData.endSeconds);
    }
  }, [originalData, baseForm.setField]);

  const hasChanges = useMemo(() => {
    if (!selectedAlarm) return false;

    const currentAlarmData = parseAlarmToEditTimingsFormState(
      selectedAlarm,
      facilityTimezone,
    );

    // Check if form has been initialized - if form has all empty/null values but alarm has timing data,
    // we're likely in the middle of initialization, so don't report changes yet
    const formIsCompletelyEmpty =
      !baseForm.formData.startDate &&
      !baseForm.formData.endDate &&
      !baseForm.formData.startTime &&
      !baseForm.formData.endTime &&
      !baseForm.formData.comments;

    // Focus on timing data to determine if alarm has data (comments might be undefined/empty for unedited alarms)
    const alarmHasTimingData =
      currentAlarmData.startDate ||
      currentAlarmData.endDate ||
      currentAlarmData.startTime ||
      currentAlarmData.endTime;

    if (formIsCompletelyEmpty && alarmHasTimingData) {
      return false; // Form is still being initialized
    }

    return hasFormDataChanged(baseForm.formData, currentAlarmData);
  }, [baseForm.formData, selectedAlarm, facilityTimezone]);

  return {
    ...baseForm,
    originalData,
    resetToOriginal,
    hasChanges,
  };
}
