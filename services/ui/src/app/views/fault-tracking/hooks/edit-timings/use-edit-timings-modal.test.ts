import { renderHook, act } from "@testing-library/react";
import { useEditTimingsModal } from "./use-edit-timings-modal";

describe("useEditTimingsModal", () => {
  it("should initialize with modal closed", () => {
    const { result } = renderHook(() => useEditTimingsModal());

    expect(result.current.isEditTimingsModalOpen).toBe(false);
  });

  it("should provide a toggle function", () => {
    const { result } = renderHook(() => useEditTimingsModal());

    expect(typeof result.current.toggleEditTimingsModal).toBe("function");
  });

  it("should open modal when toggle is called from closed state", () => {
    const { result } = renderHook(() => useEditTimingsModal());

    act(() => {
      result.current.toggleEditTimingsModal();
    });

    expect(result.current.isEditTimingsModalOpen).toBe(true);
  });

  it("should close modal when toggle is called from open state", () => {
    const { result } = renderHook(() => useEditTimingsModal());

    // First open the modal
    act(() => {
      result.current.toggleEditTimingsModal();
    });

    expect(result.current.isEditTimingsModalOpen).toBe(true);

    // Then close it
    act(() => {
      result.current.toggleEditTimingsModal();
    });

    expect(result.current.isEditTimingsModalOpen).toBe(false);
  });

  it("should toggle modal multiple times correctly", () => {
    const { result } = renderHook(() => useEditTimingsModal());

    // Start closed
    expect(result.current.isEditTimingsModalOpen).toBe(false);

    // Open
    act(() => {
      result.current.toggleEditTimingsModal();
    });
    expect(result.current.isEditTimingsModalOpen).toBe(true);

    // Close
    act(() => {
      result.current.toggleEditTimingsModal();
    });
    expect(result.current.isEditTimingsModalOpen).toBe(false);

    // Open again
    act(() => {
      result.current.toggleEditTimingsModal();
    });
    expect(result.current.isEditTimingsModalOpen).toBe(true);
  });

  it("should maintain state consistency across re-renders", () => {
    const { result, rerender } = renderHook(() => useEditTimingsModal());

    // Open the modal
    act(() => {
      result.current.toggleEditTimingsModal();
    });

    expect(result.current.isEditTimingsModalOpen).toBe(true);

    // Re-render the hook
    rerender();

    // State should persist
    expect(result.current.isEditTimingsModalOpen).toBe(true);
  });

  it("should return the correct interface shape", () => {
    const { result } = renderHook(() => useEditTimingsModal());

    expect(result.current).toHaveProperty("isEditTimingsModalOpen");
    expect(result.current).toHaveProperty("toggleEditTimingsModal");
    expect(Object.keys(result.current)).toHaveLength(2);
  });
});
