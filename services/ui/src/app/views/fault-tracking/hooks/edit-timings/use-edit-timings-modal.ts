import { useState } from "react";

interface EditTimingsModalState {
  isEditTimingsModalOpen: boolean;
  toggleEditTimingsModal: () => void;
}

export function useEditTimingsModal(): EditTimingsModalState {
  const [isEditTimingsModalOpen, setIsEditTimingsModalOpen] = useState(false);
  const toggleEditTimingsModal = () => {
    setIsEditTimingsModalOpen(!isEditTimingsModalOpen);
  };
  return {
    isEditTimingsModalOpen,
    toggleEditTimingsModal,
  };
}
