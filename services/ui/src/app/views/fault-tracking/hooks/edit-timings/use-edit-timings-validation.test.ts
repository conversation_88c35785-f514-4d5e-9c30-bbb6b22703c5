import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook } from "@testing-library/react";
import { useEditTimingsValidation } from "./use-edit-timings-validation";
import type { EditTimingsForm } from "./use-edit-timings-form";

// Mock the translation hook
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (_key: string, fallback: string) => fallback,
  }),
}));

// Mock the shared fault tracking validation hook
vi.mock("../shared/use-fault-tracking-validation", () => ({
  useFaultTrackingValidation: vi.fn(),
}));

import { useFaultTrackingValidation } from "../shared/use-fault-tracking-validation";

const validFormData: EditTimingsForm = {
  startDate: new Date("2024-02-15"),
  endDate: new Date("2024-02-15"),
  startTime: "10:00",
  startPeriod: "AM",
  endTime: "11:00",
  endPeriod: "AM",
  startSeconds: "",
  endSeconds: "",
  comments: "Test comment",
};

describe("useEditTimingsValidation", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useFaultTrackingValidation).mockReturnValue({
      validateForm: vi.fn().mockReturnValue({}),
      isValid: vi.fn().mockReturnValue(true),
    });
  });

  describe("basic functionality", () => {
    it("should call useFaultTrackingValidation with form data", () => {
      renderHook(() => useEditTimingsValidation(validFormData));

      expect(vi.mocked(useFaultTrackingValidation)).toHaveBeenCalledWith(
        validFormData,
      );
    });

    it("should call useFaultTrackingValidation without additional validation config", () => {
      renderHook(() => useEditTimingsValidation(validFormData));

      // Should be called with just the form data, no config object
      expect(vi.mocked(useFaultTrackingValidation)).toHaveBeenCalledWith(
        validFormData,
      );
      expect(vi.mocked(useFaultTrackingValidation)).toHaveBeenCalledTimes(1);
    });

    it("should return the result from useFaultTrackingValidation", () => {
      const mockResult = {
        validateForm: vi.fn().mockReturnValue({ startTime: "Error message" }),
        isValid: vi.fn().mockReturnValue(false),
      };
      vi.mocked(useFaultTrackingValidation).mockReturnValue(mockResult);

      const { result } = renderHook(() =>
        useEditTimingsValidation(validFormData),
      );

      expect(result.current).toBe(mockResult);
      expect(result.current.validateForm).toBe(mockResult.validateForm);
      expect(result.current.isValid).toBe(mockResult.isValid);
    });
  });

  describe("form data variations", () => {
    it("should work with empty form data", () => {
      const emptyFormData: EditTimingsForm = {
        startDate: null,
        endDate: null,
        startTime: "",
        startPeriod: "AM",
        endTime: "",
        endPeriod: "AM",
        startSeconds: "",
        endSeconds: "",
        comments: "",
      };

      renderHook(() => useEditTimingsValidation(emptyFormData));

      expect(vi.mocked(useFaultTrackingValidation)).toHaveBeenCalledWith(
        emptyFormData,
      );
    });

    it("should work with partial form data", () => {
      const partialFormData: EditTimingsForm = {
        startDate: new Date("2024-02-15"),
        endDate: null,
        startTime: "10:00",
        startPeriod: "PM",
        endTime: "",
        endPeriod: "AM",
        startSeconds: "15:500",
        endSeconds: "",
        comments: "Partial data",
      };

      renderHook(() => useEditTimingsValidation(partialFormData));

      expect(vi.mocked(useFaultTrackingValidation)).toHaveBeenCalledWith(
        partialFormData,
      );
    });

    it("should work with complete form data", () => {
      const completeFormData: EditTimingsForm = {
        startDate: new Date("2024-02-15"),
        endDate: new Date("2024-02-15"),
        startTime: "10:30",
        startPeriod: "AM",
        endTime: "11:45",
        endPeriod: "AM",
        startSeconds: "15:250",
        endSeconds: "30:750",
        comments: "Complete test comment with details",
      };

      renderHook(() => useEditTimingsValidation(completeFormData));

      expect(vi.mocked(useFaultTrackingValidation)).toHaveBeenCalledWith(
        completeFormData,
      );
    });
  });

  describe("hook behavior", () => {
    it("should re-call useFaultTrackingValidation when form data changes", () => {
      const { rerender } = renderHook(
        ({ formData }) => useEditTimingsValidation(formData),
        { initialProps: { formData: validFormData } },
      );

      expect(vi.mocked(useFaultTrackingValidation)).toHaveBeenCalledTimes(1);

      const updatedFormData = { ...validFormData, comments: "Updated comment" };
      rerender({ formData: updatedFormData });

      expect(vi.mocked(useFaultTrackingValidation)).toHaveBeenCalledTimes(2);
      expect(vi.mocked(useFaultTrackingValidation)).toHaveBeenLastCalledWith(
        updatedFormData,
      );
    });

    it("should maintain function identity when form data doesn't change", () => {
      const mockResult = {
        validateForm: vi.fn(),
        isValid: vi.fn(),
      };
      vi.mocked(useFaultTrackingValidation).mockReturnValue(mockResult);

      const { result, rerender } = renderHook(() =>
        useEditTimingsValidation(validFormData),
      );

      const firstResult = result.current;
      rerender();
      const secondResult = result.current;

      expect(firstResult).toBe(secondResult);
    });
  });

  describe("integration with fault tracking validation", () => {
    it("should pass through validation errors", () => {
      const mockErrors = {
        startDate: "Start date is required.",
        endTime: "End time is required.",
        comments: "Comments are required.",
      };
      vi.mocked(useFaultTrackingValidation).mockReturnValue({
        validateForm: vi.fn().mockReturnValue(mockErrors),
        isValid: vi.fn().mockReturnValue(false),
      });

      const { result } = renderHook(() =>
        useEditTimingsValidation(validFormData),
      );

      const errors = result.current.validateForm();
      expect(errors).toEqual(mockErrors);
      expect(result.current.isValid()).toBe(false);
    });

    it("should pass through valid state", () => {
      vi.mocked(useFaultTrackingValidation).mockReturnValue({
        validateForm: vi.fn().mockReturnValue({}),
        isValid: vi.fn().mockReturnValue(true),
      });

      const { result } = renderHook(() =>
        useEditTimingsValidation(validFormData),
      );

      const errors = result.current.validateForm();
      expect(errors).toEqual({});
      expect(result.current.isValid()).toBe(true);
    });

    it("should work with different validation function behaviors", () => {
      const mockValidateForm = vi.fn();
      const mockIsValid = vi.fn();

      vi.mocked(useFaultTrackingValidation).mockReturnValue({
        validateForm: mockValidateForm,
        isValid: mockIsValid,
      });

      const { result } = renderHook(() =>
        useEditTimingsValidation(validFormData),
      );

      // Call the validation functions
      result.current.validateForm();
      result.current.isValid();

      // Verify the underlying functions were called
      expect(mockValidateForm).toHaveBeenCalled();
      expect(mockIsValid).toHaveBeenCalled();
    });
  });

  describe("type safety", () => {
    it("should enforce EditTimingsForm type for form data", () => {
      // This test verifies that TypeScript compilation would catch type errors
      // The actual runtime behavior is tested in other tests

      const formData: EditTimingsForm = validFormData;
      renderHook(() => useEditTimingsValidation(formData));

      expect(vi.mocked(useFaultTrackingValidation)).toHaveBeenCalledWith(
        formData,
      );
    });

    it("should return properly typed validation result", () => {
      const mockResult = {
        validateForm: vi.fn().mockReturnValue({}),
        isValid: vi.fn().mockReturnValue(true),
      };
      vi.mocked(useFaultTrackingValidation).mockReturnValue(mockResult);

      const { result } = renderHook(() =>
        useEditTimingsValidation(validFormData),
      );

      // These calls should be type-safe
      expect(typeof result.current.validateForm).toBe("function");
      expect(typeof result.current.isValid).toBe("function");
    });
  });
});
