import { useFaultTrackingValidation } from "../shared/use-fault-tracking-validation";
import type {
  EditTimingsForm,
  EditTimingsFormField,
} from "./use-edit-timings-form";

export function useEditTimingsValidation(formData: EditTimingsForm) {
  // Edit timings form only has base timing fields, so no additional validation needed
  return useFaultTrackingValidation<EditTimingsForm, EditTimingsFormField>(
    formData,
  );
}
