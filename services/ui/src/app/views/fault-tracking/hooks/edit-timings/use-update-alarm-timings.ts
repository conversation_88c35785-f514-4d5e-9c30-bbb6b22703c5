import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useConfigSetting } from "../../../../config/hooks/use-config";
import { useNotification } from "../../../../components/toast/use-notification";
import { Logger } from "../../../../utils/logger";
import { formatEditTimingsForAPI } from "../../utils/edit-timings-api-formatter";
import { ictApi } from "../../../../api/ict-api";
import type { FaultAlarm } from "../../types/types";
import type { EditTimingsForm } from "./use-edit-timings-form";

const logger = new Logger("useUpdateAlarmTimings");

export function useUpdateAlarmTimings(onSuccess?: () => void) {
  const { t } = useTranslation();
  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");
  const facilityTimezone =
    (timezoneConfig?.value as string) ?? "America/New_York";
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { success, error: showError } = useNotification();

  const updateAlarmTimings = async (
    formData: EditTimingsForm,
    selectedAlarm: FaultAlarm,
  ): Promise<boolean> => {
    setIsSubmitting(true);

    try {
      const apiPayload = formatEditTimingsForAPI(
        formData,
        selectedAlarm,
        facilityTimezone,
      );

      logger.info("Submitting alarm timing update", {
        alarmId: selectedAlarm.id,
        payload: apiPayload,
      });

      const response = await ictApi.fetchClient.PUT("/availability/alarms", {
        body: apiPayload,
      });

      logger.info("Successfully updated alarm timings", {
        alarmId: selectedAlarm.id,
        response,
      });

      success(
        t(
          "faultTracking.editAlarmTimingsSavedSuccessfully",
          "Edit alarm timings saved successfully",
        ),
      );

      if (onSuccess) {
        onSuccess();
      }

      return true;
    } catch (error: unknown) {
      logger.error("Failed to update alarm timings", {
        alarmId: selectedAlarm.id,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });

      showError(
        t(
          "faultTracking.editAlarmTimingsSubmissionFailed",
          "Failed to save alarm timing changes. Please try again.",
        ),
      );
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    updateAlarmTimings,
    isSubmitting,
  };
}
