import { renderHook, act } from "@testing-library/react";
import { useManualEntryFormState } from "./use-manual-entry-form-state";

describe("useManualEntryFormState", () => {
  it("should initialize with default form state", () => {
    const { result } = renderHook(() => useManualEntryFormState());

    expect(result.current.formData).toEqual({
      startDate: null,
      endDate: null,
      startTime: "",
      startPeriod: "AM",
      endTime: "",
      endPeriod: "AM",
      startSeconds: "",
      endSeconds: "",
      description: "",
      comments: "",
      section: null,
      equipment: null,
    });
  });

  it("should initialize with empty errors and touched states", () => {
    const { result } = renderHook(() => useManualEntryFormState());

    expect(result.current.errors).toEqual({});
    expect(result.current.touched).toEqual({});
  });

  it("should provide all required functions", () => {
    const { result } = renderHook(() => useManualEntryFormState());

    expect(typeof result.current.setField).toBe("function");
    expect(typeof result.current.resetState).toBe("function");
    expect(typeof result.current.resetAll).toBe("function");
    expect(typeof result.current.setErrors).toBe("function");
    expect(typeof result.current.resetErrors).toBe("function");
    expect(typeof result.current.setFieldTouched).toBe("function");
    expect(typeof result.current.resetTouched).toBe("function");
  });

  describe("setField", () => {
    it("should update string fields", () => {
      const { result } = renderHook(() => useManualEntryFormState());

      act(() => {
        result.current.setField("description", "Test description");
      });

      expect(result.current.formData.description).toBe("Test description");
    });

    it("should update date fields", () => {
      const { result } = renderHook(() => useManualEntryFormState());
      const testDate = new Date("2024-01-15");

      act(() => {
        result.current.setField("startDate", testDate);
      });

      expect(result.current.formData.startDate).toBe(testDate);
    });

    it("should update time fields", () => {
      const { result } = renderHook(() => useManualEntryFormState());

      act(() => {
        result.current.setField("startTime", "10:30");
      });

      expect(result.current.formData.startTime).toBe("10:30");
    });

    it("should update period fields", () => {
      const { result } = renderHook(() => useManualEntryFormState());

      act(() => {
        result.current.setField("startPeriod", "PM");
      });

      expect(result.current.formData.startPeriod).toBe("PM");
    });

    it("should update multiple fields independently", () => {
      const { result } = renderHook(() => useManualEntryFormState());

      act(() => {
        result.current.setField("description", "Test");
        result.current.setField("comments", "Comments");
        result.current.setField("startTime", "09:15");
      });

      expect(result.current.formData.description).toBe("Test");
      expect(result.current.formData.comments).toBe("Comments");
      expect(result.current.formData.startTime).toBe("09:15");
      // Other fields should remain unchanged
      expect(result.current.formData.endTime).toBe("");
    });
  });

  describe("error management", () => {
    it("should set errors", () => {
      const { result } = renderHook(() => useManualEntryFormState());
      const errors = { description: "Required", comments: "Too short" };

      act(() => {
        result.current.setErrors(errors);
      });

      expect(result.current.errors).toEqual(errors);
    });

    it("should reset errors", () => {
      const { result } = renderHook(() => useManualEntryFormState());

      act(() => {
        result.current.setErrors({ description: "Required" });
      });

      expect(result.current.errors.description).toBe("Required");

      act(() => {
        result.current.resetErrors();
      });

      expect(result.current.errors).toEqual({});
    });

    it("should overwrite previous errors", () => {
      const { result } = renderHook(() => useManualEntryFormState());

      act(() => {
        result.current.setErrors({ description: "Required" });
      });

      act(() => {
        result.current.setErrors({ comments: "Too short" });
      });

      expect(result.current.errors).toEqual({ comments: "Too short" });
      expect(result.current.errors.description).toBeUndefined();
    });
  });

  describe("touched state management", () => {
    it("should mark a field as touched", () => {
      const { result } = renderHook(() => useManualEntryFormState());

      act(() => {
        result.current.setFieldTouched("description");
      });

      expect(result.current.touched.description).toBe(true);
    });

    it("should mark multiple fields as touched", () => {
      const { result } = renderHook(() => useManualEntryFormState());

      act(() => {
        result.current.setFieldTouched("description");
        result.current.setFieldTouched("comments");
      });

      expect(result.current.touched.description).toBe(true);
      expect(result.current.touched.comments).toBe(true);
    });

    it("should reset touched state", () => {
      const { result } = renderHook(() => useManualEntryFormState());

      act(() => {
        result.current.setFieldTouched("description");
        result.current.setFieldTouched("comments");
      });

      expect(result.current.touched.description).toBe(true);
      expect(result.current.touched.comments).toBe(true);

      act(() => {
        result.current.resetTouched();
      });

      expect(result.current.touched).toEqual({});
    });

    it("should maintain touched state independently of form data", () => {
      const { result } = renderHook(() => useManualEntryFormState());

      act(() => {
        result.current.setField("description", "Test");
        result.current.setFieldTouched("description");
      });

      expect(result.current.formData.description).toBe("Test");
      expect(result.current.touched.description).toBe(true);

      act(() => {
        result.current.setField("description", "Updated");
      });

      expect(result.current.formData.description).toBe("Updated");
      expect(result.current.touched.description).toBe(true); // Should remain touched
    });
  });

  describe("reset functions", () => {
    it("should reset form state only", () => {
      const { result } = renderHook(() => useManualEntryFormState());

      // Set some data
      act(() => {
        result.current.setField("description", "Test");
        result.current.setField("startTime", "10:00");
        result.current.setErrors({ description: "Error" });
        result.current.setFieldTouched("description");
      });

      // Reset only form state
      act(() => {
        result.current.resetState();
      });

      expect(result.current.formData).toEqual({
        startDate: null,
        endDate: null,
        startTime: "",
        startPeriod: "AM",
        endTime: "",
        endPeriod: "AM",
        startSeconds: "",
        endSeconds: "",
        description: "",
        comments: "",
        section: null,
        equipment: null,
      });
      // Errors and touched should remain
      expect(result.current.errors.description).toBe("Error");
      expect(result.current.touched.description).toBe(true);
    });

    it("should reset all state", () => {
      const { result } = renderHook(() => useManualEntryFormState());

      // Set some data
      act(() => {
        result.current.setField("description", "Test");
        result.current.setField("startTime", "10:00");
        result.current.setErrors({ description: "Error" });
        result.current.setFieldTouched("description");
      });

      // Reset everything
      act(() => {
        result.current.resetAll();
      });

      expect(result.current.formData).toEqual({
        startDate: null,
        endDate: null,
        startTime: "",
        startPeriod: "AM",
        endTime: "",
        endPeriod: "AM",
        startSeconds: "",
        endSeconds: "",
        description: "",
        comments: "",
        section: null,
        equipment: null,
      });
      expect(result.current.errors).toEqual({});
      expect(result.current.touched).toEqual({});
    });
  });

  describe("state isolation", () => {
    it("should handle complex state changes without side effects", () => {
      const { result } = renderHook(() => useManualEntryFormState());

      act(() => {
        // Set form data
        result.current.setField("description", "Test description");
        result.current.setField("startDate", new Date("2024-01-15"));
        result.current.setField("startTime", "10:00");
        result.current.setField("startPeriod", "PM");

        // Set errors
        result.current.setErrors({
          endDate: "Required",
          comments: "Required",
        });

        // Set touched
        result.current.setFieldTouched("description");
        result.current.setFieldTouched("startDate");
      });

      // Verify all states are independent
      expect(result.current.formData.description).toBe("Test description");
      expect(result.current.formData.startDate).toEqual(new Date("2024-01-15"));
      expect(result.current.formData.startTime).toBe("10:00");
      expect(result.current.formData.startPeriod).toBe("PM");

      expect(result.current.errors.endDate).toBe("Required");
      expect(result.current.errors.comments).toBe("Required");

      expect(result.current.touched.description).toBe(true);
      expect(result.current.touched.startDate).toBe(true);
      expect(result.current.touched.endDate).toBeUndefined();
    });

    it("should maintain immutability of state objects", () => {
      const { result } = renderHook(() => useManualEntryFormState());

      const initialFormData = result.current.formData;
      const initialErrors = result.current.errors;
      const initialTouched = result.current.touched;

      act(() => {
        result.current.setField("description", "Test");
        result.current.setErrors({ description: "Error" });
        result.current.setFieldTouched("description");
      });

      // Original objects should not be mutated
      expect(initialFormData.description).toBe("");
      expect(Object.keys(initialErrors)).toHaveLength(0);
      expect(Object.keys(initialTouched)).toHaveLength(0);

      // New objects should have the changes
      expect(result.current.formData.description).toBe("Test");
      expect(result.current.errors.description).toBe("Error");
      expect(result.current.touched.description).toBe(true);
    });
  });
});
