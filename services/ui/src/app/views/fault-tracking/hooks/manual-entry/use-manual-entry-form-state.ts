import type {
  ManualEntryForm,
  ManualEntryFormField,
} from "./use-manual-entry-form";
import { useFaultTrackingFormState } from "../shared/use-fault-tracking-form-state";

const initialFormState: ManualEntryForm = {
  startDate: null,
  endDate: null,
  startTime: "",
  startPeriod: "AM",
  endTime: "",
  endPeriod: "AM",
  startSeconds: "",
  endSeconds: "",
  description: "",
  comments: "",
  section: null,
  equipment: null,
};

export function useManualEntryFormState() {
  return useFaultTrackingFormState<ManualEntryForm, ManualEntryFormField>(
    initialFormState,
  );
}
