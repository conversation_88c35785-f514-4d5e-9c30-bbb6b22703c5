import { renderHook, act } from "@testing-library/react";
import { useManualEntryForm } from "./use-manual-entry-form";

// Mock the utility functions
vi.mock("../utils/manual-entry-validation", () => ({
  validateSecondsFormat: vi.fn((input: string) => {
    const regex = /^\d{1,2}:\d{1,3}$/;
    return regex.test(input);
  }),
  parseSecondsToMilliseconds: vi.fn((input: string) => {
    if (!input) return null;
    const parts = input.split(":");
    if (parts.length !== 2) return null;
    const sec = parseInt(parts[0], 10);
    const ms = parseInt(parts[1], 10);
    if (isNaN(sec) || isNaN(ms)) return null;
    return sec * 1000 + ms;
  }),
  parseTimeToMinutes: vi.fn((time: string, period: "AM" | "PM") => {
    if (!/^\d{1,2}:\d{2}$/.test(time)) return null;
    const [hourStr, minuteStr] = time.split(":");
    let hour = parseInt(hourStr, 10);
    const minutes = parseInt(minuteStr, 10);
    if (isNaN(hour) || isNaN(minutes)) return null;
    if (period === "AM" && hour === 12) hour = 0;
    if (period === "PM" && hour < 12) hour += 12;
    return hour * 60 + minutes;
  }),
  getMinStartDate: vi.fn(() => {
    const date = new Date();
    date.setDate(date.getDate() - 30);
    return date;
  }),
  stripTime: vi.fn((date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), date.getDate());
  }),
}));

vi.mock("../utils/duration-calculator", () => ({
  calculateFormDuration: vi.fn((formState: any) => {
    if (
      !formState.startDate ||
      !formState.endDate ||
      !formState.startTime ||
      !formState.endTime
    ) {
      return "";
    }
    return "1h 30m"; // Mock duration
  }),
}));
// temp skipping bc all of the mocks are causing heap memory issues
describe.skip("useManualEntryForm", () => {
  it("should initialize with default form state", () => {
    const { result } = renderHook(() => useManualEntryForm());

    expect(result.current.formData).toEqual({
      startDate: null,
      endDate: null,
      startTime: "",
      startPeriod: "AM",
      endTime: "",
      endPeriod: "AM",
      startSeconds: "",
      endSeconds: "",
      description: "",
      comments: "",
    });
  });

  it("should have all required functions", () => {
    const { result } = renderHook(() => useManualEntryForm());

    expect(typeof result.current.setField).toBe("function");
    expect(typeof result.current.reset).toBe("function");
    expect(typeof result.current.validate).toBe("function");
    expect(typeof result.current.setErrors).toBe("function");
    expect(typeof result.current.setFieldTouched).toBe("function");
    expect(typeof result.current.isValid).toBe("boolean");
    expect(typeof result.current.calculateDuration).toBe("function");
  });

  it("should initialize with empty errors and touched states", () => {
    const { result } = renderHook(() => useManualEntryForm());

    expect(result.current.errors).toEqual({});
    expect(result.current.touched).toEqual({});
  });

  describe("setField", () => {
    it("should update string fields", () => {
      const { result } = renderHook(() => useManualEntryForm());

      act(() => {
        result.current.setField("description", "Test description");
      });

      expect(result.current.formData.description).toBe("Test description");
    });

    it("should update date fields", () => {
      const { result } = renderHook(() => useManualEntryForm());
      const testDate = new Date("2024-01-15");

      act(() => {
        result.current.setField("startDate", testDate);
      });

      expect(result.current.formData.startDate).toBe(testDate);
    });

    it("should update time fields", () => {
      const { result } = renderHook(() => useManualEntryForm());

      act(() => {
        result.current.setField("startTime", "10:30");
      });

      expect(result.current.formData.startTime).toBe("10:30");
    });

    it("should update period fields", () => {
      const { result } = renderHook(() => useManualEntryForm());

      act(() => {
        result.current.setField("startPeriod", "PM");
      });

      expect(result.current.formData.startPeriod).toBe("PM");
    });
  });

  describe("reset", () => {
    it("should reset form to initial state", () => {
      const { result } = renderHook(() => useManualEntryForm());

      // Set some values
      act(() => {
        result.current.setField("description", "Test");
        result.current.setField("startTime", "10:30");
        result.current.setField("startDate", new Date("2024-01-15"));
      });

      // Reset
      act(() => {
        result.current.reset();
      });

      expect(result.current.formData).toEqual({
        startDate: null,
        endDate: null,
        startTime: "",
        startPeriod: "AM",
        endTime: "",
        endPeriod: "AM",
        startSeconds: "",
        endSeconds: "",
        description: "",
        comments: "",
      });
    });

    it("should reset errors and touched states", () => {
      const { result } = renderHook(() => useManualEntryForm());

      // Set some errors and touched states
      act(() => {
        result.current.setErrors({ description: "Required" });
        result.current.setFieldTouched("description");
      });

      expect(result.current.errors.description).toBe("Required");
      expect(result.current.touched.description).toBe(true);

      // Reset
      act(() => {
        result.current.reset();
      });

      expect(result.current.errors).toEqual({});
      expect(result.current.touched).toEqual({});
    });
  });

  describe("setFieldTouched", () => {
    it("should mark a field as touched", () => {
      const { result } = renderHook(() => useManualEntryForm());

      act(() => {
        result.current.setFieldTouched("description");
      });

      expect(result.current.touched.description).toBe(true);
    });

    it("should mark multiple fields as touched", () => {
      const { result } = renderHook(() => useManualEntryForm());

      act(() => {
        result.current.setFieldTouched("description");
        result.current.setFieldTouched("comments");
      });

      expect(result.current.touched.description).toBe(true);
      expect(result.current.touched.comments).toBe(true);
    });
  });

  describe("setErrors", () => {
    it("should set error state", () => {
      const { result } = renderHook(() => useManualEntryForm());

      const errors = { description: "Required", comments: "Too short" };

      act(() => {
        result.current.setErrors(errors);
      });

      expect(result.current.errors).toEqual(errors);
    });

    it("should overwrite previous errors", () => {
      const { result } = renderHook(() => useManualEntryForm());

      act(() => {
        result.current.setErrors({ description: "Required" });
      });

      act(() => {
        result.current.setErrors({ comments: "Too short" });
      });

      expect(result.current.errors).toEqual({ comments: "Too short" });
      expect(result.current.errors.description).toBeUndefined();
    });
  });

  describe("validation", () => {
    it("should validate required fields", () => {
      const { result } = renderHook(() => useManualEntryForm());

      let errors: any;
      act(() => {
        errors = result.current.validate();
      });

      expect(errors).toHaveProperty("startDate");
      expect(errors).toHaveProperty("endDate");
      expect(errors).toHaveProperty("startTime");
      expect(errors).toHaveProperty("endTime");
      expect(errors).toHaveProperty("description");
      expect(errors).toHaveProperty("comments");
    });

    it("should pass validation with valid data", () => {
      const { result } = renderHook(() => useManualEntryForm());

      // Use today's date to avoid min date validation issues
      const today = new Date();

      // Set valid data
      act(() => {
        result.current.setField("startDate", today);
        result.current.setField("endDate", today);
        result.current.setField("startTime", "10:00");
        result.current.setField("endTime", "11:00");
        result.current.setField("description", "Test description");
        result.current.setField("comments", "Test comments");
      });

      let errors: any;
      act(() => {
        errors = result.current.validate();
      });

      expect(Object.keys(errors)).toHaveLength(0);
    });

    it("should validate date order", () => {
      const { result } = renderHook(() => useManualEntryForm());

      act(() => {
        result.current.setField("startDate", new Date("2024-01-16"));
        result.current.setField("endDate", new Date("2024-01-15")); // End before start
        result.current.setField("startTime", "10:00");
        result.current.setField("endTime", "11:00");
        result.current.setField("description", "Test");
        result.current.setField("comments", "Test");
      });

      let errors: any;
      act(() => {
        errors = result.current.validate();
      });

      expect(errors.endDate).toContain("End date cannot be before start date");
    });

    it("should validate time order on same day", () => {
      const { result } = renderHook(() => useManualEntryForm());

      act(() => {
        result.current.setField("startDate", new Date("2024-01-15"));
        result.current.setField("endDate", new Date("2024-01-15"));
        result.current.setField("startTime", "11:00");
        result.current.setField("endTime", "10:00"); // End before start
        result.current.setField("description", "Test");
        result.current.setField("comments", "Test");
      });

      let errors: any;
      act(() => {
        errors = result.current.validate();
      });

      expect(errors.endTime).toContain("End time cannot be before start time");
    });
  });

  describe("isValid", () => {
    it("should return false for invalid form", () => {
      const { result } = renderHook(() => useManualEntryForm());

      const isValid = result.current.isValid;
      expect(isValid).toBe(false);
    });

    it("should return true for valid form", () => {
      const { result } = renderHook(() => useManualEntryForm());

      // Use today's date to avoid min date validation issues
      const today = new Date();

      act(() => {
        result.current.setField("startDate", today);
        result.current.setField("endDate", today);
        result.current.setField("startTime", "10:00");
        result.current.setField("endTime", "11:00");
        result.current.setField("description", "Test description");
        result.current.setField("comments", "Test comments");
      });

      const isValid = result.current.isValid;
      expect(isValid).toBe(true);
    });
  });

  describe("calculateDuration", () => {
    it("should call duration calculator with form state", () => {
      const { result } = renderHook(() => useManualEntryForm());

      act(() => {
        result.current.setField("startDate", new Date("2024-01-15"));
        result.current.setField("endDate", new Date("2024-01-15"));
        result.current.setField("startTime", "10:00");
        result.current.setField("endTime", "11:30");
      });

      const duration = result.current.calculateDuration();
      expect(duration).toBe("1h 30m");
    });

    it("should return empty string for incomplete data", () => {
      const { result } = renderHook(() => useManualEntryForm());

      const duration = result.current.calculateDuration();
      expect(duration).toBe("");
    });
  });

  describe("complex validation scenarios", () => {
    it("should validate seconds format", () => {
      const { result } = renderHook(() => useManualEntryForm());

      act(() => {
        result.current.setField("startDate", new Date("2024-01-15"));
        result.current.setField("endDate", new Date("2024-01-15"));
        result.current.setField("startTime", "10:00");
        result.current.setField("endTime", "11:00");
        result.current.setField("startSeconds", "invalid");
        result.current.setField("description", "Test");
        result.current.setField("comments", "Test");
      });

      let errors: any;
      act(() => {
        errors = result.current.validate();
      });

      expect(errors.startSeconds).toContain("Format should be ss:ms");
    });

    it("should handle same time with seconds validation", () => {
      const { result } = renderHook(() => useManualEntryForm());

      act(() => {
        result.current.setField("startDate", new Date("2024-01-15"));
        result.current.setField("endDate", new Date("2024-01-15"));
        result.current.setField("startTime", "10:00");
        result.current.setField("endTime", "10:00"); // Same time
        result.current.setField("startSeconds", "");
        result.current.setField("endSeconds", "");
        result.current.setField("description", "Test");
        result.current.setField("comments", "Test");
      });

      let errors: any;
      act(() => {
        errors = result.current.validate();
      });

      expect(errors.endTime).toContain("End time must be after start time");
    });
  });
});
