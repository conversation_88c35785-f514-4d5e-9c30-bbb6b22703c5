import { useManualEntryFormState } from "./use-manual-entry-form-state";
import { useManualEntryValidation } from "./use-manual-entry-validation";
import {
  useFaultTrackingForm,
  getMinStartDate,
  type BaseFaultTrackingForm,
} from "../shared/use-fault-tracking-form";
import type { Section, Equipment } from "../../types/types";

// Re-export for convenience
export { getMinStartDate };

export interface ManualEntryForm extends BaseFaultTrackingForm {
  description: string;
  section: Section | null;
  equipment: Equipment | null;
}
export type ManualEntryFormField = keyof ManualEntryForm;

export function useManualEntryForm() {
  return useFaultTrackingForm(
    useManualEntryFormState,
    useManualEntryValidation,
  );
}
