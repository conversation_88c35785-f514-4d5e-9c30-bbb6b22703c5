import { renderHook, act } from "@testing-library/react";
import { useManualEntryModal } from "./use-manual-entry-modal";

describe("useManualEntryModal", () => {
  it("should initialize with modal closed", () => {
    const { result } = renderHook(() => useManualEntryModal());

    expect(result.current.isManualEntryModalOpen).toBe(false);
  });

  it("should provide a toggle function", () => {
    const { result } = renderHook(() => useManualEntryModal());

    expect(typeof result.current.toggleManualEntryModal).toBe("function");
  });

  it("should open modal when toggle is called from closed state", () => {
    const { result } = renderHook(() => useManualEntryModal());

    act(() => {
      result.current.toggleManualEntryModal();
    });

    expect(result.current.isManualEntryModalOpen).toBe(true);
  });

  it("should close modal when toggle is called from open state", () => {
    const { result } = renderHook(() => useManualEntryModal());

    // First open the modal
    act(() => {
      result.current.toggleManualEntryModal();
    });

    expect(result.current.isManualEntryModalOpen).toBe(true);

    // Then close it
    act(() => {
      result.current.toggleManualEntryModal();
    });

    expect(result.current.isManualEntryModalOpen).toBe(false);
  });

  it("should toggle modal multiple times correctly", () => {
    const { result } = renderHook(() => useManualEntryModal());

    // Start closed
    expect(result.current.isManualEntryModalOpen).toBe(false);

    // Open
    act(() => {
      result.current.toggleManualEntryModal();
    });
    expect(result.current.isManualEntryModalOpen).toBe(true);

    // Close
    act(() => {
      result.current.toggleManualEntryModal();
    });
    expect(result.current.isManualEntryModalOpen).toBe(false);

    // Open again
    act(() => {
      result.current.toggleManualEntryModal();
    });
    expect(result.current.isManualEntryModalOpen).toBe(true);
  });

  it("should maintain state consistency across re-renders", () => {
    const { result, rerender } = renderHook(() => useManualEntryModal());

    // Open the modal
    act(() => {
      result.current.toggleManualEntryModal();
    });

    expect(result.current.isManualEntryModalOpen).toBe(true);

    // Re-render the hook
    rerender();

    // State should persist
    expect(result.current.isManualEntryModalOpen).toBe(true);
  });

  it("should return the correct interface shape", () => {
    const { result } = renderHook(() => useManualEntryModal());

    expect(result.current).toHaveProperty("isManualEntryModalOpen");
    expect(result.current).toHaveProperty("toggleManualEntryModal");
    expect(Object.keys(result.current)).toHaveLength(2);
  });
});
