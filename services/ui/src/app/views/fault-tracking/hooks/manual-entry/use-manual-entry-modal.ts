import { useState } from "react";

interface ManualEntryModalState {
  isManualEntryModalOpen: boolean;
  toggleManualEntryModal: () => void;
}

export function useManualEntryModal(): ManualEntryModalState {
  const [isManualEntryModalOpen, setIsManualEntryModalOpen] = useState(false);
  const toggleManualEntryModal = () => {
    setIsManualEntryModalOpen(!isManualEntryModalOpen);
  };
  return {
    isManualEntryModalOpen,
    toggleManualEntryModal,
  };
}
