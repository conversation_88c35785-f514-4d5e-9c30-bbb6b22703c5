import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useFaultTrackingValidation } from "../shared/use-fault-tracking-validation";
import type {
  ManualEntryForm,
  ManualEntryFormField,
} from "./use-manual-entry-form";

export function useManualEntryValidation(formData: ManualEntryForm) {
  const { t } = useTranslation();

  // Manual entry specific field validations
  const validateAdditionalFields = useCallback(
    (
      formData: ManualEntryForm,
    ): Partial<Record<ManualEntryFormField, string>> => {
      const errors: Partial<Record<ManualEntryFormField, string>> = {};

      if (!formData.description || !formData.description.trim())
        errors.description = t(
          "faultTracking.validation.descriptionRequired",
          "Description is required.",
        );
      if (!formData.section)
        errors.section = t(
          "faultTracking.validation.sectionRequired",
          "Section is required.",
        );
      if (!formData.equipment)
        errors.equipment = t(
          "faultTracking.validation.equipmentRequired",
          "Equipment is required.",
        );

      return errors;
    },
    [t],
  );

  return useFaultTrackingValidation<ManualEntryForm, ManualEntryFormField>(
    formData,
    { validateAdditionalFields },
  );
}
