import { renderHook, act } from "@testing-library/react";
import { useSectionEquipmentSelector } from "./use-section-equipment-selector";

// Simple mock that focuses on testing the hook logic, not API integration
vi.mock("../../../../api/ict-api", () => ({
  ictApi: {
    queryClient: {
      fetchQuery: vi.fn().mockResolvedValue({
        data: [
          { pk: 1, name: "Test Section" },
          { pk: 10, name: "Test Equipment" },
        ],
      }),
    },
    client: {
      queryOptions: vi.fn(() => ({})),
    },
  },
}));

describe("useSectionEquipmentSelector", () => {
  it("initializes with loading state", () => {
    const { result } = renderHook(() => useSectionEquipmentSelector());

    expect(result.current.isLoading).toBe(true);
    expect(result.current.isSectionLoading).toBe(false);
    expect(result.current.selectedSection).toBe(null);
    expect(result.current.selectedEquipment).toBe(null);
  });

  it("clears everything when equipment is cleared", async () => {
    const { result } = renderHook(() => useSectionEquipmentSelector());

    await act(async () => {
      await result.current.onEquipmentChange(null);
    });

    expect(result.current.selectedEquipment).toBe(null);
    expect(result.current.selectedSection).toBe(null);
    expect(result.current.isSectionLoading).toBe(false);
  });

  it("resets all state when reset is called", () => {
    const { result } = renderHook(() => useSectionEquipmentSelector());

    act(() => {
      result.current.reset();
    });

    expect(result.current.selectedSection).toBe(null);
    expect(result.current.selectedEquipment).toBe(null);
    expect(result.current.isSectionLoading).toBe(false);
  });

  it("sets section loading state when equipment changes", async () => {
    const { result } = renderHook(() => useSectionEquipmentSelector());

    // Wait for initial load to complete
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    const mockEquipment = { pk: 10, name: "Test Equipment" };

    // Start equipment change - should set loading to true
    act(() => {
      result.current.onEquipmentChange(mockEquipment);
    });

    // Should be loading immediately after equipment change
    expect(result.current.isSectionLoading).toBe(true);
    expect(result.current.selectedEquipment).toBe(mockEquipment);

    // Wait for async operation to complete
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    // Loading should be false after completion
    expect(result.current.isSectionLoading).toBe(false);
  });
});
