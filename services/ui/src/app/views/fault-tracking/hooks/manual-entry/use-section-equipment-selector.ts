import { useState, useEffect } from "react";
import { ictApi } from "../../../../api/ict-api";
import type { Section, Equipment } from "../../types/types";
import { Logger } from "../../../../utils/logger";

const logger = new Logger("SectionEquipmentSelector");

export function useSectionEquipmentSelector() {
  const [allSections, setAllSections] = useState<Section[]>([]);
  const [allEquipment, setAllEquipment] = useState<Equipment[]>([]);
  const [filteredEquipment, setFilteredEquipment] = useState<Equipment[]>([]);
  const [selectedSection, setSelectedSection] = useState<Section | null>(null);
  const [selectedEquipment, setSelectedEquipment] = useState<Equipment | null>(
    null,
  );
  const [_userSelectedSection, setUserSelectedSection] =
    useState<Section | null>(null); // Track user's original section choice
  const [isLoading, setIsLoading] = useState(true);
  const [isSectionLoading, setIsSectionLoading] = useState(false); // For equipment->section lookup
  const [error, setError] = useState<string | null>(null);

  // load all data on mount - small enough to still be performant without pagination, can scale to 1000s of sections and equipment
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        const [sectionsResponse, equipmentResponse] = await Promise.all([
          ictApi.queryClient.fetchQuery(
            ictApi.client.queryOptions("get", "/availability/sections/list", {
              params: { query: { limit: 3000 } },
            }),
          ),
          ictApi.queryClient.fetchQuery(
            ictApi.client.queryOptions("get", "/availability/equipment/list", {
              params: { query: { limit: 3000 } },
            }),
          ),
        ]);

        const sections = sectionsResponse?.data || [];
        const equipment = equipmentResponse?.data || [];

        setAllSections(sections);
        setAllEquipment(equipment);
        setFilteredEquipment(equipment);
        setError(null);
      } catch (err) {
        logger.error("Failed to load sections and equipment", {
          error: err instanceof Error ? err.message : String(err),
        });
        setError("Failed to load sections and equipment");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const onSectionChange = async (section: Section | null) => {
    setSelectedSection(section);
    setUserSelectedSection(section); // track the user's manual selection
    setSelectedEquipment(null); // clear equipment when section changes

    if (!section) {
      setFilteredEquipment(allEquipment);
      return;
    }

    try {
      const response = await ictApi.queryClient.fetchQuery(
        ictApi.client.queryOptions("get", "/availability/equipment/list", {
          params: {
            query: {
              section_id: section.pk,
              limit: 3000,
            },
          },
        }),
      );

      setFilteredEquipment(response?.data || []);
    } catch (err) {
      logger.error("Failed to load equipment for section", {
        sectionId: section?.pk,
        sectionName: section?.name,
        error: err instanceof Error ? err.message : String(err),
      });
      setFilteredEquipment([]);
    }
  };

  const onEquipmentChange = async (
    equipment: Equipment | null,
    onSectionAutoSet?: (section: Section | null) => void,
  ) => {
    setSelectedEquipment(equipment);

    if (!equipment) {
      // When equipment is cleared, always show all equipment and clear section
      // This provides a clean slate for the user which is a better UX
      setSelectedSection(null);
      setUserSelectedSection(null);
      onSectionAutoSet?.(null);
      setFilteredEquipment(allEquipment);
      setIsSectionLoading(false);
      return;
    }

    try {
      setIsSectionLoading(true); // Start loading state
      const response = await ictApi.queryClient.fetchQuery(
        ictApi.client.queryOptions("get", "/availability/sections/list", {
          params: {
            query: {
              equipment_id: equipment.pk,
              limit: 3000,
            },
          },
        }),
      );

      if (response?.data && response.data.length > 0) {
        const equipmentSection = response.data[0];
        setSelectedSection(equipmentSection);
        onSectionAutoSet?.(equipmentSection); // notify parent of auto-set section (when equipment is selected)

        // filter equipment to this section
        try {
          const equipmentResponse = await ictApi.queryClient.fetchQuery(
            ictApi.client.queryOptions("get", "/availability/equipment/list", {
              params: {
                query: {
                  section_id: equipmentSection.pk,
                  limit: 3000,
                },
              },
            }),
          );
          setFilteredEquipment(equipmentResponse?.data || []);
        } catch (equipmentErr) {
          logger.error("Failed to filter equipment for section", {
            equipmentId: equipment?.pk,
            equipmentName: equipment?.name,
            error:
              equipmentErr instanceof Error
                ? equipmentErr.message
                : String(equipmentErr),
          });
          setFilteredEquipment(allEquipment);
        }
      } else {
        setSelectedSection(null);
        onSectionAutoSet?.(null);
      }
    } catch (err) {
      logger.error("Failed to load section for equipment", {
        equipmentId: equipment?.pk,
        equipmentName: equipment?.name,
        error: err instanceof Error ? err.message : String(err),
      });
      setSelectedSection(null);
      onSectionAutoSet?.(null);
    } finally {
      setIsSectionLoading(false); // End loading state
    }
  };

  // reset all the things
  const reset = () => {
    setSelectedSection(null);
    setSelectedEquipment(null);
    setUserSelectedSection(null);
    setFilteredEquipment(allEquipment);
    setIsSectionLoading(false);
  };

  return {
    sections: allSections,
    equipment: filteredEquipment,
    selectedSection,
    selectedEquipment,
    isLoading,
    isSectionLoading,
    error,
    onSectionChange,
    onEquipmentChange,
    reset,
  };
}
