import { describe, it, expect } from "vitest";
import { renderHook, act } from "@testing-library/react";
import { useFaultTrackingFormState } from "./use-fault-tracking-form-state";
import type { BaseFaultTrackingForm } from "./use-fault-tracking-form";

interface TestForm extends BaseFaultTrackingForm {
  testField: string;
  anotherField: number;
}

type TestFormField = keyof TestForm;

const initialTestState: TestForm = {
  startDate: null,
  endDate: null,
  startTime: "",
  startPeriod: "AM",
  endTime: "",
  endPeriod: "AM",
  startSeconds: "",
  endSeconds: "",
  comments: "",
  testField: "initial",
  anotherField: 0,
};

describe("useFaultTrackingFormState", () => {
  describe("initialization", () => {
    it("should initialize with provided initial state", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      expect(result.current.formData).toEqual(initialTestState);
      expect(result.current.errors).toEqual({});
      expect(result.current.touched).toEqual({});
    });

    it("should provide all required functions", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      expect(typeof result.current.setField).toBe("function");
      expect(typeof result.current.setFieldTouched).toBe("function");
      expect(typeof result.current.setErrors).toBe("function");
      expect(typeof result.current.resetState).toBe("function");
      expect(typeof result.current.resetErrors).toBe("function");
      expect(typeof result.current.resetTouched).toBe("function");
      expect(typeof result.current.resetAll).toBe("function");
    });
  });

  describe("field management", () => {
    it("should update string fields", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      act(() => {
        result.current.setField("testField", "updated value");
      });

      expect(result.current.formData.testField).toBe("updated value");
      expect(result.current.formData.anotherField).toBe(0); // Other fields unchanged
    });

    it("should update date fields", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      const testDate = new Date("2024-02-15");

      act(() => {
        result.current.setField("startDate", testDate);
      });

      expect(result.current.formData.startDate).toBe(testDate);
    });

    it("should update number fields", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      act(() => {
        result.current.setField("anotherField", 42);
      });

      expect(result.current.formData.anotherField).toBe(42);
    });

    it("should update timing fields", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      act(() => {
        result.current.setField("startTime", "10:30");
        result.current.setField("startPeriod", "PM");
        result.current.setField("startSeconds", "15:500");
      });

      expect(result.current.formData.startTime).toBe("10:30");
      expect(result.current.formData.startPeriod).toBe("PM");
      expect(result.current.formData.startSeconds).toBe("15:500");
    });

    it("should update multiple fields independently", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      act(() => {
        result.current.setField("testField", "first update");
      });

      act(() => {
        result.current.setField("anotherField", 100);
      });

      act(() => {
        result.current.setField("comments", "test comments");
      });

      expect(result.current.formData.testField).toBe("first update");
      expect(result.current.formData.anotherField).toBe(100);
      expect(result.current.formData.comments).toBe("test comments");
      expect(result.current.formData.startTime).toBe(""); // Unchanged fields remain
    });
  });

  describe("error management", () => {
    it("should set errors", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      const errors = { testField: "Test error", startTime: "Time error" };

      act(() => {
        result.current.setErrors(errors);
      });

      expect(result.current.errors).toEqual(errors);
    });

    it("should reset errors", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      // Set some errors first
      act(() => {
        result.current.setErrors({
          testField: "Error",
          anotherField: "Another error",
        });
      });

      expect(result.current.errors).toEqual({
        testField: "Error",
        anotherField: "Another error",
      });

      // Reset errors
      act(() => {
        result.current.resetErrors();
      });

      expect(result.current.errors).toEqual({});
    });

    it("should overwrite previous errors", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      act(() => {
        result.current.setErrors({ testField: "First error" });
      });

      act(() => {
        result.current.setErrors({ anotherField: "Second error" });
      });

      expect(result.current.errors).toEqual({ anotherField: "Second error" });
      expect(result.current.errors.testField).toBeUndefined();
    });
  });

  describe("touched state management", () => {
    it("should mark a field as touched", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      act(() => {
        result.current.setFieldTouched("testField");
      });

      expect(result.current.touched.testField).toBe(true);
      expect(result.current.touched.anotherField).toBeUndefined();
    });

    it("should mark multiple fields as touched", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      act(() => {
        result.current.setFieldTouched("testField");
      });

      act(() => {
        result.current.setFieldTouched("startTime");
      });

      expect(result.current.touched.testField).toBe(true);
      expect(result.current.touched.startTime).toBe(true);
    });

    it("should reset touched state", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      // Set some touched fields first
      act(() => {
        result.current.setFieldTouched("testField");
        result.current.setFieldTouched("anotherField");
      });

      expect(result.current.touched).toEqual({
        testField: true,
        anotherField: true,
      });

      // Reset touched
      act(() => {
        result.current.resetTouched();
      });

      expect(result.current.touched).toEqual({});
    });

    it("should maintain touched state independently of form data", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      act(() => {
        result.current.setFieldTouched("testField");
        result.current.setField("testField", "new value");
      });

      expect(result.current.touched.testField).toBe(true);
      expect(result.current.formData.testField).toBe("new value");

      // Updating field doesn't affect touched state
      act(() => {
        result.current.setField("testField", "another value");
      });

      expect(result.current.touched.testField).toBe(true);
    });
  });

  describe("reset functions", () => {
    it("should reset form state only", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      // Make changes
      act(() => {
        result.current.setField("testField", "changed");
        result.current.setFieldTouched("testField");
        result.current.setErrors({ testField: "error" });
      });

      // Reset only state
      act(() => {
        result.current.resetState();
      });

      expect(result.current.formData).toEqual(initialTestState);
      expect(result.current.touched.testField).toBe(true); // Still touched
      expect(result.current.errors.testField).toBe("error"); // Still has error
    });

    it("should reset all state", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      // Make changes
      act(() => {
        result.current.setField("testField", "changed");
        result.current.setField("anotherField", 999);
        result.current.setFieldTouched("testField");
        result.current.setFieldTouched("anotherField");
        result.current.setErrors({
          testField: "error",
          anotherField: "another error",
        });
      });

      // Reset all
      act(() => {
        result.current.resetAll();
      });

      expect(result.current.formData).toEqual(initialTestState);
      expect(result.current.touched).toEqual({});
      expect(result.current.errors).toEqual({});
    });
  });

  describe("state isolation", () => {
    it("should handle complex state changes without side effects", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      act(() => {
        result.current.setField("testField", "step1");
        result.current.setFieldTouched("testField");
        result.current.setErrors({ testField: "error1" });
      });

      act(() => {
        result.current.setField("anotherField", 50);
        result.current.setFieldTouched("anotherField");
      });

      act(() => {
        result.current.setErrors({ anotherField: "error2" }); // This should replace previous errors
      });

      expect(result.current.formData.testField).toBe("step1");
      expect(result.current.formData.anotherField).toBe(50);
      expect(result.current.touched.testField).toBe(true);
      expect(result.current.touched.anotherField).toBe(true);
      expect(result.current.errors).toEqual({ anotherField: "error2" });
    });

    it("should maintain immutability of state objects", () => {
      const { result } = renderHook(() =>
        useFaultTrackingFormState<TestForm, TestFormField>(initialTestState),
      );

      const initialFormData = result.current.formData;

      act(() => {
        result.current.setField("testField", "changed");
      });

      const updatedFormData = result.current.formData;

      expect(initialFormData).not.toBe(updatedFormData); // Different object references
      expect(initialFormData.testField).toBe("initial"); // Original unchanged
      expect(updatedFormData.testField).toBe("changed"); // New object has changes
    });
  });

  describe("different initial states", () => {
    it("should work with different initial form structures", () => {
      const minimalState: BaseFaultTrackingForm = {
        startDate: null,
        endDate: null,
        startTime: "",
        startPeriod: "AM",
        endTime: "",
        endPeriod: "AM",
        startSeconds: "",
        endSeconds: "",
        comments: "",
      };

      const { result } = renderHook(() =>
        useFaultTrackingFormState<
          BaseFaultTrackingForm,
          keyof BaseFaultTrackingForm
        >(minimalState),
      );

      expect(result.current.formData).toEqual(minimalState);

      act(() => {
        result.current.setField("comments", "test");
      });

      expect(result.current.formData.comments).toBe("test");
    });

    it("should work with different field types", () => {
      interface CustomForm extends BaseFaultTrackingForm {
        customString: string;
        customNumber: number;
        customBoolean: boolean;
        customDate: Date | null;
      }

      const customState: CustomForm = {
        ...initialTestState,
        customString: "default",
        customNumber: 123,
        customBoolean: false,
        customDate: null,
      };

      const { result } = renderHook(() =>
        useFaultTrackingFormState<CustomForm, keyof CustomForm>(customState),
      );

      const testDate = new Date("2024-01-01");

      act(() => {
        result.current.setField("customString", "updated");
        result.current.setField("customNumber", 456);
        result.current.setField("customDate", testDate);
      });

      expect(result.current.formData.customString).toBe("updated");
      expect(result.current.formData.customNumber).toBe(456);
      expect(result.current.formData.customDate).toBe(testDate);
    });
  });
});
