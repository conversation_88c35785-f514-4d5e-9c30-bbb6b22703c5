import { useReducer, useState, useEffect, useRef, useCallback } from "react";
import type { Section, Equipment } from "../../types/types";

type FormAction<TForm, <PERSON><PERSON><PERSON> extends keyof TForm> =
  | {
      type: "SET_FIELD";
      field: TField;
      value: string | Date | number | Section | Equipment | null;
    }
  | { type: "RESET" };

function createFormReducer<TForm, T<PERSON><PERSON> extends keyof TForm>(
  initialState: TForm,
) {
  return function reducer(
    state: TForm,
    action: FormAction<TForm, TField>,
  ): TForm {
    switch (action.type) {
      case "SET_FIELD":
        return { ...state, [action.field]: action.value };
      case "RESET":
        return initialState;
      default:
        return state;
    }
  };
}

export function useFaultTrackingFormState<TForm, T<PERSON>ield extends keyof TForm>(
  initialFormState: TForm,
) {
  const initialStateRef = useRef<TForm>(initialFormState);
  const reducer = createFormReducer<TForm, TField>(initialFormState);
  const [formData, dispatch] = useReducer(reducer, initialFormState);
  const [errors, setErrors] = useState<Partial<Record<TField, string>>>({});
  const [touched, setTouched] = useState<Partial<Record<TField, boolean>>>({});

  // Update the reducer's initial state and reset form when initialFormState changes
  useEffect(() => {
    initialStateRef.current = initialFormState;

    // Reset to new initial state by setting each field individually
    Object.entries(initialFormState as Record<string, unknown>).forEach(
      ([field, value]) => {
        dispatch({
          type: "SET_FIELD",
          field: field as TField,
          value: value as string | Date | number | Section | Equipment | null,
        });
      },
    );
  }, [initialFormState]);
  const setField = (
    field: TField,
    value: string | Date | number | Section | Equipment | null,
  ) => {
    dispatch({ type: "SET_FIELD", field, value });
  };

  const setFieldTouched = useCallback((field: TField) => {
    setTouched((prev) => ({ ...prev, [field]: true }));
  }, []);

  const resetState = useCallback(() => {
    dispatch({ type: "RESET" });
  }, []);

  const resetErrors = useCallback(() => {
    setErrors({});
  }, []);

  const resetTouched = useCallback(() => {
    setTouched({});
  }, []);

  const resetAll = useCallback(() => {
    resetState();
    resetErrors();
    resetTouched();
  }, [resetState, resetErrors, resetTouched]);

  return {
    formData,
    setField,
    resetState,
    resetAll,
    errors,
    setErrors,
    resetErrors,
    touched,
    setFieldTouched,
    resetTouched,
  };
}
