import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook, act } from "@testing-library/react";
import {
  useFaultTrackingForm,
  type BaseFaultTrackingForm,
  type FormStateHook,
  type ValidationHook,
} from "./use-fault-tracking-form";

// Mock the calculateFormDuration function
vi.mock("../../utils/duration-calculator", () => ({
  calculateFormDuration: vi.fn(() => "2h 30m"),
}));

interface TestForm extends BaseFaultTrackingForm {
  testField: string;
}

type TestFormField = keyof TestForm;

describe("useFaultTrackingForm", () => {
  let mockFormState: FormStateHook<TestForm, TestFormField>;
  let mockValidation: ValidationHook<TestForm>;
  let useFormStateMock: () => FormStateHook<TestForm, TestFormField>;
  let useValidationMock: (formData: TestForm) => ValidationHook<TestForm>;

  const mockFormData: TestForm = {
    startDate: new Date("2024-02-15"),
    endDate: new Date("2024-02-15"),
    startTime: "10:30",
    startPeriod: "AM",
    endTime: "11:45",
    endPeriod: "AM",
    startSeconds: "15:500",
    endSeconds: "30:250",
    comments: "Test comments",
    testField: "test value",
  };

  beforeEach(() => {
    mockFormState = {
      formData: mockFormData,
      setField: vi.fn(),
      resetAll: vi.fn(),
      errors: {},
      setErrors: vi.fn(),
      touched: {},
      setFieldTouched: vi.fn(),
    };

    mockValidation = {
      validateForm: vi.fn(() => ({})),
      isValid: vi.fn(() => true),
    };

    useFormStateMock = vi.fn(() => mockFormState);
    useValidationMock = vi.fn(() => mockValidation);
  });

  describe("basic functionality", () => {
    it("should return form state and validation data", () => {
      const { result } = renderHook(() =>
        useFaultTrackingForm(useFormStateMock, useValidationMock),
      );

      expect(result.current.formData).toBe(mockFormData);
      expect(result.current.setField).toBe(mockFormState.setField);
      expect(result.current.errors).toBe(mockFormState.errors);
      expect(result.current.touched).toBe(mockFormState.touched);
      expect(result.current.setFieldTouched).toBe(
        mockFormState.setFieldTouched,
      );
      expect(result.current.isValid).toBe(true);
    });

    it("should call validation hook with form data", () => {
      renderHook(() =>
        useFaultTrackingForm(useFormStateMock, useValidationMock),
      );

      expect(useValidationMock).toHaveBeenCalledWith(mockFormData);
    });

    it("should provide calculated duration", () => {
      const { result } = renderHook(() =>
        useFaultTrackingForm(useFormStateMock, useValidationMock),
      );

      const duration = result.current.calculateDuration();
      expect(duration).toBe("2h 30m");
    });

    it("should provide reset trigger starting at 0", () => {
      const { result } = renderHook(() =>
        useFaultTrackingForm(useFormStateMock, useValidationMock),
      );

      expect(result.current.resetTrigger).toBe(0);
    });
  });

  describe("validation behavior", () => {
    it("should validate form and set errors", () => {
      const mockErrors = { startTime: "Invalid time" };
      mockValidation.validateForm = vi.fn(() => mockErrors);

      const { result } = renderHook(() =>
        useFaultTrackingForm(useFormStateMock, useValidationMock),
      );

      const errors = result.current.validate();

      expect(mockValidation.validateForm).toHaveBeenCalled();
      expect(mockFormState.setErrors).toHaveBeenCalledWith(mockErrors);
      expect(errors).toBe(mockErrors);
    });

    it("should auto-validate when form data changes and fields are touched", () => {
      mockFormState.touched = { startTime: true };
      const mockErrors = { startTime: "Invalid time" };
      mockValidation.validateForm = vi.fn(() => mockErrors);

      const { rerender } = renderHook(() =>
        useFaultTrackingForm(useFormStateMock, useValidationMock),
      );

      // Change form data
      const newFormData = { ...mockFormData, startTime: "25:00" };
      mockFormState.formData = newFormData;

      rerender();

      expect(mockValidation.validateForm).toHaveBeenCalled();
      expect(mockFormState.setErrors).toHaveBeenCalledWith(mockErrors);
    });

    it("should not auto-validate when no fields are touched", () => {
      mockFormState.touched = {};
      mockValidation.validateForm = vi.fn(() => ({}));

      const { rerender } = renderHook(() =>
        useFaultTrackingForm(useFormStateMock, useValidationMock),
      );

      // Reset the mock call count after initial render
      vi.clearAllMocks();

      // Change form data
      const newFormData = { ...mockFormData, startTime: "25:00" };
      mockFormState.formData = newFormData;

      rerender();

      // validateForm should not be called again since no fields are touched
      expect(mockValidation.validateForm).not.toHaveBeenCalled();
      expect(mockFormState.setErrors).not.toHaveBeenCalled();
    });
  });

  describe("reset functionality", () => {
    it("should reset form state and increment trigger", () => {
      const { result } = renderHook(() =>
        useFaultTrackingForm(useFormStateMock, useValidationMock),
      );

      const initialTrigger = result.current.resetTrigger;

      act(() => {
        result.current.reset();
      });

      expect(mockFormState.resetAll).toHaveBeenCalled();
      expect(result.current.resetTrigger).toBe(initialTrigger + 1);
    });

    it("should increment reset trigger each time reset is called", () => {
      const { result } = renderHook(() =>
        useFaultTrackingForm(useFormStateMock, useValidationMock),
      );

      expect(result.current.resetTrigger).toBe(0);

      act(() => {
        result.current.reset();
      });
      expect(result.current.resetTrigger).toBe(1);

      act(() => {
        result.current.reset();
      });
      expect(result.current.resetTrigger).toBe(2);
    });
  });

  describe("isValid handling", () => {
    it("should call isValid function and return the result", () => {
      mockValidation.isValid = vi.fn(() => false);

      const { result } = renderHook(() =>
        useFaultTrackingForm(useFormStateMock, useValidationMock),
      );

      expect(mockValidation.isValid).toHaveBeenCalled();
      expect(result.current.isValid).toBe(false);
    });

    it("should return true when validation isValid returns true", () => {
      mockValidation.isValid = vi.fn(() => true);

      const { result } = renderHook(() =>
        useFaultTrackingForm(useFormStateMock, useValidationMock),
      );

      expect(result.current.isValid).toBe(true);
    });
  });

  describe("hook composition", () => {
    it("should work with different form state implementations", () => {
      const alternativeFormState = {
        ...mockFormState,
        formData: { ...mockFormData, testField: "alternative value" },
      };
      const useAlternativeFormState = vi.fn(() => alternativeFormState);

      const { result } = renderHook(() =>
        useFaultTrackingForm(useAlternativeFormState, useValidationMock),
      );

      expect(result.current.formData.testField).toBe("alternative value");
      expect(useAlternativeFormState).toHaveBeenCalled();
    });

    it("should work with different validation implementations", () => {
      const alternativeValidation = {
        validateForm: vi.fn(() => ({ testField: "Alternative error" })),
        isValid: vi.fn(() => false),
      };
      const useAlternativeValidation = vi.fn(() => alternativeValidation);

      const { result } = renderHook(() =>
        useFaultTrackingForm(useFormStateMock, useAlternativeValidation),
      );

      expect(result.current.isValid).toBe(false);
      expect(useAlternativeValidation).toHaveBeenCalledWith(mockFormData);
    });
  });

  describe("edge cases", () => {
    it("should handle empty errors object", () => {
      mockFormState.errors = {};
      mockValidation.validateForm = vi.fn(() => ({}));

      const { result } = renderHook(() =>
        useFaultTrackingForm(useFormStateMock, useValidationMock),
      );

      const errors = result.current.validate();
      expect(errors).toEqual({});
    });

    it("should handle all fields touched", () => {
      mockFormState.touched = {
        startDate: true,
        endDate: true,
        startTime: true,
        startPeriod: true,
        endTime: true,
        endPeriod: true,
        startSeconds: true,
        endSeconds: true,
        comments: true,
        testField: true,
      };

      const { rerender } = renderHook(() =>
        useFaultTrackingForm(useFormStateMock, useValidationMock),
      );

      // Change form data
      mockFormState.formData = {
        ...mockFormData,
        comments: "Updated comments",
      };
      rerender();

      expect(mockValidation.validateForm).toHaveBeenCalled();
    });

    it("should handle validation function returning undefined", () => {
      mockValidation.validateForm = vi.fn(() => undefined as any);

      const { result } = renderHook(() =>
        useFaultTrackingForm(useFormStateMock, useValidationMock),
      );

      const errors = result.current.validate();
      expect(errors).toBeUndefined();
    });
  });
});
