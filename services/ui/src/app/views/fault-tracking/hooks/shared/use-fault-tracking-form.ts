import { useState, useEffect, useCallback } from "react";
import { calculateFormDuration } from "../../utils/duration-calculator";
import { getMinStartDate } from "../../utils/time-validation";

// Re-export for convenience
export { getMinStartDate };

export interface BaseFaultTrackingForm {
  startDate: Date | null;
  endDate: Date | null;
  startTime: string;
  startPeriod: "AM" | "PM";
  endTime: string;
  endPeriod: "AM" | "PM";
  startSeconds: string;
  endSeconds: string;
  comments: string;
}

export interface ValidationHook<T> {
  validateForm: () => Partial<Record<keyof T, string>>;
  isValid: () => boolean;
}

export interface FormStateHook<T, K extends keyof T> {
  formData: T;
  setField: (field: K, value: T[K]) => void;
  resetAll: () => void;
  errors: Partial<Record<K, string>>;
  setErrors: (errors: Partial<Record<K, string>>) => void;
  touched: Partial<Record<K, boolean>>;
  setFieldTouched: (field: K) => void;
}

export function useFaultTrackingForm<
  TForm extends BaseFaultTrackingForm,
  <PERSON><PERSON>ield extends keyof TForm,
>(
  useFormState: () => FormStateHook<TForm, TField>,
  useValidation: (formData: TForm) => ValidationHook<TForm>,
) {
  const {
    formData,
    setField,
    resetAll,
    errors,
    setErrors,
    touched,
    setFieldTouched,
  } = useFormState();

  const { validateForm, isValid } = useValidation(formData);

  // reset trigger for child components
  const [resetTrigger, setResetTrigger] = useState(0);

  // auto-validate when formData changes, but only if form has been touched
  useEffect(() => {
    const hasTouchedFields = Object.values(touched).some(
      (isTouched) => isTouched,
    );
    if (hasTouchedFields) {
      const validationErrors = validateForm();
      setErrors(validationErrors);
    }
  }, [formData, validateForm, setErrors, touched]);

  const validate = useCallback((): Partial<Record<TField, string>> => {
    const validationErrors = validateForm();
    setErrors(validationErrors);
    return validationErrors;
  }, [validateForm, setErrors]);

  const reset = useCallback(() => {
    resetAll();
    setResetTrigger((prev) => prev + 1);
  }, [resetAll]);

  const calculateDuration = (): string => {
    return calculateFormDuration(formData);
  };

  return {
    formData,
    setField,
    reset,
    validate,
    setErrors,
    errors,
    touched,
    setFieldTouched,
    isValid: isValid(),
    calculateDuration,
    resetTrigger,
  };
}
