import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook } from "@testing-library/react";
import { useFaultTrackingValidation } from "./use-fault-tracking-validation";
import type { BaseFaultTrackingForm } from "./use-fault-tracking-form";

// Mock react-i18next
const mockT = vi.fn((_key: string, defaultValue: string) => defaultValue);
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}));

// Mock time validation utilities with simpler implementations
vi.mock("../../utils/time-validation", () => ({
  validateSecondsFormat: vi.fn(() => true),
  parseSecondsToMilliseconds: vi.fn((value: string) => {
    if (!value) return null;
    return 1000; // Simple default
  }),
  parseTimeToMinutes: vi.fn((time: string, period: "AM" | "PM") => {
    if (!time) return null;
    if (time === "10:30" && period === "AM") return 630;
    if (time === "01:00" && period === "PM") return 780;
    return 600; // default
  }),
  getMinStartDate: vi.fn(() => new Date("2024-01-16")),
  stripTime: vi.fn((date: Date) => {
    const stripped = new Date(date);
    stripped.setHours(0, 0, 0, 0);
    return stripped;
  }),
}));

describe("useFaultTrackingValidation", () => {
  const createMockFormData = (
    overrides: Partial<BaseFaultTrackingForm> = {},
  ): BaseFaultTrackingForm => ({
    startDate: new Date("2024-02-15"),
    endDate: new Date("2024-02-15"),
    startTime: "10:30",
    startPeriod: "AM" as const,
    endTime: "01:00",
    endPeriod: "PM" as const,
    startSeconds: "15:250",
    endSeconds: "30:500",
    comments: "Test comments",
    ...overrides,
  });

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock current date to be consistent
    vi.setSystemTime(new Date("2024-02-15T14:30:00Z")); // 2:30 PM
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe("hook initialization", () => {
    it("should return validation functions", () => {
      const formData = createMockFormData();
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      expect(typeof result.current.validateForm).toBe("function");
      expect(typeof result.current.isValid).toBe("function");
    });

    it("should work with additional validation config", () => {
      const formData = createMockFormData();
      const mockValidateAdditionalFields = vi.fn(() => ({}));

      const { result } = renderHook(() =>
        useFaultTrackingValidation(formData, {
          validateAdditionalFields: mockValidateAdditionalFields,
        }),
      );

      expect(typeof result.current.validateForm).toBe("function");
      expect(typeof result.current.isValid).toBe("function");
    });
  });

  describe("required field validation", () => {
    it("should validate that start date is required", () => {
      const formData = createMockFormData({ startDate: null });
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      const errors = result.current.validateForm();
      expect(errors.startDate).toBe("Start date is required.");
      expect(mockT).toHaveBeenCalledWith(
        "faultTracking.validation.startDateRequired",
        "Start date is required.",
      );
    });

    it("should validate that end date is required", () => {
      const formData = createMockFormData({ endDate: null });
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      const errors = result.current.validateForm();
      expect(errors.endDate).toBe("End date is required.");
    });

    it("should validate that start time is required", () => {
      const formData = createMockFormData({ startTime: "" });
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      const errors = result.current.validateForm();
      expect(errors.startTime).toBe("Start time is required.");
    });

    it("should validate that end time is required", () => {
      const formData = createMockFormData({ endTime: "" });
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      const errors = result.current.validateForm();
      expect(errors.endTime).toBe("End time is required.");
    });

    it("should validate that comments are required", () => {
      const formData = createMockFormData({ comments: "" });
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      const errors = result.current.validateForm();
      expect(errors.comments).toBe("Comments are required.");
    });

    it("should validate that comments are not just whitespace", () => {
      const formData = createMockFormData({ comments: "   " });
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      const errors = result.current.validateForm();
      expect(errors.comments).toBe("Comments are required.");
    });
  });

  describe("date range validation", () => {
    it("should validate that end date is not before start date", () => {
      const startDate = new Date("2024-02-15");
      const endDate = new Date("2024-02-10"); // Before start date

      const formData = createMockFormData({
        startDate,
        endDate,
      });
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      const errors = result.current.validateForm();
      expect(errors.endDate).toBe("End date cannot be before start date");
    });

    it("should allow valid date ranges", () => {
      const startDate = new Date("2024-02-10");
      const endDate = new Date("2024-02-15");

      const formData = createMockFormData({
        startDate,
        endDate,
      });
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      const errors = result.current.validateForm();
      expect(errors.startDate).toBeUndefined();
      expect(errors.endDate).toBeUndefined();
    });
  });

  describe("utility function integration", () => {
    it("should call time validation utilities", async () => {
      const { parseTimeToMinutes, validateSecondsFormat } = await import(
        "../../utils/time-validation"
      );

      const formData = createMockFormData();
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      result.current.validateForm();

      expect(vi.mocked(parseTimeToMinutes)).toHaveBeenCalled();
      expect(vi.mocked(validateSecondsFormat)).toHaveBeenCalled();
    });

    it("should handle invalid time formats", async () => {
      const { parseTimeToMinutes } = await import(
        "../../utils/time-validation"
      );

      // Mock to return null for invalid format
      vi.mocked(parseTimeToMinutes).mockReturnValueOnce(null);

      const formData = createMockFormData({ startTime: "invalid" });
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      const errors = result.current.validateForm();
      expect(errors.startTime).toBe("Invalid time format - use HH:MM");
    });

    it("should handle invalid seconds formats", async () => {
      const { validateSecondsFormat } = await import(
        "../../utils/time-validation"
      );

      // Mock to return false for invalid format
      vi.mocked(validateSecondsFormat).mockReturnValueOnce(false);

      const formData = createMockFormData({ startSeconds: "invalid" });
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      const errors = result.current.validateForm();
      expect(errors.startSeconds).toBe("Format should be ss:ms - use 30:500");
    });
  });

  describe("additional field validation", () => {
    it("should call additional validation function when provided", () => {
      const mockValidateAdditionalFields = vi.fn(() => ({
        comments: "Custom error", // Use a field that exists in BaseFaultTrackingForm
      }));

      const formData = createMockFormData();
      const { result } = renderHook(() =>
        useFaultTrackingValidation<
          BaseFaultTrackingForm,
          keyof BaseFaultTrackingForm
        >(formData, {
          validateAdditionalFields: mockValidateAdditionalFields,
        }),
      );

      const errors = result.current.validateForm();
      expect(mockValidateAdditionalFields).toHaveBeenCalledWith(formData);
      expect(errors.comments).toBe("Custom error");
    });

    it("should merge additional errors with base validation errors", () => {
      const mockValidateAdditionalFields = vi.fn(() => ({
        startTime: "Overridden error", // This should override base validation
      }));

      const formData = createMockFormData({ startTime: "" }); // This would normally cause required error
      const { result } = renderHook(() =>
        useFaultTrackingValidation<
          BaseFaultTrackingForm,
          keyof BaseFaultTrackingForm
        >(formData, {
          validateAdditionalFields: mockValidateAdditionalFields,
        }),
      );

      const errors = result.current.validateForm();
      expect(errors.startTime).toBe("Overridden error"); // Additional validation takes precedence
    });

    it("should work without additional validation config", () => {
      const formData = createMockFormData();
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      const errors = result.current.validateForm();
      // Should validate base fields without errors
      expect(Object.keys(errors)).toHaveLength(0);
    });
  });

  describe("isValid function", () => {
    it("should return true when form is valid", () => {
      const formData = createMockFormData();
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      expect(result.current.isValid()).toBe(true);
    });

    it("should return false when form has validation errors", () => {
      const formData = createMockFormData({ startTime: "" });
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      expect(result.current.isValid()).toBe(false);
    });

    it("should return false when additional validation fails", () => {
      const mockValidateAdditionalFields = vi.fn(() => ({
        comments: "Custom error",
      }));

      const formData = createMockFormData();
      const { result } = renderHook(() =>
        useFaultTrackingValidation<
          BaseFaultTrackingForm,
          keyof BaseFaultTrackingForm
        >(formData, {
          validateAdditionalFields: mockValidateAdditionalFields,
        }),
      );

      expect(result.current.isValid()).toBe(false);
    });
  });

  describe("memoization", () => {
    it("should memoize validation function based on form data", () => {
      const formData = createMockFormData();
      const { result, rerender } = renderHook(
        ({ data }) => useFaultTrackingValidation(data),
        { initialProps: { data: formData } },
      );

      const firstValidateForm = result.current.validateForm;

      // Rerender with same data
      rerender({ data: formData });
      expect(result.current.validateForm).toBe(firstValidateForm);

      // Rerender with different data
      const newFormData = createMockFormData({
        comments: "Different comments",
      });
      rerender({ data: newFormData });
      expect(result.current.validateForm).not.toBe(firstValidateForm);
    });

    it("should memoize isValid function based on validateForm", () => {
      const formData = createMockFormData();
      const { result, rerender } = renderHook(
        ({ data }) => useFaultTrackingValidation(data),
        { initialProps: { data: formData } },
      );

      const firstIsValid = result.current.isValid;

      // Rerender with same data
      rerender({ data: formData });
      expect(result.current.isValid).toBe(firstIsValid);

      // Rerender with different data
      const newFormData = createMockFormData({
        comments: "Different comments",
      });
      rerender({ data: newFormData });
      expect(result.current.isValid).not.toBe(firstIsValid);
    });
  });

  describe("translation integration", () => {
    it("should call translation function for all error messages", () => {
      const formData = createMockFormData({
        startDate: null,
        endDate: null,
        startTime: "",
        endTime: "",
        comments: "",
      });
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      result.current.validateForm();

      expect(mockT).toHaveBeenCalledWith(
        "faultTracking.validation.startDateRequired",
        "Start date is required.",
      );
      expect(mockT).toHaveBeenCalledWith(
        "faultTracking.validation.endDateRequired",
        "End date is required.",
      );
      expect(mockT).toHaveBeenCalledWith(
        "faultTracking.validation.startTimeRequired",
        "Start time is required.",
      );
      expect(mockT).toHaveBeenCalledWith(
        "faultTracking.validation.endTimeRequired",
        "End time is required.",
      );
      expect(mockT).toHaveBeenCalledWith(
        "faultTracking.validation.commentsRequired",
        "Comments are required.",
      );
    });

    it("should return translated error messages", () => {
      mockT.mockImplementation((key: string, defaultValue: string) => {
        if (key === "faultTracking.validation.startDateRequired") {
          return "Date de début requise";
        }
        return defaultValue;
      });

      const formData = createMockFormData({ startDate: null });
      const { result } = renderHook(() => useFaultTrackingValidation(formData));

      const errors = result.current.validateForm();
      expect(errors.startDate).toBe("Date de début requise");
    });
  });
});
