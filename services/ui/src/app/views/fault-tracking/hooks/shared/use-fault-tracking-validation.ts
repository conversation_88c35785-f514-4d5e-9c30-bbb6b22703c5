import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import {
  validateSecondsFormat,
  parseSecondsToMilliseconds,
  parseTimeToMinutes,
  getMinStartDate,
  stripTime,
} from "../../utils/time-validation";
import type { BaseFaultTrackingForm } from "./use-fault-tracking-form";

/**
 * Configuration for additional field validations beyond the base timing fields
 */
export interface AdditionalValidationConfig<
  TForm extends BaseFaultTrackingForm,
  T<PERSON>ield extends keyof TForm,
> {
  /**
   * Function to validate additional fields specific to the form type
   * Should return validation errors for non-base fields
   */
  validateAdditionalFields?: (
    formData: TForm,
  ) => Partial<Record<TField, string>>;
}

/**
 * Generic fault tracking validation hook that handles common validation logic
 * @param formData - The form data to validate
 * @param config - Optional configuration for additional field validations
 * @returns Validation functions and state
 */
export function useFaultTrackingValidation<
  TForm extends BaseFaultTrackingForm,
  <PERSON><PERSON><PERSON> extends keyof TForm,
>(formData: TForm, config: AdditionalValidationConfig<TForm, TField> = {}) {
  const { t } = useTranslation();
  const { validateAdditionalFields } = config;

  const validateForm = useCallback((): Partial<Record<TField, string>> => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const errors: any = {};

    // Base required field validation (common to all fault tracking forms)
    if (!formData.startDate)
      errors.startDate = t(
        "faultTracking.validation.startDateRequired",
        "Start date is required.",
      ) as TForm[TField];
    if (!formData.endDate)
      errors.endDate = t(
        "faultTracking.validation.endDateRequired",
        "End date is required.",
      ) as TForm[TField];
    if (!formData.startTime)
      errors.startTime = t(
        "faultTracking.validation.startTimeRequired",
        "Start time is required.",
      ) as TForm[TField];
    if (!formData.endTime)
      errors.endTime = t(
        "faultTracking.validation.endTimeRequired",
        "End time is required.",
      ) as TForm[TField];
    if (!formData.comments || !formData.comments.trim())
      errors.comments = t(
        "faultTracking.validation.commentsRequired",
        "Comments are required.",
      ) as TForm[TField];

    // Additional field validation (form-specific)
    if (validateAdditionalFields) {
      const additionalErrors = validateAdditionalFields(formData);
      Object.assign(errors, additionalErrors);
    }

    // Time format validation
    if (
      formData.startTime &&
      parseTimeToMinutes(formData.startTime, formData.startPeriod) === null
    ) {
      errors.startTime = t(
        "faultTracking.validation.invalidTimeFormat",
        "Invalid time format - use HH:MM",
      ) as TForm[TField];
    }
    if (
      formData.endTime &&
      parseTimeToMinutes(formData.endTime, formData.endPeriod) === null
    ) {
      errors.endTime = t(
        "faultTracking.validation.invalidTimeFormat",
        "Invalid time format - use HH:MM",
      ) as TForm[TField];
    }

    // Seconds format validation
    if (
      formData.startSeconds &&
      !validateSecondsFormat(formData.startSeconds)
    ) {
      errors.startSeconds = t(
        "faultTracking.validation.invalidSecondsFormat",
        "Format should be ss:ms - use 30:500",
      ) as TForm[TField];
    }
    if (formData.endSeconds && !validateSecondsFormat(formData.endSeconds)) {
      errors.endSeconds = t(
        "faultTracking.validation.invalidSecondsFormat",
        "Format should be ss:ms - use 30:500",
      ) as TForm[TField];
    }

    // Date range validation
    if (
      formData.startDate &&
      stripTime(formData.startDate) < stripTime(getMinStartDate())
    ) {
      errors.startDate = t(
        "faultTracking.validation.startDateTooOld",
        "Start date cannot be more than 30 days ago",
      ) as TForm[TField];
    }

    // Future date validation
    const today = new Date();
    if (
      formData.startDate &&
      stripTime(formData.startDate) > stripTime(today)
    ) {
      errors.startDate = t(
        "faultTracking.validation.startDateInFuture",
        "Start date cannot be in the future",
      ) as TForm[TField];
    }

    if (formData.endDate && stripTime(formData.endDate) > stripTime(today)) {
      errors.endDate = t(
        "faultTracking.validation.endDateInFuture",
        "End date cannot be in the future",
      ) as TForm[TField];
    }

    if (
      formData.startDate &&
      formData.endDate &&
      formData.endDate < formData.startDate
    ) {
      errors.endDate = t(
        "faultTracking.validation.endDateBeforeStart",
        "End date cannot be before start date",
      ) as TForm[TField];
    }

    // Future time validation (for same-day entries)
    const now = new Date();
    const currentTimeMinutes = now.getHours() * 60 + now.getMinutes();

    if (
      formData.startDate &&
      formData.startTime &&
      stripTime(formData.startDate).getTime() === stripTime(now).getTime()
    ) {
      const startTimeMinutes = parseTimeToMinutes(
        formData.startTime,
        formData.startPeriod,
      );
      if (startTimeMinutes !== null && startTimeMinutes > currentTimeMinutes) {
        errors.startTime = t(
          "faultTracking.validation.startTimeInFuture",
          "Start time cannot be in the future",
        ) as TForm[TField];
      }
    }

    if (
      formData.endDate &&
      formData.endTime &&
      stripTime(formData.endDate).getTime() === stripTime(now).getTime()
    ) {
      const endTimeMinutes = parseTimeToMinutes(
        formData.endTime,
        formData.endPeriod,
      );
      if (endTimeMinutes !== null && endTimeMinutes > currentTimeMinutes) {
        errors.endTime = t(
          "faultTracking.validation.endTimeInFuture",
          "End time cannot be in the future",
        ) as TForm[TField];
      }
    }

    // Time validation
    const start = parseTimeToMinutes(formData.startTime, formData.startPeriod);
    const end = parseTimeToMinutes(formData.endTime, formData.endPeriod);
    if (start !== null && end !== null) {
      const sameDay =
        formData.startDate &&
        formData.endDate &&
        formData.startDate.toDateString() === formData.endDate.toDateString();

      if (sameDay && end < start) {
        errors.endTime = t(
          "faultTracking.validation.endTimeBeforeStart",
          "End time cannot be before start time",
        ) as TForm[TField];
      } else if (sameDay && end === start) {
        // Same date and time - check seconds:milliseconds
        const startMs = parseSecondsToMilliseconds(formData.startSeconds);
        const endMs = parseSecondsToMilliseconds(formData.endSeconds);

        if (startMs !== null && endMs !== null) {
          // Both fields have values - compare them
          if (endMs <= startMs) {
            errors.endSeconds = t(
              "faultTracking.validation.endSecondsBeforeStart",
              "End seconds:milliseconds must be after start seconds:milliseconds",
            ) as TForm[TField];
          }
        } else if (startMs === null && endMs === null) {
          // Both fields are empty - start time equals end time, which is invalid
          errors.endTime = t(
            "faultTracking.validation.endTimeEqualsStart",
            "End time must be after start time (add seconds:milliseconds for precision)",
          ) as TForm[TField];
        } else if (startMs !== null && endMs === null) {
          // Start has seconds but end doesn't - end time is effectively earlier
          errors.endSeconds = t(
            "faultTracking.validation.endSecondsRequired",
            "End seconds:milliseconds required when start seconds:milliseconds is specified",
          ) as TForm[TField];
        } else if (startMs === null && endMs !== null) {
          // End has seconds but start doesn't - this is valid (end is later)
          // No error needed
        }
      }
    }

    return errors;
  }, [formData, t, validateAdditionalFields]);

  const isValid = useCallback(() => {
    return Object.keys(validateForm()).length === 0;
  }, [validateForm]);

  return {
    validateForm,
    isValid,
  };
}
