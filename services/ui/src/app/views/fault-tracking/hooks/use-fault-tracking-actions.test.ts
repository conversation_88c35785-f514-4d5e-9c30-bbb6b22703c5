import { renderHook } from "@testing-library/react";
import { useFaultTrackingActions } from "./use-fault-tracking-actions";
import { vi } from "vitest";

// Mock the dependencies
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: vi.fn((_key: string, defaultValue: string) => defaultValue),
  }),
}));

vi.mock("../../../config/hooks/use-config", () => ({
  useFeatureFlag: vi.fn(),
}));

import * as configHooks from "../../../config/hooks/use-config";
const mockUseFeatureFlag = vi.mocked(configHooks.useFeatureFlag);

describe("useFaultTrackingActions", () => {
  const mockToggleEditTimingsModal = vi.fn();
  const defaultProps = {
    toggleEditTimingsModal: mockToggleEditTimingsModal,
    selectedRowsCount: 1,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should return empty batch actions when feature flags are disabled", () => {
    mockUseFeatureFlag.mockReturnValue({
      enabled: false,
      isLoading: false,
      error: null,
    });

    const { result } = renderHook(() => useFaultTrackingActions(defaultProps));

    expect(result.current.batchActions).toEqual([]);
  });

  it("should return edit timings batch action when feature flag is enabled", () => {
    mockUseFeatureFlag
      .mockReturnValueOnce({
        enabled: true, // edit timings enabled
        isLoading: false,
        error: null,
      })
      .mockReturnValueOnce({
        enabled: false, // calculation status disabled
        isLoading: false,
        error: null,
      });

    const { result } = renderHook(() => useFaultTrackingActions(defaultProps));

    expect(result.current.batchActions).toHaveLength(1);
    expect(result.current.batchActions[0]).toEqual({
      label: "Edit Alarm Timings",
      onClick: expect.any(Function),
      disabled: false, // selectedRowsCount is 1, so disabled should be false (1 > 1 = false)
    });
  });

  it("should call toggleEditTimingsModal when edit action is clicked", () => {
    mockUseFeatureFlag
      .mockReturnValueOnce({
        enabled: true, // edit timings enabled
        isLoading: false,
        error: null,
      })
      .mockReturnValueOnce({
        enabled: false, // calculation status disabled
        isLoading: false,
        error: null,
      });

    const { result } = renderHook(() => useFaultTrackingActions(defaultProps));

    const editAction = result.current.batchActions[0];
    editAction.onClick();

    expect(mockToggleEditTimingsModal).toHaveBeenCalledTimes(1);
  });

  it("should call useFeatureFlag with correct flag names", () => {
    mockUseFeatureFlag.mockReturnValue({
      enabled: false,
      isLoading: false,
      error: null,
    });

    renderHook(() => useFaultTrackingActions(defaultProps));

    expect(mockUseFeatureFlag).toHaveBeenCalledWith(
      "fault-tracking-edit-timings-button",
    );
    expect(mockUseFeatureFlag).toHaveBeenCalledWith(
      "fault-tracking-calculation-status-button",
    );
  });

  it("should maintain function stability across re-renders", () => {
    mockUseFeatureFlag.mockReturnValue({
      enabled: true,
      isLoading: false,
      error: null,
    });

    const { result, rerender } = renderHook(() =>
      useFaultTrackingActions(defaultProps),
    );

    const firstRender = result.current;

    rerender();

    const secondRender = result.current;

    // The batchActions array should be referentially stable when feature flags don't change
    expect(firstRender.batchActions[0].onClick).toBe(
      secondRender.batchActions[0].onClick,
    );
  });

  it("should disable edit action when more than one row is selected", () => {
    mockUseFeatureFlag
      .mockReturnValueOnce({
        enabled: true, // edit timings enabled
        isLoading: false,
        error: null,
      })
      .mockReturnValueOnce({
        enabled: false, // calculation status disabled
        isLoading: false,
        error: null,
      });

    const { result } = renderHook(() =>
      useFaultTrackingActions({
        ...defaultProps,
        selectedRowsCount: 2, // More than 1, so should be disabled
      }),
    );

    expect(result.current.batchActions[0]).toEqual({
      label: "Edit Alarm Timings",
      onClick: expect.any(Function),
      disabled: true,
    });
  });

  it("should enable edit action when exactly one row is selected", () => {
    mockUseFeatureFlag
      .mockReturnValueOnce({
        enabled: true, // edit timings enabled
        isLoading: false,
        error: null,
      })
      .mockReturnValueOnce({
        enabled: false, // calculation status disabled
        isLoading: false,
        error: null,
      });

    const { result } = renderHook(() =>
      useFaultTrackingActions({
        ...defaultProps,
        selectedRowsCount: 1, // Exactly 1, so should be enabled (1 > 1 = false)
      }),
    );

    expect(result.current.batchActions[0]).toEqual({
      label: "Edit Alarm Timings",
      onClick: expect.any(Function),
      disabled: false,
    });
  });

  it("should update disabled state when selectedRowsCount changes", () => {
    mockUseFeatureFlag.mockReturnValue({
      enabled: true,
      isLoading: false,
      error: null,
    });

    const { result, rerender } = renderHook(
      ({ selectedRowsCount }) =>
        useFaultTrackingActions({
          ...defaultProps,
          selectedRowsCount,
        }),
      {
        initialProps: { selectedRowsCount: 2 }, // More than 1, so disabled
      },
    );

    // Initially disabled (2 > 1 = true)
    expect(result.current.batchActions[0].disabled).toBe(true);

    // Re-render with exactly one selected row
    rerender({ selectedRowsCount: 1 });
    expect(result.current.batchActions[0].disabled).toBe(false);

    // Re-render with multiple selected rows again
    rerender({ selectedRowsCount: 3 });
    expect(result.current.batchActions[0].disabled).toBe(true);
  });

  it("should return both batch actions when both feature flags are enabled", () => {
    mockUseFeatureFlag
      .mockReturnValueOnce({
        enabled: true, // edit timings enabled
        isLoading: false,
        error: null,
      })
      .mockReturnValueOnce({
        enabled: true, // calculation status enabled
        isLoading: false,
        error: null,
      });

    const { result } = renderHook(() => useFaultTrackingActions(defaultProps));

    expect(result.current.batchActions).toHaveLength(2);
    expect(result.current.batchActions[0].label).toBe("Edit Alarm Timings");
    expect(result.current.batchActions[1].label).toBe(
      "Update Calculation Status",
    );
  });
});
