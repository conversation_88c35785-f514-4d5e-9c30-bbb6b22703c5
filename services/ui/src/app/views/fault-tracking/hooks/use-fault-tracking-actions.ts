import { useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useFeatureFlag } from "../../../config/hooks/use-config";

interface FaultTrackingActionsProps {
  toggleEditTimingsModal: () => void;
  selectedRowsCount: number;
}

export function useFaultTrackingActions({
  toggleEditTimingsModal,
  selectedRowsCount,
}: FaultTrackingActionsProps) {
  const { t } = useTranslation();

  const { enabled: editTimingsButtonEnabled } = useFeatureFlag(
    "fault-tracking-edit-timings-button",
  );
  const { enabled: calculationStatusButtonEnabled } = useFeatureFlag(
    "fault-tracking-calculation-status-button",
  );

  const handleEditTimingsClick = useCallback(() => {
    toggleEditTimingsModal();
  }, [toggleEditTimingsModal]);

  const batchActions = useMemo(() => {
    const actions = [];

    if (editTimingsButtonEnabled) {
      actions.push({
        label: t("faultTracking.editAlarmTimings", "Edit Alarm Timings"),
        onClick: handleEditTimingsClick,
        disabled: selectedRowsCount > 1,
        // figure out why tooltip is not working
      });
    }
    // TODO: disable logic needed
    if (calculationStatusButtonEnabled) {
      actions.push({
        label: t(
          "faultTracking.updateCalculationStatus",
          "Update Calculation Status",
        ),
        onClick: () => {},
        disabled: true,
      });
    }

    return actions;
  }, [
    editTimingsButtonEnabled,
    calculationStatusButtonEnabled,
    t,
    handleEditTimingsClick,
    selectedRowsCount,
  ]);

  return {
    batchActions,
  };
}
