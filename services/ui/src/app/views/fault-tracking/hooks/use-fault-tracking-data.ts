import { useMemo, useState } from "react";
import type { ColumnFiltersState, SortingState } from "@tanstack/react-table";
import { ictApi } from "../../../api/ict-api";
import { transformFilters } from "../../../api/util/filter-transform-util";
import type { SortField, AlarmListResponse } from "../types/types";

/**
 * Custom hook for managing fault tracking API data and filters
 */
export function useFaultTrackingData() {
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const [sorting, setSorting] = useState<SortingState>([
    { id: "faultStartTime", desc: true },
  ]);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 50,
  });

  const [dateRange] = useState({
    start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    end_date: new Date(),
  });

  // Transform filters for API
  const apiFilters = useMemo(() => {
    return transformFilters(columnFilters);
  }, [columnFilters]);

  // Transform sorting for API
  const sortFields: SortField[] = useMemo(() => {
    return sorting.map((sort) => {
      let columnName = sort.id;
      if (
        sort.id === "sectionArea" ||
        sort.id === "location.area" ||
        sort.id === "location_area"
      ) {
        columnName = "area";
      } else if (
        sort.id === "sectionName" ||
        sort.id === "location.section" ||
        sort.id === "location_section"
      ) {
        columnName = "section";
      } else if (
        sort.id === "equipmentName" ||
        sort.id === "location.equipment" ||
        sort.id === "location_equipment"
      ) {
        columnName = "equipment";
      } else if (sort.id === "alarmDescription" || sort.id === "description") {
        columnName = "description";
      } else if (sort.id === "faultStartTime") {
        columnName = "startTime";
      } else if (sort.id === "faultEndTime") {
        columnName = "endTime";
      } else if (sort.id === "faultId" || sort.id === "id") {
        columnName = "faultId";
      } else if (sort.id === "faultDuration") {
        columnName = "duration";
      } else if (sort.id === "removalStatus" || sort.id === "status") {
        columnName = "status";
      } else if (
        sort.id === "timing.updatedDuration" ||
        sort.id === "timing_updatedDuration"
      ) {
        columnName = "updatedDuration";
      } else if (
        sort.id === "timing.origDuration" ||
        sort.id === "timing_origDuration"
      ) {
        columnName = "origDuration";
      } else if (
        sort.id === "timing.origStartTime" ||
        sort.id === "timing_origStartTime"
      ) {
        columnName = "origStartTime";
      } else if (
        sort.id === "timing.origEndTime" ||
        sort.id === "timing_origEndTime"
      ) {
        columnName = "origEndTime";
      } else if (
        sort.id === "timing.updatedStartTime" ||
        sort.id === "timing_updatedStartTime"
      ) {
        columnName = "updatedStartTime";
      } else if (
        sort.id === "timing.updatedEndTime" ||
        sort.id === "timing_updatedEndTime"
      ) {
        columnName = "updatedEndTime";
      } else if (sort.id === "comments") {
        columnName = "comments";
      }
      return {
        columnName,
        isDescending: sort.desc,
      };
    });
  }, [sorting]);

  // Build API request body
  const apiRequestBody = useMemo(() => {
    const body = {
      start_date: dateRange.start_date.toISOString(),
      end_date: dateRange.end_date.toISOString(),
      limit: pagination.pageSize,
      page: pagination.pageIndex + 1, // Convert 0-based to 1-based page number
      filters: apiFilters,
      sortFields,
      ...(globalFilter !== "" && { searchString: globalFilter }),
    };
    return body;
  }, [
    dateRange.start_date,
    dateRange.end_date,
    pagination.pageSize,
    pagination.pageIndex,
    apiFilters,
    sortFields,
    globalFilter,
  ]);

  // API query
  const { data, error, isLoading, isFetching, refetch } =
    ictApi.client.useQuery(
      "post",
      "/availability/alarms/list",
      {
        body: apiRequestBody,
      },
      {
        enabled: true,
        keepPreviousData: true,
        placeholderData: (prev) => prev,
        retry: false,
        refetchOnWindowFocus: false,
        staleTime: 30000,
        cacheTime: 300000,
        onSuccess: (_data: AlarmListResponse) => {},
      },
    );

  return {
    // Data
    data,
    error,
    isLoading,
    isFetching,
    refetch,

    // Filters
    columnFilters,
    setColumnFilters,
    globalFilter,
    setGlobalFilter,

    // Sorting
    sorting,
    setSorting,

    // Pagination
    pagination,
    setPagination,
  };
}
