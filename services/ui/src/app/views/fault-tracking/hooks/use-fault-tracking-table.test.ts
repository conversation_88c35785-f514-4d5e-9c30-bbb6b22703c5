import { renderHook, act } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import { useFaultTrackingTable } from "./use-fault-tracking-table";
import type { FaultAlarm } from "../types/types";
import { transformFaultData } from "../utils/cell-data-transforms";
import { useConfigSetting } from "../../../config/hooks/use-config";

// Mock the utilities and Carbon components
vi.mock("../utils/time-formatting", () => ({
  formatFaultTrackingDuration: vi.fn((duration) => `formatted-${duration}`),
}));

vi.mock("../utils/cell-data-transforms", () => ({
  transformFaultData: vi.fn((data, _facilityTimezone) => {
    // Mock transformation that adds missing fields
    return data.map((fault: any) => ({
      ...fault,
      timing: {
        ...fault.timing,
        updatedStartTime: fault.timing?.updatedStartTime || "",
        updatedEndTime: fault.timing?.updatedEndTime || "",
      },
    }));
  }),
  getStatusColor: vi.fn(() => "blue"),
}));

vi.mock("../../../config/hooks/use-config", () => ({
  useConfigSetting: vi.fn(() => ({
    setting: {
      id: "site-time-zone",
      name: "Site Time Zone",
      value: "America/New_York",
      dataType: "string",
    },
    isLoading: false,
    error: null,
  })),
}));

vi.mock("@carbon/react", () => ({
  Tag: vi.fn(({ children }) => children),
}));

describe("useFaultTrackingTable", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const mockFaultData: FaultAlarm[] = [
    {
      id: "1",
      faultId: "FAULT-001",
      title: "Test fault 1",
      description: "Test fault 1",
      tag: "TAG001",
      status: "Included",
      reason: "Test reason",
      location: { area: "A", section: "S1", equipment: "EQ1" },
      timing: {
        startTime: "2024-01-01 10:00:00 AM",
        endTime: "2024-01-01 11:00:00 AM",
        duration: "01:00:00",
        origStartTime: "2024-01-01 10:00:00 AM",
        origEndTime: "2024-01-01 11:00:00 AM",
        origDuration: "01:00:00",
        updatedStartTime: "",
        updatedEndTime: "",
      },
    },
    {
      id: "2",
      faultId: "FAULT-002",
      title: "Test fault 2",
      description: "Test fault 2",
      tag: "TAG002",
      status: "Excluded",
      reason: "Test reason",
      location: { area: "B", section: "S2", equipment: "EQ2" },
      timing: {
        startTime: "2024-01-01 12:00:00 PM",
        endTime: "2024-01-01 01:00:00 PM",
        duration: "01:00:00",
        origStartTime: "2024-01-01 12:00:00 PM",
        origEndTime: "2024-01-01 01:00:00 PM",
        origDuration: "01:00:00",
        updatedStartTime: "",
        updatedEndTime: "",
      },
    },
  ];

  it("should initialize with default values", () => {
    const { result } = renderHook(() => useFaultTrackingTable());

    expect(result.current.tableData).toEqual([]);
    expect(result.current.tableKey).toBe(0);
    expect(result.current.rowSelection).toEqual({});
    expect(result.current.selectedFaults).toEqual([]);
  });

  it("should update tableData when API data changes", () => {
    const { result, rerender } = renderHook(
      ({ data }: { data?: FaultAlarm[] | undefined }) =>
        useFaultTrackingTable(data),
      { initialProps: { data: undefined as FaultAlarm[] | undefined } },
    );

    expect(result.current.tableData).toEqual([]);

    rerender({ data: mockFaultData });
    // The data should be transformed to include missing updatedStartTime and updatedEndTime
    const expectedTransformedData = mockFaultData.map((fault) => ({
      ...fault,
      timing: {
        ...fault.timing,
        updatedStartTime: "",
        updatedEndTime: "",
      },
    }));
    expect(result.current.tableData).toEqual(expectedTransformedData);
  });

  it("should update tableData via setTableData", () => {
    const { result } = renderHook(() => useFaultTrackingTable());

    act(() => {
      result.current.setTableData(mockFaultData);
    });

    expect(result.current.tableData).toEqual(mockFaultData);
  });

  it("should calculate selectedFaults from rowSelection", () => {
    const { result } = renderHook(() => useFaultTrackingTable(mockFaultData));

    act(() => {
      result.current.setRowSelection({ "0": true });
    });

    expect(result.current.selectedFaults).toHaveLength(1);
    // The selected fault should be the transformed version
    const expectedFault = {
      ...mockFaultData[0],
      timing: {
        ...mockFaultData[0].timing,
      },
    };
    expect(result.current.selectedFaults[0]).toEqual(expectedFault);
  });

  it("should handle multiple selections", () => {
    const { result } = renderHook(() => useFaultTrackingTable(mockFaultData));

    act(() => {
      result.current.setRowSelection({ "0": true, "1": true });
    });

    expect(result.current.selectedFaults).toHaveLength(2);
  });

  it("should increment tableKey on refresh", () => {
    const { result } = renderHook(() => useFaultTrackingTable());

    const initialKey = result.current.tableKey;

    act(() => {
      result.current.handleRefresh();
    });

    expect(result.current.tableKey).toBe(initialKey + 1);
  });

  it("should provide all required interface methods", () => {
    const { result } = renderHook(() => useFaultTrackingTable());

    // Verify all expected properties exist
    expect(result.current).toHaveProperty("tableData");
    expect(result.current).toHaveProperty("setTableData");
    expect(result.current).toHaveProperty("tableKey");
    expect(result.current).toHaveProperty("rowSelection");
    expect(result.current).toHaveProperty("setRowSelection");
    expect(result.current).toHaveProperty("selectedFaults");
    expect(result.current).toHaveProperty("columns");
    expect(result.current).toHaveProperty("handleRefresh");

    // Verify functions are callable
    expect(typeof result.current.setTableData).toBe("function");
    expect(typeof result.current.setRowSelection).toBe("function");
    expect(typeof result.current.handleRefresh).toBe("function");
  });

  it("should retrieve facility timezone from config", () => {
    renderHook(() => useFaultTrackingTable(mockFaultData));

    expect(vi.mocked(useConfigSetting)).toHaveBeenCalledWith("site-time-zone");
  });

  it("should pass facility timezone to transformFaultData", () => {
    renderHook(() => useFaultTrackingTable(mockFaultData));

    expect(vi.mocked(transformFaultData)).toHaveBeenCalledWith(
      mockFaultData,
      "America/New_York",
    );
  });

  it("should use default timezone when config is unavailable", () => {
    // Mock config setting returning undefined
    vi.mocked(useConfigSetting).mockReturnValue({
      setting: undefined,
      isLoading: false,
      error: null,
    });

    renderHook(() => useFaultTrackingTable(mockFaultData));

    expect(vi.mocked(transformFaultData)).toHaveBeenCalledWith(
      mockFaultData,
      "America/New_York", // Should fallback to default
    );
  });
});
