import { useMemo, useState, useEffect } from "react";
import type { FaultAlarm } from "../types/types";
import { transformFaultData } from "../utils/cell-data-transforms";
import { createFaultTrackingColumns } from "../components/fault-tracking-columns";
import { useConfigSetting } from "../../../config/hooks/use-config";

export function useFaultTrackingTable(apiData?: FaultAlarm[]) {
  // Get facility timezone for proper date formatting
  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");
  const facilityTimezone =
    (timezoneConfig?.value as string) ?? "America/New_York";
  const [tableData, setTableData] = useState<FaultAlarm[]>([]);
  const [rawData, setRawData] = useState<FaultAlarm[]>([]);
  const [tableKey, setTableKey] = useState(0);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const [selectedAlarmIds, setSelectedAlarmIds] = useState<Set<string>>(
    new Set(),
  );

  useEffect(() => {
    if (apiData) {
      const transformedData = transformFaultData(apiData, facilityTimezone);
      setTableData(transformedData);
      setRawData(apiData);

      const newRowSelection: Record<string, boolean> = {};
      apiData.forEach((alarm, index) => {
        if (selectedAlarmIds.has(alarm.id)) {
          newRowSelection[index] = true;
        }
      });
      setRowSelection(newRowSelection);
    }
  }, [apiData, selectedAlarmIds, facilityTimezone]);

  const selectedFaults = useMemo(() => {
    return rawData.filter((alarm) => selectedAlarmIds.has(alarm.id));
  }, [selectedAlarmIds, rawData]);

  const columns = useMemo(() => createFaultTrackingColumns(), []);

  const handleRefresh = () => {
    setTableKey((prev) => prev + 1);
  };

  const handleRowSelectionChange = (
    updaterOrValue:
      | Record<string, boolean>
      | ((old: Record<string, boolean>) => Record<string, boolean>),
  ) => {
    const newRowSelection =
      typeof updaterOrValue === "function"
        ? updaterOrValue(rowSelection)
        : updaterOrValue;
    setRowSelection(newRowSelection);

    const newSelectedIds = new Set<string>();
    Object.entries(newRowSelection).forEach(([indexStr, isSelected]) => {
      if (isSelected) {
        const index = parseInt(indexStr, 10);
        const alarm = rawData[index];
        if (alarm) {
          newSelectedIds.add(alarm.id);
        }
      }
    });
    setSelectedAlarmIds(newSelectedIds);
  };

  const clearSelection = () => {
    setRowSelection({});
    setSelectedAlarmIds(new Set());
  };

  return {
    // Table data
    tableData,
    setTableData,
    tableKey,

    // Selection
    rowSelection,
    setRowSelection: handleRowSelectionChange,
    selectedFaults,
    clearSelection,

    // Configuration
    columns,

    // Actions
    handleRefresh,
  };
}
