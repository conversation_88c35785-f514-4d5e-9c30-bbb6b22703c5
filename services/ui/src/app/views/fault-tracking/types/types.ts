import type { components } from "@ict/sdk/openapi-react-query";

export type FaultAlarm = components["schemas"]["Alarm"];
export type SortField = components["schemas"]["SortField"];
export type AlarmListResponse = components["schemas"]["PostAlarmsListResponse"];

export type AlarmPaginationInfo = components["schemas"]["AlarmPaginationInfo"];

export type Section =
  components["schemas"]["ApiResponseArray__name-string--pk-number_-Array.SectionsPaginationInfo_"]["data"][number];
export type Equipment =
  components["schemas"]["ApiResponseArray__name-string--pk-number_-Array.EquipmentPaginationInfo_"]["data"][number];

export type FieldValidity = {
  startDate: boolean;
  endDate: boolean;
  startTime: boolean;
  endTime: boolean;
  startMillis: boolean;
  endMillis: boolean;
};
