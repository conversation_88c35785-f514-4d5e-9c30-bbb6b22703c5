import {
  getDefaultEditTimingsFormState,
  parseAlarmToEditTimingsFormState,
} from "./alarm-data-parser";
import type { FaultAlarm } from "../types/types";

describe("alarm-data-parser", () => {
  describe("getDefaultEditTimingsFormState", () => {
    it("should return default empty form state", () => {
      const result = getDefaultEditTimingsFormState();

      expect(result).toEqual({
        startDate: null,
        endDate: null,
        startTime: "",
        startPeriod: "AM",
        endTime: "",
        endPeriod: "AM",
        startSeconds: "",
        endSeconds: "",
        comments: "",
      });
    });
  });

  describe("parseAlarmToEditTimingsFormState", () => {
    it("should parse alarm with complete timing data", () => {
      const mockAlarm: FaultAlarm = {
        faultId: "FAULT-001",
        title: "Test fault",
        description: "Test description",
        tag: "TAG001",
        status: "Included",
        reason: "Test reason",
        id: "user123",
        comments: "Test comment",
        location: {
          area: "Area A",
          section: "Section 1",
          equipment: "Equipment 1",
        },
        timing: {
          startTime: "2024-01-15T14:30:45.123Z", // 2:30:45.123 PM UTC
          endTime: "2024-01-15T16:45:30.456Z", // 4:45:30.456 PM UTC
          duration: "02:14:45",
          updatedStartTime: "2024-01-15T14:30:45.123Z",
          updatedEndTime: "2024-01-15T16:45:30.456Z",
          origStartTime: "2024-01-15T14:30:45.123Z",
          origEndTime: "2024-01-15T16:45:30.456Z",
          origDuration: "02:14:45",
        },
      };

      const result = parseAlarmToEditTimingsFormState(
        mockAlarm,
        "America/New_York",
      );

      expect(result.startDate).toBeInstanceOf(Date);
      expect(result.endDate).toBeInstanceOf(Date);
      expect(result.startTime).toBe("9:30"); // UTC 14:30 = 9:30 AM EST
      expect(result.startPeriod).toBe("AM");
      expect(result.endTime).toBe("11:45"); // UTC 16:45 = 11:45 AM EST
      expect(result.endPeriod).toBe("AM");
      expect(result.startSeconds).toBe("45:123"); // Should be ss:ms format
      expect(result.endSeconds).toBe("30:456");
      expect(result.comments).toBe("Test comment");
    });

    it("should handle alarm with missing timing data", () => {
      const mockAlarm: FaultAlarm = {
        faultId: "FAULT-002",
        title: "Test fault",
        description: "Test description",
        tag: "TAG002",
        status: "Included",
        reason: "Test reason",
        id: "user123",
        comments: "Another comment",
        location: {
          area: "Area B",
          section: "Section 2",
          equipment: "Equipment 2",
        },
        timing: {
          // No origStartTime or origEndTime
          duration: "01:00:00",
          startTime: "",
          origDuration: "01:00:00",
        },
      };

      const result = parseAlarmToEditTimingsFormState(
        mockAlarm,
        "America/New_York",
      );

      expect(result.startDate).toBeNull();
      expect(result.endDate).toBeNull();
      expect(result.startTime).toBe("");
      expect(result.startPeriod).toBe("AM");
      expect(result.endTime).toBe("");
      expect(result.endPeriod).toBe("AM");
      expect(result.startSeconds).toBe("");
      expect(result.endSeconds).toBe("");
      expect(result.comments).toBe("Another comment");
    });

    it("should handle alarm with invalid datetime strings", () => {
      const mockAlarm: FaultAlarm = {
        faultId: "FAULT-003",
        title: "Test fault",
        description: "Test description",
        tag: "TAG003",
        status: "Included",
        reason: "Test reason",
        id: "user123",
        comments: "",
        location: {
          area: "Area C",
          section: "Section 3",
          equipment: "Equipment 3",
        },
        timing: {
          duration: "01:00:00",
          startTime: "invalid-date-string",
          origStartTime: "invalid-date-string",
          origEndTime: "another-invalid-date",
          origDuration: "01:00:00",
        },
      };

      const result = parseAlarmToEditTimingsFormState(
        mockAlarm,
        "America/New_York",
      );

      expect(result.startDate).toBeNull();
      expect(result.endDate).toBeNull();
      expect(result.startTime).toBe("");
      expect(result.startPeriod).toBe("AM");
      expect(result.endTime).toBe("");
      expect(result.endPeriod).toBe("AM");
      expect(result.startSeconds).toBe("");
      expect(result.endSeconds).toBe("");
      expect(result.comments).toBe("");
    });

    it("should handle midnight and noon times correctly", () => {
      const mockAlarm: FaultAlarm = {
        faultId: "FAULT-004",
        title: "Test fault",
        description: "Test description",
        tag: "TAG004",
        status: "Included",
        reason: "Test reason",
        id: "user123",
        comments: "",
        location: {
          area: "Area D",
          section: "Section 4",
          equipment: "Equipment 4",
        },
        timing: {
          startTime: "2024-01-15T00:00:00.000Z", // Midnight
          endTime: "2024-01-15T12:00:00.000Z", // Noon
          duration: "12:00:00",
          updatedStartTime: "2024-01-15T00:00:00.000Z",
          updatedEndTime: "2024-01-15T12:00:00.000Z",
          origStartTime: "2024-01-15T00:00:00.000Z",
          origEndTime: "2024-01-15T12:00:00.000Z",
          origDuration: "12:00:00",
        },
      };

      const result = parseAlarmToEditTimingsFormState(
        mockAlarm,
        "America/New_York",
      );

      expect(result.startTime).toBe("7:00"); // UTC 00:00 = 7:00 PM EST (previous day)
      expect(result.startPeriod).toBe("PM");
      expect(result.endTime).toBe("7:00"); // UTC 12:00 = 7:00 AM EST
      expect(result.endPeriod).toBe("AM");
    });

    it("should only include seconds when non-zero", () => {
      const mockAlarm: FaultAlarm = {
        faultId: "FAULT-005",
        title: "Test fault",
        description: "Test description",
        tag: "TAG005",
        status: "Included",
        reason: "Test reason",
        id: "user123",
        comments: "",
        location: {
          area: "Area E",
          section: "Section 5",
          equipment: "Equipment 5",
        },
        timing: {
          startTime: "2024-01-15T14:30:00.000Z", // No seconds/milliseconds
          endTime: "2024-01-15T16:45:30.000Z", // Has seconds but no milliseconds
          duration: "02:15:30",
          updatedStartTime: "2024-01-15T14:30:00.000Z",
          updatedEndTime: "2024-01-15T16:45:30.000Z",
          origStartTime: "2024-01-15T14:30:00.000Z",
          origEndTime: "2024-01-15T16:45:30.000Z",
          origDuration: "02:15:30",
        },
      };

      const result = parseAlarmToEditTimingsFormState(
        mockAlarm,
        "America/New_York",
      );

      expect(result.startSeconds).toBe(""); // Should be empty when 0 seconds and 0 ms
      expect(result.endSeconds).toBe("30:000"); // Should include when seconds > 0
    });
  });
});
