import { DateTime } from "luxon";
import type { FaultAlarm } from "../types/types";
import type { EditTimingsForm } from "../hooks/edit-timings/use-edit-timings-form";
import { Logger } from "../../../utils/logger";

const logger = new Logger("AlarmDataParser");

/**
 * Parse a datetime string to extract date, time components, and seconds
 * Follows the same pattern as existing datetime-formatter.ts and cell-data-transforms.ts
 */
function parseAlarmDateTime(
  dateTimeString?: string,
  facilityTimezone: string = "America/New_York",
) {
  if (!dateTimeString) {
    return {
      date: null,
      time: "",
      period: "AM" as const,
      seconds: "",
    };
  }

  try {
    // Try ISO format first (like startTime, endTime fields)
    // Convert UTC time to facility timezone for consistent editing
    let parsed = DateTime.fromISO(dateTimeString).setZone(facilityTimezone);

    // If ISO parsing fails, try the alarm format "YYYY-MM-DD HH:MM AM/PM"
    if (!parsed.isValid) {
      parsed = DateTime.fromFormat(dateTimeString, "yyyy-MM-dd h:mm a", {
        zone: facilityTimezone,
      });
    }

    if (!parsed.isValid) {
      return {
        date: null,
        time: "",
        period: "AM" as const,
        seconds: "",
      };
    }

    // Extract date
    const date = parsed.toJSDate();

    // Extract time in 12-hour format (following existing patterns)
    const hour12 =
      parsed.hour === 0
        ? 12
        : parsed.hour > 12
          ? parsed.hour - 12
          : parsed.hour;
    const period = parsed.hour >= 12 ? "PM" : "AM";
    const time = `${hour12}:${parsed.minute.toString().padStart(2, "0")}`;

    // Extract seconds in the format expected by the form (ss:ms)
    const seconds =
      parsed.second > 0 || parsed.millisecond > 0
        ? `${parsed.second.toString().padStart(2, "0")}:${parsed.millisecond.toString().padStart(3, "0")}`
        : "";

    return {
      date,
      time,
      period,
      seconds,
    };
  } catch (error) {
    logger.warn("Failed to parse alarm datetime", {
      dateTimeString,
      error: error instanceof Error ? error.message : String(error),
    });
    return {
      date: null,
      time: "",
      period: "AM" as const,
      seconds: "",
    };
  }
}

/**
 * Get default empty form state for edit timings
 */
export function getDefaultEditTimingsFormState(): EditTimingsForm {
  return {
    startDate: null,
    endDate: null,
    startTime: "",
    startPeriod: "AM",
    endTime: "",
    endPeriod: "AM",
    startSeconds: "",
    endSeconds: "",
    comments: "",
  };
}

/**
 * Parse a FaultAlarm into EditTimingsForm state
 * Uses the updated timing data (updatedStartTime/updatedEndTime) as the baseline
 * Falls back to primary timing (startTime/endTime) if updated timing is not available
 */
export function parseAlarmToEditTimingsFormState(
  alarm: FaultAlarm,
  facilityTimezone: string = "America/New_York",
): EditTimingsForm {
  // Priority: updatedStartTime/updatedEndTime (user edits) -> startTime/endTime (current values)
  const startTimeToUse =
    alarm.timing?.updatedStartTime || alarm.timing?.startTime;
  const endTimeToUse = alarm.timing?.updatedEndTime || alarm.timing?.endTime;

  const startData = parseAlarmDateTime(startTimeToUse, facilityTimezone);
  const endData = parseAlarmDateTime(endTimeToUse, facilityTimezone);

  return {
    startDate: startData.date || null,
    endDate: endData.date || null,
    startTime: startData.time,
    startPeriod: startData.period as "AM" | "PM",
    endTime: endData.time,
    endPeriod: endData.period as "AM" | "PM",
    startSeconds: startData.seconds,
    endSeconds: endData.seconds,
    comments: alarm.comments || "",
  };
}

/**
 * Parse original timing data from a FaultAlarm
 * Uses origStartTime/origEndTime (the true original values) if available,
 * Falls back to startTime/endTime if orig fields are not present
 */
export function parseOriginalTimingData(
  alarm: FaultAlarm,
  facilityTimezone: string = "America/New_York",
) {
  // Priority: origStartTime/origEndTime (true original) -> startTime/endTime (fallback)
  const originalStartTime =
    alarm.timing?.origStartTime || alarm.timing?.startTime;
  const originalEndTime = alarm.timing?.origEndTime || alarm.timing?.endTime;

  const startData = parseAlarmDateTime(originalStartTime, facilityTimezone);
  const endData = parseAlarmDateTime(originalEndTime, facilityTimezone);

  return {
    startDate: startData.date,
    endDate: endData.date,
    startTime: startData.time,
    startPeriod: startData.period as "AM" | "PM",
    endTime: endData.time,
    endPeriod: endData.period as "AM" | "PM",
    startSeconds: startData.seconds,
    endSeconds: endData.seconds,
  };
}
