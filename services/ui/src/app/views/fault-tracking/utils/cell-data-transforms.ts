import type { FaultAlarm } from "../types/types";
import { Logger } from "../../../utils/logger";
import { DateTime } from "luxon";

const logger = new Logger("CellDataTransforms");

export function transformFaultData(
  rawData: FaultAlarm[],
  facilityTimezone: string = "America/New_York",
): FaultAlarm[] {
  return rawData.map((fault) => ({
    ...fault,
    // Transform status values for display
    status: transformStatusValue(fault.status),
    // Format timing values for display using facility timezone
    timing: {
      ...fault.timing,
      origStartTime: formatDateTime(
        fault.timing?.origStartTime,
        facilityTimezone,
      ),
      origEndTime: formatDateTime(fault.timing?.origEndTime, facilityTimezone),
      updatedStartTime: formatDateTime(
        fault.timing?.updatedStartTime,
        facilityTimezone,
      ),
      updatedEndTime: formatDateTime(
        fault.timing?.updatedEndTime,
        facilityTimezone,
      ),
    },
  }));
}

function transformStatusValue(status: string): string {
  // Transform "KEPT" to "Included"
  if (status === "KEPT") {
    return "Included";
  }

  // Everything else becomes "Excluded"
  return "Excluded";
}

function formatDateTime(
  dateTime?: string,
  targetTimezone: string = "America/New_York",
): string {
  if (!dateTime) return "";

  try {
    // Try ISO format first (like startTime, endTime fields)
    let parsed = DateTime.fromISO(dateTime);

    // If ISO parsing fails, try the alarm format "YYYY-MM-DD HH:MM AM/PM"
    if (!parsed.isValid) {
      parsed = DateTime.fromFormat(dateTime, "yyyy-MM-dd h:mm a");
    }

    if (parsed.isValid) {
      // Convert to target timezone for display
      const localDateTime = parsed.setZone(targetTimezone);
      // Smart formatting: include seconds/milliseconds only when meaningful
      if (localDateTime.second > 0 && localDateTime.millisecond > 0) {
        // Both seconds and milliseconds exist
        return localDateTime.toFormat("yyyy-LL-dd h:mm:ss.SSS a");
      } else if (localDateTime.second > 0) {
        // Only seconds exist (no milliseconds or milliseconds are 000)
        return localDateTime.toFormat("yyyy-LL-dd h:mm:ss a");
      } else {
        // No seconds or milliseconds
        return localDateTime.toFormat("yyyy-LL-dd h:mm a");
      }
    } else {
      // Fallback to original string with milliseconds stripped if parsing fails
      return dateTime.replace(/:\d{2}\.\d+/, "");
    }
  } catch (error) {
    logger.error("Failed to format date time", {
      error: error instanceof Error ? error.message : String(error),
      context: {
        dateTime,
        operation: "format_date_time",
      },
    });
    return dateTime.replace(/:\d{2}\.\d+/, "");
  }
}

export function getStatusColor(status: string): "blue" | "outline" {
  return status.toLowerCase() === "included" ? "blue" : "outline";
}
