import { describe, it, expect } from "vitest";
import { DateTime } from "luxon";
import { combineDateTime } from "./datetime-formatter";

describe("combineDateTime", () => {
  // Create dates that won't shift when converted to different timezones
  const testDate = new Date(2024, 1, 15); // February 15, 2024 (month is 0-indexed)
  const christmasDate = new Date(2023, 11, 25); // December 25, 2023
  const leapDate = new Date(2024, 1, 29); // February 29, 2024

  describe("basic time parsing", () => {
    it("should parse AM times correctly", () => {
      const result = combineDateTime(testDate, "10:30", "AM", undefined, "UTC");

      expect(result.hour).toBe(10);
      expect(result.minute).toBe(30);
      expect(result.second).toBe(0);
      expect(result.millisecond).toBe(0);
    });

    it("should parse PM times correctly", () => {
      const result = combineDateTime(testDate, "10:30", "PM", undefined, "UTC");

      expect(result.hour).toBe(22); // 10 PM = 22 in 24-hour format
      expect(result.minute).toBe(30);
      expect(result.second).toBe(0);
      expect(result.millisecond).toBe(0);
    });

    it("should handle 12 AM (midnight) correctly", () => {
      const result = combineDateTime(testDate, "12:00", "AM", undefined, "UTC");

      expect(result.hour).toBe(0); // 12 AM = 00:00 in 24-hour format
      expect(result.minute).toBe(0);
    });

    it("should handle 12 PM (noon) correctly", () => {
      const result = combineDateTime(testDate, "12:00", "PM", undefined, "UTC");

      expect(result.hour).toBe(12); // 12 PM = 12:00 in 24-hour format
      expect(result.minute).toBe(0);
    });

    it("should handle edge case times", () => {
      // 11:59 PM should be 23:59
      const result = combineDateTime(testDate, "11:59", "PM", undefined, "UTC");

      expect(result.hour).toBe(23);
      expect(result.minute).toBe(59);
    });
  });

  describe("seconds and milliseconds parsing", () => {
    it("should parse valid seconds:milliseconds format", () => {
      const result = combineDateTime(testDate, "10:30", "AM", "15:500", "UTC");

      expect(result.hour).toBe(10);
      expect(result.minute).toBe(30);
      expect(result.second).toBe(15);
      expect(result.millisecond).toBe(500);
    });

    it("should handle zero seconds and milliseconds", () => {
      const result = combineDateTime(testDate, "10:30", "AM", "00:000", "UTC");

      expect(result.second).toBe(0);
      expect(result.millisecond).toBe(0);
    });

    it("should handle maximum valid seconds and milliseconds", () => {
      const result = combineDateTime(testDate, "10:30", "AM", "59:999", "UTC");

      expect(result.second).toBe(59);
      expect(result.millisecond).toBe(999);
    });

    it("should ignore invalid seconds format", () => {
      const result = combineDateTime(testDate, "10:30", "AM", "invalid", "UTC");

      expect(result.second).toBe(0);
      expect(result.millisecond).toBe(0);
    });

    it("should ignore malformed seconds format", () => {
      const result = combineDateTime(testDate, "10:30", "AM", "15:abc", "UTC");

      expect(result.second).toBe(0);
      expect(result.millisecond).toBe(0);
    });

    it("should handle empty seconds string", () => {
      const result = combineDateTime(testDate, "10:30", "AM", "", "UTC");

      expect(result.second).toBe(0);
      expect(result.millisecond).toBe(0);
    });

    it("should handle undefined seconds", () => {
      const result = combineDateTime(testDate, "10:30", "AM", undefined, "UTC");

      expect(result.second).toBe(0);
      expect(result.millisecond).toBe(0);
    });
  });

  describe("date handling", () => {
    it("should preserve the correct date when using UTC timezone", () => {
      const result = combineDateTime(testDate, "10:30", "AM", undefined, "UTC");

      expect(result.year).toBe(2024);
      expect(result.month).toBe(2); // Luxon uses 1-based months
      expect(result.day).toBe(15);
    });

    it("should handle different dates correctly when using UTC timezone", () => {
      const result = combineDateTime(
        christmasDate,
        "10:30",
        "AM",
        undefined,
        "UTC",
      );

      expect(result.year).toBe(2023);
      expect(result.month).toBe(12);
      expect(result.day).toBe(25);
    });

    it("should handle leap year dates when using UTC timezone", () => {
      const result = combineDateTime(leapDate, "10:30", "AM", undefined, "UTC");

      expect(result.year).toBe(2024);
      expect(result.month).toBe(2);
      expect(result.day).toBe(29);
    });

    it("should handle date components correctly regardless of timezone conversion", () => {
      // Test that the input date components are properly used
      const result = combineDateTime(
        testDate,
        "10:30",
        "AM",
        undefined,
        "America/New_York",
      );

      // The result should be a valid DateTime in UTC
      expect(result.isValid).toBe(true);
      expect(result.zoneName).toBe("UTC");

      // The date should be reasonable (within a day of expected due to timezone conversion)
      expect(result.year).toBe(2024);
      expect(result.month).toBe(2);
      expect([14, 15, 16]).toContain(result.day); // Allow for timezone shift
    });
  });

  describe("timezone handling", () => {
    it("should convert to UTC from specified timezone", () => {
      const result = combineDateTime(
        testDate,
        "10:30",
        "AM",
        undefined,
        "America/New_York",
      );

      // Result should be in UTC
      expect(result.zoneName).toBe("UTC");
      expect(result.isValid).toBe(true);
    });

    it("should handle different timezones", () => {
      const estResult = combineDateTime(
        testDate,
        "10:30",
        "AM",
        undefined,
        "America/New_York",
      );
      const pstResult = combineDateTime(
        testDate,
        "10:30",
        "AM",
        undefined,
        "America/Los_Angeles",
      );

      // Same local time in different timezones should produce different UTC times
      expect(estResult.toISO()).not.toBe(pstResult.toISO());
    });

    it("should use local timezone as default", () => {
      const result = combineDateTime(testDate, "10:30", "AM");

      expect(result.zoneName).toBe("UTC"); // Should be converted to UTC
      expect(result.isValid).toBe(true);
    });

    it("should handle UTC timezone explicitly", () => {
      const result = combineDateTime(testDate, "10:30", "AM", undefined, "UTC");

      expect(result.zoneName).toBe("UTC");
      expect(result.hour).toBe(10); // Should remain 10:30 since already in UTC
      expect(result.minute).toBe(30);
    });
  });

  describe("return value validation", () => {
    it("should return a valid DateTime object", () => {
      const result = combineDateTime(testDate, "10:30", "AM", "15:500", "UTC");

      expect(result).toBeInstanceOf(DateTime);
      expect(result.isValid).toBe(true);
    });

    it("should return DateTime in UTC zone", () => {
      const result = combineDateTime(
        testDate,
        "10:30",
        "AM",
        undefined,
        "America/New_York",
      );

      expect(result.zoneName).toBe("UTC");
    });

    it("should produce valid ISO string", () => {
      const result = combineDateTime(testDate, "10:30", "AM", "15:500", "UTC");
      const isoString = result.toISO();

      expect(isoString).toBeDefined();
      expect(isoString).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
      );

      // Should be parseable back to a Date
      expect(new Date(isoString!).getTime()).not.toBeNaN();
    });
  });

  describe("comprehensive scenarios", () => {
    it("should handle complete data with all components", () => {
      const marchDate = new Date(2024, 2, 15); // March 15, 2024
      const result = combineDateTime(
        marchDate,
        "02:45",
        "PM",
        "30:750",
        "America/New_York",
      );

      expect(result.isValid).toBe(true);
      expect(result.zoneName).toBe("UTC");

      // Verify all components are included
      const isoString = result.toISO();
      expect(isoString).toBeDefined();
      expect(new Date(isoString!)).toBeInstanceOf(Date);
    });

    it("should handle minimal data (no seconds, local timezone)", () => {
      const result = combineDateTime(testDate, "09:15", "AM");

      expect(result.isValid).toBe(true);
      expect(result.zoneName).toBe("UTC");
      expect(result.second).toBe(0);
      expect(result.millisecond).toBe(0);
    });

    it("should maintain precision for milliseconds", () => {
      const result = combineDateTime(testDate, "10:30", "AM", "15:001", "UTC");

      expect(result.second).toBe(15);
      expect(result.millisecond).toBe(1); // Should preserve single millisecond
    });

    it("should handle boundary times correctly", () => {
      // Test various boundary conditions
      const scenarios = [
        { time: "12:00", period: "AM" as const, expectedHour: 0 }, // Midnight
        { time: "12:00", period: "PM" as const, expectedHour: 12 }, // Noon
        { time: "01:00", period: "AM" as const, expectedHour: 1 }, // Early morning
        { time: "11:59", period: "PM" as const, expectedHour: 23 }, // Late night
      ];

      scenarios.forEach(({ time, period, expectedHour }) => {
        const result = combineDateTime(
          testDate,
          time,
          period,
          undefined,
          "UTC",
        );
        expect(result.hour).toBe(expectedHour);
      });
    });
  });
});
