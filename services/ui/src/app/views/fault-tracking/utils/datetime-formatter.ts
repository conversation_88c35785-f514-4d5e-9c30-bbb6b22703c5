import { DateTime } from "luxon";
import { validateSecondsFormat } from "./time-validation";

export function combineDateTime(
  date: Date,
  time: string,
  period: "AM" | "PM",
  seconds?: string,
  facilityTimezone: string = "local",
): DateTime {
  const [hours, minutes] = time.split(":").map(Number);

  let adjustedHours = hours;
  if (period === "PM" && hours < 12) adjustedHours += 12;
  if (period === "AM" && hours === 12) adjustedHours = 0;

  let secs = 0;
  let ms = 0;
  if (seconds && validateSecondsFormat(seconds)) {
    const parts = seconds.split(":");
    secs = parseInt(parts[0], 10);
    ms = parseInt(parts[1], 10);
  }

  const facilityDateTime = DateTime.fromObject(
    {
      year: date.getFullYear(),
      month: date.getMonth() + 1, // Luxon uses 1-based months
      day: date.getDate(),
      hour: adjustedHours,
      minute: minutes,
      second: secs,
      millisecond: ms,
    },
    { zone: facilityTimezone },
  );

  return facilityDateTime.toUTC();
}
