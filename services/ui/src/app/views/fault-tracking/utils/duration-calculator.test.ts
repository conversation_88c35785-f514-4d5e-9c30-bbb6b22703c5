import {
  calculateTimeDuration,
  calculateFormDuration,
  TimeData,
} from "./duration-calculator";

// Mock the validation utility
vi.mock("./manual-entry-validation", () => ({
  validateSecondsFormat: vi.fn((input: string) => {
    // Simple validation mock - expects "ss:ms" format
    const regex = /^\d{1,2}:\d{1,3}$/;
    return regex.test(input);
  }),
}));

describe("duration-calculator", () => {
  describe("calculateTimeDuration", () => {
    it("should calculate duration between two times on same day", () => {
      const startTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "10:00",
        period: "AM",
      };
      const endTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "11:30",
        period: "AM",
      };

      const result = calculateTimeDuration(startTime, endTime);
      expect(result).toBe("1h 30m");
    });

    it("should calculate duration across AM/PM boundary", () => {
      const startTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "11:30",
        period: "AM",
      };
      const endTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "1:15",
        period: "PM",
      };

      const result = calculateTimeDuration(startTime, endTime);
      expect(result).toBe("1h 45m");
    });

    it("should calculate duration with seconds and milliseconds", () => {
      const startTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "10:00",
        period: "AM",
        seconds: "30:500",
      };
      const endTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "10:01",
        period: "AM",
        seconds: "45:750",
      };

      const result = calculateTimeDuration(startTime, endTime);
      expect(result).toBe("1m 15.250s");
    });

    it("should handle 12 AM (midnight) correctly", () => {
      const startTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "12:00",
        period: "AM",
      };
      const endTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "1:00",
        period: "AM",
      };

      const result = calculateTimeDuration(startTime, endTime);
      expect(result).toBe("1h");
    });

    it("should handle 12 PM (noon) correctly", () => {
      const startTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "12:00",
        period: "PM",
      };
      const endTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "1:00",
        period: "PM",
      };

      const result = calculateTimeDuration(startTime, endTime);
      expect(result).toBe("1h");
    });

    it("should handle duration across different dates", () => {
      const startTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "11:00",
        period: "PM",
      };
      const endTime: TimeData = {
        date: new Date("2024-01-02"),
        time: "1:00",
        period: "AM",
      };

      const result = calculateTimeDuration(startTime, endTime);
      expect(result).toBe("2h");
    });

    it("should return 'Invalid duration' for negative duration", () => {
      const startTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "2:00",
        period: "PM",
      };
      const endTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "1:00",
        period: "PM",
      };

      const result = calculateTimeDuration(startTime, endTime);
      expect(result).toBe("Invalid duration");
    });

    it("should return empty string for invalid time data", () => {
      const startTime: TimeData = {
        date: new Date("invalid"),
        time: "invalid",
        period: "AM",
      };
      const endTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "1:00",
        period: "PM",
      };

      const result = calculateTimeDuration(startTime, endTime);
      expect(result).toBe("");
    });

    it("should handle seconds without milliseconds", () => {
      const startTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "10:00",
        period: "AM",
        seconds: "30:0",
      };
      const endTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "10:00",
        period: "AM",
        seconds: "45:0",
      };

      const result = calculateTimeDuration(startTime, endTime);
      expect(result).toBe("15s");
    });

    it("should ignore invalid seconds format", () => {
      const startTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "10:00",
        period: "AM",
        seconds: "invalid",
      };
      const endTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "10:01",
        period: "AM",
        seconds: "invalid",
      };

      const result = calculateTimeDuration(startTime, endTime);
      expect(result).toBe("1m");
    });
  });

  describe("calculateFormDuration", () => {
    it("should calculate duration from form state", () => {
      const formState = {
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-01-01"),
        startTime: "10:00",
        startPeriod: "AM" as const,
        endTime: "11:30",
        endPeriod: "AM" as const,
        startSeconds: "0:0",
        endSeconds: "0:0",
      };

      const result = calculateFormDuration(formState);
      expect(result).toBe("1h 30m");
    });

    it("should return empty string when startDate is null", () => {
      const formState = {
        startDate: null,
        endDate: new Date("2024-01-01"),
        startTime: "10:00",
        startPeriod: "AM" as const,
        endTime: "11:30",
        endPeriod: "AM" as const,
      };

      const result = calculateFormDuration(formState);
      expect(result).toBe("");
    });

    it("should return empty string when endDate is null", () => {
      const formState = {
        startDate: new Date("2024-01-01"),
        endDate: null,
        startTime: "10:00",
        startPeriod: "AM" as const,
        endTime: "11:30",
        endPeriod: "AM" as const,
      };

      const result = calculateFormDuration(formState);
      expect(result).toBe("");
    });

    it("should return empty string when startTime is empty", () => {
      const formState = {
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-01-01"),
        startTime: "",
        startPeriod: "AM" as const,
        endTime: "11:30",
        endPeriod: "AM" as const,
      };

      const result = calculateFormDuration(formState);
      expect(result).toBe("");
    });

    it("should return empty string when endTime is empty", () => {
      const formState = {
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-01-01"),
        startTime: "10:00",
        startPeriod: "AM" as const,
        endTime: "",
        endPeriod: "AM" as const,
      };

      const result = calculateFormDuration(formState);
      expect(result).toBe("");
    });

    it("should handle form state with seconds", () => {
      const formState = {
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-01-01"),
        startTime: "10:00",
        startPeriod: "AM" as const,
        endTime: "10:01",
        endPeriod: "AM" as const,
        startSeconds: "15:250",
        endSeconds: "30:500",
      };

      const result = calculateFormDuration(formState);
      expect(result).toBe("1m 15.250s");
    });

    it("should handle form state without seconds", () => {
      const formState = {
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-01-01"),
        startTime: "10:00",
        startPeriod: "AM" as const,
        endTime: "10:30",
        endPeriod: "AM" as const,
      };

      const result = calculateFormDuration(formState);
      expect(result).toBe("30m");
    });
  });

  describe("edge cases", () => {
    it("should handle zero duration", () => {
      const startTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "10:00",
        period: "AM",
      };
      const endTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "10:00",
        period: "AM",
      };

      const result = calculateTimeDuration(startTime, endTime);
      expect(result).toBe("0s");
    });

    it("should handle very small durations (milliseconds only)", () => {
      const startTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "10:00",
        period: "AM",
        seconds: "0:100",
      };
      const endTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "10:00",
        period: "AM",
        seconds: "0:250",
      };

      const result = calculateTimeDuration(startTime, endTime);
      expect(result).toBe("0.150s");
    });

    it("should handle duration with only hours", () => {
      const startTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "10:00",
        period: "AM",
      };
      const endTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "2:00",
        period: "PM",
      };

      const result = calculateTimeDuration(startTime, endTime);
      expect(result).toBe("4h");
    });

    it("should handle duration with only minutes", () => {
      const startTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "10:00",
        period: "AM",
      };
      const endTime: TimeData = {
        date: new Date("2024-01-01"),
        time: "10:45",
        period: "AM",
      };

      const result = calculateTimeDuration(startTime, endTime);
      expect(result).toBe("45m");
    });
  });
});
