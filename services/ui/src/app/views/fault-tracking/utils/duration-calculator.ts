import { DateTime } from "luxon";
import { validateSecondsFormat } from "./time-validation";

export interface TimeData {
  date: Date;
  time: string;
  period: "AM" | "PM";
  seconds?: string;
}

function createDateTimeFromFormData(timeData: TimeData): DateTime {
  const [hours, minutes] = timeData.time.split(":").map(Number);
  let adjustedHours = hours;
  if (timeData.period === "PM" && hours < 12) adjustedHours += 12;
  if (timeData.period === "AM" && hours === 12) adjustedHours = 0;

  let secs = 0;
  let ms = 0;
  if (timeData.seconds && validateSecondsFormat(timeData.seconds)) {
    const parts = timeData.seconds.split(":");
    secs = parseInt(parts[0], 10);
    ms = parseInt(parts[1], 10);
  }

  return DateTime.fromObject({
    year: timeData.date.getFullYear(),
    month: timeData.date.getMonth() + 1,
    day: timeData.date.getDate(),
    hour: adjustedHours,
    minute: minutes,
    second: secs,
    millisecond: ms,
  });
}

function formatDuration(durationMs: number): string {
  const totalSeconds = Math.floor(durationMs / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  const milliseconds = durationMs % 1000;

  const parts = [];
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (seconds > 0 || milliseconds > 0) {
    if (milliseconds > 0) {
      parts.push(`${seconds}.${milliseconds.toString().padStart(3, "0")}s`);
    } else {
      parts.push(`${seconds}s`);
    }
  }

  return parts.length > 0 ? parts.join(" ") : "0s";
}

export function calculateTimeDuration(
  startTime: TimeData,
  endTime: TimeData,
): string {
  try {
    const startDateTime = createDateTimeFromFormData(startTime);
    const endDateTime = createDateTimeFromFormData(endTime);

    if (!startDateTime.isValid || !endDateTime.isValid) {
      return "";
    }

    const duration = endDateTime.diff(startDateTime);

    if (duration.milliseconds < 0) {
      return "Invalid duration";
    }

    return formatDuration(duration.milliseconds);
  } catch (_error) {
    return "";
  }
}

export function calculateFormDuration(formState: {
  startDate: Date | null;
  endDate: Date | null;
  startTime: string;
  startPeriod: "AM" | "PM";
  endTime: string;
  endPeriod: "AM" | "PM";
  startSeconds?: string;
  endSeconds?: string;
}): string {
  if (
    !formState.startDate ||
    !formState.endDate ||
    !formState.startTime ||
    !formState.endTime
  ) {
    return "";
  }

  const startTime: TimeData = {
    date: formState.startDate,
    time: formState.startTime,
    period: formState.startPeriod,
    seconds: formState.startSeconds,
  };

  const endTime: TimeData = {
    date: formState.endDate,
    time: formState.endTime,
    period: formState.endPeriod,
    seconds: formState.endSeconds,
  };

  return calculateTimeDuration(startTime, endTime);
}
