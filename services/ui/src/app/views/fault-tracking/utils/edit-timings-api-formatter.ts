import type { EditTimingsForm } from "../hooks/edit-timings/use-edit-timings-form";
import type { FaultAlarm } from "../types/types";
import { combineDateTime } from "./datetime-formatter";

/**
 * Formats edit-timings form data for API submission
 * @param formData - The edit-timings form data
 * @param selectedAlarm - The alarm being updated (needed for ID and current status)
 * @param facilityTimezone - Facility timezone (defaults to America/New_York)
 * @returns Formatted data for API
 */
export function formatEditTimingsForAPI(
  formData: EditTimingsForm,
  selectedAlarm: FaultAlarm,
  facilityTimezone: string = "America/New_York", // Default to EST/EDT, can be configured
) {
  if (!formData.startDate || !formData.endDate) {
    throw new Error("Start date and end date are required");
  }

  if (!selectedAlarm.id) {
    throw new Error("Alarm ID is required for updating");
  }

  const startDateTime = combineDateTime(
    formData.startDate,
    formData.startTime,
    formData.startPeriod,
    formData.startSeconds,
    facilityTimezone,
  );

  const endDateTime = combineDateTime(
    formData.endDate,
    formData.endTime,
    formData.endPeriod,
    formData.endSeconds,
    facilityTimezone,
  );

  // Convert to ISO strings as required by the API
  const startDateLocal = startDateTime.toISO()!;
  const endDateLocal = endDateTime.toISO()!;

  // Use the raw status directly - preserve whatever the current state is
  // We're only updating timing, not changing inclusion/exclusion status
  const isIncluded = selectedAlarm.status === "KEPT";

  return {
    id: selectedAlarm.id,
    startDateLocal,
    endDateLocal,
    isIncluded,
    comments: formData.comments,
  };
}
