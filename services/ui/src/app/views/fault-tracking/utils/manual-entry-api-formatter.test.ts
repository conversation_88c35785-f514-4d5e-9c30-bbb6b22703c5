import { formatManualEntryForAPI } from "./manual-entry-api-formatter";
import type { ManualEntryForm } from "../hooks/manual-entry/use-manual-entry-form";

// Mock the validation utility
vi.mock("./manual-entry-validation", () => ({
  validateSecondsFormat: vi.fn((input: string) => {
    // Simple validation mock - expects "ss:ms" format
    const regex = /^\d{1,2}:\d{1,3}$/;
    return regex.test(input);
  }),
}));

describe("manual-entry-api-formatter", () => {
  describe("formatManualEntryForAPI", () => {
    const baseFormData: ManualEntryForm = {
      startDate: new Date(2024, 0, 15), // January 15, 2024 (local time)
      endDate: new Date(2024, 0, 15), // January 15, 2024 (local time)
      startTime: "10:00",
      startPeriod: "AM",
      endTime: "11:30",
      endPeriod: "AM",
      startSeconds: "",
      endSeconds: "",
      description: "Test alarm description",
      comments: "Test comments",
      section: null,
      equipment: null,
    };

    it("should format basic form data correctly", () => {
      const result = formatManualEntryForAPI(
        baseFormData,
        "Test Section",
        "Test Equipment",
      );

      expect(result).toMatchObject({
        isIncluded: true,
        description: "Test alarm description",
        comments: "Test comments",
        equipment: "Test Equipment",
        section: "Test Section",
      });
      expect(result.startDateLocal).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
      );
      expect(result.endDateLocal).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
      );
    });

    it("should handle AM times correctly", () => {
      const formData = {
        ...baseFormData,
        startTime: "9:00",
        startPeriod: "AM" as const,
        endTime: "11:30",
        endPeriod: "AM" as const,
      };

      const result = formatManualEntryForAPI(
        formData,
        "Test Section",
        "Test Equipment",
        "UTC",
      );

      // For UTC timezone, times should be straightforward
      expect(result.startDateLocal).toContain("T09:00:00.000Z");
      expect(result.endDateLocal).toContain("T11:30:00.000Z");
    });

    it("should handle PM times correctly", () => {
      const formData = {
        ...baseFormData,
        startTime: "2:00",
        startPeriod: "PM" as const,
        endTime: "4:30",
        endPeriod: "PM" as const,
      };

      const result = formatManualEntryForAPI(
        formData,
        "Test Section",
        "Test Equipment",
        "UTC",
      );

      expect(result.startDateLocal).toContain("T14:00:00.000Z");
      expect(result.endDateLocal).toContain("T16:30:00.000Z");
    });

    it("should handle 12 AM (midnight) correctly", () => {
      const formData = {
        ...baseFormData,
        startTime: "12:00",
        startPeriod: "AM" as const,
        endTime: "12:30",
        endPeriod: "AM" as const,
      };

      const result = formatManualEntryForAPI(
        formData,
        "Test Section",
        "Test Equipment",
        "UTC",
      );

      expect(result.startDateLocal).toContain("T00:00:00.000Z");
      expect(result.endDateLocal).toContain("T00:30:00.000Z");
    });

    it("should handle 12 PM (noon) correctly", () => {
      const formData = {
        ...baseFormData,
        startTime: "12:00",
        startPeriod: "PM" as const,
        endTime: "12:30",
        endPeriod: "PM" as const,
      };

      const result = formatManualEntryForAPI(
        formData,
        "Test Section",
        "Test Equipment",
        "UTC",
      );

      expect(result.startDateLocal).toContain("T12:00:00.000Z");
      expect(result.endDateLocal).toContain("T12:30:00.000Z");
    });

    it("should handle seconds and milliseconds", () => {
      const formData = {
        ...baseFormData,
        startTime: "10:00",
        startPeriod: "AM" as const,
        startSeconds: "30:500",
        endTime: "10:01",
        endPeriod: "AM" as const,
        endSeconds: "45:750",
      };

      const result = formatManualEntryForAPI(
        formData,
        "Test Section",
        "Test Equipment",
        "UTC",
      );

      expect(result.startDateLocal).toContain("T10:00:30.500Z");
      expect(result.endDateLocal).toContain("T10:01:45.750Z");
    });

    it("should ignore invalid seconds format", () => {
      const formData = {
        ...baseFormData,
        startSeconds: "invalid",
        endSeconds: "also-invalid",
      };

      const result = formatManualEntryForAPI(
        formData,
        "Test Section",
        "Test Equipment",
        "UTC",
      );

      expect(result.startDateLocal).toContain("T10:00:00.000Z");
      expect(result.endDateLocal).toContain("T11:30:00.000Z");
    });

    it("should handle different dates", () => {
      const formData = {
        ...baseFormData,
        startDate: new Date(2024, 0, 15), // January 15, 2024
        endDate: new Date(2024, 0, 16), // January 16, 2024
        startTime: "11:00",
        startPeriod: "PM" as const,
        endTime: "1:00",
        endPeriod: "AM" as const,
      };

      const result = formatManualEntryForAPI(
        formData,
        "Test Section",
        "Test Equipment",
        "UTC",
      );

      expect(result.startDateLocal).toContain("T23:00:00.000Z");
      expect(result.endDateLocal).toContain("T01:00:00.000Z");
      expect(result.startDateLocal).toContain("2024-01-");
      expect(result.endDateLocal).toContain("2024-01-");
    });

    it("should use default timezone when not specified", () => {
      const result = formatManualEntryForAPI(
        baseFormData,
        "Test Section",
        "Test Equipment",
      );

      // Should still produce valid ISO strings
      expect(result.startDateLocal).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
      );
      expect(result.endDateLocal).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
      );
    });

    it("should handle timezone conversion correctly", () => {
      const formData = {
        ...baseFormData,
        startTime: "12:00",
        startPeriod: "PM" as const,
        endTime: "1:00",
        endPeriod: "PM" as const,
      };

      // Test with EST (UTC-5 standard time)
      const resultEST = formatManualEntryForAPI(
        formData,
        "Test Section",
        "Test Equipment",
        "America/New_York",
      );

      // The exact time will depend on whether it's daylight saving time,
      // but we can verify it's a valid ISO string
      expect(resultEST.startDateLocal).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
      );
      expect(resultEST.endDateLocal).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
      );
    });

    it("should throw error when startDate is null", () => {
      const formData = {
        ...baseFormData,
        startDate: null,
      };

      expect(() =>
        formatManualEntryForAPI(formData, "Test Section", "Test Equipment"),
      ).toThrow("Start date and end date are required");
    });

    it("should throw error when endDate is null", () => {
      const formData = {
        ...baseFormData,
        endDate: null,
      };

      expect(() =>
        formatManualEntryForAPI(formData, "Test Section", "Test Equipment"),
      ).toThrow("Start date and end date are required");
    });

    it("should set default values correctly", () => {
      const result = formatManualEntryForAPI(
        baseFormData,
        "Test Section",
        "Test Equipment",
      );

      expect(result.isIncluded).toBe(true);
      expect(result.equipment).toBe("Test Equipment");
      expect(result.section).toBe("Test Section");
    });

    it("should preserve form data fields", () => {
      const formData = {
        ...baseFormData,
        description: "Custom description",
        comments: "Custom comments",
      };

      const result = formatManualEntryForAPI(
        formData,
        "Test Section",
        "Test Equipment",
      );

      expect(result.description).toBe("Custom description");
      expect(result.comments).toBe("Custom comments");
    });

    it("should handle empty strings in form data", () => {
      const formData = {
        ...baseFormData,
        description: "",
        comments: "",
        startSeconds: "",
        endSeconds: "",
      };

      const result = formatManualEntryForAPI(
        formData,
        "Test Section",
        "Test Equipment",
        "UTC",
      );

      expect(result.description).toBe("");
      expect(result.comments).toBe("");
      expect(result.startDateLocal).toContain("T10:00:00.000Z");
      expect(result.endDateLocal).toContain("T11:30:00.000Z");
    });
  });
});
