import type { ManualEntryForm } from "../hooks/manual-entry/use-manual-entry-form";
import { combineDateTime } from "./datetime-formatter";

export function formatManualEntryForAPI(
  formData: ManualEntryForm,
  sectionName: string,
  equipmentName: string,
  facilityTimezone: string = "America/New_York", // Default to EST/EDT, can be configured
) {
  if (!formData.startDate || !formData.endDate) {
    throw new Error("Start date and end date are required");
  }

  const startDateTime = combineDateTime(
    formData.startDate,
    formData.startTime,
    formData.startPeriod,
    formData.startSeconds,
    facilityTimezone,
  );

  const endDateTime = combineDateTime(
    formData.endDate,
    formData.endTime,
    formData.endPeriod,
    formData.endSeconds,
    facilityTimezone,
  );

  return {
    startDateLocal: startDateTime.toISO() || "",
    endDateLocal: endDateTime.toISO() || "",
    isIncluded: true, // Default to true, can be made configurable later
    comments: formData.comments,
    description: formData.description,
    equipment: equipmentName,
    section: sectionName,
  };
}
