import { formatDuration } from "../../../utils";
import { Logger } from "../../../utils/logger";

const logger = new Logger("TimeFormatting");

// Format milliseconds for display and calculations
export const formatMillisForDisplay = (rawValue: string) => {
  if (!rawValue) return "";

  const cleanValue = rawValue.replace(/\D/g, "");

  if (cleanValue.length === 0) {
    return "";
  } else if (cleanValue.length <= 2) {
    // Just seconds: pad and add :000
    const seconds = cleanValue.padStart(2, "0");
    return `${seconds}:000`;
  } else if (cleanValue.length <= 5) {
    // Seconds + milliseconds
    const seconds = cleanValue.slice(0, 2).padStart(2, "0");
    const milliseconds = cleanValue.slice(2).padEnd(3, "0");
    return `${seconds}:${milliseconds}`;
  }
  return rawValue;
};

// Get formatted milliseconds value for calculations (returns 00:000 if empty)
export const getFormattedMillis = (millisValue: string) => {
  if (!millisValue) return "00:000";

  // If it's already formatted (contains colon), return as is
  if (millisValue.includes(":")) {
    return millisValue;
  }

  // If it's raw input, format it
  return formatMillisForDisplay(millisValue) || "00:000";
};

// Format time for display and calculations
export const formatTimeForDisplay = (rawValue: string) => {
  if (!rawValue) return "";

  // Remove any non-digit characters
  const cleanValue = rawValue.replace(/\D/g, "");

  if (cleanValue.length === 0) {
    return "";
  } else if (cleanValue.length <= 2) {
    // Just hours: add :00
    const hours = cleanValue;
    return `${hours}:00`;
  } else if (cleanValue.length <= 4) {
    // Hours + minutes
    const hours = cleanValue.slice(0, cleanValue.length - 2) || "0";
    const minutes = cleanValue.slice(-2).padStart(2, "0");
    return `${hours}:${minutes}`;
  }
  return rawValue;
};

/**
 * Formats a fault tracking duration to a human-readable string
 * Handles both raw millisecond numbers and HH:MM:SS format strings
 * @param duration The duration value (number as string or HH:MM:SS format)
 * @returns Formatted duration string (e.g., "4 hrs 30 mins", "45 mins")
 */
export function formatFaultTrackingDuration(
  duration: string | undefined,
): string {
  if (!duration) return "";

  // Check if the duration is a raw millisecond number (like "4000")
  if (/^\d+$/.test(duration)) {
    const milliseconds = parseInt(duration, 10);
    return formatDuration(milliseconds);
  }

  // For existing HH:MM:SS:mmm format, convert to hours/minutes format
  try {
    const parts = duration.split(":");
    if (parts.length >= 3) {
      const hours = parseInt(parts[0], 10) || 0;
      const minutes = parseInt(parts[1], 10) || 0;

      if (hours > 0) {
        // Show hours and minutes if there are hours
        if (minutes > 0) {
          return `${hours} hrs ${minutes} mins`;
        } else {
          return `${hours} hrs`;
        }
      } else {
        // Show only minutes if less than 1 hour
        return `${minutes} mins`;
      }
    }
  } catch (e) {
    logger.error("Duration parsing error", {
      duration: duration || "unknown",
      error: e instanceof Error ? e.message : String(e),
    });
  }

  // Fallback to raw value
  return duration;
}
