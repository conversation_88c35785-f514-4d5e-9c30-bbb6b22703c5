import { DateTime } from "luxon";

export function validateSecondsFormat(seconds: string): boolean {
  if (!seconds) return true; // Optional field

  const parts = seconds.split(":");
  if (parts.length !== 2) return false;

  const [secStr, msStr] = parts;

  const paddedSec = secStr.padStart(2, "0");
  const secTime = DateTime.fromFormat(paddedSec, "ss");
  if (!secTime.isValid) return false;

  const secValue = parseInt(secStr, 10);
  if (isNaN(secValue) || secValue < 0 || secValue > 59) return false;

  const msValue = parseInt(msStr, 10);
  if (isNaN(msValue) || msValue < 0 || msValue > 999) return false;

  return true;
}

export function parseSecondsToMilliseconds(seconds: string): number | null {
  if (!seconds || !validateSecondsFormat(seconds)) return null;

  const parts = seconds.split(":");
  const secValue = parseInt(parts[0], 10);
  const msValue = parseInt(parts[1], 10);

  // Convert to total milliseconds for comparison
  return secValue * 1000 + msValue;
}

export function parseTimeToMinutes(
  time: string,
  period: "AM" | "PM",
): number | null {
  if (!/^\d{1,2}:\d{2}$/.test(time)) return null;

  const [hourStr, minuteStr] = time.split(":");
  let hour = parseInt(hourStr, 10);
  const minutes = parseInt(minuteStr, 10);
  if (isNaN(hour) || isNaN(minutes)) return null;

  if (period === "AM" && hour === 12) hour = 0;
  if (period === "PM" && hour < 12) hour += 12;

  return hour * 60 + minutes;
}

export function getMinStartDate() {
  const today = new Date();
  const minStartDate = new Date(today);
  minStartDate.setDate(minStartDate.getDate() - 30);
  return minStartDate;
}

export function stripTime(date: Date) {
  return new Date(date.getFullYear(), date.getMonth(), date.getDate());
}

/**
 * Compare two dates for equality, handling null values properly
 * @param date1 - First date to compare (can be null)
 * @param date2 - Second date to compare (can be null)
 * @returns true if dates are different, false if they are the same
 */
export function datesAreDifferent(
  date1: Date | null,
  date2: Date | null,
): boolean {
  if (date1 === null && date2 === null) return false;
  if (date1 === null || date2 === null) return true;
  return date1.getTime() !== date2.getTime();
}

export function hasFormDataChanged(
  currentData: {
    startDate: Date | null;
    endDate: Date | null;
    startTime: string;
    endTime: string;
    startPeriod: "AM" | "PM";
    endPeriod: "AM" | "PM";
    startSeconds: string;
    endSeconds: string;
    comments: string;
  },
  originalData: {
    startDate: Date | null;
    endDate: Date | null;
    startTime: string;
    endTime: string;
    startPeriod: "AM" | "PM";
    endPeriod: "AM" | "PM";
    startSeconds: string;
    endSeconds: string;
    comments: string;
  } | null,
): boolean {
  if (!originalData) return true;

  return (
    datesAreDifferent(currentData.startDate, originalData.startDate) ||
    datesAreDifferent(currentData.endDate, originalData.endDate) ||
    currentData.startTime !== originalData.startTime ||
    currentData.endTime !== originalData.endTime ||
    currentData.startPeriod !== originalData.startPeriod ||
    currentData.endPeriod !== originalData.endPeriod ||
    currentData.startSeconds !== originalData.startSeconds ||
    currentData.endSeconds !== originalData.endSeconds ||
    currentData.comments !== originalData.comments
  );
}
