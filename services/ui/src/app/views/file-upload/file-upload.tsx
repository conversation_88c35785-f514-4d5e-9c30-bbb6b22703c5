import { CheckmarkFilled, Upload } from "@carbon/icons-react";
import { <PERSON><PERSON>, FileUploader, <PERSON><PERSON>, Stack, Tile } from "@carbon/react";
import { useMemo, useState } from "react";
import { ictApi } from "../../api/ict-api";
import { useRoles } from "../../auth/hooks/use-roles";
import { useNotification } from "../../components/toast/use-notification";
import { ViewBar } from "../../components/view-bar/view-bar";
import {
  useConfigSetting,
  type AppConfigSetting,
} from "../../config/hooks/use-config";
import { useViewOptions } from "../../hooks/use-view-options";
import { useLogger } from "../../utils/logger";
import type { BaseViewProps } from "../view-registry.types";
import { FileUploadOptions } from "./file-upload-options";
import classes from "./file-upload.module.css";
import { useTranslation } from "react-i18next";
import { createColumnHelper } from "@tanstack/react-table";
import { FileUploadHistory } from "./types";
import { Datagrid } from "../../components/datagrid";
import { useApiErrorState } from "../../hooks/use-api-error-state";
import { WidgetContainer } from "../../components/widget-container/widget-container";
import { formatISOTime } from "../../utils/date-util";

type FileUploadProps = BaseViewProps & {
  id: string;
  setting: AppConfigSetting;
};

export function FileUpload({
  setting,
  title = "File Upload",
}: FileUploadProps) {
  const [managerFile, setManagerFile] = useState<File | null>(null);
  const [detailsFile, setDetailsFile] = useState<File | null>(null);
  const { success, error } = useNotification();
  const { hasConfiguratorAccess } = useRoles();
  const { t } = useTranslation();
  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");

  const logger = useLogger("file-upload");

  const defaultOptions = {
    sapOrderManagement: true,
    sapOrderDetails: true,
    sapOtherFiles: false,
  };

  const {
    draftOptions,
    handleShowSettings,
    handleShowHistory,
    handleSaveOptions,
    isDirty,
  } = useViewOptions<FileUploadOptions>({
    setting,
    defaultOptions,
    optionsComponent: FileUploadOptions,
  });

  const { mutate: uploadFiles, isPending } = ictApi.client.useMutation(
    "post",
    "/inventory/upload/known-demand",
    {
      onSuccess: (_data) => {
        success(t("fileUpload.uploadSuccessful", "Upload successful")); // Consider translating success/error messages
        setManagerFile(null);
        setDetailsFile(null);
      },
      onError: (err: Error) => {
        error(t("fileUpload.uploadFailed", "Upload failed")); // Consider translating success/error messages
        logger.error(t("fileUpload.uploadFailed", "Upload failed"), err);
      },
    },
  );

  const {
    data: recentUploadData,
    isLoading: uploadHistoryIsLoading,
    error: recentUploadError,
  } = ictApi.client.useQuery(
    "get",
    "/inventory/upload/recent-activity",
    {},
    { retry: false },
  );

  const isUploadHistoryNoDataAvailable = useApiErrorState(recentUploadError);

  let uploadHistoryErrorMessage = undefined;
  if (!isUploadHistoryNoDataAvailable && recentUploadError) {
    uploadHistoryErrorMessage = "No data available";
  }

  const handleManagerFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setManagerFile(files[0]);
    }
  };

  const handleDetailsFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setDetailsFile(files[0]);
    }
  };

  const handleSubmit = () => {
    if (!managerFile || !detailsFile) return;

    const formData = new FormData();
    formData.append("manager", managerFile);
    formData.append("details", detailsFile);

    uploadFiles({
      body: formData as unknown as { manager: string; details: string },
    });
  };

  const isSubmitDisabled = !managerFile || !detailsFile;

  // Define column helper for type safety
  const columnHelper = createColumnHelper<FileUploadHistory>();

  // Define columns for the table
  const columns = useMemo(
    () => [
      columnHelper.accessor("date", {
        header: t("fileUpload.date", "Date"),
        size: 100,
        cell: (info) =>
          formatISOTime(
            info.getValue(),
            "yyyy-LL-dd h:mm:ss a",
            timezoneConfig?.value as string,
          ),
      }),
      columnHelper.accessor("knownOrderCount", {
        header: t("fileUpload.knownOrderCount", "Known Order Count"),
        size: 100,
        cell: (info) => {
          const formatter = new Intl.NumberFormat();
          return formatter.format(info.getValue());
        },
      }),
      columnHelper.accessor("knownOrderLineCount", {
        header: t("fileUpload.knownOrderLineCount", "Known Order Line Count"),
        size: 100,
        cell: (info) => {
          const formatter = new Intl.NumberFormat();
          return formatter.format(info.getValue());
        },
      }),
    ],
    [columnHelper],
  );

  return (
    <div className={classes.container}>
      <ViewBar
        title={title}
        showDatePeriodRange={false}
        showSettings={true}
        saveEnabled={isDirty}
        showSave={true}
        hasConfiguratorAccess={hasConfiguratorAccess}
        showViewHistory={true}
        onSettingsClick={handleShowSettings}
        onSaveClick={handleSaveOptions}
        onViewHistoryClick={handleShowHistory}
      />

      <div className={classes.contentWrapper}>
        <div className={classes.formCard}>
          <Stack gap={5}>
            <Heading className={classes.heading}>
              {t("fileUpload.chooseDocuments", "Choose documents to upload")}
            </Heading>

            <p className={classes.fileFormatNote}>
              {t(
                "fileUpload.supportedFileFormats",
                "Supported file formats: .xlsx, .xls",
              )}
            </p>

            <div className={classes.uploadSteps}>
              {draftOptions.sapOrderManagement && (
                <Tile
                  className={`${classes.uploadTile} ${managerFile ? classes.uploadComplete : ""}`}
                >
                  <div className={classes.uploadTileHeader}>
                    <h4>
                      {t("fileUpload.step1", "Step 1: SAP Order Manager File")}
                    </h4>
                    {managerFile && (
                      <div className={classes.fileStatus}>
                        <CheckmarkFilled className={classes.checkmark} />
                        <span>
                          {t("fileUpload.fileSelected", "File selected")}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="cds--file__container">
                    <FileUploader
                      accept={[".xlsx", ".xls"]}
                      buttonKind="tertiary"
                      buttonLabel={
                        managerFile
                          ? t("fileUpload.replaceFile", "Replace file")
                          : t("fileUpload.selectFile", "Select file")
                      }
                      filenameStatus={managerFile ? "edit" : "complete"}
                      iconDescription={t(
                        "fileUpload.deleteFileDescription",
                        "Delete file",
                      )}
                      labelTitle=""
                      multiple={false}
                      name="managerFile"
                      onChange={handleManagerFileChange}
                      onDelete={() => setManagerFile(null)}
                      size="md"
                    />
                  </div>
                </Tile>
              )}

              {draftOptions.sapOrderDetails && (
                <Tile
                  className={`${classes.uploadTile} ${detailsFile ? classes.uploadComplete : ""}`}
                >
                  <div className={classes.uploadTileHeader}>
                    <h4>
                      {t("fileUpload.step2", "Step 2: SAP Order Details File")}
                    </h4>
                    {detailsFile && (
                      <div className={classes.fileStatus}>
                        <CheckmarkFilled className={classes.checkmark} />
                        <span>
                          {t("fileUpload.fileSelected", "File selected")}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="cds--file__container">
                    <FileUploader
                      accept={[".xlsx", ".xls"]}
                      buttonKind="tertiary"
                      buttonLabel={
                        detailsFile
                          ? t("fileUpload.replaceFile", "Replace file")
                          : t("fileUpload.selectFile", "Select file")
                      }
                      filenameStatus={detailsFile ? "edit" : "complete"}
                      iconDescription={t(
                        "fileUpload.deleteFileDescription",
                        "Delete file",
                      )}
                      labelTitle=""
                      multiple={false}
                      name="detailsFile"
                      onChange={handleDetailsFileChange}
                      onDelete={() => setDetailsFile(null)}
                      size="md"
                    />
                  </div>
                </Tile>
              )}
            </div>

            <div className={classes.buttonContainer}>
              <Button
                disabled={isSubmitDisabled || isPending}
                onClick={handleSubmit}
                renderIcon={Upload}
                className={classes.submitButton}
                size="lg"
              >
                {isPending
                  ? t("fileUpload.uploading", "Uploading...")
                  : t("fileUpload.uploadFilesButton", "Upload Files")}
              </Button>
            </div>
          </Stack>
        </div>
      </div>

      <div style={{ flex: 1, width: "100%" }}>
        <div style={{ margin: 15 }}>
          <div className={classes.tableContainer}>
            <WidgetContainer
              title={t("fileUpload.fileUploadHistory", "File Upload History")}
            >
              <Datagrid
                columns={columns}
                isLoading={uploadHistoryIsLoading}
                error={uploadHistoryErrorMessage}
                data={recentUploadData ?? []}
                mode="client"
                initialPagination={{ pageIndex: 0, pageSize: 25 }}
              />
            </WidgetContainer>
          </div>
        </div>
      </div>
    </div>
  );
}

export default FileUpload;
