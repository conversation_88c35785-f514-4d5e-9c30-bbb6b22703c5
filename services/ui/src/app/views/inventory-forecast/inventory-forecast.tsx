import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { useMemo, useState } from "react";
import { ictApi } from "../../api/ict-api";
import { transformFilters } from "../../api/util/filter-transform-util";
import { ViewBar } from "../../components/view-bar/view-bar";
import { InventoryForecastListTable } from "./components/inventory-forecast-list-table/inventory-forecast-list-table";
import {
  SortField,
  InventoryForecastItem,
} from "./components/inventory-forecast-list-table/types";
import { InventoryForecastTimestamp } from "./components/inventory-forecast-timestamp/inventory-forecast-timestamp";
import { InventoryForecastTimestamp as InventoryForecastTimestampType } from "./components/inventory-forecast-timestamp/types";
import { useApiErrorState } from "../../../app/hooks/use-api-error-state";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";
import { BaseViewProps } from "../view-registry.types";
import { ExportModal } from "../../components/export-modal/export-modal";

export default function InventoryForecast({
  title = "Inventory Forecast",
}: Pick<BaseViewProps, "title">) {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 50,
  });
  const [sorting, setSorting] = useState<SortingState>([
    // Default sort by forwardPickTomorrow
    { id: "forwardPickTomorrow", desc: false },
  ]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const sortFields: SortField[] = useMemo(
    () =>
      sorting.map((sort) => ({
        columnName: sort.id,
        isDescending: sort.desc,
      })),
    [sorting],
  );
  const apiFilters = useMemo(
    () => transformFilters(columnFilters),
    [columnFilters],
  );
  // Use the useQuery hook to fetch data
  const {
    data: listData,
    error: listError,
    isLoading: isListLoading,
    isFetching: isListFetching,
    refetch: refetchList,
  } = ictApi.client.useQuery(
    "post",
    "/inventory/forecast/list",
    {
      body: {
        limit: pagination.pageSize,
        page: pagination.pageIndex,
        filters: apiFilters,
        sortFields,
        ...(globalFilter !== "" && { searchString: globalFilter }),
      },
    },
    {
      enabled: true,
      keepPreviousData: true,
      placeholderData: (prev) => prev,
      retry: false,
      refetchOnWindowFocus: false,
    },
  );

  // Fetch timestamp data
  const {
    data: timestampData,
    isLoading: isTimestampLoading,
    error: timestampError,
  } = ictApi.client.useQuery(
    "get",
    "/inventory/forecast/data-analysis-timestamp",
    {},
    {
      enabled: true,
      retry: false,
      refetchOnWindowFocus: false,
    },
  );

  const isNoDataAvailable = useApiErrorState(listError || timestampError);

  // Type-safe error handling
  type AppError = Error | undefined;
  const inventoryForecastError: AppError = isNoDataAvailable
    ? undefined
    : (listError ?? timestampError) || undefined;

  const inventoryForecastData = useMemo(
    () => (listData?.data ?? []) as unknown as InventoryForecastItem[],
    [listData],
  );
  const rawTimestamps = useMemo(() => {
    if (timestampData) {
      const tsData = timestampData as InventoryForecastTimestampType;
      return {
        inventoryUpdatedAt: tsData.dataUpdateTimestamp,
        forecastPerformedAt: tsData.analysisPerformedTimestamp,
      };
    }
    return { inventoryUpdatedAt: undefined, forecastPerformedAt: undefined };
  }, [timestampData]);
  // Handle refresh click
  const handleRefresh = () => {
    refetchList();
  };

  // Export modal state and handlers
  const [isExportExcelModalOpen, setIsExportExcelModalOpen] = useState(false);
  const handleExportExcelModalClose = () => setIsExportExcelModalOpen(false);

  // Column definitions for export modal
  const getColumnDefinitions = () => {
    return [
      { id: "sku", header: "SKU" },
      { id: "current.reserveStorage", header: "Reserve Storage" },
      { id: "current.forwardPick", header: "Forward Pick" },
      { id: "projected.pendingReplenishment", header: "Pending Replenishment" },
      { id: "projected.pendingPicks", header: "Pending Picks" },
      { id: "projected.allocatedOrders", header: "Allocated Orders" },
      { id: "projected.projectedForwardPick", header: "Forward Pick" },
      { id: "forecast.averageReplenishment", header: "Average Replenishment" },
      { id: "forecast.averageDemand", header: "Average Demand" },
      { id: "forecast.demandTomorrow", header: "Demand Tomorrow" },
      { id: "forecast.knownDemand", header: "Known Demand" },
      { id: "forecast.forwardPickTomorrow", header: "Forward Pick Tomorrow" },
      { id: "forecast.twoDayDemand", header: "Two-Day Demand" },
      { id: "forecast.twoDayForwardPick", header: "Two-Day Forward Pick" },
    ];
  };

  // Export handler
  const handleExportExcel = async (
    fileName: string,
    selectedColumns: Record<string, boolean>,
    signal: AbortSignal,
  ) => {
    const { error, data: blob } = await ictApi.fetchClient.POST(
      "/inventory/forecast/list/export",
      {
        body: {
          page: pagination.pageIndex,
          limit: pagination.pageSize,
          filters: apiFilters,
          sortFields,
          columns: selectedColumns,
          ...(globalFilter !== "" && { searchString: globalFilter }),
        },
        parseAs: "blob",
        signal,
      },
    );

    if (error) {
      throw new Error("Export failed");
    }
    if (!blob) {
      throw new Error("No data to export");
    }

    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(a);
  };

  return (
    <div
      data-testid="ict-inventory-forecast"
      style={{ flex: 1, width: "100%" }}
    >
      <ViewBar title={title} showDatePeriodRange={false}>
        <InventoryForecastTimestamp
          isLoading={isTimestampLoading}
          error={timestampError}
          inventoryUpdatedAt={rawTimestamps.inventoryUpdatedAt}
          forecastPerformedAt={rawTimestamps.forecastPerformedAt}
        />
      </ViewBar>
      <FullPageContainer>
        <InventoryForecastListTable
          data={inventoryForecastData}
          pagination={pagination}
          setPagination={setPagination}
          sorting={sorting}
          setSorting={setSorting}
          columnFilters={columnFilters}
          setColumnFilters={setColumnFilters}
          isLoading={isListLoading || isTimestampLoading}
          isFetching={isListFetching}
          error={inventoryForecastError}
          rowCount={listData?.metadata.totalResults ?? 0}
          onRefresh={handleRefresh}
          inventoryUpdatedAt={rawTimestamps.inventoryUpdatedAt}
          forecastPerformedAt={rawTimestamps.forecastPerformedAt}
          onExport={() => setIsExportExcelModalOpen(true)}
          setGlobalFilter={setGlobalFilter}
        />

        <ExportModal
          open={isExportExcelModalOpen}
          onClose={handleExportExcelModalClose}
          onExport={handleExportExcel}
          filters={columnFilters}
          columnDefs={getColumnDefinitions()}
          baseFileName="Inventory_Forecast"
          globalFilter={globalFilter}
        />
      </FullPageContainer>
    </div>
  );
}
