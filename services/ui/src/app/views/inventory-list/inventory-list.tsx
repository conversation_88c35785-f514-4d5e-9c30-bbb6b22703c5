import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { useMemo, useState } from "react";
import { ictApi } from "../../api/ict-api";
import { transformFilters } from "../../api/util/filter-transform-util";
import { ViewBar } from "../../components/view-bar/view-bar";
import { InventoryListTable } from "./components/inventory-list-table/inventory-list-table";
import { useTranslation } from "react-i18next";
import { ExportModal } from "../../components/export-modal/export-modal";
import { useApiErrorState } from "../../../app/hooks/use-api-error-state";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";
import { formatISOTimeToReadableDateTime } from "../../utils/date-util";
import {
  AppConfigSetting,
  useConfigSetting,
} from "../../config/hooks/use-config";
import InventoryListOptions, {
  InventoryListOptionsForm,
} from "./inventory-list-options";
import { useViewOptions } from "../../hooks/use-view-options";
import { BaseViewProps } from "../view-registry.types";
import { useRoles } from "../../auth/hooks/use-roles";
import {
  facilityColumns,
  InventoryItem,
  InventoryListDataSource,
  wmsColumns,
} from "./types";

type InventoryListProps = BaseViewProps & {
  id: string;
  setting: AppConfigSetting;
};

export function InventoryList({
  setting,
  title = "Inventory List",
}: InventoryListProps) {
  const { draftOptions, handleShowSettings, handleSaveOptions, isDirty } =
    useViewOptions<InventoryListOptions>({
      setting,
      defaultOptions: {
        dataSource: "facility",
      },
      optionsComponent: InventoryListOptionsForm,
    });

  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");
  const { hasConfiguratorAccess } = useRoles();
  // State that was previously in the table component
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 50,
  });

  const [sorting, setSorting] = useState<SortingState>([
    // Default sort by days on hand
    { id: "daysOnHand", desc: false },
  ]);

  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const sortFields = useMemo(
    () =>
      sorting.map((sort) => ({
        columnName: sort.id,
        isDescending: sort.desc,
      })),
    [sorting],
  );
  // Function to generate column definitions for the export dialog
  const getColumnDefinitions = () => {
    return draftOptions.dataSource === "wms" ? wmsColumns : facilityColumns;
  };

  // Determine which endpoint to use based on data source
  const endpoint =
    draftOptions.dataSource === "wms"
      ? "/inventory/wms/skus/list"
      : "/inventory/facility/skus/list";

  // Use the API to fetch inventory data based on selected data source
  const { data, error, isLoading, isFetching, refetch } =
    ictApi.client.useQuery(
      "post",
      endpoint,
      {
        body: {
          page: pagination.pageIndex,
          limit: pagination.pageSize,
          filters: transformFilters(columnFilters),
          sortFields,
          searchString: globalFilter || undefined,
        },
      },
      {
        enabled: true,
        keepPreviousData: true,
        placeholderData: (prev) => prev,
        retry: false,
        refetchOnWindowFocus: false,
      },
    );

  const isNoDataAvailable = useApiErrorState(error);

  // Cast the data to the correct type
  const inventoryData = useMemo(
    () => (data?.data ?? []) as unknown as InventoryItem[],
    [data],
  );

  const { t } = useTranslation();

  // Format the last updated timestamp
  const formattedLastUpdated = data?.data[0]?.latestInventorySnapshotTimestamp
    ? formatISOTimeToReadableDateTime(
        data.data[0].latestInventorySnapshotTimestamp,
        timezoneConfig?.value as string,
      )
    : t("inventoryList.loading", "Loading...");
  // Add state for the export dialog
  const [exportDialogOpen, setExportDialogOpen] = useState(false);

  // Export handler to use selected columns
  const handleExport = async (
    fileName: string,
    selectedColumns: Record<string, boolean>,
    signal: AbortSignal,
  ) => {
    console.log("Starting export process...");

    const { error, data: blob } = await ictApi.fetchClient.POST(
      `${endpoint}/export`,
      {
        body: {
          page: pagination.pageIndex,
          limit: pagination.pageSize,
          filters: transformFilters(columnFilters),
          sortFields,
          columns: selectedColumns,
          searchString: globalFilter || undefined,
        },
        parseAs: "blob",
        signal,
      },
    );

    if (error) {
      throw new Error("Export failed");
    }
    if (!blob) {
      throw new Error("No data to export");
    }

    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(a);
    console.log("Export completed successfully!");
  };

  // Add a function to handle modal close
  const handleExportModalClose = () => {
    setExportDialogOpen(false);
  };

  const handleRefresh = () => {
    refetch();
  };

  return (
    <div
      data-testid="inventory-list"
      style={{ flex: 1, width: "100%", overflowX: "hidden" }}
    >
      <ViewBar
        title={title}
        showDatePeriodRange={false}
        showSettings={true}
        hasConfiguratorAccess={hasConfiguratorAccess}
        onSettingsClick={handleShowSettings}
        saveEnabled={isDirty}
        showSave={true}
        onSaveClick={handleSaveOptions}
      >
        <p
          data-testid="inventory-list-last-update"
          className="cds--type-body-01"
          style={{ color: "#6f6f6f", marginRight: "1rem" }}
        >
          {t(
            "inventoryList.lastUpdated",
            "Inventory Data Updated: {{formattedLastUpdated}}",
            { formattedLastUpdated: formattedLastUpdated },
          )}
        </p>
      </ViewBar>

      <FullPageContainer>
        <InventoryListTable
          data-testid="inventory-list-table"
          dataSource={draftOptions.dataSource as InventoryListDataSource}
          data={inventoryData}
          pagination={pagination}
          setPagination={setPagination}
          sorting={sorting}
          setSorting={setSorting}
          columnFilters={columnFilters}
          setColumnFilters={setColumnFilters}
          isLoading={isLoading}
          isFetching={isFetching}
          onExport={() => setExportDialogOpen(true)}
          onRefresh={handleRefresh}
          error={
            isNoDataAvailable ? undefined : error ? "Error fetching data" : ""
          }
          rowCount={data?.metadata.totalResults ?? 0}
          setGlobalFilter={setGlobalFilter}
        />
        <ExportModal
          open={exportDialogOpen}
          onClose={handleExportModalClose}
          onExport={handleExport}
          filters={columnFilters}
          globalFilter={globalFilter}
          columnDefs={getColumnDefinitions()}
          baseFileName="SKUs_Forward-Pick"
        />
      </FullPageContainer>
    </div>
  );
}

export default InventoryList;
