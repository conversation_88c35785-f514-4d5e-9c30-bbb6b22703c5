import { But<PERSON> } from "@carbon/react";
import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { Route, Routes } from "react-router";
import { useState, useMemo, useCallback } from "react";
import React from "react";
import type { BaseViewProps } from "../view-registry.types";
import { ViewBar } from "../../components/view-bar/view-bar";
import { SimulationTable } from "./components/simulation-table/simulation-table";
import SimulationJobDetail from "./components/simulation-job-detail/simulation-job-detail";
import SimulationPlayerView from "./components/simulation-player-view/simulation-player-view";
import { SimulationJob } from "./types";
import { useApiErrorState } from "../../hooks/use-api-error-state";
import { ictApi } from "../../api/ict-api";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";

// Component to render the simulation job detail with parameters from URL
const SimulationJobDetailWithParams = () => {
  return <SimulationJobDetail />;
};

// Define the props interface for SimulationListViewComponent
interface SimulationListViewProps {
  simulationJobs: SimulationJob[];
  pagination: PaginationState;
  setPagination: (pagination: PaginationState) => void;
  sorting: SortingState;
  setSorting: (sorting: SortingState) => void;
  columnFilters: ColumnFiltersState;
  setColumnFilters: (filters: ColumnFiltersState) => void;
  isLoading: boolean;
  isFetching: boolean;
  isNoDataAvailable: boolean;
  error: unknown;
  totalResults: number;
  setGlobalFilter: (globalFilter: string) => void;
  onRefresh: () => void;
  onSimulation: () => void;
  title: string;
}

// Component to render the simulation list (moved outside of SimulationView)
function SimulationListViewComponent({
  simulationJobs,
  pagination,
  setPagination,
  sorting,
  setSorting,
  columnFilters,
  setColumnFilters,
  isLoading,
  isFetching,
  isNoDataAvailable,
  error,
  totalResults,
  setGlobalFilter,
  onRefresh,
  onSimulation,
  title,
}: SimulationListViewProps) {
  return (
    <>
      <ViewBar title={title}>
        <Button onClick={onSimulation}>Run Simulation</Button>
      </ViewBar>
      <FullPageContainer>
        <SimulationTable
          data={simulationJobs}
          pagination={pagination}
          setPagination={setPagination}
          sorting={sorting}
          setSorting={setSorting}
          columnFilters={columnFilters}
          setColumnFilters={setColumnFilters}
          isLoading={isLoading}
          isFetching={isFetching}
          error={isNoDataAvailable ? undefined : error ? String(error) : ""}
          rowCount={totalResults}
          setGlobalFilter={setGlobalFilter}
          onRefresh={onRefresh}
        />
      </FullPageContainer>
    </>
  );
}

// Memoize the SimulationListView component to prevent unnecessary re-renders
const SimulationListView = React.memo(SimulationListViewComponent);

export function SimulationView(_props: BaseViewProps) {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 50,
  });
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [_globalFilter, setGlobalFilter] = useState<string>("");

  // Use the useQuery hook to fetch data
  const { data, error, isLoading, isFetching, refetch } =
    ictApi.client.useQuery(
      "get",
      "/simulation/jobs/list",
      {},
      { enabled: true, retry: false },
    );

  const isNoDataAvailable = useApiErrorState(error);

  // Cast the data to the correct type
  const simulationJobs = useMemo(
    () => (data?.data ?? []) as SimulationJob[],
    [data],
  );

  // Get total count from metadata
  const totalResults = useMemo(
    () => data?.metadata?.totalResults || simulationJobs.length,
    [data, simulationJobs],
  );

  // Memoize handlers to prevent them from causing re-renders
  const handleSimulation = useCallback(() => {
    // TODO: Implement simulation API call
    console.log("Starting simulation...");
  }, []);

  // Handle refresh click
  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  return (
    <Routes>
      <Route
        index
        element={
          <SimulationListView
            simulationJobs={simulationJobs}
            pagination={pagination}
            setPagination={setPagination}
            sorting={sorting}
            setSorting={setSorting}
            columnFilters={columnFilters}
            setColumnFilters={setColumnFilters}
            isLoading={isLoading}
            isFetching={isFetching}
            isNoDataAvailable={isNoDataAvailable}
            error={error}
            totalResults={totalResults}
            setGlobalFilter={setGlobalFilter}
            onRefresh={handleRefresh}
            onSimulation={handleSimulation}
            title={_props.title ?? "Simulation"}
          />
        }
      />
      <Route path="job/:jobId" element={<SimulationJobDetailWithParams />} />
      <Route path="job/:jobId/player" element={<SimulationPlayerView />} />
    </Routes>
  );
}
export default SimulationView;
