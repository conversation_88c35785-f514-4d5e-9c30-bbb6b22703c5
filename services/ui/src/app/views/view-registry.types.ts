import type { ComponentType, LazyExoticComponent } from "react";
import type { AppConfigSetting } from "../config/hooks/use-config";

/**
 * Base type for view options - can be extended by specific views
 */
export type BaseViewOptions = Record<string, unknown>;

/**
 * The base view props that all Views will use.
 */
export interface BaseViewProps<
  TOptions extends BaseViewOptions = BaseViewOptions,
> {
  id: string;
  setting?: AppConfigSetting;
  options: TOptions;
  title?: string;
}

/**
 * The view definition to map a widget id to React lazily loaded components.
 */
export interface ViewDefinition {
  id: string;
  component: LazyExoticComponent<ComponentType<BaseViewProps>>;
}

/**
 * The base view options props that all Views will use.
 */
export interface BaseViewOptionsProps<
  TOptions extends BaseViewOptions = BaseViewOptions,
> {
  onChange: (options: TOptions) => void;
  options: TOptions;
  onClose: () => void;
  onSave: () => void;
}
