// Mock the chartResolver
// vi.mock("../../api/resolver/category-chart-resolver/category-chart-resolver", () => ({
//   chartResolver: {
//     getCategoryChart: vi.fn(),
//     getCategoryChartInfo: vi.fn(),
//   },
// }));

vi.mock(import("../../api/resolver/category-chart-resolver/category-chart-resolver"), async (importOriginal) => {
  const actual = await importOriginal()
  return {
    ...actual,
    getCategoryChart: vi.fn(),
    getCategoryChartInfo: vi.fn(),
  }
})


import { vi } from "vitest";
import { fireEvent, render, screen, waitFor } from "../../../test-utils";
import { categoryChartResolver } from "../../api/resolver/category-chart-resolver/category-chart-resolver";
import type { ChartInfo } from "../../api/resolver/category-chart-resolver/category-chart-resolver-types";
import { DatePeriod } from "../../types/date-types";
import { ComboChartExtWidgetOptionsForm } from "./combo-chart-ext-widget-options";
import type { ComboChartExtWidgetOptions } from "./types";

// Mock color utility function
vi.mock("../../../components/echarts/utils/carbon-colors", () => ({
  getColorById: vi.fn().mockReturnValue("#mock-color-id"),
}));

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    useTheme: () => ({ theme: "white" }),
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

describe("ComboChartExtWidgetOptionsForm", () => {
  const mockOnChange = vi.fn();
  const defaultOptions: ComboChartExtWidgetOptions = {
    title: "Test Combo Chart",
    type: "dms-movements-by-day",
    chartStyle: "line",
    showAverageLine: false,
    showTargetLine: false,
    targetValue: 0,
    filters: {
      datePeriodRange: DatePeriod.today,
    },
    groupBy: "",
    dmsId: ""
  };

  const mockChartInfo = [
    {
      id: "dms-movements-by-day",
      title: "Test Chart",
    },
    {
      id: "another_chart",
      title: "Another Chart",
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    categoryChartResolver.getCategoryChartInfo = vi.fn().mockResolvedValue(
      mockChartInfo as ChartInfo[]
    )
    // vi.mocked(categoryChartResolver.getCategoryChartInfo).mockResolvedValue(
    //   mockChartInfo as ChartInfo[],
    // );
  });

  it("renders with default options", async () => {
    render(
      <ComboChartExtWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    // Check if title input is rendered with correct value
    expect(
      await screen.findByRole("textbox", { name: "Chart Title" }),
    ).toHaveValue("Test Combo Chart");

    // Check if chart style is selected correctly
    expect(
      await screen.findByRole("combobox", { name: "Chart Style" }),
    ).toHaveValue("Line");

    // Wait for chart info to load
    await waitFor(() => {
      expect(categoryChartResolver.getCategoryChartInfo).toHaveBeenCalledTimes(1);
    });
  });

  it("updates title when input changes", async () => {
    render(
      <ComboChartExtWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    const titleInput = await screen.findByLabelText("Chart Title");
    fireEvent.change(titleInput, { target: { value: "Updated Title" } });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      title: "Updated Title",
    });
  });

  it("changes chart type when selected", async () => {
    render(
      <ComboChartExtWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    // Wait for chart info to load
    await waitFor(() => {
      expect(categoryChartResolver.getCategoryChartInfo).toHaveBeenCalledTimes(1);
    });

    // Find and click the chart type combobox
    const chartTypeField = await screen.findByDisplayValue("Test Chart");
    fireEvent.click(chartTypeField);

    // Select the other chart option
    await waitFor(async () => {
      const anotherChartOption = await screen.findByText("Another Chart");
      fireEvent.click(anotherChartOption);
    });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      type: "another_chart",
    });
  });

  it("changes chart style when selected", async () => {
    render(
      <ComboChartExtWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    // Open the combobox
    const chartStyleCombobox = await screen.findByDisplayValue("Line");
    fireEvent.click(chartStyleCombobox);

    // Wait for the dropdown to appear and select "Area"
    await waitFor(() => {
      const areaOption = screen.getByText("Area");
      fireEvent.click(areaOption);
    });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      chartStyle: "area",
    });
  });

  it("toggles show average line option", async () => {
    render(
      <ComboChartExtWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    const showAverageLineToggle = await screen.findByText("Show Average Line");
    fireEvent.click(showAverageLineToggle);

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      showAverageLine: true,
    });
  });

  it("toggles show target line option", async () => {
    render(
      <ComboChartExtWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    const showTargetLineToggle = await screen.findByText("Show Target Line");
    fireEvent.click(showTargetLineToggle);

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      showTargetLine: true,
    });
  });

  it("shows target value input when target line is enabled", async () => {
    render(
      <ComboChartExtWidgetOptionsForm
        options={{
          ...defaultOptions,
          showTargetLine: true,
        }}
        onChange={mockOnChange}
      />,
    );

    // Check if target value input is rendered
    const targetValueInput = await screen.findByLabelText("Target Value");
    expect(targetValueInput).toBeInTheDocument();

    // Test changing target value
    fireEvent.change(targetValueInput, { target: { value: "75" } });

    // The NumberInput calls onChange with the event and an object containing the value
    expect(mockOnChange).toHaveBeenCalledWith(
      expect.objectContaining({
        targetValue: 75,
      }),
    );
  });
});
