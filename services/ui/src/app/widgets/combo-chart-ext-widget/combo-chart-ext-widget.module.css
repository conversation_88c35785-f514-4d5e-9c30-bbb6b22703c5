.container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
}
 
.chartWrapper {
  flex: 1;
  position: relative;
}
 
.rotatedSortIcon {
  display: inline-flex;
  transform: rotate(-90deg);
  cursor: pointer;
}
 
.bottomSortIcon {
  position: absolute;
  z-index: 10;
  padding: 15px;
  cursor: pointer;
  bottom: 20px;
  right: 10px;
}
 
.sortWrapper {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  position: absolute;
  top: -20px;
  z-index: 10;
  border-radius: 4px;
}
 
.caretIcon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
 
.sortDropdowns {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  margin-bottom: 1rem;
}
 
.dropdownMenu {
  position: absolute;
  top: 80%;
  left: 23px;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  min-width: 150px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow: hidden;
}
 
.dropdownItem {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  cursor: pointer;
  position: relative;
  white-space: nowrap;
}
 
.dropdownItem:hover {
  background-color: #f0f0f0;
}
 
.dropdownItem:not(:last-child)::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  background-color: #e0e0e0;
}
 
.tickMark {
  margin-right: 8px;
  color: green;
}
 
.dropdownLabel {
  flex: 1;
}