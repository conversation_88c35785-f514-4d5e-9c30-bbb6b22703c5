import type { ApiFilters } from "../../api/api.types";
import type { CategoryChartType } from "../../api/resolver/category-chart-resolver/category-chart-types";
import { ChartStyle } from "../../components/combo-chart";

export interface ComboChartExtWidgetOptions {
  /**
   * Title for the chart
   */
  title?: string;
  /**
   * The type of chart data to fetch from the time-chart-resolver
   */
  type?: CategoryChartType;
  /**
   * The style to display the data
   */
  chartStyle?: ChartStyle;
  /**
   * Filters to apply to the chart data
   */
  filters?: ApiFilters;
  colorId?: string;
  chartColor?: string;
  showAverageLine?: boolean;
  showTargetLine?: boolean;
  showLegend?: boolean;
  targetValue?: number;
  dateFormat?: Intl.DateTimeFormatOptions;
  enableSorting?: boolean;
  sortBy?: "none" | "ascending" | "descending";
  sortByName?: "none" | "ascending" | "descending";
  [key: string]: unknown;
}
