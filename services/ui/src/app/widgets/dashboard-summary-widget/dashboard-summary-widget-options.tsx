import { ColorPalette, Analytics, DataBase } from "@carbon/icons-react";
import { useTheme, ComboBox, TextInput } from "@carbon/react";
import { OptionsAccordionGroup } from "../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../components/options/options-accordion/options-accordion";
import type { BaseWidgetOptionsProps } from "../widget.types";
import type { DashboardSummaryWidgetOptions } from "./types";
import { getColorById } from "../../components/echarts/utils/carbon-colors";
import { ColorDropdown } from "../../components/color-dropdown";
import { ThemeMode } from "../../layout/theme";
import { useTranslation } from "react-i18next";
 
// Import dynamic data
import { dashboardSummaryInfo } from "../../api/resolver/dashboard-resolver/dashboard-resolver-data";
 
export const DashboardSummaryWidgetOptionsForm = ({
  options,
  onChange,
}: BaseWidgetOptionsProps<DashboardSummaryWidgetOptions>) => {
  const summaryOptions = options;
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;
  const { t } = useTranslation();
 
  // Use dynamic summary info from resolver
  const localizedSummaryOptions = dashboardSummaryInfo(t).map((opt) => ({
    id: opt.id,
    text: opt.label,
    dms_ids: opt.dms_ids,
  }));
  const allDmsIds = localizedSummaryOptions.flatMap(opt => opt.dms_ids);
 
  const fixedDmsId = allDmsIds[0];
 
  return (
    <OptionsAccordion>
      <OptionsAccordionGroup
        id="display"
        title={t("dashboardWidgetOptions.displayTitle", "Display")}
        icon={<Analytics size="24" />}
      >
        <TextInput
          labelText={t("dashboardWidgetOptions.Title", "Title")}
          id="title"
          value={summaryOptions.title || ""}
          onChange={(e) => {
            onChange({
              ...summaryOptions,
              title: e.target.value,
              dms_id: fixedDmsId,
            });
          }}
        />
      </OptionsAccordionGroup>
 
      <OptionsAccordionGroup
        id="data"
        title={t("dashboardWidgetOptions.dataTitle", "Data")}
        icon={<DataBase size="24" />}
      >
        <ComboBox
          titleText={t("dashboardWidgetOptions.chartType", "Dashboard Type")}
          id="dashboard-type"
          items={localizedSummaryOptions}
          selectedItem={
            summaryOptions.type
              ? localizedSummaryOptions.find(
                (opt) => opt.id === summaryOptions.type
              ) || null
              : null
          }
          onChange={({ selectedItem }) => {
            if (selectedItem) {
              onChange({
                ...summaryOptions,
                type: selectedItem.id,
                dms_id: fixedDmsId,
              });
            }
          }}
          itemToString={(item) => (item ? item.text : "")}
        />
      </OptionsAccordionGroup>
 
      <OptionsAccordionGroup
        id="styling"
        title="Styling"
        icon={<ColorPalette size="24" />}
      >
        <ColorDropdown
          id="chart-color"
          titleText="Color"
          selectedColorId={summaryOptions.colorId ?? undefined}
          onChange={(colorId) => {
            onChange({
              ...summaryOptions,
              colorId: colorId ?? undefined,
              chartColor: colorId ? getColorById(colorId, isDark) : undefined,
              dms_id: fixedDmsId,
            });
          }}
          showSelectedSwatch={true}
          allowDeselect={true}
        />
      </OptionsAccordionGroup>
    </OptionsAccordion>
  );
};
 
export default DashboardSummaryWidgetOptionsForm;