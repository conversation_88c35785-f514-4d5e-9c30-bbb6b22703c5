.container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 80%;
  overflow-x: auto;
  overflow-y: hidden;
}

.movementBox {
  flex: 1 1 0;
  min-width: 5rem;
  color: var(--cds-dashboard-summary-text);
  height: 8.75rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0.375rem;
  font-size: 0.8125rem;
  font-weight: bold;
  text-align: center;
  line-height: 1.4;
}

.movementType {
  white-space: nowrap;
}

.movementValues {
  white-space: normal;
  word-break: break-word;
  font-size: 0.75rem;
  opacity: 0.9;
}

.smallHeading {
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.2;
  margin: 0;
}