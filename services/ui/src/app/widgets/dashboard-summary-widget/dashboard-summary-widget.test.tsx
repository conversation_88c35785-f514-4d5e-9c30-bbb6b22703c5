import { vi } from "vitest";
import { render, screen } from "../../../test-utils";
import type { DashboardSummaryWidgetOptions } from "./dashboard-summary-widget";
import { DatePeriod } from "../../types/date-types";

const startDate = new Date("2023-01-01");
const endDate = new Date("2023-01-31");

vi.mock("../../hooks/use-dates", () => ({
  useDates: () => ({ startDate, endDate }),
}));

vi.mock("../../../../src/app/components/echarts/utils/carbon-colors", () => ({
  getDashboardColorById: vi.fn().mockImplementation((id, isDark) => {
    return isDark ? `#dark-${id}` : `#light-${id}`;
  }),
}));

vi.mock("../../api/ict-api", () => ({
  ictApi: {
    client: { useQuery: vi.fn() },
  },
}));

vi.mock("../../api/resolver/dashboard-resolver/dashboard-resolver", () => ({
  dashboardSummaryResolver: {
    getSummary: vi.fn().mockResolvedValue({
      result: "200",
      unit: "kg",
      data: [
        { name: "Inbound", value: { totalMovements: 100, percentage: 50 } },
        { name: "Outbound", value: { totalMovements: 80, percentage: 40 } },
      ],
    }),
  },
}));

// --- Defaults ---
const defaultOptions: DashboardSummaryWidgetOptions = {
  type: "summary-movements",
  title: "Movements Summary",
  precision: 2,
  unit: false,
  dms_id: "mock-dms-id",
  filters: { datePeriodRange: DatePeriod.today },
  chartColor: undefined,
};

const defaultProps = {
  id: "summary-widget",
  type: "summary-movements",
  options: defaultOptions,
  filters: { datePeriodRange: DatePeriod.today },
};

// --- Tests ---
describe("DashboardSummaryWidget", () => {
  it("renders widget with data", async () => {
    const { DashboardSummaryWidget } = await vi.importActual<
      typeof import("./dashboard-summary-widget")
    >("./dashboard-summary-widget");

    render(<DashboardSummaryWidget {...defaultProps} />);

    expect(await screen.findByText("Movements Summary")).toBeInTheDocument();
    expect(await screen.findByText("Inbound")).toBeInTheDocument();
    expect(await screen.findByText("50.00% | 100.00")).toBeInTheDocument();
    expect(await screen.findByText("Outbound")).toBeInTheDocument();
    expect(await screen.findByText("40.00% | 80.00")).toBeInTheDocument();
  });

  it("applies custom chartColor if provided", async () => {
    const propsWithColor = {
      ...defaultProps,
      options: { ...defaultOptions, chartColor: "#FF0000" },
    };

    const { DashboardSummaryWidget } = await vi.importActual<
      typeof import("./dashboard-summary-widget")
    >("./dashboard-summary-widget");

    render(<DashboardSummaryWidget {...propsWithColor} />);

    const inboundBox = await screen.findByTestId("dashboard-summary-inbound");
    const outboundBox = await screen.findByTestId("dashboard-summary-outbound");

    expect(inboundBox).toHaveStyle({ backgroundColor: "#FF0000" });
    expect(outboundBox).toHaveStyle({ backgroundColor: "#FF0000" });
  });

  it("renders loading state", async () => {
    vi.doMock("@tanstack/react-query", () => ({
      useQuery: () => ({ data: null, isLoading: true, error: null }),
    }));

    const { DashboardSummaryWidget } = await vi.importActual<
      typeof import("./dashboard-summary-widget")
    >("./dashboard-summary-widget");

    render(<DashboardSummaryWidget {...defaultProps} />);
    expect(screen.getByText("Movements Summary")).toBeInTheDocument();
  });

  it("renders with precision formatting", async () => {
    const propsWithPrecision = {
      ...defaultProps,
      options: { ...defaultOptions, precision: 0 },
    };

    const { DashboardSummaryWidget } = await vi.importActual<
      typeof import("./dashboard-summary-widget")
    >("./dashboard-summary-widget");

    render(<DashboardSummaryWidget {...propsWithPrecision} />);
    expect(await screen.findByText("50.00% | 100")).toBeInTheDocument();
    expect(await screen.findByText("40.00% | 80")).toBeInTheDocument();
  });

  it("uses fallback title if none is provided", async () => {
    const propsWithoutTitle = {
      ...defaultProps,
      options: { ...defaultOptions, title: "" },
    };

    const { DashboardSummaryWidget } = await vi.importActual<
      typeof import("./dashboard-summary-widget")
    >("./dashboard-summary-widget");

    render(<DashboardSummaryWidget {...propsWithoutTitle} />);
    expect(await screen.findByText("Dashboard Summary")).toBeInTheDocument();
  });

  it("renders with unit when enabled", async () => {
    const propsWithUnit = {
      ...defaultProps,
      options: { ...defaultOptions, unit: true },
    };

    const { DashboardSummaryWidget } = await vi.importActual<
      typeof import("./dashboard-summary-widget")
    >("./dashboard-summary-widget");

    render(<DashboardSummaryWidget {...propsWithUnit} />);
    expect(await screen.findByText(/100.00 kg/)).toBeInTheDocument();
  });

  it("renders initializing state when type is missing", async () => {
    const propsWithoutType = {
      ...defaultProps,
      options: { ...defaultOptions, type: "" as any },
    };

    const { DashboardSummaryWidget } = await vi.importActual<
      typeof import("./dashboard-summary-widget")
    >("./dashboard-summary-widget");

    render(<DashboardSummaryWidget {...propsWithoutType} />);
    expect(await screen.findByText("Movements Summary")).toBeInTheDocument();
  });

  it("applies dark theme colors when theme is DARK", async () => {
    vi.doMock("@carbon/react", async () => {
      const actual = await vi.importActual<any>("@carbon/react");
      return {
        ...actual,
        useTheme: () => ({ theme: "dark" }),
      };
    });

    const { DashboardSummaryWidget } = await vi.importActual<
      typeof import("./dashboard-summary-widget")
    >("./dashboard-summary-widget");

    render(<DashboardSummaryWidget {...defaultProps} />);
    const inboundBox = await screen.findByTestId("dashboard-summary-inbound");
    expect(inboundBox).toHaveStyle({ backgroundColor: "#dark-inbound" });
  });
});