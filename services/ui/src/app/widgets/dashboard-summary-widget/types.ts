import type { WidgetFilters } from "../widget.types";
 
export interface DashboardSummaryWidgetOptions {
  title?: string;
  type?: string;
  // chartStyle?: ChartStyle;
  filters?: WidgetFilters;
  colorId?: string;
  chartColor?: string;
  showAverageLine?: boolean;
  showTargetLine?: boolean;
  showLegend?: boolean;
  targetValue?: number;
  dateFormat?: Intl.DateTimeFormatOptions;
  [key: string]: unknown;
}