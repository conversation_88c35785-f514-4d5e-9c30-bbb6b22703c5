import { vi } from "vitest";
import { fireEvent, render, screen, waitFor } from "../../../test-utils";
import { heatmapChartResolver } from "../../api/resolver/heatmap-chart-resolver/heatmap-chart-resolver";
import type { HeatmapChartInfo } from "../../api/resolver/heatmap-chart-resolver/heatmap-chart-resolver-types";
import { HeatmapChartWidgetOptionsForm } from "./heatmap-chart-widget-options";
import type { HeatmapChartWidgetOptions } from "./types";

// Mock the heatmapChartResolver
vi.mock(
  "../../api/resolver/heatmap-chart-resolver/heatmap-chart-resolver",
  () => ({
    heatmapChartResolver: {
      getChartInfo: vi.fn(),
    },
  }),
);

describe("HeatmapChartWidgetOptionsForm", () => {
  const mockOnChange = vi.fn();
  const defaultOptions: HeatmapChartWidgetOptions = {
    title: "Test Heatmap Chart",
    type: "test_heatmap",
    showLegend: true,
    showToolbar: false,
    swapAxes: false,
    sidePosition: "left",
    sideLabelFormatter: "default",
    bottomLabelFormatter: "default",
  };

  const mockChartInfo: HeatmapChartInfo[] = [
    {
      id: "test_heatmap",
      title: "Test Heatmap",
    },
    {
      id: "another_heatmap",
      title: "Another Heatmap",
    },
  ] as any;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(heatmapChartResolver.getChartInfo).mockResolvedValue(
      mockChartInfo,
    );
  });

  it("renders with default options", async () => {
    render(
      <HeatmapChartWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    expect(
      await screen.findByRole("textbox", { name: "Chart Title" }),
    ).toHaveValue("Test Heatmap Chart");
    expect(
      await screen.findByRole("switch", { name: "Show Legend" }),
    ).toBeChecked();
    expect(
      await screen.findByRole("switch", { name: "Show Toolbar" }),
    ).not.toBeChecked();
    expect(
      await screen.findByRole("switch", { name: "Swap Axes" }),
    ).not.toBeChecked();
    expect(
      await screen.findByRole("combobox", { name: "Side Position" }),
    ).toHaveValue("Left");

    await waitFor(() => {
      expect(heatmapChartResolver.getChartInfo).toHaveBeenCalledTimes(1);
    });
  });

  it("updates title when input changes", async () => {
    render(
      <HeatmapChartWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    const titleInput = await screen.findByLabelText("Chart Title");
    fireEvent.change(titleInput, { target: { value: "Updated Title" } });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      title: "Updated Title",
    });
  });

  it("toggles show legend", async () => {
    render(
      <HeatmapChartWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    const showLegendToggle = screen.getByRole("switch", {
      name: "Show Legend",
    });
    fireEvent.click(showLegendToggle);

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      showLegend: false,
    });
  });

  it("toggles show toolbar", async () => {
    render(
      <HeatmapChartWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    const showToolbarToggle = screen.getByRole("switch", {
      name: "Show Toolbar",
    });
    fireEvent.click(showToolbarToggle);

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      showToolbar: true,
    });
  });

  it("toggles swap axes", async () => {
    render(
      <HeatmapChartWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    const swapAxesToggle = screen.getByRole("switch", { name: "Swap Axes" });
    fireEvent.click(swapAxesToggle);

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      swapAxes: true,
    });
  });

  it("changes side position, left -> right", async () => {
    render(
      <HeatmapChartWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    const sidePositionField = await screen.findByRole("combobox", {
      name: "Side Position",
    });
    fireEvent.click(sidePositionField);

    await waitFor(async () => {
      const rightOption = await screen.findByText("Right");
      fireEvent.click(rightOption);
    });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      sidePosition: "right",
    });
  });

  it("changes side label formatter, default -> localDate", async () => {
    render(
      <HeatmapChartWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    const sideLabelFormatterField = await screen.findByRole("combobox", {
      name: "Side Label Formatter",
    });
    fireEvent.click(sideLabelFormatterField);

    await waitFor(async () => {
      // Assuming formatters are available and one is 'hourOfDay'
      const formatterOption = await screen.findByText("Local Date");
      fireEvent.click(formatterOption);
    });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      sideLabelFormatter: "localDate",
    });
  });

  it("changes bottom label formatter, default -> localDate", async () => {
    render(
      <HeatmapChartWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    const bottomLabelFormatterField = await screen.findByRole("combobox", {
      name: "Bottom Label Formatter",
    });
    fireEvent.click(bottomLabelFormatterField);

    await waitFor(async () => {
      // Assuming formatters are available and one is 'dayOfWeek'
      const formatterOption = await screen.findByText("Local Date");
      fireEvent.click(formatterOption);
    });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      bottomLabelFormatter: "localDate",
    });
  });

  it("changes chart type when selected", async () => {
    render(
      <HeatmapChartWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    await waitFor(() => {
      expect(heatmapChartResolver.getChartInfo).toHaveBeenCalledTimes(1);
    });

    const chartTypeField = await screen.findByRole("combobox", {
      name: "Chart Type",
    });
    fireEvent.click(chartTypeField);

    await waitFor(async () => {
      const anotherChartOption = await screen.findByText("Another Heatmap");
      fireEvent.click(anotherChartOption);
    });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      type: "another_heatmap",
    });
  });
});
