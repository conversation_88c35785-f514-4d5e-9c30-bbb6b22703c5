import { Analytics, DataBase } from "@carbon/icons-react";
import { ComboBox, TextInput, Toggle } from "@carbon/react";
import { useQuery } from "@tanstack/react-query";
import { OptionsAccordionGroup } from "../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../components/options/options-accordion/options-accordion";
import type { BaseWidgetOptionsProps } from "../widget.types";
import type { HeatmapChartWidgetOptions } from "./types";
import { useTranslation } from "react-i18next";
import { heatmapChartResolver } from "../../api/resolver/heatmap-chart-resolver/heatmap-chart-resolver";
import { heatmapChartFormatters } from "../../components/heatmap-chart/heatmap-chart-formatters";
import { formatCamelCaseToTitleCase } from "../../utils/string-util";

export const HeatmapChartWidgetOptionsForm = ({
  options,
  onChange,
}: BaseWidgetOptionsProps) => {
  const heatmapOptions = options as HeatmapChartWidgetOptions;
  const { t } = useTranslation();

  const { data: heatmapChartInfo } = useQuery({
    queryKey: ["heatmap-chart-info"],
    queryFn: () => heatmapChartResolver.getChartInfo(),
  });

  return (
    <OptionsAccordion>
      <OptionsAccordionGroup
        id="display"
        title={t("chartWidgetOptions.displayTitle", "Display")}
        icon={<Analytics size="24" />}
      >
        <TextInput
          labelText={t("chartWidgetOptions.chartTitle", "Chart Title")}
          id="chart-title"
          value={heatmapOptions.title || ""}
          onChange={(e) =>
            onChange({ ...heatmapOptions, title: e.target.value })
          }
        />
        <Toggle
          labelText="Show Legend"
          id="show-legend"
          toggled={heatmapOptions.showLegend !== false}
          onToggle={(toggled) =>
            onChange({
              ...heatmapOptions,
              showLegend: toggled,
            })
          }
        />
        <Toggle
          labelText="Show Toolbar"
          id="show-toolbar"
          toggled={heatmapOptions.showToolbar || false}
          onToggle={(toggled) =>
            onChange({
              ...heatmapOptions,
              showToolbar: toggled,
            })
          }
        />
        <Toggle
          labelText="Swap Axes"
          id="swap-axes"
          toggled={heatmapOptions.swapAxes || false}
          onToggle={(toggled) =>
            onChange({
              ...heatmapOptions,
              swapAxes: toggled,
            })
          }
        />
        <ComboBox
          titleText="Side Position"
          id="side-position"
          items={[
            { id: "left", text: "Left" },
            { id: "right", text: "Right" },
          ]}
          selectedItem={
            heatmapOptions.sidePosition
              ? {
                  id: heatmapOptions.sidePosition,
                  text:
                    heatmapOptions.sidePosition.charAt(0).toUpperCase() +
                    heatmapOptions.sidePosition.slice(1),
                }
              : { id: "left", text: "Left" }
          }
          onChange={({ selectedItem }) => {
            onChange({
              ...heatmapOptions,
              sidePosition: selectedItem?.id ?? "left",
            });
          }}
          itemToString={(item) => item?.text ?? "Left"}
        />
        <ComboBox
          titleText="Side Label Formatter"
          id="side-label-formatter"
          items={Object.keys(heatmapChartFormatters).map((key) => ({
            id: key,
            text: formatCamelCaseToTitleCase(key),
          }))}
          selectedItem={
            heatmapOptions.sideLabelFormatter
              ? {
                  id: heatmapOptions.sideLabelFormatter,
                  text: formatCamelCaseToTitleCase(
                    heatmapOptions.sideLabelFormatter,
                  ),
                }
              : { id: "default", text: "Default" }
          }
          onChange={({ selectedItem }) => {
            onChange({
              ...heatmapOptions,
              sideLabelFormatter: selectedItem?.id ?? "default",
            });
          }}
          itemToString={(item) => item?.text ?? "Default"}
        />
        <ComboBox
          titleText="Bottom Label Formatter"
          id="bottom-label-formatter"
          items={Object.keys(heatmapChartFormatters).map((key) => ({
            id: key,
            text: formatCamelCaseToTitleCase(key),
          }))}
          selectedItem={
            heatmapOptions.bottomLabelFormatter
              ? {
                  id: heatmapOptions.bottomLabelFormatter,
                  text: formatCamelCaseToTitleCase(
                    heatmapOptions.bottomLabelFormatter,
                  ),
                }
              : { id: "default", text: "Default" }
          }
          onChange={({ selectedItem }) => {
            onChange({
              ...heatmapOptions,
              bottomLabelFormatter: selectedItem?.id ?? "default",
            });
          }}
          itemToString={(item) => item?.text ?? "Default"}
        />
      </OptionsAccordionGroup>

      <OptionsAccordionGroup
        id="data"
        title={t("chartWidgetOptions.dataTitle", "Data")}
        icon={<DataBase size="24" />}
      >
        <ComboBox
          titleText={t("chartWidgetOptions.chartType", "Chart Type")}
          id="chart-type"
          items={
            heatmapChartInfo
              ? [...heatmapChartInfo]
                  .sort((a, b) => a.id.localeCompare(b.id))
                  .map((info) => ({
                    id: info.id,
                    text: info.title,
                  }))
              : []
          }
          selectedItem={
            heatmapOptions.type
              ? {
                  id: heatmapOptions.type,
                  text:
                    heatmapChartInfo?.find(
                      (info) => info.id === heatmapOptions.type,
                    )?.title || "",
                }
              : null
          }
          onChange={({ selectedItem }) => {
            onChange({ ...heatmapOptions, type: selectedItem?.id });
          }}
          itemToString={(item) => item?.text ?? ""}
        />
      </OptionsAccordionGroup>
    </OptionsAccordion>
  );
};

export default HeatmapChartWidgetOptionsForm;
