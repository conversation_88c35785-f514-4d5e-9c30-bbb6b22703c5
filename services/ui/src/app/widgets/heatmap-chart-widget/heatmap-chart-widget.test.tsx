import { useQuery } from "@tanstack/react-query";
import { vi } from "vitest";
import { render, screen } from "../../../test-utils";
import { heatmapChartResolver } from "../../api/resolver/heatmap-chart-resolver/heatmap-chart-resolver";
import { DatePeriod } from "../../types/date-types";
import { HeatmapChartWidget } from "./heatmap-chart-widget";
import type { HeatmapChartWidgetOptions } from "./types";
import { HeatmapChartData } from "../../components/heatmap-chart";

const startDate = new Date("2023-01-01");
const endDate = new Date("2023-01-31");

vi.mock("../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate,
    endDate,
  }),
}));

// Mock the heatmapChartResolver
vi.mock(
  "../../api/resolver/heatmap-chart-resolver/heatmap-chart-resolver",
  () => ({
    heatmapChartResolver: {
      getHeatmapChart: vi.fn(),
    },
  }),
);

// Mock the useQuery hook
vi.mock("@tanstack/react-query", async () => {
  const actual = await vi.importActual("@tanstack/react-query");
  return {
    ...actual,
    useQuery: vi.fn().mockImplementation(({ queryFn, enabled }) => {
      if (!enabled) {
        return { isLoading: false, error: null, data: null };
      }
      try {
        const data = queryFn();
        return { isLoading: false, error: null, data };
      } catch (error) {
        return { isLoading: false, error, data: null };
      }
    }),
  };
});

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    useTheme: () => ({ theme: "white" }),
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock the HeatmapChartComponent
vi.mock("../../components/heatmap-chart/heatmap-chart-component", () => ({
  HeatmapChartComponent: ({
    chartData,
    legendTitle,
    sidePosition,
    showLegend,
    showToolbar,
    swapAxes,
    bottomLabelFormatter,
    sideLabelFormatter,
  }: any) => (
    <div data-testid="heatmap-chart-component">
      <div data-testid="chart-data">{JSON.stringify(chartData)}</div>
      <div data-testid="legend-title">{legendTitle}</div>
      <div data-testid="side-position">{sidePosition}</div>
      <div data-testid="show-legend">{showLegend ? "true" : "false"}</div>
      <div data-testid="show-toolbar">{showToolbar ? "true" : "false"}</div>
      <div data-testid="swap-axes">{swapAxes ? "true" : "false"}</div>
      <div data-testid="bottom-label-formatter">
        {bottomLabelFormatter ? "custom" : "default"}
      </div>
      <div data-testid="side-label-formatter">
        {sideLabelFormatter ? "custom" : "default"}
      </div>
    </div>
  ),
}));

// Mock the WidgetContainer component
vi.mock("../../components/widget-container/widget-container", () => ({
  WidgetContainer: ({
    children,
    title,
    loading,
    error,
    noData,
    initializing,
  }: any) => (
    <div data-testid="widget-container">
      <h3>{title}</h3>
      {initializing && <div data-testid="initializing">Initializing</div>}
      {loading && <div data-testid="loading">Loading</div>}
      {error && <div data-testid="error">Error</div>}
      {noData && <div data-testid="no-data">No Data</div>}
      {!initializing && !loading && !error && !noData && children}
    </div>
  ),
}));

describe("HeatmapChartWidget", () => {
  const mockHeatmapChartData: HeatmapChartData = {
    id: "test_heatmap",
    bottomTitle: "Bottom Categories",
    sideTitle: "Side Categories",
    series: [
      {
        id: "category1",
        data: [
          { label: "itemA", value: 10 },
          { label: "itemB", value: 20 },
        ],
      },
      {
        id: "category2",
        data: [
          { label: "itemA", value: 30 },
          { label: "itemB", value: 40 },
        ],
      },
    ],
  };

  const mockSuccessResponse = {
    success: true,
    data: mockHeatmapChartData,
  };

  const mockEmptyResponse = {
    success: true,
    data: null,
  };

  const defaultOptions: HeatmapChartWidgetOptions = {
    title: "Test Heatmap Chart",
    type: "test_heatmap",
    showLegend: true,
    showToolbar: false,
    swapAxes: false,
    sidePosition: "left",
    bottomLabelFormatter: "default",
    sideLabelFormatter: "default",
    filters: {
      datePeriodRange: DatePeriod.today,
    },
  };

  const defaultFilters = {
    datePeriodRange: DatePeriod.today,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(heatmapChartResolver.getHeatmapChart).mockReturnValue(
      mockSuccessResponse as any,
    );
  });

  it("renders the widget with chart data", () => {
    render(
      <HeatmapChartWidget
        id="test_heatmap_widget"
        type="test_heatmap"
        options={defaultOptions}
        filters={defaultFilters}
      />,
    );

    expect(screen.getByTestId("widget-container")).toBeInTheDocument();
    expect(screen.getByText("Test Heatmap Chart")).toBeInTheDocument();
    expect(screen.getByTestId("heatmap-chart-component")).toBeInTheDocument();
    expect(screen.getByTestId("chart-data")).toHaveTextContent(
      JSON.stringify(mockHeatmapChartData),
    );
    expect(screen.getByTestId("legend-title")).toHaveTextContent("");
    expect(screen.getByTestId("side-position")).toHaveTextContent("left");
    expect(screen.getByTestId("show-legend")).toHaveTextContent("true");
    expect(screen.getByTestId("show-toolbar")).toHaveTextContent("false");
    expect(screen.getByTestId("swap-axes")).toHaveTextContent("false");
    expect(screen.getByTestId("bottom-label-formatter")).toHaveTextContent(
      "custom",
    );
    expect(screen.getByTestId("side-label-formatter")).toHaveTextContent(
      "custom",
    );
  });

  it("shows loading state while fetching data", () => {
    vi.mocked(useQuery).mockReturnValueOnce({
      isLoading: true,
      error: null,
      data: null,
    } as any);

    render(
      <HeatmapChartWidget
        id="test_heatmap_widget"
        type="test_heatmap"
        options={defaultOptions}
        filters={defaultFilters}
      />,
    );

    expect(screen.getByTestId("loading")).toBeInTheDocument();
  });

  it("shows no data state when API returns empty data", () => {
    vi.mocked(heatmapChartResolver.getHeatmapChart).mockReturnValue(
      mockEmptyResponse as any,
    );

    render(
      <HeatmapChartWidget
        id="test_heatmap_widget"
        type="test_heatmap"
        options={defaultOptions}
        filters={defaultFilters}
      />,
    );

    expect(screen.getByTestId("no-data")).toBeInTheDocument();
  });

  it("makes API request with correct parameters", () => {
    render(
      <HeatmapChartWidget
        id="test_heatmap_widget"
        type="test_heatmap"
        options={defaultOptions}
        filters={defaultFilters}
      />,
    );

    expect(heatmapChartResolver.getHeatmapChart).toHaveBeenCalledWith(
      "test_heatmap",
      {
        ...defaultOptions.filters,
        ...defaultFilters,
      },
      startDate,
      endDate,
    );
  });

  it("merges filters from options and props", () => {
    const customFilters = {
      datePeriodRange: DatePeriod.yesterday,
      customFilter: "custom_value",
    };

    render(
      <HeatmapChartWidget
        id="test_heatmap_widget"
        type="test_heatmap"
        options={defaultOptions}
        filters={customFilters}
      />,
    );

    expect(heatmapChartResolver.getHeatmapChart).toHaveBeenCalledWith(
      "test_heatmap",
      {
        ...defaultOptions.filters,
        ...customFilters,
      },
      startDate,
      endDate,
    );
  });

  it("passes legendTitle to chart component", () => {
    const optionsWithLegendTitle = {
      ...defaultOptions,
      legendTitle: "Custom Legend",
    };

    render(
      <HeatmapChartWidget
        id="test_heatmap_widget"
        type="test_heatmap"
        options={optionsWithLegendTitle}
        filters={defaultFilters}
      />,
    );

    expect(screen.getByTestId("legend-title")).toHaveTextContent(
      "Custom Legend",
    );
  });

  it("passes sidePosition to chart component", () => {
    const optionsWithSidePosition = {
      ...defaultOptions,
      sidePosition: "right" as const,
    };

    render(
      <HeatmapChartWidget
        id="test_heatmap_widget"
        type="test_heatmap"
        options={optionsWithSidePosition}
        filters={defaultFilters}
      />,
    );

    expect(screen.getByTestId("side-position")).toHaveTextContent("right");
  });

  it("passes showLegend to chart component", () => {
    const optionsWithShowLegend = {
      ...defaultOptions,
      showLegend: false,
    };

    render(
      <HeatmapChartWidget
        id="test_heatmap_widget"
        type="test_heatmap"
        options={optionsWithShowLegend}
        filters={defaultFilters}
      />,
    );

    expect(screen.getByTestId("show-legend")).toHaveTextContent("false");
  });

  it("passes showToolbar to chart component", () => {
    const optionsWithShowToolbar = {
      ...defaultOptions,
      showToolbar: true,
    };

    render(
      <HeatmapChartWidget
        id="test_heatmap_widget"
        type="test_heatmap"
        options={optionsWithShowToolbar}
        filters={defaultFilters}
      />,
    );

    expect(screen.getByTestId("show-toolbar")).toHaveTextContent("true");
  });

  it("passes swapAxes to chart component", () => {
    const optionsWithSwapAxes = {
      ...defaultOptions,
      swapAxes: true,
    };

    render(
      <HeatmapChartWidget
        id="test_heatmap_widget"
        type="test_heatmap"
        options={optionsWithSwapAxes}
        filters={defaultFilters}
      />,
    );

    expect(screen.getByTestId("swap-axes")).toHaveTextContent("true");
  });
});
