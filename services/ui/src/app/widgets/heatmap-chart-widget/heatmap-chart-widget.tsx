import { BaseWidgetProps } from "../widget.types";
import { useQuery } from "@tanstack/react-query";
import { useDates } from "../../hooks/use-dates";
import { useWidgetAutoRefresh } from "../../hooks/use-widget-auto-refresh";
import { WidgetContainer } from "../../components/widget-container/widget-container";
import { HeatmapChartWidgetOptions } from "./types";
import { HeatmapChartType } from "../../api/resolver/heatmap-chart-resolver/heatmap-chart-types";
import { heatmapChartResolver } from "../../api/resolver/heatmap-chart-resolver/heatmap-chart-resolver";
import { HeatmapChartComponent } from "../../components/heatmap-chart";
import styles from "./heatmap-chart-widget.module.css";
import { heatmapChartFormatters } from "../../components/heatmap-chart/heatmap-chart-formatters";

interface HeatmapChartWidgetProps
  extends BaseWidgetProps<HeatmapChartWidgetOptions> {
  options: HeatmapChartWidgetOptions;
}

export const HeatmapChartWidget = ({
  options,
  filters,
}: HeatmapChartWidgetProps) => {
  const mergedFilters = { ...options.filters, ...filters };
  const { startDate, endDate } = useDates(mergedFilters.datePeriodRange);

  const {
    data: response,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: [
      "heatmap-chart",
      options.type,
      options.filters,
      filters.datePeriodRange,
    ],
    queryFn: () =>
      heatmapChartResolver.getHeatmapChart(
        options.type as HeatmapChartType,
        mergedFilters,
        startDate,
        endDate,
      ),
    enabled: !!options.type,
    retry: 0,
  });

  const handleRefresh = () => {
    refetch();
  };

  useWidgetAutoRefresh({
    filters,
    refetch: handleRefresh,
  });

  const chartTitle = options.title || "Heatmap Chart";

  if (!options.type) {
    return <WidgetContainer title={chartTitle} initializing />;
  }

  if (error) {
    return <WidgetContainer title={chartTitle} error={error} />;
  }

  if (isLoading || !response) {
    return <WidgetContainer title={chartTitle} loading />;
  }

  if (!response.success || !response.data) {
    return <WidgetContainer title={chartTitle} noData />;
  }

  return (
    <WidgetContainer title={chartTitle}>
      <div
        className={styles.container}
        onMouseDown={(e) => e.stopPropagation()}
        onTouchStart={(e) => e.stopPropagation()}
      >
        <HeatmapChartComponent
          chartData={response.data}
          legendTitle={options.legendTitle}
          sidePosition={options.sidePosition}
          bottomLabelFormatter={
            heatmapChartFormatters[options.bottomLabelFormatter ?? "default"]
          }
          sideLabelFormatter={
            heatmapChartFormatters[options.sideLabelFormatter ?? "default"]
          }
          showLegend={options.showLegend}
          showToolbar={options.showToolbar}
          swapAxes={options.swapAxes}
        />
      </div>
    </WidgetContainer>
  );
};

export default HeatmapChartWidget;
