import type { WidgetFilters } from "../widget.types";
import { HeatmapChartType } from "../../api/resolver/heatmap-chart-resolver/heatmap-chart-types";
import { HeatmapChartFormatter } from "../../components/heatmap-chart/heatmap-chart-formatters";

export interface HeatmapChartWidgetOptions {
  title: string;
  type?: HeatmapChartType;
  sidePosition?: "left" | "right";
  legendTitle?: string;
  showLegend?: boolean;
  showToolbar?: boolean;
  swapAxes?: boolean;
  bottomLabelFormatter?: HeatmapChartFormatter;
  sideLabelFormatter?: HeatmapChartFormatter;
  filters?: WidgetFilters;
  [key: string]: unknown;
}
