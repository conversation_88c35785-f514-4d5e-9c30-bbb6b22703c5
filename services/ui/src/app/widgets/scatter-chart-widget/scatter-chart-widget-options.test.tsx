import { useQuery } from "@tanstack/react-query";
import { vi } from "vitest";
import { render, screen, fireEvent } from "../../../test-utils";
import { chartResolver } from "../../api/resolver/time-chart-resolver/time-chart-resolver";
import type { ChartInfo } from "../../api/resolver/time-chart-resolver/time-chart-resolver-types";
import { ScatterChartWidgetOptionsForm } from "./scatter-chart-widget-options";
import type { ScatterChartWidgetOptions } from "./types";

// Mock the chartResolver
vi.mock("../../api/resolver/time-chart-resolver/time-chart-resolver", () => ({
  chartResolver: {
    getChartInfo: vi.fn(),
  },
}));

// Mock the useQuery hook
vi.mock("@tanstack/react-query", async () => {
  const actual = await vi.importActual("@tanstack/react-query");
  return {
    ...actual,
    useQuery: vi.fn().mockImplementation(({ queryFn, enabled }) => {
      if (!enabled) {
        return { isLoading: false, error: null, data: null };
      }
      try {
        const data = queryFn();
        return { isLoading: false, error: null, data };
      } catch (error) {
        return { isLoading: false, error, data: null };
      }
    }),
  };
});

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    TextInput: ({ labelText, value, onChange, id }: any) => (
      <div>
        <label htmlFor={id}>{labelText}</label>
        <input
          id={id}
          value={value}
          onChange={(e) => onChange(e)}
          data-testid={id}
        />
      </div>
    ),
    ComboBox: ({ titleText, items = [], selectedItem, onChange, id }: any) => (
      <div>
        <label htmlFor={id}>{titleText}</label>
        <select
          id={id}
          value={selectedItem?.id || ""}
          onChange={(e) => {
            const item = items.find((i: any) => i.id === e.target.value);
            if (item) {
              onChange({ selectedItem: item });
            }
          }}
          data-testid={id}
        >
          <option value="">Select...</option>
          {items.map((item: any) => (
            <option key={item.id} value={item.id}>
              {item.text}
            </option>
          ))}
        </select>
      </div>
    ),
  };
});

describe("ScatterChartWidgetOptionsForm", () => {
  const mockTimeChartInfo: ChartInfo[] = [
    {
      id: "orders-customer-line-throughput",
      title: "Order Line Throughput",
      description: "Area order line throughput series trend data.",
    },
    {
      id: "orders-facility-line-progress-series",
      title: "Facility Order Line Progress Series",
      description: "Facility order line progress series trend data.",
    },
    {
      id: "inventory-stock-distribution-at-percentage",
      title: "Inventory Stock Distribution at Percentage",
      description:
        "Area inventory stock distribution at percentage series trend data.",
    },
  ];

  const defaultOptions: ScatterChartWidgetOptions = {
    title: "Test Scatter Chart",
    type: "orders-customer-line-throughput",
  };

  const mockOnChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (chartResolver.getChartInfo as any).mockResolvedValue(mockTimeChartInfo);

    // Mock useQuery to return the chart info data
    (useQuery as any).mockReturnValue({
      isLoading: false,
      error: null,
      data: mockTimeChartInfo,
    });
  });

  describe("rendering", () => {
    it("should render title input field", () => {
      render(
        <ScatterChartWidgetOptionsForm
          options={defaultOptions}
          onChange={mockOnChange}
        />,
      );

      expect(screen.getByLabelText("Chart Title")).toBeInTheDocument();
      expect(
        screen.getByDisplayValue("Test Scatter Chart"),
      ).toBeInTheDocument();
    });

    it("should render chart type dropdown", () => {
      render(
        <ScatterChartWidgetOptionsForm
          options={defaultOptions}
          onChange={mockOnChange}
        />,
      );

      expect(screen.getByLabelText("Chart Type")).toBeInTheDocument();
      expect(screen.getByTestId("chart-type")).toBeInTheDocument();
    });

    it("should populate chart type dropdown with sorted options", () => {
      render(
        <ScatterChartWidgetOptionsForm
          options={defaultOptions}
          onChange={mockOnChange}
        />,
      );

      const dropdown = screen.getByTestId("chart-type");
      const options = Array.from(dropdown.querySelectorAll("option"));

      // Should have 4 options (3 chart types + 1 "Select..." option)
      expect(options).toHaveLength(4);

      // Check that options are sorted by title
      expect(options[1].textContent).toBe(
        "Facility Order Line Progress Series",
      );
      expect(options[2].textContent).toBe(
        "Inventory Stock Distribution at Percentage",
      );
      expect(options[3].textContent).toBe("Order Line Throughput");
    });
  });

  describe("user interactions", () => {
    it("should update title when input changes", () => {
      render(
        <ScatterChartWidgetOptionsForm
          options={defaultOptions}
          onChange={mockOnChange}
        />,
      );

      const titleInput = screen.getByTestId("chart-title");
      fireEvent.change(titleInput, { target: { value: "New Title" } });

      expect(mockOnChange).toHaveBeenCalledWith({
        ...defaultOptions,
        title: "New Title",
      });
    });

    it("should update chart type when dropdown selection changes", () => {
      render(
        <ScatterChartWidgetOptionsForm
          options={defaultOptions}
          onChange={mockOnChange}
        />,
      );

      const chartTypeDropdown = screen.getByTestId("chart-type");
      fireEvent.change(chartTypeDropdown, {
        target: { value: "inventory-stock-distribution-at-percentage" },
      });

      expect(mockOnChange).toHaveBeenCalledWith({
        ...defaultOptions,
        type: "inventory-stock-distribution-at-percentage",
      });
    });

    it("should not update when invalid chart type is selected", () => {
      render(
        <ScatterChartWidgetOptionsForm
          options={defaultOptions}
          onChange={mockOnChange}
        />,
      );

      const chartTypeDropdown = screen.getByTestId("chart-type");
      fireEvent.change(chartTypeDropdown, { target: { value: "" } });

      expect(mockOnChange).not.toHaveBeenCalled();
    });
  });

  describe("chart info loading", () => {
    it("should handle empty chart info", () => {
      (useQuery as any).mockReturnValue({
        isLoading: false,
        error: null,
        data: [],
      });

      render(
        <ScatterChartWidgetOptionsForm
          options={defaultOptions}
          onChange={mockOnChange}
        />,
      );

      const dropdown = screen.getByTestId("chart-type");
      const options = Array.from(dropdown.querySelectorAll("option"));

      // Should only have the "Select..." option
      expect(options).toHaveLength(1);
    });

    it("should call chartResolver.getChartInfo on mount", () => {
      render(
        <ScatterChartWidgetOptionsForm
          options={defaultOptions}
          onChange={mockOnChange}
        />,
      );

      // The useQuery mock should be called, which means the queryFn (chartResolver.getChartInfo) would be called
      expect(useQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          queryKey: ["time-chart-info"],
        }),
      );
    });
  });

  describe("accessibility", () => {
    it("should have proper labels for form inputs", () => {
      render(
        <ScatterChartWidgetOptionsForm
          options={defaultOptions}
          onChange={mockOnChange}
        />,
      );

      expect(screen.getByLabelText("Chart Title")).toBeInTheDocument();
      expect(screen.getByLabelText("Chart Type")).toBeInTheDocument();
    });

    it("should have proper test ids for form inputs", () => {
      render(
        <ScatterChartWidgetOptionsForm
          options={defaultOptions}
          onChange={mockOnChange}
        />,
      );

      expect(screen.getByTestId("chart-title")).toBeInTheDocument();
      expect(screen.getByTestId("chart-type")).toBeInTheDocument();
    });
  });
});
