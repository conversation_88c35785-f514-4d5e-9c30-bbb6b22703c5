import { Analytics, DataBase } from "@carbon/icons-react";
import { ComboBox, TextInput } from "@carbon/react";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { chartResolver } from "../../api/resolver/time-chart-resolver/time-chart-resolver";
import type { ChartInfo } from "../../api/resolver/time-chart-resolver/time-chart-resolver-types";
import type { TimeChartType } from "../../api/resolver/time-chart-resolver/time-chart-types";
import { OptionsAccordionGroup } from "../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../components/options/options-accordion/options-accordion";
import type { BaseWidgetOptionsProps } from "../widget.types";
import type { ScatterChartWidgetOptions } from "./types";
import { useTranslation } from "react-i18next";

export const ScatterChartWidgetOptionsForm = ({
  options,
  onChange,
}: BaseWidgetOptionsProps<ScatterChartWidgetOptions>) => {
  const scatterChartOptions = options;
  const { t } = useTranslation();
  const [_selectedChart, _setSelectedChart] = useState<ChartInfo | null>(null);

  const { data: timeChartInfo } = useQuery({
    queryKey: ["time-chart-info"],
    queryFn: () => chartResolver.getChartInfo(),
  });

  useEffect(() => {
    if (timeChartInfo) {
      const selectedMetric = timeChartInfo?.find(
        (info) => info.id === scatterChartOptions.type,
      );
      _setSelectedChart(selectedMetric || null);
    }
  }, [timeChartInfo, scatterChartOptions.type]);

  const handleChartTypeChange = (selectedId: string | null) => {
    if (!selectedId) {
      return;
    }

    const newChartType = selectedId as TimeChartType;
    const selected = timeChartInfo?.find((info) => info.id === selectedId);
    if (selected) {
      onChange({
        ...scatterChartOptions,
        type: newChartType,
      });
    }
  };

  return (
    <OptionsAccordion>
      <OptionsAccordionGroup
        id="display"
        title={t("scatterChartWidgetOptions.displayTitle", "Display")}
        icon={<Analytics size="24" />}
      >
        <TextInput
          labelText={t("scatterChartWidgetOptions.chartTitle", "Chart Title")}
          id="chart-title"
          value={scatterChartOptions.title}
          onChange={(e) =>
            onChange({ ...scatterChartOptions, title: e.target.value })
          }
        />
      </OptionsAccordionGroup>

      <OptionsAccordionGroup
        id="data"
        title={t("scatterChartWidgetOptions.dataTitle", "Data")}
        icon={<DataBase size="24" />}
      >
        <ComboBox
          titleText={t("scatterChartWidgetOptions.chartType", "Chart Type")}
          id="chart-type"
          items={
            timeChartInfo
              ? [...timeChartInfo]
                  .sort((a, b) => a.title.localeCompare(b.title))
                  .map((info) => ({
                    id: info.id,
                    text: info.title,
                  }))
              : []
          }
          selectedItem={
            scatterChartOptions.type
              ? {
                  id: scatterChartOptions.type,
                  text:
                    timeChartInfo?.find(
                      (info) => info.id === scatterChartOptions.type,
                    )?.title || "",
                }
              : null
          }
          onChange={({ selectedItem }) => {
            if (selectedItem) {
              handleChartTypeChange(selectedItem.id);
            }
          }}
          itemToString={(item) => (item ? item.text : "")}
        />
      </OptionsAccordionGroup>
    </OptionsAccordion>
  );
};

export default ScatterChartWidgetOptionsForm;
