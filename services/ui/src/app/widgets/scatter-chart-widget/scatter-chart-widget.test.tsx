import { useQuery } from "@tanstack/react-query";
import { vi } from "vitest";
import { render, screen } from "../../../test-utils";
import { chartResolver } from "../../api/resolver/time-chart-resolver/time-chart-resolver";
import type { TimeChartData } from "../../api/resolver/time-chart-resolver/time-chart-resolver-types";
import { DatePeriod } from "../../types/date-types";
import { ScatterChartWidget } from "./scatter-chart-widget";
import type { ScatterChartWidgetOptions } from "./types";

const startDate = new Date("2023-01-01");
const endDate = new Date("2023-01-31");

vi.mock("../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate,
    endDate,
  }),
}));

// Mock the chartResolver
vi.mock("../../api/resolver/time-chart-resolver/time-chart-resolver", () => ({
  chartResolver: {
    getChart: vi.fn(),
  },
}));

// Mock the useQuery hook
vi.mock("@tanstack/react-query", async () => {
  const actual = await vi.importActual("@tanstack/react-query");
  return {
    ...actual,
    useQuery: vi.fn().mockImplementation(({ queryFn, enabled }) => {
      if (!enabled) {
        return { isLoading: false, error: null, data: null };
      }
      try {
        const data = queryFn();
        return { isLoading: false, error: null, data };
      } catch (error) {
        return { isLoading: false, error, data: null };
      }
    }),
  };
});

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock Carbon Charts
vi.mock("@carbon/charts-react", () => ({
  ScatterChart: ({ data, options }: any) => (
    <div data-testid="scatter-chart">
      <div data-testid="chart-data">{JSON.stringify(data)}</div>
      <div data-testid="chart-options">{JSON.stringify(options)}</div>
    </div>
  ),
  ScaleTypes: {
    LINEAR: "linear",
    TIME: "time",
    LABELS: "labels",
  },
}));

// Mock useMeasure hook
vi.mock("@uidotdev/usehooks", () => ({
  useMeasure: () => [vi.fn(), { height: 400, width: 600 }],
}));

// Mock useTheme hook
vi.mock("../../layout/theme", () => ({
  useTheme: () => ({ theme: "g10" }),
  ThemeMode: { DARK: "g100", LIGHT: "g10" },
}));

describe("ScatterChartWidget", () => {
  const mockFilters = { datePeriodRange: DatePeriod.today };

  const defaultOptions: ScatterChartWidgetOptions = {
    title: "Test Scatter Chart",
    type: "orders-customer-line-throughput",
    filters: mockFilters,
  };

  const mockTimeChartData: TimeChartData = {
    id: "orders-customer-line-throughput",
    series: [
      {
        id: "throughput",
        unit: "orders/hr",
        data: [
          { unit: "2023-01-01T08:00", value: 115 },
          { unit: "2023-01-01T09:00", value: 8 },
          { unit: "2023-01-01T10:00", value: 4 },
        ],
      },
    ],
  };

  const mockMultiSeriesTimeChartData: TimeChartData = {
    id: "orders-customer-line-throughput",
    series: [
      {
        id: "area_1_throughput",
        unit: "orders/hr",
        data: [
          { unit: "2023-01-01T08:00", value: 115 },
          { unit: "2023-01-01T09:00", value: 120 },
        ],
      },
      {
        id: "area_2_throughput",
        unit: "orders/hr",
        data: [
          { unit: "2023-01-01T08:00", value: 85 },
          { unit: "2023-01-01T09:00", value: 90 },
        ],
      },
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("rendering states", () => {
    it("should show initializing state when no type is provided", () => {
      const options = { ...defaultOptions, type: undefined };
      render(
        <ScatterChartWidget
          id="test-widget"
          type="scatter-chart"
          options={options}
          filters={mockFilters}
        />,
      );

      expect(screen.getByText("Test Scatter Chart")).toBeInTheDocument();
      expect(
        screen.getByTestId("widget-container-initializing"),
      ).toBeInTheDocument();
      expect(screen.getByText("Get started")).toBeInTheDocument();
    });

    it("should show loading state when query is loading", () => {
      (useQuery as any).mockReturnValue({
        isLoading: true,
        error: null,
        data: null,
      });

      render(
        <ScatterChartWidget
          id="test-widget"
          type="scatter-chart"
          options={defaultOptions}
          filters={mockFilters}
        />,
      );

      expect(screen.getByTestId("widget-container-loader")).toBeInTheDocument();
      expect(screen.getByText("Loading data")).toBeInTheDocument();
    });

    it("should show error state when query has error", () => {
      const mockError = new Error("API Error");
      (useQuery as any).mockReturnValue({
        isLoading: false,
        error: mockError,
        data: null,
      });

      render(
        <ScatterChartWidget
          id="test-widget"
          type="scatter-chart"
          options={defaultOptions}
          filters={mockFilters}
        />,
      );

      expect(screen.getByText("Error")).toBeInTheDocument();
    });

    it("should show no data state when response has no data", () => {
      (useQuery as any).mockReturnValue({
        isLoading: false,
        error: null,
        data: { success: true, data: null },
      });

      render(
        <ScatterChartWidget
          id="test-widget"
          type="scatter-chart"
          options={defaultOptions}
          filters={mockFilters}
        />,
      );

      expect(
        screen.getByTestId("widget-container-no-data"),
      ).toBeInTheDocument();
      expect(screen.getByText("No Data Available")).toBeInTheDocument();
    });
  });

  describe("data handling", () => {
    it("should render scatter chart with single series data", () => {
      (useQuery as any).mockReturnValue({
        isLoading: false,
        error: null,
        data: {
          success: true,
          data: mockTimeChartData,
        },
      });

      render(
        <ScatterChartWidget
          id="test-widget"
          type="scatter-chart"
          options={defaultOptions}
          filters={mockFilters}
        />,
      );

      expect(screen.getByTestId("scatter-chart")).toBeInTheDocument();

      const chartData = JSON.parse(
        screen.getByTestId("chart-data").textContent || "[]",
      );
      expect(chartData).toHaveLength(3);
      expect(chartData[0]).toEqual({
        group: "throughput",
        key: "2023-01-01T08:00",
        value: 115,
      });
    });

    it("should render scatter chart with multiple series data", () => {
      (useQuery as any).mockReturnValue({
        isLoading: false,
        error: null,
        data: {
          success: true,
          data: mockMultiSeriesTimeChartData,
        },
      });

      render(
        <ScatterChartWidget
          id="test-widget"
          type="scatter-chart"
          options={defaultOptions}
          filters={mockFilters}
        />,
      );

      expect(screen.getByTestId("scatter-chart")).toBeInTheDocument();

      const chartData = JSON.parse(
        screen.getByTestId("chart-data").textContent || "[]",
      );
      expect(chartData).toHaveLength(4); // 2 points per series, 2 series

      // Check first series
      expect(chartData[0]).toEqual({
        group: "area_1_throughput",
        key: "2023-01-01T08:00",
        value: 115,
      });

      // Check second series
      expect(chartData[2]).toEqual({
        group: "area_2_throughput",
        key: "2023-01-01T08:00",
        value: 85,
      });
    });

    it("should handle empty series data", () => {
      const emptyTimeChartData: TimeChartData = {
        id: "orders-customer-line-throughput",
        series: [],
      };

      (useQuery as any).mockReturnValue({
        isLoading: false,
        error: null,
        data: {
          success: true,
          data: emptyTimeChartData,
        },
      });

      render(
        <ScatterChartWidget
          id="test-widget"
          type="scatter-chart"
          options={defaultOptions}
          filters={mockFilters}
        />,
      );

      expect(screen.getByTestId("scatter-chart")).toBeInTheDocument();

      const chartData = JSON.parse(
        screen.getByTestId("chart-data").textContent || "[]",
      );
      expect(chartData).toHaveLength(0);
    });
  });

  describe("chart configuration", () => {
    it("should configure chart options correctly", () => {
      (useQuery as any).mockReturnValue({
        isLoading: false,
        error: null,
        data: {
          success: true,
          data: mockTimeChartData,
        },
      });

      render(
        <ScatterChartWidget
          id="test-widget"
          type="scatter-chart"
          options={defaultOptions}
          filters={mockFilters}
        />,
      );

      const chartOptions = JSON.parse(
        screen.getByTestId("chart-options").textContent || "{}",
      );

      expect(chartOptions.animations).toBe(true);
      expect(chartOptions.legend.enabled).toBe(true);
      expect(chartOptions.toolbar.enabled).toBe(false);
      expect(chartOptions.axes.left.mapsTo).toBe("value");
      expect(chartOptions.axes.left.title).toBe("orders/hr");
      expect(chartOptions.axes.bottom.mapsTo).toBe("key");
      expect(chartOptions.axes.bottom.title).toBe("Time Period");
    });

    it("should use fallback unit title when no unit provided", () => {
      const dataWithoutUnit: TimeChartData = {
        id: "orders-customer-line-throughput",
        series: [
          {
            id: "throughput",
            unit: "",
            data: [{ unit: "2023-01-01T08:00", value: 115 }],
          },
        ],
      };

      (useQuery as any).mockReturnValue({
        isLoading: false,
        error: null,
        data: {
          success: true,
          data: dataWithoutUnit,
        },
      });

      render(
        <ScatterChartWidget
          id="test-widget"
          type="scatter-chart"
          options={defaultOptions}
          filters={mockFilters}
        />,
      );

      const chartOptions = JSON.parse(
        screen.getByTestId("chart-options").textContent || "{}",
      );
      expect(chartOptions.axes.left.title).toBe("Value");
    });
  });

  describe("props and filters", () => {
    it("should merge widget and dashboard filters correctly", () => {
      const widgetFilters = {
        groupBy: "area",
        datePeriodRange: DatePeriod.today,
      };
      const dashboardFilters = { datePeriodRange: DatePeriod.last7days };

      const optionsWithFilters = {
        ...defaultOptions,
        filters: widgetFilters,
      };

      // Mock the chartResolver call directly
      const mockGetChart = vi.fn().mockResolvedValue({
        success: true,
        data: mockTimeChartData,
      });
      (chartResolver.getChart as any) = mockGetChart;

      (useQuery as any).mockImplementation(({ queryFn, enabled }: any) => {
        if (enabled) {
          queryFn();
        }
        return {
          isLoading: false,
          error: null,
          data: { success: true, data: mockTimeChartData },
        };
      });

      render(
        <ScatterChartWidget
          id="test-widget"
          type="scatter-chart"
          options={optionsWithFilters}
          filters={dashboardFilters}
        />,
      );

      expect(mockGetChart).toHaveBeenCalledWith(
        "orders-customer-line-throughput",
        { groupBy: "area", datePeriodRange: DatePeriod.last7days },
        startDate,
        endDate,
      );
    });

    it("should call chartResolver with correct parameters", () => {
      // Mock the chartResolver call directly
      const mockGetChart = vi.fn().mockResolvedValue({
        success: true,
        data: mockTimeChartData,
      });
      (chartResolver.getChart as any) = mockGetChart;

      (useQuery as any).mockImplementation(({ queryFn, enabled }: any) => {
        if (enabled) {
          queryFn();
        }
        return {
          isLoading: false,
          error: null,
          data: { success: true, data: mockTimeChartData },
        };
      });

      render(
        <ScatterChartWidget
          id="test-widget"
          type="scatter-chart"
          options={defaultOptions}
          filters={mockFilters}
        />,
      );

      expect(mockGetChart).toHaveBeenCalledWith(
        "orders-customer-line-throughput",
        mockFilters,
        startDate,
        endDate,
      );
    });
  });
});
