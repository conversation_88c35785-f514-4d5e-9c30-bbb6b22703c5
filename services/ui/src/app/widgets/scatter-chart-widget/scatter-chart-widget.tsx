import {
  ScatterChartOptions,
  ScaleTypes,
  Scatter<PERSON>hart,
} from "@carbon/charts-react";
import "@carbon/charts/styles.css";
import { useTheme } from "@carbon/react";
import { useQuery } from "@tanstack/react-query";
import { chartResolver } from "../../api/resolver/time-chart-resolver/time-chart-resolver";
import type { TimeChartData } from "../../api/resolver/time-chart-resolver/time-chart-resolver-types";
import type { TimeChartType } from "../../api/resolver/time-chart-resolver/time-chart-types";
import { ThemeMode } from "../../layout/theme";
import { WidgetContainer } from "../../components/widget-container/widget-container";
import type { BaseWidgetProps } from "../widget.types";
import styles from "./scatter-chart-widget.module.css";
import type { ScatterChartWidgetOptions } from "./types";
import { useMeasure } from "@uidotdev/usehooks";
import { useDates } from "../../hooks/use-dates";

interface ScatterChartWidgetProps
  extends BaseWidgetProps<ScatterChartWidgetOptions> {
  options: ScatterChartWidgetOptions;
}

export const ScatterChartWidget = ({
  options,
  filters,
}: ScatterChartWidgetProps) => {
  const mergedFilters = { ...options.filters, ...filters };
  const { startDate, endDate } = useDates(mergedFilters.datePeriodRange);
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;
  const [ref, { height }] = useMeasure();

  const {
    data: response,
    isLoading,
    error,
  } = useQuery({
    queryKey: [
      `scatter-chart-${mergedFilters?.datePeriodRange}-${mergedFilters?.groupBy}`,
      options.type,
      options.filters,
      filters.datePeriodRange,
    ],
    queryFn: () =>
      chartResolver.getChart(
        options.type as TimeChartType,
        mergedFilters,
        startDate,
        endDate,
      ),
    enabled: !!options.type,
    retry: 0,
  });

  if (!options.type) {
    return <WidgetContainer title={options.title} initializing />;
  }

  if (error) {
    return <WidgetContainer title={options.title} error={error} />;
  }

  if (isLoading || !response) {
    return <WidgetContainer title={options.title} loading />;
  }

  if (response?.success && !response.data) {
    return <WidgetContainer title={options.title} noData />;
  }

  if (response?.success && response.data) {
    const chartData = response.data as TimeChartData;

    const scatterData = chartData.series.flatMap((series) =>
      series.data.map((point) => ({
        group: series.id,
        key: point.unit,
        value: point.value,
      })),
    );

    const chartOptions: ScatterChartOptions = {
      animations: true,
      legend: { enabled: true },
      toolbar: { enabled: false },
      axes: {
        left: {
          mapsTo: "value",
          title: chartData.series[0]?.unit || "Value",
          scaleType: ScaleTypes.LINEAR,
          includeZero: false,
        },
        bottom: {
          mapsTo: "key",
          title: "Time Period",
          scaleType: ScaleTypes.LABELS,
          includeZero: false,
        },
      },
      height: height ? `${height - 5}px` : undefined,
      width: "100%",
      theme: isDark ? "g100" : "g10",
    };

    return (
      <WidgetContainer title={options.title}>
        <div className={styles.container} ref={ref}>
          <ScatterChart data={scatterData} options={chartOptions} />
        </div>
      </WidgetContainer>
    );
  }

  return null;
};

export default ScatterChartWidget;
