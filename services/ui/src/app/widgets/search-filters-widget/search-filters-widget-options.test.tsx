import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { vi, describe, it, beforeEach, expect } from "vitest";

// --- MOCKS ---
vi.mock("@tanstack/react-query", () => ({
	useQuery: vi.fn(),
}));
vi.mock("../../api/resolver/search-filter-resolver/search-filter-resolver", () => ({
	searchFilterResolver: {
		getSearchFilterInfo: vi.fn(),
	}
}));

import SearchFiltersWidgetOptionsForm from "./search-filters-widget-options";
import { useQuery } from "@tanstack/react-query";

// --- Mock Data ---
const searchFilterInfoMock = [
	{ id: "type_a", label: "Type A", apiKey: "type_a_api", apiSearchKey: "type_a_search" },
	{ id: "type_b", label: "Type B", apiKey: "type_b_api", apiSearchKey: "type_b_search" },
	{ id: "type_c", label: "Type C", apiKey: "type_c_api", apiSearchKey: "type_c_search" },
];

describe("SearchFiltersWidgetOptionsForm", () => {
	let onChange: ReturnType<typeof vi.fn>;
	let options: any;

	beforeEach(() => {
		window.HTMLElement.prototype.scrollIntoView = vi.fn();

		vi.clearAllMocks();
		(useQuery as any).mockReturnValue({
			data: searchFilterInfoMock,
		});
		onChange = vi.fn();
		options = {
			dataTypeLabel: "Type A",
			dataTypeId: "type_a",
			apiKey: "type_a_api",
			apiSearchKey: "type_a_search",
			selectedFilters: [
				{ id: "type_b", label: "Type B", apiKey: "type_b_api", apiSearchKey: "type_b_search" },
			]
		};
	});

	it("calls onChange when Add Filter is clicked", () => {
		render(<SearchFiltersWidgetOptionsForm options={{ ...options, selectedFilters: [] }} onChange={onChange} />);
		fireEvent.click(screen.getByRole("button", { name: /Add filter/i }));
		expect(onChange).toHaveBeenCalledWith(expect.objectContaining({
			selectedFilters: [{ id: "", label: "" }]
		}));
	});

	it("calls onChange when Remove filter button is clicked", () => {
		render(<SearchFiltersWidgetOptionsForm options={options} onChange={onChange} />);
		fireEvent.click(screen.getByRole("button", { name: /Remove filter/i }));
		expect(onChange).toHaveBeenCalledWith(expect.objectContaining({
			selectedFilters: []
		}));
	});

	it("calls onChange when selecting a new Data Type", () => {
		render(<SearchFiltersWidgetOptionsForm options={options} onChange={onChange} />);
		const dataTypeInput = screen.getByRole('combobox', { name: /Data Type/ });
		fireEvent.change(dataTypeInput, { target: { value: 'Type C' } });
		fireEvent.click(screen.getByText("Type C"));

		expect(onChange).toHaveBeenCalledWith(expect.objectContaining({
			dataTypeLabel: "Type C",
			dataTypeId: "type_c",
			apiKey: "type_c_api",
			apiSearchKey: "type_c_search"
		}));
	});

	it("calls onChange when selecting a new Additional Filter", () => {
		// Start with no additional filters so we can add Type C
		const opts = { ...options, selectedFilters: [{ id: "", label: "" }] };

		render(<SearchFiltersWidgetOptionsForm options={opts} onChange={onChange} />);

		const filterInput = screen.getByRole('combobox', { name: /Additional Filter/ });
		fireEvent.change(filterInput, { target: { value: 'Type C' } });
		fireEvent.click(screen.getByText("Type C"));

		// The current component does not include apiKey/apiSearchKey for additional filters
		expect(onChange).toHaveBeenCalledWith(expect.objectContaining({
			selectedFilters: [
				{ id: "type_c", label: "Type C", apiKey: undefined, apiSearchKey: undefined }
			]
		}));
	});
});
