@use "@carbon/colors";

/**
 * This file is used to override the Carbon theme variables and tokens
 * and force specific component styles if needed.
 * 
 * To use:
 *  - Add custom colors under the Carbon theme variables section.
 *  - Use the colors on custom tokens under the Custom theme tokens section.
 *  - Add custom component overrides under the Carbon component theme overrides section.
 *
 * ICT uses "g10" for "light" theme and "g100" for "dark" theme.
 */

////////////////////////////////////////////////////////////////////////////////////////
// Carbon theme variables
////////////////////////////////////////////////////////////////////////////////////////

// Define our brand colors as variables for easy reference
$dematic-gold: #ffb517;
$dematic-gold-hover: #cc9112;
$dematic-gold-active: #996d0e;

////////////////////////////////////////////////////////////////////////////////////////
// Custom theme tokens (https://carbondesignsystem.com/elements/color/tokens/#layer-accent)
////////////////////////////////////////////////////////////////////////////////////////

// Define our custom light theme tokens (g10)
$custom-light-theme-tokens: (
  // Brand colors
  interactive: $dematic-gold,
  background-brand: $dematic-gold,
  border-interactive: $dematic-gold,
  focus: colors.$gray-100,

  // Button tokens
  button-primary: $dematic-gold,
  button-primary-hover: $dematic-gold-hover,
  button-primary-active: $dematic-gold-active,

  button-tertiary: colors.$gray-80,
  button-tertiary-hover: colors.$gray-80-hover,
  button-tertiary-active: colors.$gray-60,

  // AI tokens - Light theme
  ai-aura-start: #4589ff1a,
  ai-aura-start-sm: #4589ff29,
  ai-aura-end: #ffffff00,
  ai-aura-hover-start: #4589ff,
  ai-aura-hover-end: #ffffff00,
  ai-aura-hover-background: #edf5ff,
  ai-border-start: #a6c8ffa3,
  ai-border-end: #78a9ff,
  ai-border-strong: #4589ff,
  ai-drop-shadow: #0f62fe1a,
  ai-inner-shadow: #4589ff1a,
  ai-popover-background: #ffffff,
  ai-popover-shadow-outer-01: #0043ce0f,
  ai-popover-shadow-outer-02: #0000000a,
  ai-skeleton-element: #4589ff,
  ai-skeleton-background: #d0e2ff,
  ai-overlay: #00114180,

  chat-avatar-bot: #6f6f6f,
  chat-avatar-agent: #393939,
  chat-avatar-user: #0f62fe,
  chat-bubble-user: #e0e0e0,
  chat-bubble-agent: #ffffff,
  chat-bubble-border: #e0e0e0,
  chat-prompt-background: #ffffff,
  chat-prompt-border-start: #f4f4f4,
  chat-prompt-border-end: #f4f4f400,
  chat-shell-background: #ffffff,
  chat-header-background: #ffffff,
  chat-button: #0f62fe,
  chat-button-hover: #8d8d8d1f,
  chat-button-text-hover: #0043ce,
  chat-button-active: #8d8d8d80,
  chat-button-selected: #8d8d8d33,
  chat-button-text-selected: #525252,

  dashboard-summary-text: #ffff,

  box-shadow-ai: (
    0 0 1px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.12),
    0 6px 24px rgba(0, 0, 0, 0.08)
  )
);

// Define our custom dark theme tokens (g100)
$custom-dark-theme-tokens: (
  // Brand colors
  interactive: $dematic-gold,
  background-brand: $dematic-gold,
  border-interactive: $dematic-gold,

  // Button tokens
  button-primary: $dematic-gold,
  button-primary-hover: $dematic-gold-hover,
  button-primary-active: $dematic-gold-active,

  // AI tokens - Dark theme
  ai-aura-start: #4589ff1a,
  ai-aura-start-sm: #4589ff29,
  ai-aura-end: #00000000,
  ai-aura-hover-start: #4589ff66,
  ai-aura-hover-end: #00000000,
  ai-aura-hover-background: #e8e8e8,
  ai-border-start: #a6c8ff5c,
  ai-border-end: #4589ff,
  ai-border-strong: #78a9ff,
  ai-drop-shadow: #00000047,
  ai-inner-shadow: #4589ff29,
  ai-popover-background: #161616,
  ai-popover-shadow-outer-01: #0000001f,
  ai-popover-shadow-outer-02: #00000014,
  ai-skeleton-element: #78a9ff4d,
  ai-skeleton-background: #78a9ff80,
  ai-overlay: #00000080,

  chat-avatar-bot: #8d8d8d,
  chat-avatar-agent: #c6c6c6,
  chat-avatar-user: #4589ff,
  chat-bubble-user: #393939,
  chat-bubble-agent: #262626,
  chat-bubble-border: #525252,
  chat-prompt-background: #161616,
  chat-prompt-border-start: #262626,
  chat-prompt-border-end: #26262600,
  chat-shell-background: #262626,
  chat-header-background: #262626,
  chat-button: #78a9ff,
  chat-button-hover: #8d8d8d29,
  chat-button-text-hover: #a6c8ff,
  chat-button-active: #8d8d8d66,
  chat-button-selected: #8d8d8d3d,
  chat-button-text-selected: #c6c6c6,
  box-shadow-ai: (
    0 0 1px rgba(0, 0, 0, 0.6),
    0 4px 8px rgba(0, 0, 0, 0.5),
    0 6px 24px rgba(0, 0, 0, 0.4)
  )
);

// Apply custom theme tokens to light themes
.cds--g10 {
  @each $token, $value in $custom-light-theme-tokens {
    --cds-#{$token}: #{$value};
  }
}

// Apply custom theme tokens to dark themes
.cds--g100 {
  @each $token, $value in $custom-dark-theme-tokens {
    --cds-#{$token}: #{$value};
  }
}

////////////////////////////////////////////////////////////////////////////////////////
// Carbon component theme overrides (https://carbondesignsystem.com/components)
////////////////////////////////////////////////////////////////////////////////////////

// Primary button styles
.cds--btn--primary {
  color: colors.$gray-100;
  &:hover {
    color: colors.$gray-100;
  }
}

.cds--header {
  background-color: var(--cds-layer);
}

.cds--accordion__content {
  padding-inline-end: 15px;
}

/** Fixes header user menu from being hidden behind the header bar */
.cds--overflow-menu-options {
  z-index: 20000;
}

/** Panel style - prevents panel from "squishing" during animatio */
.cds--header-panel {
  width: 320px !important; /* Fixed width for panels */
  transform: translateX(100%);
  transition: transform 0.2s ease-out !important;
  overflow-x: hidden;
}

.cds--header-panel--expanded {
  transform: translateX(0);
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
}
